"""FinRL-Crypto: 加密货币强化学习交易框架

这是一个专门为加密货币交易设计的强化学习框架，提供完整的数据处理、
环境建模、智能体训练和策略回测功能。

主要模块:
    - data: 数据获取和处理
    - environment: 交易环境
    - agent: 强化学习智能体
    - strategy: 交易策略
    - backtest: 回测系统
    - risk: 风险管理
    - visualization: 可视化
    - utils: 工具函数
"""

__version__ = "0.1.0"
__author__ = "FinRL-Crypto Team"
__email__ = "<EMAIL>"

# 核心模块导入
try:
    from . import data
    from . import environment
    from . import agent
    from . import training
except ImportError:
    # 在开发阶段，某些模块可能还未实现
    pass

# 便捷导入
__all__ = [
    "data",
    "environment", 
    "agent",
    "training",
    "__version__"
]

# 配置日志
import logging
logging.getLogger(__name__).addHandler(logging.NullHandler())