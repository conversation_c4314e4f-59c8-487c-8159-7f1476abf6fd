#!/usr/bin/env python3
"""
ElegantRL训练器适配器

这个模块实现了TrainerInterface接口，将ElegantRL框架封装为统一的训练接口。
通过这个适配器，可以在不修改业务逻辑的情况下使用ElegantRL进行训练。

作者: FinRL-Crypto 重构项目
日期: 2024
"""

import os
import torch
import numpy as np
from typing import Any, Dict, List, Optional
from pathlib import Path

# ElegantRL相关导入
from train.config import Arguments
from train.run import train_and_evaluate, init_agent
from drl_agents.agents import AgentDDPG, AgentPPO, AgentSAC, AgentTD3, AgentA2C

# 接口导入
from interfaces.trainer_interface import (
    TrainerInterface, ModelInterface, TrainingError, EvaluationError, 
    PredictionError, ModelNotFoundError, ConfigurationError
)


class ElegantRLAdapter(TrainerInterface):
    """
    ElegantRL训练器适配器
    
    将ElegantRL框架封装为统一的训练接口，提供训练、评估、预测等功能。
    """
    
    # 支持的模型映射
    MODELS = {
        "ddpg": AgentDDPG, 
        "td3": AgentTD3, 
        "sac": AgentSAC, 
        "ppo": AgentPPO, 
        "a2c": AgentA2C
    }
    
    # 离线策略模型
    OFF_POLICY_MODELS = ["ddpg", "td3", "sac"]
    
    # 在线策略模型
    ON_POLICY_MODELS = ["ppo", "a2c"]
    
    def __init__(self):
        """
        初始化ElegantRL适配器
        """
        self.current_model = None
        self.current_args = None
    
    def train(self, agent_config: Dict[str, Any], env_config: Dict[str, Any], 
              training_config: Dict[str, Any]) -> str:
        """
        训练强化学习代理
        
        Args:
            agent_config: 代理配置参数，包含model_name, model_kwargs等
            env_config: 环境配置参数，包含env_class, price_array, tech_array等
            training_config: 训练配置参数，包含cwd, total_timesteps, gpu_id等
            
        Returns:
            str: 训练结果保存路径
        """
        try:
            # 验证配置
            self._validate_training_config(agent_config, env_config, training_config)
            
            # 提取配置参数
            model_name = agent_config['model_name']
            model_kwargs = agent_config.get('model_kwargs', {})
            
            env_class = env_config['env_class']
            price_array = env_config['price_array']
            tech_array = env_config['tech_array']
            env_params = env_config['env_params']
            if_log = env_config.get('if_log', True)
            
            cwd = training_config['cwd']
            total_timesteps = training_config.get('total_timesteps', 5000)
            gpu_id = training_config.get('gpu_id', 0)
            
            # 创建环境配置
            env_config_dict = {
                "price_array": price_array,
                "tech_array": tech_array,
                "if_train": True,  # 训练模式
            }
            
            # 创建环境实例
            env = env_class(config=env_config_dict, env_params=env_params, if_log=if_log)
            env.env_num = 1
            
            # 获取代理类
            if model_name not in self.MODELS:
                raise ModelNotFoundError(f"不支持的模型: {model_name}")
            
            agent_class = self.MODELS[model_name]
            
            # 创建Arguments对象
            args = Arguments(agent=agent_class, env=env)
            args.learner_gpus = gpu_id
            args.cwd = cwd
            args.break_step = total_timesteps
            
            # 设置策略类型
            if model_name in self.OFF_POLICY_MODELS:
                args.if_off_policy = True
            else:
                args.if_off_policy = False
            
            # 应用模型参数
            if model_kwargs:
                self._apply_model_kwargs(args, model_kwargs)
            
            # 确保保存目录存在
            os.makedirs(cwd, exist_ok=True)
            
            # 保存当前配置
            self.current_args = args
            
            # 开始训练
            train_and_evaluate(args)
            
            return cwd
            
        except Exception as e:
            raise TrainingError(f"训练过程中发生错误: {str(e)}") from e
    
    def evaluate(self, model_path: str, env_config: Dict[str, Any], 
                 evaluation_config: Dict[str, Any]) -> Dict[str, float]:
        """
        评估训练好的模型
        
        Args:
            model_path: 模型文件路径
            env_config: 环境配置参数
            evaluation_config: 评估配置参数
            
        Returns:
            Dict[str, float]: 评估指标字典
        """
        try:
            # 使用predict方法获取资产变化
            episode_total_assets = self.predict(model_path, env_config, evaluation_config)
            
            if not episode_total_assets:
                raise EvaluationError("评估结果为空")
            
            # 计算评估指标
            initial_asset = episode_total_assets[0]
            final_asset = episode_total_assets[-1]
            
            total_return = (final_asset - initial_asset) / initial_asset
            max_asset = max(episode_total_assets)
            min_asset = min(episode_total_assets)
            max_drawdown = (max_asset - min_asset) / max_asset if max_asset > 0 else 0
            
            # 计算夏普比率（简化版本）
            returns = np.diff(episode_total_assets) / episode_total_assets[:-1]
            sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
            
            return {
                'total_return': total_return,
                'final_asset': final_asset,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'volatility': np.std(returns)
            }
            
        except Exception as e:
            raise EvaluationError(f"评估过程中发生错误: {str(e)}") from e
    
    def predict(self, model_path: str, env_config: Dict[str, Any], 
                prediction_config: Dict[str, Any]) -> List[float]:
        """
        使用训练好的模型进行预测
        
        Args:
            model_path: 模型文件路径
            env_config: 环境配置参数
            prediction_config: 预测配置参数
            
        Returns:
            List[float]: 预测结果（总资产变化）
        """
        try:
            # 提取配置参数
            model_name = prediction_config['model_name']
            net_dimension = prediction_config.get('net_dimension', 512)
            gpu_id = prediction_config.get('gpu_id', 0)
            
            env_class = env_config['env_class']
            price_array = env_config['price_array']
            tech_array = env_config['tech_array']
            env_params = env_config['env_params']
            if_log = env_config.get('if_log', False)
            
            # 验证模型名称
            if model_name not in self.MODELS:
                raise ModelNotFoundError(f"不支持的模型: {model_name}")
            
            # 创建环境配置
            env_config_dict = {
                "price_array": price_array,
                "tech_array": tech_array,
                "if_train": False,  # 预测模式
            }
            
            # 创建环境实例
            environment = env_class(config=env_config_dict, env_params=env_params, if_log=if_log)
            environment.env_num = 1
            
            # 获取代理类
            agent_class = self.MODELS[model_name]
            
            # 创建Arguments对象
            args = Arguments(agent=agent_class, env=environment)
            args.cwd = model_path
            args.net_dim = net_dimension
            
            # 加载代理
            try:
                agent = init_agent(args, gpu_id=gpu_id)
                act = agent.act
                device = agent.device
            except Exception as e:
                raise PredictionError(f"加载代理失败: {str(e)}") from e
            
            # 执行预测
            state = environment.reset()
            episode_total_assets = [environment.initial_total_asset]
            
            with torch.no_grad():
                for i in range(environment.max_step):
                    # 转换状态为张量
                    s_tensor = torch.as_tensor((state,), device=device)
                    a_tensor = act(s_tensor)
                    action = a_tensor.detach().cpu().numpy()[0]
                    
                    # 执行动作
                    state, reward, done, _ = environment.step(action)
                    
                    # 计算总资产
                    total_asset = (
                        environment.cash + 
                        (environment.price_array[environment.time] * environment.stocks).sum()
                    )
                    episode_total_assets.append(total_asset)
                    
                    if done:
                        break
            
            return episode_total_assets
            
        except Exception as e:
            if isinstance(e, (PredictionError, ModelNotFoundError)):
                raise
            raise PredictionError(f"预测过程中发生错误: {str(e)}") from e
    
    def get_supported_models(self) -> List[str]:
        """
        获取支持的模型列表
        
        Returns:
            List[str]: 支持的模型名称列表
        """
        return list(self.MODELS.keys())
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        验证配置参数的有效性
        
        Args:
            config: 配置参数字典
            
        Returns:
            bool: 配置是否有效
        """
        try:
            # 基本配置验证
            required_keys = ['model_name']
            for key in required_keys:
                if key not in config:
                    return False
            
            # 验证模型名称
            if config['model_name'] not in self.MODELS:
                return False
            
            # 验证模型参数
            if 'model_kwargs' in config:
                model_kwargs = config['model_kwargs']
                if not isinstance(model_kwargs, dict):
                    return False
                
                # 验证必要的参数类型
                numeric_params = ['learning_rate', 'batch_size', 'gamma', 'net_dimension', 'target_step']
                for param in numeric_params:
                    if param in model_kwargs and not isinstance(model_kwargs[param], (int, float)):
                        return False
            
            return True
            
        except Exception:
            return False
    
    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """
        获取模型信息
        
        Args:
            model_name: 模型名称
            
        Returns:
            Dict[str, Any]: 模型信息字典
        """
        if model_name not in self.MODELS:
            raise ModelNotFoundError(f"模型 '{model_name}' 不存在")
        
        agent_class = self.MODELS[model_name]
        
        return {
            'name': model_name,
            'class': agent_class.__name__,
            'policy_type': 'off_policy' if model_name in self.OFF_POLICY_MODELS else 'on_policy',
            'description': agent_class.__doc__.split('\n')[0] if agent_class.__doc__ else '',
            'supported_parameters': [
                'learning_rate', 'batch_size', 'gamma', 'net_dimension', 
                'target_step', 'eval_time_gap'
            ]
        }
    
    def _validate_training_config(self, agent_config: Dict[str, Any], 
                                  env_config: Dict[str, Any], 
                                  training_config: Dict[str, Any]) -> None:
        """
        验证训练配置的完整性
        
        Args:
            agent_config: 代理配置
            env_config: 环境配置
            training_config: 训练配置
            
        Raises:
            ConfigurationError: 配置错误
        """
        # 验证代理配置
        if 'model_name' not in agent_config:
            raise ConfigurationError("代理配置中缺少 'model_name'")
        
        if not self.validate_config(agent_config):
            raise ConfigurationError("代理配置验证失败")
        
        # 验证环境配置
        required_env_keys = ['env_class', 'price_array', 'tech_array', 'env_params']
        for key in required_env_keys:
            if key not in env_config:
                raise ConfigurationError(f"环境配置中缺少 '{key}'")
        
        # 验证训练配置
        if 'cwd' not in training_config:
            raise ConfigurationError("训练配置中缺少 'cwd'")
    
    def _apply_model_kwargs(self, args: Arguments, model_kwargs: Dict[str, Any]) -> None:
        """
        应用模型参数到Arguments对象
        
        Args:
            args: Arguments对象
            model_kwargs: 模型参数字典
        """
        param_mapping = {
            'learning_rate': 'learning_rate',
            'batch_size': 'batch_size',
            'gamma': 'gamma',
            'net_dimension': 'net_dim',
            'target_step': 'target_step',
            'eval_time_gap': 'eval_gap'
        }
        
        for param_key, args_key in param_mapping.items():
            if param_key in model_kwargs:
                setattr(args, args_key, model_kwargs[param_key])


class ElegantRLModel(ModelInterface):
    """
    ElegantRL模型包装器
    
    将ElegantRL的模型封装为统一的模型接口。
    """
    
    def __init__(self, agent, device):
        """
        初始化模型包装器
        
        Args:
            agent: ElegantRL代理对象
            device: 计算设备
        """
        self.agent = agent
        self.device = device
        self.act = agent.act
    
    def save(self, path: str) -> None:
        """
        保存模型到指定路径
        
        Args:
            path: 保存路径
        """
        os.makedirs(path, exist_ok=True)
        self.agent.save_or_load_agent(path, if_save=True)
    
    def load(self, path: str) -> None:
        """
        从指定路径加载模型
        
        Args:
            path: 模型文件路径
        """
        if not os.path.exists(path):
            raise FileNotFoundError(f"模型路径不存在: {path}")
        
        self.agent.save_or_load_agent(path, if_save=False)
    
    def predict(self, state: np.ndarray) -> np.ndarray:
        """
        根据状态预测动作
        
        Args:
            state: 环境状态
            
        Returns:
            np.ndarray: 预测的动作
        """
        with torch.no_grad():
            s_tensor = torch.as_tensor((state,), device=self.device)
            a_tensor = self.act(s_tensor)
            return a_tensor.detach().cpu().numpy()[0]