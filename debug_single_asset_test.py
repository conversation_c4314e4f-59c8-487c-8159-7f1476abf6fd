#!/usr/bin/env python3
"""
调试SingleAssetTrainer测试问题
"""

import sys
import os
sys.path.insert(0, os.getcwd())

try:
    # 导入必要的模块
    from finrl_crypto.training.single_asset import SingleAssetTrainer
    from finrl_crypto.training.base import TrainingConfig
    from finrl_crypto.agent.base import BaseAgent
    from finrl_crypto.environment.base import BaseEnvironment
    import pandas as pd
    import numpy as np
    print("✓ 所有导入成功")
    
    # 创建mock数据
    mock_data = pd.DataFrame({
        'timestamp': pd.date_range('2023-01-01', periods=1000, freq='h'),
        'open': 100 + np.random.randn(1000) * 5,
        'high': 105 + np.random.randn(1000) * 5,
        'low': 95 + np.random.randn(1000) * 5,
        'close': 102 + np.random.randn(1000) * 5,
        'volume': 1000 + np.random.randn(1000) * 100
    })
    
    # 创建mock agent
    class MockAgent(BaseAgent):
        def __init__(self):
            super().__init__(state_dim=10, action_dim=3)
        
        def _build_networks(self):
            pass
        
        def _build_optimizers(self):
            pass
        
        def _get_save_dict(self):
            return {}
        
        def _load_from_dict(self, data):
            pass
        
        def act(self, observation, deterministic=True):
            return [0.5, 0.3, 0.2]
        
        def predict(self, observation):
            return [0.5, 0.3, 0.2]
        
        def learn(self, *args, **kwargs):
            pass
        
        def save(self, path):
            pass
        
        def load(self, path):
            pass
    
    # 创建mock environment
    class MockEnvironment(BaseEnvironment):
        def __init__(self):
            super().__init__(data=mock_data)
        
        def _setup_spaces(self, state_space, action_space):
            pass
        
        def _reset_specific_state(self):
            pass
        
        def _get_state(self):
            return [1.0, 2.0, 3.0]
        
        def _execute_action(self, action):
            return True
        
        def _calculate_reward(self):
            return 0.1
        
        def reset(self):
            return [1.0, 2.0, 3.0]
        
        def step(self, action):
            return [1.0, 2.0, 3.0], 0.1, False, {}
        
        def render(self):
            pass
    
    # 创建实例
    config = TrainingConfig()
    agent = MockAgent()
    env = MockEnvironment()
    
    # 测试SingleAssetTrainer初始化
    trainer = SingleAssetTrainer(agent=agent, env=env, config=config)
    trainer.data = mock_data
    trainer.symbol = "BTCUSDT"
    
    print("✓ SingleAssetTrainer初始化成功")
    print(f"✓ Symbol: {trainer.symbol}")
    print(f"✓ Data length: {len(trainer.data)}")
    print(f"✓ Has data attribute: {hasattr(trainer, 'data')}")
    
    # 运行测试断言
    assert trainer.symbol == "BTCUSDT"
    assert hasattr(trainer, 'data')
    assert len(trainer.data) == 1000
    
    print("✓ 所有测试断言通过")
    
except Exception as e:
    print(f"✗ 错误: {e}")
    import traceback
    traceback.print_exc()