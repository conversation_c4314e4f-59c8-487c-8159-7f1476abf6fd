"""基础训练器模块

定义强化学习训练的基础抽象类和通用功能。
"""

import os
import time
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass, field
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from collections import deque

from ..agent.base import BaseAgent
from ..environment.base import BaseEnvironment


@dataclass
class TrainingConfig:
    """训练配置类"""
    total_timesteps: int = 100000
    eval_freq: int = 10000
    save_freq: int = 50000
    log_freq: int = 1000
    n_eval_episodes: int = 10
    early_stopping_patience: int = 10
    early_stopping_threshold: float = 1e-4
    max_no_improvement_evals: int = 5
    save_best_model: bool = True
    save_replay_buffer: bool = False
    tensorboard_log: Optional[str] = None
    verbose: int = 1
    seed: Optional[int] = None
    
    # 训练参数
    learning_starts: int = 1000
    train_freq: int = 1
    gradient_steps: int = 1
    target_update_interval: int = 1
    
    # 评估参数
    eval_env: Optional[BaseEnvironment] = None
    deterministic_eval: bool = True
    render_eval: bool = False
    
    # 回调函数
    callback: Optional[Callable] = None
    
    # 其他参数
    device: str = 'auto'
    

@dataclass
class EvaluationMetrics:
    """评估指标类"""
    episode_rewards: List[float] = field(default_factory=list)
    episode_lengths: List[int] = field(default_factory=list)
    success_rate: float = 0.0
    mean_reward: float = 0.0
    std_reward: float = 0.0
    min_reward: float = 0.0
    max_reward: float = 0.0
    
    # 交易相关指标
    total_return: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    volatility: float = 0.0
    win_rate: float = 0.0
    
    # 时间信息
    evaluation_time: float = 0.0
    timestamp: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'mean_reward': self.mean_reward,
            'std_reward': self.std_reward,
            'min_reward': self.min_reward,
            'max_reward': self.max_reward,
            'total_return': self.total_return,
            'sharpe_ratio': self.sharpe_ratio,
            'max_drawdown': self.max_drawdown,
            'volatility': self.volatility,
            'win_rate': self.win_rate,
            'success_rate': self.success_rate,
            'evaluation_time': self.evaluation_time,
            'timestamp': self.timestamp,
        }


class BaseTrainer(ABC):
    """基础训练器抽象类
    
    定义强化学习训练的通用接口和基础功能。
    """
    
    def __init__(self,
                 agent: BaseAgent,
                 env: BaseEnvironment,
                 config: Optional[TrainingConfig] = None,
                 save_path: str = "./models",
                 experiment_name: str = "experiment"):
        """初始化训练器
        
        Args:
            agent: 智能体
            env: 环境
            config: 训练配置
            save_path: 模型保存路径
            experiment_name: 实验名称
        """
        self.agent = agent
        self.env = env
        self.config = config or TrainingConfig()
        self.save_path = save_path
        self.experiment_name = experiment_name
        
        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)
        
        # 设置设备
        if self.config.device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(self.config.device)
        
        # 设置随机种子
        if self.config.seed is not None:
            self._set_seed(self.config.seed)
        
        # 训练状态
        self.current_timestep = 0
        self.current_episode = 0
        self.best_mean_reward = -np.inf
        self.no_improvement_count = 0
        
        # 训练历史
        self.training_history = {
            'timesteps': [],
            'episodes': [],
            'rewards': [],
            'losses': [],
            'eval_rewards': [],
            'eval_timesteps': [],
        }
        
        # 设置日志
        self._setup_logging()
        
        # 评估环境
        self.eval_env = self.config.eval_env or env
        
    def _set_seed(self, seed: int):
        """设置随机种子"""
        np.random.seed(seed)
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
        self.env.seed(seed)
        if hasattr(self.eval_env, 'seed'):
            self.eval_env.seed(seed + 1)
    
    def _setup_logging(self):
        """设置日志"""
        self.logger = logging.getLogger(f"{self.__class__.__name__}_{self.experiment_name}")
        self.logger.setLevel(logging.INFO if self.config.verbose > 0 else logging.WARNING)
        
        # 创建处理器
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    @abstractmethod
    def train(self) -> Dict[str, Any]:
        """训练智能体
        
        Returns:
            训练结果字典
        """
        pass
    
    @abstractmethod
    def evaluate(self, n_episodes: Optional[int] = None) -> EvaluationMetrics:
        """评估智能体
        
        Args:
            n_episodes: 评估回合数
            
        Returns:
            评估指标
        """
        pass
    
    def save_model(self, path: Optional[str] = None):
        """保存模型
        
        Args:
            path: 保存路径
        """
        if path is None:
            path = os.path.join(self.save_path, f"{self.experiment_name}_best.pth")
        
        self.agent.save(path)
        self.logger.info(f"模型已保存到: {path}")
    
    def load_model(self, path: str):
        """加载模型
        
        Args:
            path: 模型路径
        """
        self.agent.load(path)
        self.logger.info(f"模型已从 {path} 加载")
    
    def save_training_history(self, path: Optional[str] = None):
        """保存训练历史
        
        Args:
            path: 保存路径
        """
        if path is None:
            path = os.path.join(self.save_path, f"{self.experiment_name}_history.pkl")
        
        import pickle
        with open(path, 'wb') as f:
            pickle.dump(self.training_history, f)
        
        self.logger.info(f"训练历史已保存到: {path}")
    
    def plot_training_curves(self, save_path: Optional[str] = None, show: bool = True):
        """绘制训练曲线
        
        Args:
            save_path: 图片保存路径
            show: 是否显示图片
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'Training Curves - {self.experiment_name}')
        
        # 训练奖励
        if self.training_history['rewards']:
            axes[0, 0].plot(self.training_history['timesteps'], 
                           self.training_history['rewards'])
            axes[0, 0].set_title('Training Rewards')
            axes[0, 0].set_xlabel('Timesteps')
            axes[0, 0].set_ylabel('Reward')
            axes[0, 0].grid(True)
        
        # 损失
        if self.training_history['losses']:
            axes[0, 1].plot(self.training_history['timesteps'][:len(self.training_history['losses'])], 
                           self.training_history['losses'])
            axes[0, 1].set_title('Training Loss')
            axes[0, 1].set_xlabel('Timesteps')
            axes[0, 1].set_ylabel('Loss')
            axes[0, 1].grid(True)
        
        # 评估奖励
        if self.training_history['eval_rewards']:
            axes[1, 0].plot(self.training_history['eval_timesteps'], 
                           self.training_history['eval_rewards'])
            axes[1, 0].set_title('Evaluation Rewards')
            axes[1, 0].set_xlabel('Timesteps')
            axes[1, 0].set_ylabel('Mean Reward')
            axes[1, 0].grid(True)
        
        # 回合长度
        if self.training_history['episodes']:
            episode_lengths = [len(ep) for ep in self.training_history['episodes'] if ep]
            if episode_lengths:
                axes[1, 1].plot(episode_lengths)
                axes[1, 1].set_title('Episode Lengths')
                axes[1, 1].set_xlabel('Episode')
                axes[1, 1].set_ylabel('Length')
                axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"训练曲线已保存到: {save_path}")
        
        if show:
            plt.show()
        else:
            plt.close()
    
    def _should_evaluate(self) -> bool:
        """判断是否应该进行评估"""
        return (self.current_timestep % self.config.eval_freq == 0 and 
                self.current_timestep > 0)
    
    def _should_save(self) -> bool:
        """判断是否应该保存模型"""
        return (self.current_timestep % self.config.save_freq == 0 and 
                self.current_timestep > 0)
    
    def _should_log(self) -> bool:
        """判断是否应该记录日志"""
        return (self.current_timestep % self.config.log_freq == 0 and 
                self.current_timestep > 0)
    
    def _check_early_stopping(self, mean_reward: float) -> bool:
        """检查是否应该早停
        
        Args:
            mean_reward: 当前平均奖励
            
        Returns:
            是否应该早停
        """
        if mean_reward > self.best_mean_reward + self.config.early_stopping_threshold:
            self.best_mean_reward = mean_reward
            self.no_improvement_count = 0
            return False
        else:
            self.no_improvement_count += 1
            return self.no_improvement_count >= self.config.max_no_improvement_evals
    
    def _log_training_info(self, info: Dict[str, Any]):
        """记录训练信息
        
        Args:
            info: 训练信息字典
        """
        if self.config.verbose > 0:
            log_msg = f"Timestep: {self.current_timestep}"
            for key, value in info.items():
                if isinstance(value, float):
                    log_msg += f", {key}: {value:.4f}"
                else:
                    log_msg += f", {key}: {value}"
            self.logger.info(log_msg)
    
    def get_training_info(self) -> Dict[str, Any]:
        """获取训练信息
        
        Returns:
            训练信息字典
        """
        return {
            'current_timestep': self.current_timestep,
            'current_episode': self.current_episode,
            'best_mean_reward': self.best_mean_reward,
            'no_improvement_count': self.no_improvement_count,
            'device': str(self.device),
            'experiment_name': self.experiment_name,
            'save_path': self.save_path,
        }
    
    def reset_training_state(self):
        """重置训练状态"""
        self.current_timestep = 0
        self.current_episode = 0
        self.best_mean_reward = -np.inf
        self.no_improvement_count = 0
        
        # 清空训练历史
        for key in self.training_history:
            self.training_history[key].clear()
        
        self.logger.info("训练状态已重置")
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"{self.__class__.__name__}("
                f"agent={self.agent.__class__.__name__}, "
                f"env={self.env.__class__.__name__}, "
                f"experiment={self.experiment_name})")
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()