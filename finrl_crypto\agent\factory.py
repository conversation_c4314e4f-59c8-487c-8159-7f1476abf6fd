"""智能体工厂模块

提供统一的智能体创建和管理接口。
"""

from typing import Dict, Any, Type, Optional, List
import torch

from .base import BaseAgent
from .dqn import DQNAgent
from .ppo import PPOAgent
from .a2c import A2CAgent
from .sac import SACAgent


class AgentFactory:
    """智能体工厂类
    
    提供统一的智能体创建和管理接口。
    """
    
    # 注册的智能体类型
    _agent_registry: Dict[str, Type[BaseAgent]] = {
        'dqn': DQNAgent,
        'ppo': PPOAgent,
        'a2c': A2CAgent,
        'sac': SACAgent,
    }
    
    # 智能体默认配置
    _default_configs: Dict[str, Dict[str, Any]] = {
        'dqn': {
            'learning_rate': 1e-3,
            'gamma': 0.99,
            'epsilon': 1.0,
            'epsilon_min': 0.01,
            'epsilon_decay': 0.995,
            'buffer_size': 100000,
            'batch_size': 64,
            'target_update_freq': 1000,
            'hidden_dims': [256, 256],
            'double_dqn': True,
            'dueling_dqn': False,
        },
        'ppo': {
            'learning_rate': 3e-4,
            'gamma': 0.99,
            'lam': 0.95,
            'clip_ratio': 0.2,
            'value_coef': 0.5,
            'entropy_coef': 0.01,
            'max_grad_norm': 0.5,
            'ppo_epochs': 10,
            'batch_size': 64,
            'buffer_size': 2048,
            'hidden_dims': [256, 256],
            'action_type': 'continuous',
        },
        'a2c': {
            'learning_rate': 3e-4,
            'gamma': 0.99,
            'value_coef': 0.5,
            'entropy_coef': 0.01,
            'max_grad_norm': 0.5,
            'n_steps': 5,
            'hidden_dims': [256, 256],
            'action_type': 'continuous',
        },
        'sac': {
            'learning_rate': 3e-4,
            'gamma': 0.99,
            'tau': 0.005,
            'alpha': 0.2,
            'automatic_entropy_tuning': True,
            'buffer_size': 1000000,
            'batch_size': 256,
            'hidden_dims': [256, 256],
        },
    }
    
    @classmethod
    def create_agent(cls,
                    agent_type: str,
                    state_dim: int,
                    action_dim: int,
                    **kwargs) -> BaseAgent:
        """创建智能体
        
        Args:
            agent_type: 智能体类型
            state_dim: 状态空间维度
            action_dim: 动作空间维度
            **kwargs: 其他参数
            
        Returns:
            智能体实例
            
        Raises:
            ValueError: 不支持的智能体类型
        """
        agent_type = agent_type.lower()
        
        if agent_type not in cls._agent_registry:
            raise ValueError(f"不支持的智能体类型: {agent_type}. "
                           f"支持的类型: {list(cls._agent_registry.keys())}")
        
        # 获取默认配置
        config = cls._default_configs[agent_type].copy()
        
        # 更新配置
        config.update(kwargs)
        
        # 创建智能体
        agent_class = cls._agent_registry[agent_type]
        agent = agent_class(
            state_dim=state_dim,
            action_dim=action_dim,
            **config
        )
        
        return agent
    
    @classmethod
    def register_agent(cls, agent_type: str, agent_class: Type[BaseAgent]):
        """注册新的智能体类型
        
        Args:
            agent_type: 智能体类型名称
            agent_class: 智能体类
        """
        if not issubclass(agent_class, BaseAgent):
            raise ValueError(f"智能体类必须继承自BaseAgent")
        
        cls._agent_registry[agent_type.lower()] = agent_class
    
    @classmethod
    def get_available_agents(cls) -> List[str]:
        """获取可用的智能体类型
        
        Returns:
            智能体类型列表
        """
        return list(cls._agent_registry.keys())
    
    @classmethod
    def get_agent_info(cls, agent_type: str) -> Dict[str, Any]:
        """获取智能体信息
        
        Args:
            agent_type: 智能体类型
            
        Returns:
            智能体信息
        """
        agent_type = agent_type.lower()
        
        if agent_type not in cls._agent_registry:
            raise ValueError(f"不支持的智能体类型: {agent_type}")
        
        agent_class = cls._agent_registry[agent_type]
        default_config = cls._default_configs.get(agent_type, {})
        
        return {
            'name': agent_type,
            'class': agent_class.__name__,
            'description': agent_class.__doc__ or "无描述",
            'default_config': default_config,
        }
    
    @classmethod
    def create_dqn_agent(cls,
                        state_dim: int,
                        action_dim: int,
                        **kwargs) -> DQNAgent:
        """创建DQN智能体
        
        Args:
            state_dim: 状态空间维度
            action_dim: 动作空间维度
            **kwargs: 其他参数
            
        Returns:
            DQN智能体
        """
        return cls.create_agent('dqn', state_dim, action_dim, **kwargs)
    
    @classmethod
    def create_ppo_agent(cls,
                        state_dim: int,
                        action_dim: int,
                        **kwargs) -> PPOAgent:
        """创建PPO智能体
        
        Args:
            state_dim: 状态空间维度
            action_dim: 动作空间维度
            **kwargs: 其他参数
            
        Returns:
            PPO智能体
        """
        return cls.create_agent('ppo', state_dim, action_dim, **kwargs)
    
    @classmethod
    def create_a2c_agent(cls,
                        state_dim: int,
                        action_dim: int,
                        **kwargs) -> A2CAgent:
        """创建A2C智能体
        
        Args:
            state_dim: 状态空间维度
            action_dim: 动作空间维度
            **kwargs: 其他参数
            
        Returns:
            A2C智能体
        """
        return cls.create_agent('a2c', state_dim, action_dim, **kwargs)
    
    @classmethod
    def create_sac_agent(cls,
                        state_dim: int,
                        action_dim: int,
                        **kwargs) -> SACAgent:
        """创建SAC智能体
        
        Args:
            state_dim: 状态空间维度
            action_dim: 动作空间维度
            **kwargs: 其他参数
            
        Returns:
            SAC智能体
        """
        return cls.create_agent('sac', state_dim, action_dim, **kwargs)
    
    @classmethod
    def validate_config(cls, agent_type: str, config: Dict[str, Any]) -> bool:
        """验证智能体配置
        
        Args:
            agent_type: 智能体类型
            config: 配置字典
            
        Returns:
            是否有效
        """
        agent_type = agent_type.lower()
        
        if agent_type not in cls._agent_registry:
            return False
        
        # 基本参数检查
        required_params = ['state_dim', 'action_dim']
        for param in required_params:
            if param not in config:
                return False
            if not isinstance(config[param], int) or config[param] <= 0:
                return False
        
        # 特定智能体的参数检查
        if agent_type == 'dqn':
            # DQN需要离散动作空间
            if config.get('action_type') == 'continuous':
                return False
        
        elif agent_type == 'sac':
            # SAC只支持连续动作空间
            if config.get('action_type') == 'discrete':
                return False
        
        return True
    
    @classmethod
    def get_recommended_config(cls,
                             agent_type: str,
                             problem_type: str = 'trading',
                             action_type: str = 'continuous') -> Dict[str, Any]:
        """获取推荐配置
        
        Args:
            agent_type: 智能体类型
            problem_type: 问题类型 ('trading', 'portfolio')
            action_type: 动作类型 ('continuous', 'discrete')
            
        Returns:
            推荐配置
        """
        agent_type = agent_type.lower()
        
        if agent_type not in cls._agent_registry:
            raise ValueError(f"不支持的智能体类型: {agent_type}")
        
        # 获取基础配置
        config = cls._default_configs[agent_type].copy()
        
        # 根据问题类型调整配置
        if problem_type == 'trading':
            if agent_type == 'ppo':
                config.update({
                    'clip_ratio': 0.1,
                    'entropy_coef': 0.005,
                    'buffer_size': 4096,
                })
            elif agent_type == 'sac':
                config.update({
                    'alpha': 0.1,
                    'batch_size': 128,
                })
        
        elif problem_type == 'portfolio':
            if agent_type == 'ppo':
                config.update({
                    'clip_ratio': 0.15,
                    'entropy_coef': 0.01,
                    'value_coef': 1.0,
                })
            elif agent_type == 'sac':
                config.update({
                    'alpha': 0.2,
                    'tau': 0.01,
                })
        
        # 设置动作类型
        if 'action_type' in config:
            config['action_type'] = action_type
        
        return config


# 便捷函数
def create_agent(agent_type: str,
                state_dim: int,
                action_dim: int,
                **kwargs) -> BaseAgent:
    """创建智能体的便捷函数
    
    Args:
        agent_type: 智能体类型
        state_dim: 状态空间维度
        action_dim: 动作空间维度
        **kwargs: 其他参数
        
    Returns:
        智能体实例
    """
    return AgentFactory.create_agent(agent_type, state_dim, action_dim, **kwargs)


def get_available_agents() -> List[str]:
    """获取可用智能体类型的便捷函数
    
    Returns:
        智能体类型列表
    """
    return AgentFactory.get_available_agents()


def get_agent_info(agent_type: str) -> Dict[str, Any]:
    """获取智能体信息的便捷函数
    
    Args:
        agent_type: 智能体类型
        
    Returns:
        智能体信息
    """
    return AgentFactory.get_agent_info(agent_type)