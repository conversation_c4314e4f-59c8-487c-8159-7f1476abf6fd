"""训练器工厂模块

提供统一的训练器创建和管理接口。
"""

from typing import Dict, Any, Optional, Type, List
import logging

from .base import BaseTrainer, TrainingConfig
from .single_asset import SingleAssetTrainer
from .multi_asset import MultiAssetTrainer
from .portfolio import PortfolioTrainer
from ..agent.base import BaseAgent
from ..environment.base import BaseEnvironment


class TrainerFactory:
    """训练器工厂
    
    提供统一的训练器创建和管理接口。
    """
    
    def __init__(self):
        """初始化训练器工厂"""
        self.logger = logging.getLogger(__name__)
        
        # 注册支持的训练器类型
        self._trainers: Dict[str, Type[BaseTrainer]] = {
            'single_asset': SingleAssetTrainer,
            'multi_asset': MultiAssetTrainer,
            'portfolio': PortfolioTrainer,
        }
        
        # 默认配置
        self._default_configs = {
            'single_asset': {
                'total_timesteps': 100000,
                'learning_starts': 1000,
                'train_freq': 4,
                'gradient_steps': 1,
                'target_update_interval': 1000,
                'eval_freq': 5000,
                'n_eval_episodes': 10,
                'log_interval': 1000,
                'save_freq': 10000,
                'early_stopping_patience': 10,
                'early_stopping_threshold': 0.01,
                'save_best_model': True,
            },
            'multi_asset': {
                'total_timesteps': 200000,
                'learning_starts': 2000,
                'train_freq': 4,
                'gradient_steps': 1,
                'target_update_interval': 1000,
                'eval_freq': 10000,
                'n_eval_episodes': 5,
                'log_interval': 2000,
                'save_freq': 20000,
                'early_stopping_patience': 15,
                'early_stopping_threshold': 0.005,
                'save_best_model': True,
            },
            'portfolio': {
                'total_timesteps': 150000,
                'learning_starts': 1500,
                'train_freq': 4,
                'gradient_steps': 1,
                'target_update_interval': 1000,
                'eval_freq': 7500,
                'n_eval_episodes': 8,
                'log_interval': 1500,
                'save_freq': 15000,
                'early_stopping_patience': 12,
                'early_stopping_threshold': 0.008,
                'save_best_model': True,
            },
        }
    
    def create_trainer(self,
                      trainer_type: str,
                      agent: BaseAgent,
                      env: BaseEnvironment,
                      config: Optional[TrainingConfig] = None,
                      save_path: str = "./models",
                      experiment_name: str = "experiment",
                      **kwargs) -> BaseTrainer:
        """创建训练器
        
        Args:
            trainer_type: 训练器类型 ('single_asset', 'multi_asset', 'portfolio')
            agent: 智能体
            env: 环境
            config: 训练配置
            save_path: 模型保存路径
            experiment_name: 实验名称
            **kwargs: 其他参数
            
        Returns:
            训练器实例
            
        Raises:
            ValueError: 不支持的训练器类型
        """
        if trainer_type not in self._trainers:
            raise ValueError(f"不支持的训练器类型: {trainer_type}. "
                           f"支持的类型: {list(self._trainers.keys())}")
        
        # 创建配置
        if config is None:
            config = self.get_default_config(trainer_type)
        
        # 验证配置
        self._validate_config(trainer_type, config)
        
        # 获取训练器类
        trainer_class = self._trainers[trainer_type]
        
        # 创建训练器
        try:
            trainer = trainer_class(
                agent=agent,
                env=env,
                config=config,
                save_path=save_path,
                experiment_name=experiment_name
            )
            
            # 设置特定的属性
            for key, value in kwargs.items():
                if hasattr(trainer, key):
                    setattr(trainer, key, value)
                else:
                    self.logger.warning(f"训练器 {trainer_class.__name__} 不支持属性: {key}")
            
            self.logger.info(f"成功创建 {trainer_type} 训练器: {trainer_class.__name__}")
            return trainer
            
        except Exception as e:
            self.logger.error(f"创建训练器失败: {e}")
            raise
    
    def register_trainer(self, name: str, trainer_class: Type[BaseTrainer]):
        """注册新的训练器类型
        
        Args:
            name: 训练器名称
            trainer_class: 训练器类
        """
        if not issubclass(trainer_class, BaseTrainer):
            raise ValueError(f"训练器类必须继承自 BaseTrainer")
        
        self._trainers[name] = trainer_class
        self.logger.info(f"注册新训练器类型: {name}")
    
    def get_available_trainers(self) -> List[str]:
        """获取可用的训练器类型
        
        Returns:
            训练器类型列表
        """
        return list(self._trainers.keys())
    
    def get_trainer_info(self, trainer_type: str) -> Dict[str, Any]:
        """获取训练器信息
        
        Args:
            trainer_type: 训练器类型
            
        Returns:
            训练器信息字典
        """
        if trainer_type not in self._trainers:
            raise ValueError(f"未知的训练器类型: {trainer_type}")
        
        trainer_class = self._trainers[trainer_type]
        default_config = self._default_configs.get(trainer_type, {})
        
        return {
            'name': trainer_type,
            'class': trainer_class.__name__,
            'description': trainer_class.__doc__ or "无描述",
            'default_config': default_config,
        }
    
    def get_default_config(self, trainer_type: str) -> TrainingConfig:
        """获取默认配置
        
        Args:
            trainer_type: 训练器类型
            
        Returns:
            默认训练配置
        """
        if trainer_type not in self._default_configs:
            # 使用基础默认配置
            config_dict = {
                'total_timesteps': 100000,
                'learning_starts': 1000,
                'train_freq': 1,
                'gradient_steps': 1,
                'target_update_interval': 1,
                'eval_freq': 5000,
                'n_eval_episodes': 10,
                'log_freq': 1000,
                'save_freq': 10000,
                'early_stopping_patience': 0,
                'early_stopping_threshold': 0.01,
                'save_best_model': True,
            }
        else:
            config_dict = self._default_configs[trainer_type].copy()
        
        # 只保留TrainingConfig支持的参数
        valid_params = {
            'total_timesteps', 'eval_freq', 'save_freq', 'log_freq', 'n_eval_episodes',
            'early_stopping_patience', 'early_stopping_threshold', 'max_no_improvement_evals',
            'save_best_model', 'save_replay_buffer', 'tensorboard_log', 'verbose', 'seed',
            'learning_starts', 'train_freq', 'gradient_steps', 'target_update_interval',
            'eval_env', 'deterministic_eval', 'render_eval', 'callback', 'device'
        }
        
        filtered_config = {k: v for k, v in config_dict.items() if k in valid_params}
        
        return TrainingConfig(**filtered_config)
    
    def create_single_asset_trainer(self,
                                   agent: BaseAgent,
                                   env: BaseEnvironment,
                                   config: Optional[TrainingConfig] = None,
                                   save_path: str = "./models",
                                   experiment_name: str = "single_asset_experiment") -> SingleAssetTrainer:
        """创建单资产训练器
        
        Args:
            agent: 智能体
            env: 环境
            config: 训练配置
            save_path: 模型保存路径
            experiment_name: 实验名称
            
        Returns:
            单资产训练器
        """
        return self.create_trainer(
            trainer_type='single_asset',
            agent=agent,
            env=env,
            config=config,
            save_path=save_path,
            experiment_name=experiment_name
        )
    
    def create_multi_asset_trainer(self,
                                  agent: BaseAgent,
                                  env: BaseEnvironment,
                                  config: Optional[TrainingConfig] = None,
                                  save_path: str = "./models",
                                  experiment_name: str = "multi_asset_experiment") -> MultiAssetTrainer:
        """创建多资产训练器
        
        Args:
            agent: 智能体
            env: 环境
            config: 训练配置
            save_path: 模型保存路径
            experiment_name: 实验名称
            
        Returns:
            多资产训练器
        """
        return self.create_trainer(
            trainer_type='multi_asset',
            agent=agent,
            env=env,
            config=config,
            save_path=save_path,
            experiment_name=experiment_name
        )
    
    def create_portfolio_trainer(self,
                                agent: BaseAgent,
                                env: BaseEnvironment,
                                config: Optional[TrainingConfig] = None,
                                save_path: str = "./models",
                                experiment_name: str = "portfolio_experiment",
                                benchmark_weights: Optional[list] = None) -> PortfolioTrainer:
        """创建投资组合训练器
        
        Args:
            agent: 智能体
            env: 环境
            config: 训练配置
            save_path: 模型保存路径
            experiment_name: 实验名称
            benchmark_weights: 基准权重
            
        Returns:
            投资组合训练器
        """
        kwargs = {}
        if benchmark_weights is not None:
            kwargs['benchmark_weights'] = benchmark_weights
        
        return self.create_trainer(
            trainer_type='portfolio',
            agent=agent,
            env=env,
            config=config,
            save_path=save_path,
            experiment_name=experiment_name,
            **kwargs
        )
    
    def _validate_config(self, trainer_type: str, config: TrainingConfig):
        """验证训练配置
        
        Args:
            trainer_type: 训练器类型
            config: 训练配置
            
        Raises:
            ValueError: 配置无效
        """
        # 基本验证
        if config.total_timesteps <= 0:
            raise ValueError("total_timesteps 必须大于 0")
        
        if config.learning_starts < 0:
            raise ValueError("learning_starts 必须大于等于 0")
        
        if config.train_freq <= 0:
            raise ValueError("train_freq 必须大于 0")
        
        if config.gradient_steps <= 0:
            raise ValueError("gradient_steps 必须大于 0")
        
        if config.eval_freq <= 0:
            raise ValueError("eval_freq 必须大于 0")
        
        if config.n_eval_episodes <= 0:
            raise ValueError("n_eval_episodes 必须大于 0")
        
        # 特定训练器的验证
        if trainer_type == 'multi_asset':
            # 多资产训练器需要更多的学习时间
            if config.total_timesteps < 50000:
                self.logger.warning("多资产训练器建议使用至少 50000 个时间步")
        
        elif trainer_type == 'portfolio':
            # 投资组合训练器需要足够的评估回合
            if config.n_eval_episodes < 5:
                self.logger.warning("投资组合训练器建议使用至少 5 个评估回合")
    
    def get_recommended_config(self,
                             trainer_type: str,
                             problem_complexity: str = 'medium',
                             computational_budget: str = 'medium') -> TrainingConfig:
        """获取推荐配置
        
        Args:
            trainer_type: 训练器类型
            problem_complexity: 问题复杂度 ('low', 'medium', 'high')
            computational_budget: 计算预算 ('low', 'medium', 'high')
            
        Returns:
            推荐的训练配置
        """
        base_config = self.get_default_config(trainer_type)
        
        # 根据问题复杂度调整
        complexity_multipliers = {
            'low': 0.5,
            'medium': 1.0,
            'high': 2.0,
        }
        
        # 根据计算预算调整
        budget_multipliers = {
            'low': 0.5,
            'medium': 1.0,
            'high': 1.5,
        }
        
        complexity_mult = complexity_multipliers.get(problem_complexity, 1.0)
        budget_mult = budget_multipliers.get(computational_budget, 1.0)
        
        # 调整配置
        total_mult = complexity_mult * budget_mult
        
        recommended_config = TrainingConfig(
            total_timesteps=int(base_config.total_timesteps * total_mult),
            learning_starts=int(base_config.learning_starts * complexity_mult),
            train_freq=base_config.train_freq,
            gradient_steps=max(1, int(base_config.gradient_steps * complexity_mult)),
            target_update_interval=base_config.target_update_interval,
            eval_freq=int(base_config.eval_freq * total_mult),
            n_eval_episodes=max(5, int(base_config.n_eval_episodes * complexity_mult)),
            log_interval=int(base_config.log_interval * total_mult),
            save_freq=int(base_config.save_freq * total_mult),
            early_stopping_patience=base_config.early_stopping_patience,
            early_stopping_threshold=base_config.early_stopping_threshold,
            save_best_model=base_config.save_best_model,
        )
        
        self.logger.info(f"为 {trainer_type} 生成推荐配置 - "
                        f"复杂度: {problem_complexity}, 预算: {computational_budget}")
        
        return recommended_config


# 全局工厂实例
_trainer_factory = TrainerFactory()


# 便捷函数
def create_trainer(trainer_type: str,
                  agent: BaseAgent,
                  env: BaseEnvironment,
                  config: Optional[TrainingConfig] = None,
                  save_path: str = "./models",
                  experiment_name: str = "experiment",
                  **kwargs) -> BaseTrainer:
    """创建训练器（便捷函数）
    
    Args:
        trainer_type: 训练器类型
        agent: 智能体
        env: 环境
        config: 训练配置
        save_path: 模型保存路径
        experiment_name: 实验名称
        **kwargs: 其他参数
        
    Returns:
        训练器实例
    """
    return _trainer_factory.create_trainer(
        trainer_type=trainer_type,
        agent=agent,
        env=env,
        config=config,
        save_path=save_path,
        experiment_name=experiment_name,
        **kwargs
    )


def create_single_asset_trainer(agent: BaseAgent,
                               env: BaseEnvironment,
                               config: Optional[TrainingConfig] = None,
                               save_path: str = "./models",
                               experiment_name: str = "single_asset_experiment") -> SingleAssetTrainer:
    """创建单资产训练器（便捷函数）"""
    return _trainer_factory.create_single_asset_trainer(
        agent=agent,
        env=env,
        config=config,
        save_path=save_path,
        experiment_name=experiment_name
    )


def create_multi_asset_trainer(agent: BaseAgent,
                              env: BaseEnvironment,
                              config: Optional[TrainingConfig] = None,
                              save_path: str = "./models",
                              experiment_name: str = "multi_asset_experiment") -> MultiAssetTrainer:
    """创建多资产训练器（便捷函数）"""
    return _trainer_factory.create_multi_asset_trainer(
        agent=agent,
        env=env,
        config=config,
        save_path=save_path,
        experiment_name=experiment_name
    )


def create_portfolio_trainer(agent: BaseAgent,
                            env: BaseEnvironment,
                            config: Optional[TrainingConfig] = None,
                            save_path: str = "./models",
                            experiment_name: str = "portfolio_experiment",
                            benchmark_weights: Optional[list] = None) -> PortfolioTrainer:
    """创建投资组合训练器（便捷函数）"""
    return _trainer_factory.create_portfolio_trainer(
        agent=agent,
        env=env,
        config=config,
        save_path=save_path,
        experiment_name=experiment_name,
        benchmark_weights=benchmark_weights
    )


def get_available_trainers() -> List[str]:
    """获取可用的训练器类型（便捷函数）"""
    return _trainer_factory.get_available_trainers()


def get_trainer_info(trainer_type: str) -> Dict[str, Any]:
    """获取训练器信息（便捷函数）"""
    return _trainer_factory.get_trainer_info(trainer_type)


def get_recommended_config(trainer_type: str,
                         problem_complexity: str = 'medium',
                         computational_budget: str = 'medium') -> TrainingConfig:
    """获取推荐配置（便捷函数）"""
    return _trainer_factory.get_recommended_config(
        trainer_type=trainer_type,
        problem_complexity=problem_complexity,
        computational_budget=computational_budget
    )