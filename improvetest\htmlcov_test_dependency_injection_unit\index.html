<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>FinRL Crypto Test Coverage Report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>FinRL Crypto Test Coverage Report:
            <span class="pc_cov">1.68%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>b</kbd>
                        <kbd>p</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-19 02:02 +0900
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="branches" aria-sort="none" data-default-sort-order="descending" data-shortcut="b">branches<span class="arrows"></span></th>
                <th id="partial" aria-sort="none" data-default-sort-order="descending" data-shortcut="p">partial<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db___init___py.html">core\__init__.py</a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html">core\config_manager.py</a></td>
                <td>176</td>
                <td>176</td>
                <td>15</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 224">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html">core\dependency_injection.py</a></td>
                <td>309</td>
                <td>229</td>
                <td>4</td>
                <td>94</td>
                <td>2</td>
                <td class="right" data-ratio="82 403">20.35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html">core\drl_agent.py</a></td>
                <td>245</td>
                <td>245</td>
                <td>0</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 281">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ddb02fcb4060c71d___init___py.html">finrl_crypto\__init__.py</a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be___init___py.html">finrl_crypto\agent\__init__.py</a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html">finrl_crypto\agent\a2c.py</a></td>
                <td>193</td>
                <td>193</td>
                <td>0</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 219">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html">finrl_crypto\agent\base.py</a></td>
                <td>118</td>
                <td>118</td>
                <td>65</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 144">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html">finrl_crypto\agent\dqn.py</a></td>
                <td>113</td>
                <td>113</td>
                <td>0</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 127">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html">finrl_crypto\agent\factory.py</a></td>
                <td>91</td>
                <td>91</td>
                <td>0</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 129">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html">finrl_crypto\agent\ppo.py</a></td>
                <td>229</td>
                <td>229</td>
                <td>0</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 265">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html">finrl_crypto\agent\sac.py</a></td>
                <td>200</td>
                <td>200</td>
                <td>0</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 226">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05___init___py.html">finrl_crypto\environment\__init__.py</a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html">finrl_crypto\environment\base.py</a></td>
                <td>155</td>
                <td>155</td>
                <td>43</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 197">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html">finrl_crypto\environment\crypto_trading.py</a></td>
                <td>219</td>
                <td>219</td>
                <td>0</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 283">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html">finrl_crypto\environment\factory.py</a></td>
                <td>111</td>
                <td>111</td>
                <td>0</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 159">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html">finrl_crypto\environment\portfolio.py</a></td>
                <td>240</td>
                <td>240</td>
                <td>0</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 316">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a___init___py.html">finrl_crypto\training\__init__.py</a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html">finrl_crypto\training\base.py</a></td>
                <td>173</td>
                <td>173</td>
                <td>28</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 211">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html">finrl_crypto\training\factory.py</a></td>
                <td>98</td>
                <td>98</td>
                <td>0</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 130">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html">finrl_crypto\training\multi_asset.py</a></td>
                <td>252</td>
                <td>252</td>
                <td>0</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="0 342">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html">finrl_crypto\training\portfolio.py</a></td>
                <td>294</td>
                <td>294</td>
                <td>0</td>
                <td>98</td>
                <td>0</td>
                <td class="right" data-ratio="0 392">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html">finrl_crypto\training\single_asset.py</a></td>
                <td>160</td>
                <td>160</td>
                <td>0</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 208">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html">finrl_crypto\training\utils.py</a></td>
                <td>395</td>
                <td>395</td>
                <td>0</td>
                <td>160</td>
                <td>0</td>
                <td class="right" data-ratio="0 555">0.00%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>3831</td>
                <td>3751</td>
                <td>155</td>
                <td>1040</td>
                <td>2</td>
                <td class="right" data-ratio="82 4871">1.68%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-19 02:02 +0900
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_65d867bacf62c25a_utils_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_57760688d1f824db___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
