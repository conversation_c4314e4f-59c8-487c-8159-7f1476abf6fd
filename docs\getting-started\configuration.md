# 配置说明

本文档详细介绍 FinRL Crypto 的配置系统，帮助您根据需求定制系统行为。

## 📋 配置概览

FinRL Crypto 使用分层配置系统，支持多种配置方式：

1. **默认配置**: 内置的基础配置
2. **配置文件**: `config_main.py` 主配置文件
3. **环境变量**: 通过 `.env` 文件或系统环境变量
4. **命令行参数**: 运行时动态配置
5. **代码配置**: 程序中直接设置

配置优先级：**代码配置** > **命令行参数** > **环境变量** > **配置文件** > **默认配置**

## 🔧 主配置文件

### config_main.py 结构

```python
# FinRL Crypto 主配置文件
# config_main.py

import os
from datetime import datetime, timedelta
from pathlib import Path

# ==================== 基础配置 ====================

# 项目信息
PROJECT_NAME = "FinRL Crypto"
VERSION = "1.0.0"
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
ENVIRONMENT = os.getenv('ENVIRONMENT', 'development')  # development, testing, production

# 路径配置
BASE_DIR = Path(__file__).parent.absolute()
DATA_DIR = BASE_DIR / 'data'
MODEL_DIR = BASE_DIR / 'models'
LOG_DIR = BASE_DIR / 'logs'
RESULT_DIR = BASE_DIR / 'results'
CACHE_DIR = BASE_DIR / 'cache'
CHECKPOINT_DIR = BASE_DIR / 'checkpoints'

# 创建必要目录
for directory in [DATA_DIR, MODEL_DIR, LOG_DIR, RESULT_DIR, CACHE_DIR, CHECKPOINT_DIR]:
    directory.mkdir(parents=True, exist_ok=True)

# ==================== 数据配置 ====================

DATA_CONFIG = {
    # 数据源配置
    'source': os.getenv('DATA_SOURCE', 'yfinance'),  # yfinance, binance, coinbase
    'backup_sources': ['yfinance', 'binance'],  # 备用数据源
    
    # 交易对配置
    'symbols': [
        'BTC-USD', 'ETH-USD', 'ADA-USD', 'DOT-USD', 'LINK-USD',
        'LTC-USD', 'BCH-USD', 'XLM-USD', 'EOS-USD', 'TRX-USD'
    ],
    'base_currency': 'USD',
    
    # 时间配置
    'start_date': '2020-01-01',
    'end_date': datetime.now().strftime('%Y-%m-%d'),
    'timeframe': '1h',  # 1m, 5m, 15m, 30m, 1h, 4h, 1d
    'timezone': 'UTC',
    
    # 数据处理
    'cache_enabled': True,
    'cache_expiry_hours': 24,
    'data_validation': True,
    'handle_missing': 'forward_fill',  # forward_fill, backward_fill, interpolate, drop
    'outlier_detection': True,
    'outlier_method': 'iqr',  # iqr, zscore, isolation_forest
    
    # 特征工程
    'normalize_features': True,
    'normalization_method': 'minmax',  # minmax, standard, robust
    'feature_selection': False,
    'feature_selection_method': 'variance',  # variance, correlation, mutual_info
    
    # 技术指标
    'indicators': {
        'sma': [5, 10, 20, 50],           # 简单移动平均
        'ema': [12, 26, 50],              # 指数移动平均
        'rsi': [14, 21],                  # 相对强弱指数
        'macd': [(12, 26, 9)],            # MACD
        'bb': [(20, 2)],                  # 布林带
        'atr': [14],                      # 平均真实波幅
        'stoch': [(14, 3, 3)],            # 随机指标
        'williams_r': [14],               # 威廉指标
        'cci': [20],                      # 商品通道指数
        'adx': [14],                      # 平均趋向指数
        'obv': True,                      # 成交量平衡指标
        'vwap': True,                     # 成交量加权平均价
    },
    
    # 数据存储
    'save_raw_data': True,
    'save_processed_data': True,
    'data_format': 'parquet',  # csv, parquet, hdf5
    'compression': 'snappy',   # gzip, bz2, snappy, lz4
}

# ==================== 模型配置 ====================

MODEL_CONFIG = {
    # 算法选择
    'algorithm': 'PPO',  # PPO, A2C, SAC, TD3, DDPG
    'policy_type': 'MlpPolicy',  # MlpPolicy, CnnPolicy, MultiInputPolicy
    
    # 网络架构
    'policy_kwargs': {
        'net_arch': [256, 256],  # 隐藏层大小
        'activation_fn': 'tanh',  # tanh, relu, elu, selu
        'ortho_init': True,
        'use_sde': False,  # 状态依赖探索
        'log_std_init': 0.0,
        'full_std': True,
        'sde_net_arch': None,
        'use_expln': False,
        'squash_output': False,
        'features_extractor_class': None,
        'features_extractor_kwargs': None,
        'normalize_images': True,
        'optimizer_class': 'Adam',
        'optimizer_kwargs': None,
    },
    
    # 训练参数
    'learning_rate': 3e-4,
    'n_steps': 2048,
    'batch_size': 64,
    'n_epochs': 10,
    'gamma': 0.99,
    'gae_lambda': 0.95,
    'clip_range': 0.2,
    'clip_range_vf': None,
    'ent_coef': 0.01,
    'vf_coef': 0.5,
    'max_grad_norm': 0.5,
    'target_kl': None,
    
    # 训练控制
    'total_timesteps': 100000,
    'eval_freq': 10000,
    'n_eval_episodes': 10,
    'eval_log_path': None,
    'reset_num_timesteps': True,
    'use_sde': False,
    'sde_sample_freq': -1,
    
    # 模型保存
    'save_freq': 10000,
    'save_path': MODEL_DIR / 'checkpoints',
    'save_replay_buffer': False,
    'save_vecnormalize': True,
    
    # 早停和调度
    'early_stopping': {
        'enabled': True,
        'patience': 5,
        'min_delta': 0.01,
        'restore_best_weights': True,
    },
    
    'lr_schedule': {
        'enabled': False,
        'schedule_type': 'linear',  # linear, exponential, cosine
        'initial_value': 3e-4,
        'final_value': 1e-5,
    },
}

# ==================== 环境配置 ====================

ENV_CONFIG = {
    # 交易环境
    'env_type': 'single_asset',  # single_asset, portfolio, multi_agent
    'action_space': 'continuous',  # discrete, continuous, multi_discrete
    'observation_space': 'vector',  # vector, image, multi_modal
    
    # 资金管理
    'initial_amount': 100000,
    'transaction_cost_pct': 0.001,  # 0.1% 交易费用
    'slippage_pct': 0.0005,         # 0.05% 滑点
    'min_trade_amount': 10,         # 最小交易金额
    'max_trade_amount': None,       # 最大交易金额
    
    # 仓位管理
    'max_position': 0.95,           # 最大仓位比例
    'min_position': 0.05,           # 最小仓位比例
    'position_scaling': 'linear',   # linear, sigmoid, tanh
    'allow_short': False,           # 是否允许做空
    'leverage': 1.0,                # 杠杆倍数
    
    # 风险管理
    'stop_loss_pct': 0.05,          # 5% 止损
    'take_profit_pct': 0.15,        # 15% 止盈
    'max_drawdown_pct': 0.20,       # 20% 最大回撤
    'risk_free_rate': 0.02,         # 2% 无风险利率
    
    # 奖励函数
    'reward_type': 'profit',        # profit, sharpe, sortino, calmar
    'reward_scaling': 1e-4,         # 奖励缩放因子
    'penalty_scaling': 1e-3,        # 惩罚缩放因子
    'lookback_window': 30,          # 回看窗口
    
    # 环境特性
    'add_noise': False,             # 添加噪声
    'noise_std': 0.01,              # 噪声标准差
    'random_start': True,           # 随机起始点
    'episode_length': None,         # 回合长度（None为全部数据）
    
    # 状态空间
    'include_position': True,       # 包含仓位信息
    'include_cash': True,           # 包含现金信息
    'include_returns': True,        # 包含收益信息
    'include_volume': True,         # 包含成交量信息
    'include_time_features': True,  # 包含时间特征
    'state_normalization': True,    # 状态归一化
}

# ==================== 回测配置 ====================

BACKTEST_CONFIG = {
    # 回测参数
    'start_date': '2023-01-01',
    'end_date': '2023-12-31',
    'initial_amount': 100000,
    'benchmark': 'BTC-USD',         # 基准资产
    
    # 交易成本
    'transaction_cost_pct': 0.001,
    'slippage_pct': 0.0005,
    'spread_pct': 0.0001,
    
    # 回测选项
    'rebalance_freq': 'daily',      # daily, weekly, monthly
    'save_trades': True,
    'save_portfolio': True,
    'save_metrics': True,
    
    # 性能分析
    'calculate_metrics': True,
    'metrics_list': [
        'total_return', 'annual_return', 'volatility',
        'sharpe_ratio', 'sortino_ratio', 'calmar_ratio',
        'max_drawdown', 'win_rate', 'profit_factor'
    ],
    
    # 可视化
    'plot_results': True,
    'plot_trades': True,
    'plot_portfolio': True,
    'save_plots': True,
    'plot_format': 'png',           # png, pdf, svg
    'plot_dpi': 300,
}

# ==================== 实时交易配置 ====================

LIVE_TRADING_CONFIG = {
    # 交易模式
    'mode': 'paper',                # paper, live
    'exchange': 'binance',          # binance, coinbase, kraken
    
    # API配置
    'api_key': os.getenv('API_KEY', ''),
    'api_secret': os.getenv('API_SECRET', ''),
    'api_passphrase': os.getenv('API_PASSPHRASE', ''),  # Coinbase需要
    'sandbox': True,                # 是否使用沙盒环境
    
    # 交易参数
    'update_interval': 300,         # 更新间隔（秒）
    'order_type': 'market',         # market, limit, stop
    'time_in_force': 'GTC',         # GTC, IOC, FOK
    'max_orders': 10,               # 最大订单数
    'order_timeout': 60,            # 订单超时（秒）
    
    # 风险管理
    'max_position_size': 0.1,       # 单个资产最大仓位
    'max_daily_trades': 50,         # 日最大交易次数
    'max_daily_loss': 0.02,         # 日最大亏损
    'emergency_stop': True,         # 紧急停止
    
    # 监控和报警
    'enable_monitoring': True,
    'alert_channels': ['email', 'webhook'],
    'alert_thresholds': {
        'drawdown': 0.05,
        'daily_loss': 0.02,
        'error_rate': 0.1,
    },
    
    # 数据同步
    'sync_interval': 60,            # 数据同步间隔
    'data_buffer_size': 1000,       # 数据缓冲区大小
    'save_live_data': True,         # 保存实时数据
}

# ==================== 日志配置 ====================

LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'json': {
            'format': '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s"}',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        }
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'standard',
            'stream': 'ext://sys.stdout'
        },
        'file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'detailed',
            'filename': LOG_DIR / 'finrl_crypto.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'detailed',
            'filename': LOG_DIR / 'errors.log',
            'maxBytes': 10485760,
            'backupCount': 5
        }
    },
    'loggers': {
        'finrl_crypto': {
            'level': 'DEBUG' if DEBUG else 'INFO',
            'handlers': ['console', 'file', 'error_file'],
            'propagate': False
        },
        'trading': {
            'level': 'INFO',
            'handlers': ['console', 'file'],
            'propagate': False
        },
        'data': {
            'level': 'INFO',
            'handlers': ['file'],
            'propagate': False
        }
    },
    'root': {
        'level': 'WARNING',
        'handlers': ['console']
    }
}

# ==================== 数据库配置 ====================

DATABASE_CONFIG = {
    'default': {
        'ENGINE': 'sqlite',
        'NAME': BASE_DIR / 'finrl_crypto.db',
        'OPTIONS': {
            'timeout': 20,
            'check_same_thread': False,
        }
    },
    'postgresql': {
        'ENGINE': 'postgresql',
        'NAME': os.getenv('DB_NAME', 'finrl_crypto'),
        'USER': os.getenv('DB_USER', 'finrl'),
        'PASSWORD': os.getenv('DB_PASSWORD', ''),
        'HOST': os.getenv('DB_HOST', 'localhost'),
        'PORT': os.getenv('DB_PORT', '5432'),
        'OPTIONS': {
            'sslmode': 'prefer',
        }
    },
    'redis': {
        'HOST': os.getenv('REDIS_HOST', 'localhost'),
        'PORT': int(os.getenv('REDIS_PORT', '6379')),
        'DB': int(os.getenv('REDIS_DB', '0')),
        'PASSWORD': os.getenv('REDIS_PASSWORD', ''),
        'DECODE_RESPONSES': True,
        'SOCKET_CONNECT_TIMEOUT': 5,
        'SOCKET_TIMEOUT': 5,
        'CONNECTION_POOL_KWARGS': {
            'max_connections': 20,
            'retry_on_timeout': True,
        }
    }
}

# ==================== 监控配置 ====================

MONITORING_CONFIG = {
    # 系统监控
    'system_monitoring': {
        'enabled': True,
        'interval': 60,             # 监控间隔（秒）
        'metrics': ['cpu', 'memory', 'disk', 'network'],
        'thresholds': {
            'cpu_percent': 80,
            'memory_percent': 85,
            'disk_percent': 90,
        }
    },
    
    # 性能监控
    'performance_monitoring': {
        'enabled': True,
        'track_training': True,
        'track_inference': True,
        'track_data_loading': True,
        'profile_memory': True,
        'profile_gpu': True,
    },
    
    # 业务监控
    'business_monitoring': {
        'enabled': True,
        'track_trades': True,
        'track_pnl': True,
        'track_positions': True,
        'track_risk_metrics': True,
    },
    
    # 报警配置
    'alerting': {
        'enabled': True,
        'channels': {
            'email': {
                'enabled': True,
                'smtp_server': os.getenv('SMTP_SERVER', ''),
                'smtp_port': int(os.getenv('SMTP_PORT', '587')),
                'username': os.getenv('SMTP_USERNAME', ''),
                'password': os.getenv('SMTP_PASSWORD', ''),
                'from_email': os.getenv('FROM_EMAIL', ''),
                'to_emails': os.getenv('TO_EMAILS', '').split(','),
            },
            'webhook': {
                'enabled': True,
                'url': os.getenv('WEBHOOK_URL', ''),
                'timeout': 10,
                'retry_count': 3,
            },
            'slack': {
                'enabled': False,
                'webhook_url': os.getenv('SLACK_WEBHOOK_URL', ''),
                'channel': os.getenv('SLACK_CHANNEL', '#alerts'),
            }
        }
    }
}

# ==================== 安全配置 ====================

SECURITY_CONFIG = {
    # 加密配置
    'encryption': {
        'enabled': True,
        'algorithm': 'AES-256-GCM',
        'key_derivation': 'PBKDF2',
        'iterations': 100000,
    },
    
    # API安全
    'api_security': {
        'rate_limiting': {
            'enabled': True,
            'requests_per_minute': 60,
            'burst_size': 10,
        },
        'authentication': {
            'enabled': True,
            'method': 'jwt',  # jwt, api_key, oauth
            'token_expiry': 3600,  # 1小时
        },
        'cors': {
            'enabled': True,
            'allowed_origins': ['http://localhost:3000'],
            'allowed_methods': ['GET', 'POST', 'PUT', 'DELETE'],
            'allowed_headers': ['Content-Type', 'Authorization'],
        }
    },
    
    # 数据安全
    'data_security': {
        'encrypt_sensitive_data': True,
        'mask_api_keys': True,
        'secure_logging': True,
        'data_retention_days': 365,
    }
}

# ==================== 插件配置 ====================

PLUGIN_CONFIG = {
    'enabled': True,
    'plugin_dir': BASE_DIR / 'plugins',
    'auto_load': True,
    'allowed_plugins': [],  # 空列表表示允许所有插件
    'blocked_plugins': [],  # 阻止的插件列表
    'plugin_timeout': 30,   # 插件执行超时
    'sandbox_mode': True,   # 沙盒模式
}

# ==================== 开发配置 ====================

if ENVIRONMENT == 'development':
    # 开发环境特殊配置
    DEBUG = True
    DATA_CONFIG['cache_enabled'] = False
    MODEL_CONFIG['total_timesteps'] = 10000  # 减少训练时间
    LOGGING_CONFIG['loggers']['finrl_crypto']['level'] = 'DEBUG'
    
elif ENVIRONMENT == 'testing':
    # 测试环境配置
    DEBUG = True
    DATA_CONFIG['symbols'] = ['BTC-USD']  # 只使用一个交易对
    MODEL_CONFIG['total_timesteps'] = 1000
    DATABASE_CONFIG['default']['NAME'] = ':memory:'  # 内存数据库
    
elif ENVIRONMENT == 'production':
    # 生产环境配置
    DEBUG = False
    LIVE_TRADING_CONFIG['mode'] = 'live'
    LIVE_TRADING_CONFIG['sandbox'] = False
    SECURITY_CONFIG['encryption']['enabled'] = True
    MONITORING_CONFIG['alerting']['enabled'] = True

# ==================== 配置验证 ====================

def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 检查必要的目录
    required_dirs = [DATA_DIR, MODEL_DIR, LOG_DIR]
    for directory in required_dirs:
        if not directory.exists():
            errors.append(f"Required directory does not exist: {directory}")
    
    # 检查数据配置
    if not DATA_CONFIG['symbols']:
        errors.append("No trading symbols configured")
    
    if DATA_CONFIG['start_date'] >= DATA_CONFIG['end_date']:
        errors.append("Start date must be before end date")
    
    # 检查模型配置
    if MODEL_CONFIG['total_timesteps'] <= 0:
        errors.append("Total timesteps must be positive")
    
    if MODEL_CONFIG['learning_rate'] <= 0:
        errors.append("Learning rate must be positive")
    
    # 检查环境配置
    if ENV_CONFIG['initial_amount'] <= 0:
        errors.append("Initial amount must be positive")
    
    if not (0 <= ENV_CONFIG['transaction_cost_pct'] <= 1):
        errors.append("Transaction cost must be between 0 and 1")
    
    # 检查实时交易配置
    if LIVE_TRADING_CONFIG['mode'] == 'live':
        if not LIVE_TRADING_CONFIG['api_key']:
            errors.append("API key required for live trading")
        if not LIVE_TRADING_CONFIG['api_secret']:
            errors.append("API secret required for live trading")
    
    if errors:
        raise ValueError(f"Configuration validation failed:\n" + "\n".join(errors))
    
    return True

# 在导入时验证配置
if __name__ != '__main__':
    validate_config()

# ==================== 配置导出 ====================

# 将所有配置组合到一个字典中
CONFIG = {
    'project': {
        'name': PROJECT_NAME,
        'version': VERSION,
        'debug': DEBUG,
        'environment': ENVIRONMENT,
    },
    'paths': {
        'base_dir': BASE_DIR,
        'data_dir': DATA_DIR,
        'model_dir': MODEL_DIR,
        'log_dir': LOG_DIR,
        'result_dir': RESULT_DIR,
        'cache_dir': CACHE_DIR,
        'checkpoint_dir': CHECKPOINT_DIR,
    },
    'data': DATA_CONFIG,
    'model': MODEL_CONFIG,
    'env': ENV_CONFIG,
    'backtest': BACKTEST_CONFIG,
    'live_trading': LIVE_TRADING_CONFIG,
    'logging': LOGGING_CONFIG,
    'database': DATABASE_CONFIG,
    'monitoring': MONITORING_CONFIG,
    'security': SECURITY_CONFIG,
    'plugin': PLUGIN_CONFIG,
}

# 配置访问函数
def get_config(key_path=None):
    """获取配置值
    
    Args:
        key_path: 配置键路径，如 'data.symbols' 或 'model.learning_rate'
    
    Returns:
        配置值或整个配置字典
    """
    if key_path is None:
        return CONFIG
    
    keys = key_path.split('.')
    value = CONFIG
    
    for key in keys:
        if isinstance(value, dict) and key in value:
            value = value[key]
        else:
            raise KeyError(f"Configuration key not found: {key_path}")
    
    return value

def set_config(key_path, value):
    """设置配置值
    
    Args:
        key_path: 配置键路径
        value: 新的配置值
    """
    keys = key_path.split('.')
    config = CONFIG
    
    for key in keys[:-1]:
        if key not in config:
            config[key] = {}
        config = config[key]
    
    config[keys[-1]] = value

def update_config(updates):
    """批量更新配置
    
    Args:
        updates: 配置更新字典
    """
    def deep_update(base_dict, update_dict):
        for key, value in update_dict.items():
            if isinstance(value, dict) and key in base_dict and isinstance(base_dict[key], dict):
                deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    deep_update(CONFIG, updates)
    validate_config()

# 导出主要配置对象
__all__ = [
    'CONFIG',
    'DATA_CONFIG',
    'MODEL_CONFIG', 
    'ENV_CONFIG',
    'BACKTEST_CONFIG',
    'LIVE_TRADING_CONFIG',
    'LOGGING_CONFIG',
    'DATABASE_CONFIG',
    'MONITORING_CONFIG',
    'SECURITY_CONFIG',
    'PLUGIN_CONFIG',
    'get_config',
    'set_config',
    'update_config',
    'validate_config'
]
```

## 🌍 环境变量配置

### .env 文件示例

```bash
# .env 文件
# 环境配置
ENVIRONMENT=development
DEBUG=true

# 数据源配置
DATA_SOURCE=yfinance

# API配置
API_KEY=your_api_key_here
API_SECRET=your_api_secret_here
API_PASSPHRASE=your_passphrase_here

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/finrl_crypto
REDIS_URL=redis://localhost:6379/0

# 邮件配置
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>
TO_EMAILS=<EMAIL>,<EMAIL>

# Webhook配置
WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_CHANNEL=#alerts

# 安全配置
SECRET_KEY=your-secret-key-here
ENCRYPTION_KEY=your-encryption-key-here

# 监控配置
MONITORING_ENABLED=true
ALERTING_ENABLED=true
```

## 📝 命令行配置

### 使用命令行参数

```bash
# 训练模型
python -m finrl_crypto.train \
    --config config_main.py \
    --algorithm PPO \
    --total-timesteps 100000 \
    --learning-rate 3e-4 \
    --symbols BTC-USD ETH-USD \
    --start-date 2022-01-01 \
    --end-date 2023-12-31

# 回测策略
python -m finrl_crypto.backtest \
    --model models/trained/ppo_model \
    --start-date 2023-01-01 \
    --end-date 2023-12-31 \
    --initial-amount 100000

# 实时交易
python -m finrl_crypto.live \
    --config config_live.py \
    --mode paper \
    --exchange binance
```

### 配置文件选择

```bash
# 使用不同的配置文件
python -m finrl_crypto.train --config config_btc_only.py
python -m finrl_crypto.train --config config_portfolio.py
python -m finrl_crypto.train --config config_high_freq.py
```

## 🔧 代码中的配置

### 动态配置修改

```python
from finrl_crypto.config import get_config, set_config, update_config

# 获取配置
symbols = get_config('data.symbols')
learning_rate = get_config('model.learning_rate')

# 修改单个配置
set_config('model.learning_rate', 1e-4)
set_config('data.symbols', ['BTC-USD', 'ETH-USD'])

# 批量更新配置
update_config({
    'model': {
        'learning_rate': 1e-4,
        'total_timesteps': 200000
    },
    'data': {
        'symbols': ['BTC-USD', 'ETH-USD', 'ADA-USD']
    }
})
```

### 配置类使用

```python
from finrl_crypto.config import Config

# 创建配置对象
config = Config()

# 从文件加载
config.load_from_file('config_main.py')

# 从环境变量加载
config.load_from_env()

# 从字典更新
config.update({
    'model.algorithm': 'SAC',
    'data.timeframe': '4h'
})

# 验证配置
config.validate()

# 保存配置
config.save_to_file('config_custom.py')
```

## 📊 配置模板

### 高频交易配置

```python
# config_high_freq.py
from config_main import *

# 覆盖特定配置
DATA_CONFIG.update({
    'timeframe': '1m',
    'symbols': ['BTC-USD', 'ETH-USD'],  # 减少交易对
    'indicators': {
        'sma': [5, 10],  # 减少指标
        'ema': [12, 26],
        'rsi': [14]
    }
})

MODEL_CONFIG.update({
    'total_timesteps': 500000,  # 增加训练步数
    'learning_rate': 1e-4,      # 降低学习率
    'n_steps': 512,             # 减少步数
})

ENV_CONFIG.update({
    'transaction_cost_pct': 0.0005,  # 降低交易成本
    'lookback_window': 10,           # 减少回看窗口
    'reward_scaling': 1e-3,          # 调整奖励缩放
})
```

### 投资组合配置

```python
# config_portfolio.py
from config_main import *

DATA_CONFIG.update({
    'symbols': [
        'BTC-USD', 'ETH-USD', 'ADA-USD', 'DOT-USD', 'LINK-USD',
        'LTC-USD', 'BCH-USD', 'XLM-USD', 'EOS-USD', 'TRX-USD'
    ],
    'timeframe': '4h',
})

ENV_CONFIG.update({
    'env_type': 'portfolio',
    'max_position': 0.2,  # 单个资产最大20%仓位
    'rebalance_freq': 'daily',
})

MODEL_CONFIG.update({
    'algorithm': 'SAC',  # 使用SAC算法
    'policy_kwargs': {
        'net_arch': [512, 512, 256],  # 更大的网络
    }
})
```

### 生产环境配置

```python
# config_production.py
from config_main import *

# 生产环境特殊配置
ENVIRONMENT = 'production'
DEBUG = False

LIVE_TRADING_CONFIG.update({
    'mode': 'live',
    'sandbox': False,
    'max_position_size': 0.05,  # 更保守的仓位
    'max_daily_loss': 0.01,     # 更严格的风险控制
})

SECURITY_CONFIG.update({
    'encryption': {'enabled': True},
    'api_security': {
        'rate_limiting': {'enabled': True},
        'authentication': {'enabled': True}
    }
})

MONITORING_CONFIG.update({
    'alerting': {'enabled': True},
    'system_monitoring': {'enabled': True}
})
```

## 🔍 配置验证

### 自定义验证规则

```python
from finrl_crypto.config import ConfigValidator

# 创建验证器
validator = ConfigValidator()

# 添加自定义验证规则
@validator.rule('data.symbols')
def validate_symbols(symbols):
    if not symbols:
        raise ValueError("At least one symbol must be specified")
    
    for symbol in symbols:
        if not symbol.endswith('-USD'):
            raise ValueError(f"Symbol {symbol} must end with -USD")

@validator.rule('model.learning_rate')
def validate_learning_rate(lr):
    if not (1e-6 <= lr <= 1e-1):
        raise ValueError("Learning rate must be between 1e-6 and 1e-1")

# 验证配置
validator.validate(CONFIG)
```

### 配置检查工具

```python
from finrl_crypto.utils import check_config

# 检查配置完整性
check_result = check_config(CONFIG)

if check_result['valid']:
    print("✅ 配置验证通过")
else:
    print("❌ 配置验证失败:")
    for error in check_result['errors']:
        print(f"  - {error}")
    
    print("⚠️ 配置警告:")
    for warning in check_result['warnings']:
        print(f"  - {warning}")
```

## 🚀 最佳实践

### 1. 配置分层

```python
# 基础配置
base_config.py

# 环境特定配置
config_development.py
config_testing.py
config_production.py

# 策略特定配置
config_btc_strategy.py
config_portfolio_strategy.py
config_arbitrage_strategy.py
```

### 2. 敏感信息保护

```python
# 不要在配置文件中硬编码敏感信息
# ❌ 错误做法
API_KEY = "your_actual_api_key"

# ✅ 正确做法
API_KEY = os.getenv('API_KEY', '')

# 使用加密存储
from finrl_crypto.security import decrypt_config
API_KEY = decrypt_config('encrypted_api_key')
```

### 3. 配置版本控制

```python
# 在配置中包含版本信息
CONFIG_VERSION = "1.2.0"
COMPATIBLE_VERSIONS = ["1.0.0", "1.1.0", "1.2.0"]

def check_compatibility():
    if CONFIG_VERSION not in COMPATIBLE_VERSIONS:
        raise ValueError(f"Unsupported config version: {CONFIG_VERSION}")
```

### 4. 配置文档化

```python
# 为每个配置项添加详细注释
DATA_CONFIG = {
    'symbols': [  # 交易对列表
        'BTC-USD',  # 比特币/美元
        'ETH-USD',  # 以太坊/美元
    ],
    'timeframe': '1h',  # 数据时间周期：1m, 5m, 15m, 30m, 1h, 4h, 1d
    'start_date': '2020-01-01',  # 数据开始日期 (YYYY-MM-DD)
    'end_date': '2023-12-31',    # 数据结束日期 (YYYY-MM-DD)
}
```

## 🔧 故障排除

### 常见配置问题

1. **配置文件未找到**
   ```python
   # 检查配置文件路径
   import os
   config_path = 'config_main.py'
   if not os.path.exists(config_path):
       print(f"配置文件不存在: {config_path}")
   ```

2. **环境变量未设置**
   ```python
   # 检查必要的环境变量
   required_env_vars = ['API_KEY', 'API_SECRET']
   missing_vars = [var for var in required_env_vars if not os.getenv(var)]
   if missing_vars:
       print(f"缺少环境变量: {missing_vars}")
   ```

3. **配置值类型错误**
   ```python
   # 类型检查和转换
   def safe_int(value, default=0):
       try:
           return int(value)
       except (ValueError, TypeError):
           return default
   
   BATCH_SIZE = safe_int(os.getenv('BATCH_SIZE'), 64)
   ```

通过合理的配置管理，您可以轻松地在不同环境和场景下使用 FinRL Crypto，同时保持代码的灵活性和可维护性。