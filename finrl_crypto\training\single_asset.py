"""单资产交易训练器模块

专门用于单个加密货币资产的强化学习交易训练。
"""

import time
from typing import Dict, Any, Optional, List
import numpy as np
import pandas as pd
from collections import deque

from .base import BaseTrainer, TrainingConfig, EvaluationMetrics
from ..agent.base import BaseAgent
from ..environment.base import BaseEnvironment


class SingleAssetTrainer(BaseTrainer):
    """单资产交易训练器
    
    专门用于单个加密货币资产的交易训练，提供针对性的训练策略和评估指标。
    """
    
    def __init__(self,
                 agent: BaseAgent,
                 env: BaseEnvironment,
                 config: Optional[TrainingConfig] = None,
                 save_path: str = "./models",
                 experiment_name: str = "single_asset_experiment"):
        """初始化单资产训练器
        
        Args:
            agent: 智能体
            env: 交易环境
            config: 训练配置
            save_path: 模型保存路径
            experiment_name: 实验名称
        """
        super().__init__(agent, env, config, save_path, experiment_name)
        
        # 单资产特定属性
        self.symbol = None  # 交易的加密货币符号
        
        # 单资产特定的训练历史
        self.training_history.update({
            'portfolio_values': [],
            'positions': [],
            'trades': [],
            'returns': [],
            'sharpe_ratios': [],
            'max_drawdowns': [],
        })
        
        # 交易统计
        self.trade_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_profit': 0.0,
            'total_loss': 0.0,
        }
        
        # 回合奖励缓冲区
        self.episode_rewards = deque(maxlen=100)
        self.episode_returns = deque(maxlen=100)
        
    def train(self) -> Dict[str, Any]:
        """训练智能体
        
        Returns:
            训练结果字典
        """
        self.logger.info(f"开始训练 - 总时间步数: {self.config.total_timesteps}")
        start_time = time.time()
        
        # 重置环境
        obs = self.env.reset()
        episode_reward = 0.0
        episode_length = 0
        episode_start_value = self.env.get_portfolio_value()
        
        while self.current_timestep < self.config.total_timesteps:
            # 选择动作
            if self.current_timestep < self.config.learning_starts:
                # 随机探索阶段
                action = self.env.action_space.sample()
            else:
                action = self.agent.select_action(obs, training=True)
            
            # 执行动作
            next_obs, reward, done, info = self.env.step(action)
            
            # 存储经验
            if hasattr(self.agent, 'store_transition'):
                self.agent.store_transition(obs, action, reward, next_obs, done)
            
            # 更新统计
            episode_reward += reward
            episode_length += 1
            self.current_timestep += 1
            
            # 学习更新
            if (self.current_timestep >= self.config.learning_starts and 
                self.current_timestep % self.config.train_freq == 0):
                
                for _ in range(self.config.gradient_steps):
                    loss_info = self.agent.learn()
                    if loss_info and 'loss' in loss_info:
                        self.training_history['losses'].append(loss_info['loss'])
            
            # 目标网络更新（如果适用）
            if (hasattr(self.agent, 'update_target_network') and 
                self.current_timestep % self.config.target_update_interval == 0):
                self.agent.update_target_network()
            
            # 回合结束处理
            if done:
                # 计算回合统计
                episode_end_value = self.env.get_portfolio_value()
                episode_return = (episode_end_value - episode_start_value) / episode_start_value
                
                # 更新历史记录
                self.episode_rewards.append(episode_reward)
                self.episode_returns.append(episode_return)
                self.training_history['rewards'].append(episode_reward)
                self.training_history['returns'].append(episode_return)
                self.training_history['timesteps'].append(self.current_timestep)
                
                # 更新交易统计
                self._update_trade_stats(info)
                
                # 记录回合信息
                if self._should_log():
                    self._log_episode_info(episode_reward, episode_return, episode_length)
                
                # 重置环境
                obs = self.env.reset()
                episode_reward = 0.0
                episode_length = 0
                episode_start_value = self.env.get_portfolio_value()
                self.current_episode += 1
            else:
                obs = next_obs
            
            # 评估
            if self._should_evaluate():
                eval_metrics = self.evaluate()
                self.training_history['eval_rewards'].append(eval_metrics.mean_reward)
                self.training_history['eval_timesteps'].append(self.current_timestep)
                
                # 早停检查
                if self.config.early_stopping_patience > 0:
                    if self._check_early_stopping(eval_metrics.mean_reward):
                        self.logger.info(f"早停触发 - 在时间步 {self.current_timestep}")
                        break
                
                # 保存最佳模型
                if self.config.save_best_model and eval_metrics.mean_reward > self.best_mean_reward:
                    self.save_model()
            
            # 定期保存
            if self._should_save():
                save_path = f"{self.save_path}/{self.experiment_name}_step_{self.current_timestep}.pth"
                self.save_model(save_path)
        
        # 训练结束
        training_time = time.time() - start_time
        
        # 最终评估
        final_eval = self.evaluate()
        
        # 保存训练历史
        self.save_training_history()
        
        # 生成训练报告
        training_results = self._generate_training_report(training_time, final_eval)
        
        self.logger.info(f"训练完成 - 总时间: {training_time:.2f}秒")
        
        return training_results
    
    def evaluate(self, n_episodes: Optional[int] = None) -> EvaluationMetrics:
        """评估智能体
        
        Args:
            n_episodes: 评估回合数
            
        Returns:
            评估指标
        """
        if n_episodes is None:
            n_episodes = self.config.n_eval_episodes
        
        self.logger.info(f"开始评估 - {n_episodes} 回合")
        start_time = time.time()
        
        # 设置为评估模式
        self.agent.set_training_mode(False)
        
        episode_rewards = []
        episode_lengths = []
        episode_returns = []
        portfolio_values = []
        
        for episode in range(n_episodes):
            obs = self.eval_env.reset()
            episode_reward = 0.0
            episode_length = 0
            start_value = self.eval_env.get_portfolio_value()
            
            done = False
            while not done:
                action = self.agent.select_action(obs, training=False)
                obs, reward, done, info = self.eval_env.step(action)
                
                episode_reward += reward
                episode_length += 1
            
            end_value = self.eval_env.get_portfolio_value()
            episode_return = (end_value - start_value) / start_value
            
            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)
            episode_returns.append(episode_return)
            portfolio_values.append(end_value)
        
        # 恢复训练模式
        self.agent.set_training_mode(True)
        
        # 计算评估指标
        eval_time = time.time() - start_time
        
        metrics = EvaluationMetrics(
            episode_rewards=episode_rewards,
            episode_lengths=episode_lengths,
            mean_reward=np.mean(episode_rewards),
            std_reward=np.std(episode_rewards),
            min_reward=np.min(episode_rewards),
            max_reward=np.max(episode_rewards),
            total_return=np.mean(episode_returns),
            evaluation_time=eval_time,
            timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
        )
        
        # 计算交易相关指标
        if episode_returns:
            returns_array = np.array(episode_returns)
            metrics.sharpe_ratio = self._calculate_sharpe_ratio(returns_array)
            metrics.max_drawdown = self._calculate_max_drawdown(portfolio_values)
            metrics.volatility = np.std(returns_array)
            metrics.win_rate = np.sum(returns_array > 0) / len(returns_array)
        
        self.logger.info(f"评估完成 - 平均奖励: {metrics.mean_reward:.4f}, "
                        f"平均回报: {metrics.total_return:.4f}, "
                        f"夏普比率: {metrics.sharpe_ratio:.4f}")
        
        return metrics
    
    def _update_trade_stats(self, info: Dict[str, Any]):
        """更新交易统计
        
        Args:
            info: 环境返回的信息
        """
        if 'trades' in info:
            trades = info['trades']
            for trade in trades:
                self.trade_stats['total_trades'] += 1
                if trade['profit'] > 0:
                    self.trade_stats['winning_trades'] += 1
                    self.trade_stats['total_profit'] += trade['profit']
                else:
                    self.trade_stats['losing_trades'] += 1
                    self.trade_stats['total_loss'] += abs(trade['profit'])
    
    def _log_episode_info(self, reward: float, return_rate: float, length: int):
        """记录回合信息
        
        Args:
            reward: 回合奖励
            return_rate: 回合回报率
            length: 回合长度
        """
        avg_reward = np.mean(self.episode_rewards) if self.episode_rewards else 0
        avg_return = np.mean(self.episode_returns) if self.episode_returns else 0
        
        info = {
            'episode': self.current_episode,
            'reward': reward,
            'return': return_rate * 100,  # 转换为百分比
            'length': length,
            'avg_reward_100': avg_reward,
            'avg_return_100': avg_return * 100,
        }
        
        self._log_training_info(info)
    
    def _calculate_sharpe_ratio(self, returns: np.ndarray, risk_free_rate: float = 0.0) -> float:
        """计算夏普比率
        
        Args:
            returns: 回报率数组
            risk_free_rate: 无风险利率
            
        Returns:
            夏普比率
        """
        if len(returns) == 0 or np.std(returns) == 0:
            return 0.0
        
        excess_returns = returns - risk_free_rate
        return np.mean(excess_returns) / np.std(returns)
    
    def _calculate_max_drawdown(self, portfolio_values: List[float]) -> float:
        """计算最大回撤
        
        Args:
            portfolio_values: 投资组合价值列表
            
        Returns:
            最大回撤
        """
        if not portfolio_values:
            return 0.0
        
        values = np.array(portfolio_values)
        peak = np.maximum.accumulate(values)
        drawdown = (peak - values) / peak
        return np.max(drawdown)
    
    def _generate_training_report(self, training_time: float, final_eval: EvaluationMetrics) -> Dict[str, Any]:
        """生成训练报告
        
        Args:
            training_time: 训练时间
            final_eval: 最终评估结果
            
        Returns:
            训练报告字典
        """
        # 计算训练期间的统计
        total_episodes = len(self.training_history['rewards'])
        avg_episode_reward = np.mean(self.training_history['rewards']) if self.training_history['rewards'] else 0
        avg_episode_return = np.mean(self.training_history['returns']) if self.training_history['returns'] else 0
        
        # 交易统计
        win_rate = (self.trade_stats['winning_trades'] / 
                   max(self.trade_stats['total_trades'], 1))
        
        profit_factor = (self.trade_stats['total_profit'] / 
                        max(self.trade_stats['total_loss'], 1e-8))
        
        report = {
            'training_info': {
                'total_timesteps': self.current_timestep,
                'total_episodes': total_episodes,
                'training_time': training_time,
                'timesteps_per_second': self.current_timestep / training_time,
            },
            'training_performance': {
                'avg_episode_reward': avg_episode_reward,
                'avg_episode_return': avg_episode_return,
                'best_mean_reward': self.best_mean_reward,
            },
            'final_evaluation': final_eval.to_dict(),
            'trading_stats': {
                'total_trades': self.trade_stats['total_trades'],
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'total_profit': self.trade_stats['total_profit'],
                'total_loss': self.trade_stats['total_loss'],
            },
            'agent_info': {
                'agent_type': self.agent.__class__.__name__,
                'state_dim': getattr(self.agent, 'state_dim', 'unknown'),
                'action_dim': getattr(self.agent, 'action_dim', 'unknown'),
            },
            'environment_info': {
                'env_type': self.env.__class__.__name__,
                'observation_space': str(self.env.observation_space),
                'action_space': str(self.env.action_space),
            }
        }
        
        return report
    
    def get_trade_statistics(self) -> Dict[str, Any]:
        """获取交易统计信息
        
        Returns:
            交易统计字典
        """
        total_trades = self.trade_stats['total_trades']
        if total_trades == 0:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'avg_profit_per_trade': 0.0,
                'avg_loss_per_trade': 0.0,
            }
        
        win_rate = self.trade_stats['winning_trades'] / total_trades
        profit_factor = (self.trade_stats['total_profit'] / 
                        max(self.trade_stats['total_loss'], 1e-8))
        
        avg_profit = (self.trade_stats['total_profit'] / 
                     max(self.trade_stats['winning_trades'], 1))
        avg_loss = (self.trade_stats['total_loss'] / 
                   max(self.trade_stats['losing_trades'], 1))
        
        return {
            'total_trades': total_trades,
            'winning_trades': self.trade_stats['winning_trades'],
            'losing_trades': self.trade_stats['losing_trades'],
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_profit': self.trade_stats['total_profit'],
            'total_loss': self.trade_stats['total_loss'],
            'avg_profit_per_trade': avg_profit,
            'avg_loss_per_trade': avg_loss,
        }
    
    def reset_trade_statistics(self):
        """重置交易统计"""
        self.trade_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_profit': 0.0,
            'total_loss': 0.0,
        }
        self.logger.info("交易统计已重置")