<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="4" skipped="0" tests="20" time="35.991" timestamp="2025-06-19T02:04:13.052688+09:00" hostname="E-5CG22747W5"><testcase classname="improvetest.test_phase1_environment_module.TestBaseEnvironment" name="test_base_environment_initialization" time="1.422"><failure message="TypeError: Can't instantiate abstract class BaseEnvironment without an implementation for abstract methods '_calculate_reward', '_execute_action', '_get_state', '_reset_specific_state', '_setup_spaces'">improvetest\test_phase1_environment_module.py:77: in test_base_environment_initialization
    env = BaseEnvironment(
E   TypeError: Can't instantiate abstract class BaseEnvironment without an implementation for abstract methods '_calculate_reward', '_execute_action', '_get_state', '_reset_specific_state', '_setup_spaces'</failure></testcase><testcase classname="improvetest.test_phase1_environment_module.TestBaseEnvironment" name="test_base_environment_with_optional_params" time="0.024"><failure message="TypeError: Can't instantiate abstract class BaseEnvironment without an implementation for abstract methods '_calculate_reward', '_execute_action', '_get_state', '_reset_specific_state', '_setup_spaces'">improvetest\test_phase1_environment_module.py:91: in test_base_environment_with_optional_params
    env = BaseEnvironment(
E   TypeError: Can't instantiate abstract class BaseEnvironment without an implementation for abstract methods '_calculate_reward', '_execute_action', '_get_state', '_reset_specific_state', '_setup_spaces'</failure></testcase><testcase classname="improvetest.test_phase1_environment_module.TestBaseEnvironment" name="test_gym_env_inheritance" time="0.007"><failure message="TypeError: Can't instantiate abstract class BaseEnvironment without an implementation for abstract methods '_calculate_reward', '_execute_action', '_get_state', '_reset_specific_state', '_setup_spaces'">improvetest\test_phase1_environment_module.py:105: in test_gym_env_inheritance
    env = BaseEnvironment(data=self.mock_data)
E   TypeError: Can't instantiate abstract class BaseEnvironment without an implementation for abstract methods '_calculate_reward', '_execute_action', '_get_state', '_reset_specific_state', '_setup_spaces'</failure></testcase><testcase classname="improvetest.test_phase1_environment_module.TestCryptoTradingEnvironment" name="test_action_space_definition" time="0.091" /><testcase classname="improvetest.test_phase1_environment_module.TestCryptoTradingEnvironment" name="test_observation_space_definition" time="0.019" /><testcase classname="improvetest.test_phase1_environment_module.TestCryptoTradingEnvironment" name="test_reset_functionality" time="0.047" /><testcase classname="improvetest.test_phase1_environment_module.TestCryptoTradingEnvironment" name="test_reward_calculation" time="0.018" /><testcase classname="improvetest.test_phase1_environment_module.TestCryptoTradingEnvironment" name="test_step_functionality" time="0.085" /><testcase classname="improvetest.test_phase1_environment_module.TestCryptoTradingEnvironment" name="test_trading_environment_initialization" time="0.015" /><testcase classname="improvetest.test_phase1_environment_module.TestCryptoTradingEnvironment" name="test_transaction_cost_calculation" time="0.029" /><testcase classname="improvetest.test_phase1_environment_module.TestPortfolioEnvironment" name="test_multi_asset_action_space" time="0.025" /><testcase classname="improvetest.test_phase1_environment_module.TestPortfolioEnvironment" name="test_portfolio_environment_initialization" time="0.019" /><testcase classname="improvetest.test_phase1_environment_module.TestPortfolioEnvironment" name="test_portfolio_rebalancing" time="0.019" /><testcase classname="improvetest.test_phase1_environment_module.TestPortfolioEnvironment" name="test_portfolio_value_calculation" time="0.029" /><testcase classname="improvetest.test_phase1_environment_module.TestEnvironmentStateManagement" name="test_episode_termination" time="0.028" /><testcase classname="improvetest.test_phase1_environment_module.TestEnvironmentStateManagement" name="test_state_normalization" time="0.023" /><testcase classname="improvetest.test_phase1_environment_module.TestEnvironmentStateManagement" name="test_state_representation" time="0.020" /><testcase classname="improvetest.test_phase1_environment_module.TestEnvironmentIntegration" name="test_complete_trading_episode" time="0.066"><failure message="IndexError: invalid index to scalar variable.">improvetest\test_phase1_environment_module.py:524: in test_complete_trading_episode
    next_state, reward, done, info = env.step(action)
finrl_crypto\environment\base.py:261: in step
    execution_info = self._execute_action(action)
finrl_crypto\environment\crypto_trading.py:369: in _execute_action
    target_values[i] / current_prices[i] if current_prices[i] &gt; 0 else 0
E   IndexError: invalid index to scalar variable.</failure></testcase><testcase classname="improvetest.test_phase1_environment_module.TestEnvironmentIntegration" name="test_environment_consistency" time="0.138" /><testcase classname="improvetest.test_phase1_environment_module.TestEnvironmentIntegration" name="test_environment_with_different_configs" time="0.064" /></testsuite></testsuites>