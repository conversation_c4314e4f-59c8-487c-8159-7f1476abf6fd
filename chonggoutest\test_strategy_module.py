#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略模块测试 - TDD测试驱动开发

测试finrl_crypto.strategy模块的所有功能：
- BaseStrategy抽象基类
- 具体策略实现（移动平均、RSI、MACD等）
- RL策略框架
- 策略组合和参数优化
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Mock外部依赖
sys.modules['gym'] = Mock()
sys.modules['stable_baselines3'] = Mock()
sys.modules['torch'] = Mock()
sys.modules['tensorflow'] = Mock()
sys.modules['matplotlib'] = Mock()
sys.modules['seaborn'] = Mock()
sys.modules['plotly'] = Mock()
sys.modules['ta'] = Mock()
sys.modules['talib'] = Mock()

class TestBaseStrategy(unittest.TestCase):
    """测试BaseStrategy抽象基类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试数据
        self.test_data = pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=100, freq='1H'),
            'open': np.random.uniform(100, 200, 100),
            'high': np.random.uniform(150, 250, 100),
            'low': np.random.uniform(50, 150, 100),
            'close': np.random.uniform(100, 200, 100),
            'volume': np.random.uniform(1000, 10000, 100)
        })
        
        self.test_config = {
            'name': 'test_strategy',
            'parameters': {'param1': 10, 'param2': 0.5},
            'risk_management': {'max_position': 0.1, 'stop_loss': 0.05}
        }
    
    def test_base_strategy_interface(self):
        """测试BaseStrategy接口定义"""
        # 这个测试确保BaseStrategy有正确的抽象方法
        try:
            from finrl_crypto.strategy.base import BaseStrategy
            
            # 检查抽象方法
            self.assertTrue(hasattr(BaseStrategy, 'generate_signals'))
            self.assertTrue(hasattr(BaseStrategy, 'update_parameters'))
            self.assertTrue(hasattr(BaseStrategy, 'get_position_size'))
            
            # 尝试实例化应该失败（抽象类）
            with self.assertRaises(TypeError):
                BaseStrategy()
                
        except ImportError:
            self.fail("BaseStrategy类未实现")
    
    def test_strategy_initialization(self):
        """测试策略初始化"""
        try:
            from finrl_crypto.strategy.base import BaseStrategy
            
            # 创建具体策略类用于测试
            class TestStrategy(BaseStrategy):
                def generate_signals(self, data):
                    return pd.Series([0] * len(data))
                
                def update_parameters(self, params):
                    self.parameters.update(params)
                
                def get_position_size(self, signal, current_price, portfolio_value):
                    return signal * 0.1
            
            strategy = TestStrategy(self.test_config)
            
            self.assertEqual(strategy.name, 'test_strategy')
            self.assertEqual(strategy.parameters['param1'], 10)
            self.assertEqual(strategy.parameters['param2'], 0.5)
            
        except ImportError:
            self.fail("BaseStrategy类未实现")
    
    def test_signal_generation_interface(self):
        """测试信号生成接口"""
        try:
            from finrl_crypto.strategy.base import BaseStrategy
            
            class TestStrategy(BaseStrategy):
                def generate_signals(self, data):
                    # 返回买入(1)、卖出(-1)、持有(0)信号
                    signals = pd.Series([0] * len(data))
                    signals.iloc[10:20] = 1  # 买入信号
                    signals.iloc[50:60] = -1  # 卖出信号
                    return signals
                
                def update_parameters(self, params):
                    pass
                
                def get_position_size(self, signal, current_price, portfolio_value):
                    return signal * 0.1
            
            strategy = TestStrategy(self.test_config)
            signals = strategy.generate_signals(self.test_data)
            
            self.assertIsInstance(signals, pd.Series)
            self.assertEqual(len(signals), len(self.test_data))
            self.assertTrue(all(signal in [-1, 0, 1] for signal in signals))
            
        except ImportError:
            self.fail("BaseStrategy类未实现")

class TestMovingAverageStrategy(unittest.TestCase):
    """测试移动平均策略"""
    
    def setUp(self):
        """测试前准备"""
        self.test_data = pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=100, freq='1H'),
            'close': np.random.uniform(100, 200, 100)
        })
        
        self.ma_config = {
            'name': 'moving_average_strategy',
            'parameters': {
                'short_window': 5,
                'long_window': 20,
                'signal_threshold': 0.02
            }
        }
    
    def test_moving_average_strategy_creation(self):
        """测试移动平均策略创建"""
        try:
            from finrl_crypto.strategy.implementations import MovingAverageStrategy
            
            strategy = MovingAverageStrategy(self.ma_config)
            
            self.assertEqual(strategy.name, 'moving_average_strategy')
            self.assertEqual(strategy.parameters['short_window'], 5)
            self.assertEqual(strategy.parameters['long_window'], 20)
            
        except ImportError:
            self.fail("MovingAverageStrategy类未实现")
    
    def test_moving_average_signal_generation(self):
        """测试移动平均信号生成"""
        try:
            from finrl_crypto.strategy.implementations import MovingAverageStrategy
            
            strategy = MovingAverageStrategy(self.ma_config)
            signals = strategy.generate_signals(self.test_data)
            
            self.assertIsInstance(signals, pd.Series)
            self.assertEqual(len(signals), len(self.test_data))
            
            # 检查前20个信号应该是0（因为需要足够的数据计算长期移动平均）
            self.assertTrue(all(signals.iloc[:20] == 0))
            
        except ImportError:
            self.fail("MovingAverageStrategy类未实现")
    
    def test_moving_average_parameter_update(self):
        """测试移动平均策略参数更新"""
        try:
            from finrl_crypto.strategy.implementations import MovingAverageStrategy
            
            strategy = MovingAverageStrategy(self.ma_config)
            
            new_params = {'short_window': 10, 'long_window': 30}
            strategy.update_parameters(new_params)
            
            self.assertEqual(strategy.parameters['short_window'], 10)
            self.assertEqual(strategy.parameters['long_window'], 30)
            
        except ImportError:
            self.fail("MovingAverageStrategy类未实现")

class TestRSIStrategy(unittest.TestCase):
    """测试RSI策略"""
    
    def setUp(self):
        """测试前准备"""
        self.test_data = pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=100, freq='1H'),
            'close': np.random.uniform(100, 200, 100)
        })
        
        self.rsi_config = {
            'name': 'rsi_strategy',
            'parameters': {
                'rsi_period': 14,
                'oversold_threshold': 30,
                'overbought_threshold': 70
            }
        }
    
    def test_rsi_strategy_creation(self):
        """测试RSI策略创建"""
        try:
            from finrl_crypto.strategy.implementations import RSIStrategy
            
            strategy = RSIStrategy(self.rsi_config)
            
            self.assertEqual(strategy.name, 'rsi_strategy')
            self.assertEqual(strategy.parameters['rsi_period'], 14)
            self.assertEqual(strategy.parameters['oversold_threshold'], 30)
            self.assertEqual(strategy.parameters['overbought_threshold'], 70)
            
        except ImportError:
            self.fail("RSIStrategy类未实现")
    
    def test_rsi_signal_generation(self):
        """测试RSI信号生成"""
        try:
            from finrl_crypto.strategy.implementations import RSIStrategy
            
            strategy = RSIStrategy(self.rsi_config)
            signals = strategy.generate_signals(self.test_data)
            
            self.assertIsInstance(signals, pd.Series)
            self.assertEqual(len(signals), len(self.test_data))
            self.assertTrue(all(signal in [-1, 0, 1] for signal in signals))
            
        except ImportError:
            self.fail("RSIStrategy类未实现")

class TestRLStrategy(unittest.TestCase):
    """测试强化学习策略框架"""
    
    def setUp(self):
        """测试前准备"""
        self.test_data = pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=100, freq='1H'),
            'close': np.random.uniform(100, 200, 100),
            'volume': np.random.uniform(1000, 10000, 100)
        })
        
        self.rl_config = {
            'name': 'rl_strategy',
            'parameters': {
                'model_type': 'PPO',
                'state_dim': 10,
                'action_dim': 3,
                'learning_rate': 0.001
            }
        }
    
    def test_rl_strategy_creation(self):
        """测试RL策略创建"""
        try:
            from finrl_crypto.strategy.implementations import RLStrategy
            
            strategy = RLStrategy(self.rl_config)
            
            self.assertEqual(strategy.name, 'rl_strategy')
            self.assertEqual(strategy.parameters['model_type'], 'PPO')
            self.assertEqual(strategy.parameters['state_dim'], 10)
            
        except ImportError:
            self.fail("RLStrategy类未实现")
    
    def test_rl_model_integration(self):
        """测试RL模型集成"""
        try:
            from finrl_crypto.strategy.implementations import RLStrategy
            
            strategy = RLStrategy(self.rl_config)
            
            # 测试模型加载
            self.assertTrue(hasattr(strategy, 'model'))
            
            # 测试状态预处理
            self.assertTrue(hasattr(strategy, 'preprocess_state'))
            
            # 测试动作后处理
            self.assertTrue(hasattr(strategy, 'postprocess_action'))
            
        except ImportError:
            self.fail("RLStrategy类未实现")

class TestStrategyComposition(unittest.TestCase):
    """测试策略组合"""
    
    def setUp(self):
        """测试前准备"""
        self.test_data = pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=100, freq='1H'),
            'close': np.random.uniform(100, 200, 100)
        })
        
        self.composition_config = {
            'name': 'composite_strategy',
            'strategies': [
                {'type': 'MovingAverageStrategy', 'weight': 0.5, 'config': {}},
                {'type': 'RSIStrategy', 'weight': 0.3, 'config': {}},
                {'type': 'MACDStrategy', 'weight': 0.2, 'config': {}}
            ],
            'combination_method': 'weighted_average'
        }
    
    def test_strategy_composition_creation(self):
        """测试策略组合创建"""
        try:
            from finrl_crypto.strategy.composition import StrategyComposition
            
            composition = StrategyComposition(self.composition_config)
            
            self.assertEqual(composition.name, 'composite_strategy')
            self.assertEqual(len(composition.strategies), 3)
            self.assertEqual(composition.combination_method, 'weighted_average')
            
        except ImportError:
            self.fail("StrategyComposition类未实现")
    
    def test_composite_signal_generation(self):
        """测试组合策略信号生成"""
        try:
            from finrl_crypto.strategy.composition import StrategyComposition
            
            composition = StrategyComposition(self.composition_config)
            signals = composition.generate_signals(self.test_data)
            
            self.assertIsInstance(signals, pd.Series)
            self.assertEqual(len(signals), len(self.test_data))
            
            # 组合信号应该在[-1, 1]范围内
            self.assertTrue(all(-1 <= signal <= 1 for signal in signals))
            
        except ImportError:
            self.fail("StrategyComposition类未实现")

class TestStrategyOptimization(unittest.TestCase):
    """测试策略参数优化"""
    
    def setUp(self):
        """测试前准备"""
        self.test_data = pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=100, freq='1H'),
            'close': np.random.uniform(100, 200, 100)
        })
        
        self.optimization_config = {
            'strategy_type': 'MovingAverageStrategy',
            'parameter_space': {
                'short_window': [5, 10, 15, 20],
                'long_window': [20, 30, 40, 50],
                'signal_threshold': [0.01, 0.02, 0.03]
            },
            'optimization_method': 'grid_search',
            'evaluation_metric': 'sharpe_ratio'
        }
    
    def test_strategy_optimizer_creation(self):
        """测试策略优化器创建"""
        try:
            from finrl_crypto.strategy.optimization import StrategyOptimizer
            
            optimizer = StrategyOptimizer(self.optimization_config)
            
            self.assertEqual(optimizer.strategy_type, 'MovingAverageStrategy')
            self.assertEqual(optimizer.optimization_method, 'grid_search')
            self.assertEqual(optimizer.evaluation_metric, 'sharpe_ratio')
            
        except ImportError:
            self.fail("StrategyOptimizer类未实现")
    
    def test_parameter_optimization(self):
        """测试参数优化过程"""
        try:
            from finrl_crypto.strategy.optimization import StrategyOptimizer
            
            optimizer = StrategyOptimizer(self.optimization_config)
            
            # 测试优化方法
            self.assertTrue(hasattr(optimizer, 'optimize'))
            
            # 测试结果获取
            self.assertTrue(hasattr(optimizer, 'get_best_parameters'))
            self.assertTrue(hasattr(optimizer, 'get_optimization_results'))
            
        except ImportError:
            self.fail("StrategyOptimizer类未实现")

class TestStrategyFactory(unittest.TestCase):
    """测试策略工厂"""
    
    def test_strategy_factory_creation(self):
        """测试策略工厂创建"""
        try:
            from finrl_crypto.strategy.factory import StrategyFactory
            
            factory = StrategyFactory()
            
            # 测试注册的策略类型
            available_strategies = factory.get_available_strategies()
            
            expected_strategies = [
                'MovingAverageStrategy',
                'RSIStrategy', 
                'MACDStrategy',
                'RLStrategy'
            ]
            
            for strategy_type in expected_strategies:
                self.assertIn(strategy_type, available_strategies)
                
        except ImportError:
            self.fail("StrategyFactory类未实现")
    
    def test_strategy_creation_from_factory(self):
        """测试从工厂创建策略"""
        try:
            from finrl_crypto.strategy.factory import StrategyFactory
            
            factory = StrategyFactory()
            
            config = {
                'name': 'test_ma_strategy',
                'parameters': {'short_window': 5, 'long_window': 20}
            }
            
            strategy = factory.create_strategy('MovingAverageStrategy', config)
            
            self.assertIsNotNone(strategy)
            self.assertEqual(strategy.name, 'test_ma_strategy')
            
        except ImportError:
            self.fail("StrategyFactory类未实现")

class TestStrategyPerformanceMetrics(unittest.TestCase):
    """测试策略性能指标"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟的策略回报数据
        np.random.seed(42)
        self.returns = pd.Series(np.random.normal(0.001, 0.02, 252))  # 一年的日回报
        self.benchmark_returns = pd.Series(np.random.normal(0.0005, 0.015, 252))
        
    def test_strategy_performance_calculation(self):
        """测试策略性能计算"""
        try:
            from finrl_crypto.strategy.metrics import StrategyPerformanceMetrics
            
            metrics = StrategyPerformanceMetrics()
            
            # 测试基本性能指标
            total_return = metrics.calculate_total_return(self.returns)
            sharpe_ratio = metrics.calculate_sharpe_ratio(self.returns)
            max_drawdown = metrics.calculate_max_drawdown(self.returns)
            
            self.assertIsInstance(total_return, float)
            self.assertIsInstance(sharpe_ratio, float)
            self.assertIsInstance(max_drawdown, float)
            
            # 最大回撤应该是负数或零
            self.assertLessEqual(max_drawdown, 0)
            
        except ImportError:
            self.fail("StrategyPerformanceMetrics类未实现")
    
    def test_benchmark_comparison(self):
        """测试基准比较"""
        try:
            from finrl_crypto.strategy.metrics import StrategyPerformanceMetrics
            
            metrics = StrategyPerformanceMetrics()
            
            # 测试Alpha和Beta计算
            alpha = metrics.calculate_alpha(self.returns, self.benchmark_returns)
            beta = metrics.calculate_beta(self.returns, self.benchmark_returns)
            
            self.assertIsInstance(alpha, float)
            self.assertIsInstance(beta, float)
            
        except ImportError:
            self.fail("StrategyPerformanceMetrics类未实现")

if __name__ == '__main__':
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加所有测试类
    test_classes = [
        TestBaseStrategy,
        TestMovingAverageStrategy,
        TestRSIStrategy,
        TestRLStrategy,
        TestStrategyComposition,
        TestStrategyOptimization,
        TestStrategyFactory,
        TestStrategyPerformanceMetrics
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果统计
    print(f"\n测试结果统计:")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")