#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DRLAgent重构

这个测试文件验证重构后的DRLAgent类能够正确使用依赖注入和配置管理基础设施。
重构的目标是将DRLAgent从一个单一职责违反的大类拆分为多个职责明确的类。
"""

import unittest
import tempfile
import os
import shutil
from unittest.mock import Mock, patch, MagicMock
import numpy as np
import torch

from core.dependency_injection import DIContainer
from core.config_manager import UnifiedConfigManager, ConfigSchema


class TestDRLAgentRefactor(unittest.TestCase):
    """DRLAgent重构测试"""
    
    def setUp(self):
        """测试前准备"""
        from core.drl_agent import register_drl_agent_dependencies
        
        self.container = DIContainer()
        self.config_manager = UnifiedConfigManager()
        self.temp_dir = tempfile.mkdtemp()
        
        # 注册DRL Agent相关依赖
        self.container.register_instance(UnifiedConfigManager, self.config_manager)
        register_drl_agent_dependencies(self.container)
        
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_agent_factory_creation(self):
        """测试代理工厂创建"""
        from core.drl_agent import AgentFactory
        
        # 注册配置
        config = {
            "agent": {
                "model_name": "ddpg",
                "net_dimension": 512,
                "learning_rate": 0.001,
                "batch_size": 256,
                "gamma": 0.99
            }
        }
        self.config_manager.load_from_dict(config)
        
        # 创建工厂
        factory = self.container.get_service(AgentFactory)
        self.assertIsNotNone(factory)
        
        # 测试支持的模型
        supported_models = factory.get_supported_models()
        self.assertIn("ddpg", supported_models)
        self.assertIn("sac", supported_models)
        self.assertIn("ppo", supported_models)
    
    def test_model_builder_creation(self):
        """测试模型构建器创建"""
        from core.drl_agent import ModelBuilder
        
        # 模拟环境
        mock_env = Mock()
        mock_env.state_dim = 10
        mock_env.action_dim = 3
        mock_env.env_num = 1
        
        # 配置
        config = {
            "model": {
                "net_dimension": 512,
                "gpu_id": 0
            }
        }
        self.config_manager.load_from_dict(config)
        
        # 创建构建器
        builder = self.container.get_service(ModelBuilder)
        self.assertIsNotNone(builder)
        
        # 测试构建模型参数
        model_args = builder.build_model_args("ddpg", mock_env)
        self.assertIsNotNone(model_args)
        self.assertEqual(model_args.net_dim, 512)
        self.assertEqual(model_args.gpu_id, 0)
    
    def test_training_service_creation(self):
        """测试训练服务创建"""
        from core.drl_agent import TrainingService
        
        # 配置
        config = {
            "training": {
                "total_timesteps": 10000,
                "eval_gap": 1000,
                "save_gap": 2000
            }
        }
        self.config_manager.load_from_dict(config)
        
        # 创建训练服务
        training_service = self.container.get_service(TrainingService)
        self.assertIsNotNone(training_service)
        
        # 模拟模型和环境
        mock_model = Mock()
        mock_model.cwd = self.temp_dir
        
        # 测试训练配置
        training_config = training_service.get_training_config()
        self.assertEqual(training_config["total_timesteps"], 10000)
        self.assertEqual(training_config["eval_gap"], 1000)
    
    def test_prediction_service_creation(self):
        """测试预测服务创建"""
        from core.drl_agent import PredictionService
        
        # 配置
        config = {
            "prediction": {
                "model_path": self.temp_dir,
                "net_dimension": 512,
                "gpu_id": 0
            }
        }
        self.config_manager.load_from_dict(config)
        
        # 创建预测服务
        prediction_service = self.container.get_service(PredictionService)
        self.assertIsNotNone(prediction_service)
        
        # 模拟环境
        mock_env = Mock()
        mock_env.reset.return_value = np.array([1.0, 2.0, 3.0])
        mock_env.step.return_value = (np.array([1.1, 2.1, 3.1]), 0.1, False, {})
        mock_env.max_step = 10
        mock_env.initial_total_asset = 10000
        mock_env.cash = 5000
        mock_env.stocks = np.array([10, 20])
        mock_env.price_array = np.array([[100, 200], [101, 201]])
        mock_env.time = 0
        
        # 测试预测配置
        prediction_config = prediction_service.get_prediction_config()
        self.assertEqual(prediction_config["model_path"], self.temp_dir)
        self.assertEqual(prediction_config["net_dimension"], 512)
    
    def test_refactored_drl_agent_integration(self):
        """测试重构后的DRLAgent集成"""
        from core.drl_agent import RefactoredDRLAgent
        
        # 完整配置
        config = {
            "agent": {
                "model_name": "ddpg",
                "net_dimension": 256,
                "learning_rate": 0.001,
                "batch_size": 128,
                "gamma": 0.99
            },
            "training": {
                "total_timesteps": 5000,
                "eval_gap": 500
            },
            "prediction": {
                "model_path": self.temp_dir,
                "net_dimension": 256,
                "gpu_id": 0
            },
            "environment": {
                "state_dim": 10,
                "action_dim": 3
            }
        }
        self.config_manager.load_from_dict(config)
        
        # 创建重构后的DRLAgent
        agent = self.container.get_service(RefactoredDRLAgent)
        self.assertIsNotNone(agent)
        
        # 验证组件注入
        self.assertIsNotNone(agent.agent_factory)
        self.assertIsNotNone(agent.model_builder)
        self.assertIsNotNone(agent.training_service)
        self.assertIsNotNone(agent.prediction_service)
    
    def test_agent_lifecycle_management(self):
        """测试代理生命周期管理"""
        from core.drl_agent import RefactoredDRLAgent
        
        # 配置
        config = {
            "agent": {"model_name": "ddpg", "net_dimension": 256},
            "training": {"total_timesteps": 1000},
            "environment": {"state_dim": 10, "action_dim": 3}
        }
        self.config_manager.load_from_dict(config)
        
        # 创建代理
        agent = self.container.get_service(RefactoredDRLAgent)
        
        # 模拟环境
        mock_env = Mock()
        mock_env.state_dim = 10
        mock_env.action_dim = 3
        mock_env.env_num = 1
        
        # 测试模型创建
        with patch('core.drl_agent.Arguments') as mock_args:
            mock_model = Mock()
            mock_args.return_value = mock_model
            
            model = agent.create_model(mock_env)
            self.assertIsNotNone(model)
            mock_args.assert_called_once()
    
    def test_configuration_validation(self):
        """测试配置验证"""
        from core.drl_agent import DRLAgentConfigValidator
        
        # 创建验证器
        validator = DRLAgentConfigValidator()
        
        # 有效配置
        valid_config = {
            "agent": {
                "model_name": "ddpg",
                "net_dimension": 512,
                "learning_rate": 0.001
            },
            "training": {
                "total_timesteps": 10000
            }
        }
        
        # 应该通过验证
        self.assertTrue(validator.validate(valid_config))
        
        # 无效配置 - 缺少必需字段
        invalid_config = {
            "agent": {
                "net_dimension": 512
                # 缺少 model_name
            }
        }
        
        # 应该验证失败
        self.assertFalse(validator.validate(invalid_config))
    
    def test_dependency_injection_integration(self):
        """测试依赖注入集成"""
        from core.drl_agent import (
            AgentFactory, ModelBuilder, TrainingService, 
            PredictionService, RefactoredDRLAgent
        )
        
        # 配置容器
        config = {
            "agent": {"model_name": "ddpg"},
            "training": {"total_timesteps": 1000},
            "prediction": {"model_path": self.temp_dir}
        }
        self.config_manager.load_from_dict(config)
        
        # 验证所有服务都可以通过容器获取
        agent_factory = self.container.get_service(AgentFactory)
        model_builder = self.container.get_service(ModelBuilder)
        training_service = self.container.get_service(TrainingService)
        prediction_service = self.container.get_service(PredictionService)
        refactored_agent = self.container.get_service(RefactoredDRLAgent)
        
        # 验证依赖注入正确
        self.assertIs(refactored_agent.agent_factory, agent_factory)
        self.assertIs(refactored_agent.model_builder, model_builder)
        self.assertIs(refactored_agent.training_service, training_service)
        self.assertIs(refactored_agent.prediction_service, prediction_service)
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        from core.drl_agent import RefactoredDRLAgent
        
        # 配置
        config = {
            "agent": {"model_name": "ddpg", "net_dimension": 256},
            "training": {"total_timesteps": 1000},
            "environment": {"state_dim": 10, "action_dim": 3}
        }
        self.config_manager.load_from_dict(config)
        
        # 创建重构后的代理
        agent = self.container.get_service(RefactoredDRLAgent)
        
        # 模拟旧式调用
        mock_env = Mock()
        mock_env.state_dim = 10
        mock_env.action_dim = 3
        
        # 测试向后兼容的方法
        with patch('core.drl_agent.Arguments') as mock_args:
            mock_model = Mock()
            mock_args.return_value = mock_model
            
            # 应该支持旧的接口
            model = agent.get_model("ddpg", 0, {"net_dimension": 256})
            self.assertIsNotNone(model)
    
    def test_error_handling(self):
        """测试错误处理"""
        from core.drl_agent import RefactoredDRLAgent, AgentFactory
        
        # 配置
        config = {
            "agent": {"model_name": "invalid_model"},
            "training": {"total_timesteps": 1000}
        }
        self.config_manager.load_from_dict(config)
        
        # 测试无效模型名称
        factory = self.container.get_service(AgentFactory)
        
        with self.assertRaises(ValueError):
            factory.create_agent("invalid_model")
    
    def test_performance_monitoring(self):
        """测试性能监控"""
        from core.drl_agent import RefactoredDRLAgent
        
        # 配置
        config = {
            "agent": {"model_name": "ddpg"},
            "training": {"total_timesteps": 100},
            "monitoring": {"enable_metrics": True}
        }
        self.config_manager.load_from_dict(config)
        
        # 创建代理
        agent = self.container.get_service(RefactoredDRLAgent)
        
        # 验证监控功能
        self.assertTrue(hasattr(agent, 'get_metrics'))
        metrics = agent.get_metrics()
        self.assertIsInstance(metrics, dict)


class TestDRLAgentComponents(unittest.TestCase):
    """DRLAgent组件单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.config_manager = UnifiedConfigManager()
        
    def test_agent_factory_standalone(self):
        """测试代理工厂独立功能"""
        from core.drl_agent import AgentFactory
        
        config = {"agent": {"model_name": "ddpg"}}
        self.config_manager.load_from_dict(config)
        
        factory = AgentFactory(self.config_manager)
        
        # 测试支持的模型
        models = factory.get_supported_models()
        self.assertIsInstance(models, list)
        self.assertGreater(len(models), 0)
        
        # 测试模型验证
        self.assertTrue(factory.is_model_supported("ddpg"))
        self.assertFalse(factory.is_model_supported("invalid_model"))
    
    def test_model_builder_standalone(self):
        """测试模型构建器独立功能"""
        from core.drl_agent import ModelBuilder
        
        config = {
            "model": {
                "net_dimension": 512,
                "learning_rate": 0.001,
                "batch_size": 256
            }
        }
        self.config_manager.load_from_dict(config)
        
        builder = ModelBuilder(self.config_manager)
        
        # 模拟环境
        mock_env = Mock()
        mock_env.state_dim = 10
        mock_env.action_dim = 3
        
        # 测试参数构建
        with patch('core.drl_agent.Arguments') as mock_args:
            mock_model = Mock()
            mock_args.return_value = mock_model
            
            args = builder.build_model_args("ddpg", mock_env)
            self.assertIsNotNone(args)
            mock_args.assert_called_once()
    
    def test_training_service_standalone(self):
        """测试训练服务独立功能"""
        from core.drl_agent import TrainingService
        
        config = {
            "training": {
                "total_timesteps": 10000,
                "eval_gap": 1000,
                "save_gap": 2000
            }
        }
        self.config_manager.load_from_dict(config)
        
        service = TrainingService(self.config_manager)
        
        # 测试配置获取
        training_config = service.get_training_config()
        self.assertEqual(training_config["total_timesteps"], 10000)
        self.assertEqual(training_config["eval_gap"], 1000)
        
        # 测试训练参数设置
        mock_model = Mock()
        service.configure_training(mock_model, "/tmp/test")
        
        self.assertEqual(mock_model.cwd, "/tmp/test")
        self.assertEqual(mock_model.break_step, 10000)
    
    def test_prediction_service_standalone(self):
        """测试预测服务独立功能"""
        from core.drl_agent import PredictionService
        
        config = {
            "prediction": {
                "model_path": "/tmp/model",
                "net_dimension": 512,
                "gpu_id": 0
            }
        }
        self.config_manager.load_from_dict(config)
        
        service = PredictionService(self.config_manager)
        
        # 测试配置获取
        prediction_config = service.get_prediction_config()
        self.assertEqual(prediction_config["model_path"], "/tmp/model")
        self.assertEqual(prediction_config["net_dimension"], 512)
        self.assertEqual(prediction_config["gpu_id"], 0)


if __name__ == '__main__':
    unittest.main()