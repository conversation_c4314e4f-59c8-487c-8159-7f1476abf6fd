#!/bin/bash
# Docker入口点脚本
# 用于初始化和启动FinRL Crypto应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "$DEBUG" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# 错误处理
error_exit() {
    log_error "$1"
    exit 1
}

# 信号处理
trap 'log_info "Received SIGTERM, shutting down gracefully..."; exit 0' SIGTERM
trap 'log_info "Received SIGINT, shutting down gracefully..."; exit 0' SIGINT

# 环境变量默认值
export PYTHONPATH="${PYTHONPATH:-/app}"
export LOG_LEVEL="${LOG_LEVEL:-INFO}"
export WORKERS="${WORKERS:-1}"
export PORT="${PORT:-8000}"
export HOST="${HOST:-0.0.0.0}"
export TIMEOUT="${TIMEOUT:-30}"
export KEEPALIVE="${KEEPALIVE:-2}"
export MAX_REQUESTS="${MAX_REQUESTS:-1000}"
export MAX_REQUESTS_JITTER="${MAX_REQUESTS_JITTER:-100}"
export PRELOAD="${PRELOAD:-true}"
export DEBUG="${DEBUG:-false}"
export ENVIRONMENT="${ENVIRONMENT:-production}"

# 显示启动信息
log_info "Starting FinRL Crypto Application"
log_info "Environment: $ENVIRONMENT"
log_info "Python Path: $PYTHONPATH"
log_info "Log Level: $LOG_LEVEL"
log_info "Workers: $WORKERS"
log_info "Host: $HOST"
log_info "Port: $PORT"

# 检查必要的目录
check_directories() {
    log_info "Checking required directories..."
    
    local dirs=("logs" "data" "models" "checkpoints" "results")
    
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_info "Creating directory: $dir"
            mkdir -p "$dir" || error_exit "Failed to create directory: $dir"
        fi
    done
}

# 检查Python环境
check_python_env() {
    log_info "Checking Python environment..."
    
    # 检查Python版本
    python_version=$(python --version 2>&1)
    log_info "Python version: $python_version"
    
    # 检查关键包
    local packages=("torch" "numpy" "pandas" "gym")
    
    for package in "${packages[@]}"; do
        if python -c "import $package" 2>/dev/null; then
            version=$(python -c "import $package; print($package.__version__)" 2>/dev/null || echo "unknown")
            log_info "$package: $version"
        else
            log_warn "Package $package not found"
        fi
    done
}

# 检查配置文件
check_config() {
    log_info "Checking configuration files..."
    
    if [[ -f "config_main.py" ]]; then
        log_info "Found main configuration file"
    else
        log_warn "Main configuration file not found"
    fi
    
    # 检查环境特定配置
    if [[ -f "config_${ENVIRONMENT}.py" ]]; then
        log_info "Found environment-specific configuration: config_${ENVIRONMENT}.py"
        export CONFIG_FILE="config_${ENVIRONMENT}.py"
    fi
}

# 数据库迁移（如果需要）
run_migrations() {
    if [[ "$RUN_MIGRATIONS" == "true" ]]; then
        log_info "Running database migrations..."
        # 这里添加数据库迁移逻辑
        # python manage.py migrate
    fi
}

# 收集静态文件（如果需要）
collect_static() {
    if [[ "$COLLECT_STATIC" == "true" ]]; then
        log_info "Collecting static files..."
        # 这里添加静态文件收集逻辑
        # python manage.py collectstatic --noinput
    fi
}

# 健康检查
health_check() {
    log_info "Performing health check..."
    
    # 检查磁盘空间
    disk_usage=$(df -h . | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $disk_usage -gt 90 ]]; then
        log_warn "Disk usage is high: ${disk_usage}%"
    fi
    
    # 检查内存使用
    if command -v free >/dev/null 2>&1; then
        memory_usage=$(free | awk 'NR==2{printf "%.2f", $3*100/$2 }')
        log_info "Memory usage: ${memory_usage}%"
    fi
}

# 启动应用的不同模式
start_web_server() {
    log_info "Starting web server with Gunicorn..."
    
    exec gunicorn \
        --bind "$HOST:$PORT" \
        --workers "$WORKERS" \
        --timeout "$TIMEOUT" \
        --keepalive "$KEEPALIVE" \
        --max-requests "$MAX_REQUESTS" \
        --max-requests-jitter "$MAX_REQUESTS_JITTER" \
        --preload="$PRELOAD" \
        --log-level "$LOG_LEVEL" \
        --access-logfile - \
        --error-logfile - \
        --capture-output \
        app:app
}

start_worker() {
    log_info "Starting background worker..."
    exec python worker.py
}

start_scheduler() {
    log_info "Starting task scheduler..."
    exec python scheduler.py
}

start_training() {
    log_info "Starting training process..."
    exec python train.py
}

start_backtesting() {
    log_info "Starting backtesting process..."
    exec python backtest.py
}

start_jupyter() {
    log_info "Starting Jupyter notebook server..."
    exec jupyter notebook \
        --ip=0.0.0.0 \
        --port=8888 \
        --no-browser \
        --allow-root \
        --NotebookApp.token='' \
        --NotebookApp.password=''
}

# 开发模式
start_dev() {
    log_info "Starting in development mode..."
    export DEBUG=true
    export LOG_LEVEL=DEBUG
    
    # 安装开发依赖（如果需要）
    if [[ "$INSTALL_DEV_DEPS" == "true" ]]; then
        log_info "Installing development dependencies..."
        pip install -e .
    fi
    
    exec python main.py
}

# 测试模式
run_tests() {
    log_info "Running tests..."
    
    # 设置测试环境
    export ENVIRONMENT=test
    export DEBUG=true
    
    # 运行测试
    if [[ -n "$TEST_PATTERN" ]]; then
        exec pytest -v "$TEST_PATTERN"
    else
        exec pytest -v
    fi
}

# 主函数
main() {
    # 执行初始化检查
    check_directories
    check_python_env
    check_config
    health_check
    
    # 运行迁移和静态文件收集
    run_migrations
    collect_static
    
    # 根据命令启动不同服务
    case "$1" in
        "web"|"server"|"gunicorn")
            start_web_server
            ;;
        "worker")
            start_worker
            ;;
        "scheduler")
            start_scheduler
            ;;
        "train"|"training")
            start_training
            ;;
        "backtest"|"backtesting")
            start_backtesting
            ;;
        "jupyter"|"notebook")
            start_jupyter
            ;;
        "dev"|"development")
            start_dev
            ;;
        "test"|"tests")
            run_tests
            ;;
        "bash"|"shell")
            log_info "Starting interactive shell..."
            exec bash
            ;;
        "python")
            log_info "Starting Python interpreter..."
            shift
            exec python "$@"
            ;;
        "help"|"--help"|"")
            echo "Usage: $0 [COMMAND]"
            echo ""
            echo "Commands:"
            echo "  web, server, gunicorn    Start web server"
            echo "  worker                   Start background worker"
            echo "  scheduler                Start task scheduler"
            echo "  train, training          Start training process"
            echo "  backtest, backtesting    Start backtesting process"
            echo "  jupyter, notebook        Start Jupyter notebook"
            echo "  dev, development         Start in development mode"
            echo "  test, tests              Run tests"
            echo "  bash, shell              Start interactive shell"
            echo "  python [args]            Run Python with arguments"
            echo "  help, --help             Show this help message"
            echo ""
            echo "Environment Variables:"
            echo "  ENVIRONMENT              Environment (production, development, test)"
            echo "  LOG_LEVEL                Log level (DEBUG, INFO, WARNING, ERROR)"
            echo "  WORKERS                  Number of worker processes"
            echo "  HOST                     Host to bind to"
            echo "  PORT                     Port to bind to"
            echo "  DEBUG                    Enable debug mode (true, false)"
            echo "  RUN_MIGRATIONS           Run database migrations (true, false)"
            echo "  COLLECT_STATIC           Collect static files (true, false)"
            exit 0
            ;;
        *)
            log_info "Starting with custom command: $*"
            exec "$@"
            ;;
    esac
}

# 执行主函数
main "$@"