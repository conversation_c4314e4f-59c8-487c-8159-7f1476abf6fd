{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "e28fb1af94001966f90610dc3ae5ff7f", "files": {"z_57760688d1f824db___init___py": {"hash": "b77dc86282be17ece529ef6c9253569f", "index": {"url": "z_57760688d1f824db___init___py.html", "file": "core\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_57760688d1f824db_config_manager_py": {"hash": "6f50d3194ecd263c6e5a22e7636a4b54", "index": {"url": "z_57760688d1f824db_config_manager_py.html", "file": "core\\config_manager.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 176, "n_excluded": 15, "n_missing": 176, "n_branches": 48, "n_partial_branches": 0, "n_missing_branches": 48}}}, "z_57760688d1f824db_dependency_injection_py": {"hash": "e4097e265a62299b2891b95a9745cdc9", "index": {"url": "z_57760688d1f824db_dependency_injection_py.html", "file": "core\\dependency_injection.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 309, "n_excluded": 4, "n_missing": 309, "n_branches": 94, "n_partial_branches": 0, "n_missing_branches": 94}}}, "z_57760688d1f824db_drl_agent_py": {"hash": "1843daef043a1389c1e4f1466b714a44", "index": {"url": "z_57760688d1f824db_drl_agent_py.html", "file": "core\\drl_agent.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 245, "n_excluded": 0, "n_missing": 245, "n_branches": 36, "n_partial_branches": 0, "n_missing_branches": 36}}}, "z_ddb02fcb4060c71d___init___py": {"hash": "0b1fb8ffad65913c743b0f40b621f8f9", "index": {"url": "z_ddb02fcb4060c71d___init___py.html", "file": "finrl_crypto\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 17, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4ef26b22018bd3be___init___py": {"hash": "049ad6b8828b0ff1c388dace9fb9afd5", "index": {"url": "z_4ef26b22018bd3be___init___py.html", "file": "finrl_crypto\\agent\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4ef26b22018bd3be_a2c_py": {"hash": "0d953f06cdb8dcec9e4b4f1cf51e0b69", "index": {"url": "z_4ef26b22018bd3be_a2c_py.html", "file": "finrl_crypto\\agent\\a2c.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 193, "n_excluded": 0, "n_missing": 164, "n_branches": 26, "n_partial_branches": 0, "n_missing_branches": 26}}}, "z_4ef26b22018bd3be_base_py": {"hash": "290a825af33cdf5c570156288bb5ca6b", "index": {"url": "z_4ef26b22018bd3be_base_py.html", "file": "finrl_crypto\\agent\\base.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 118, "n_excluded": 65, "n_missing": 92, "n_branches": 26, "n_partial_branches": 0, "n_missing_branches": 26}}}, "z_4ef26b22018bd3be_dqn_py": {"hash": "5c68839ec328a688ac3142591a356677", "index": {"url": "z_4ef26b22018bd3be_dqn_py.html", "file": "finrl_crypto\\agent\\dqn.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 113, "n_excluded": 0, "n_missing": 90, "n_branches": 14, "n_partial_branches": 0, "n_missing_branches": 14}}}, "z_4ef26b22018bd3be_factory_py": {"hash": "e8c9cf5ec5de0c97be82b1666f91ade4", "index": {"url": "z_4ef26b22018bd3be_factory_py.html", "file": "finrl_crypto\\agent\\factory.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 58, "n_branches": 38, "n_partial_branches": 0, "n_missing_branches": 38}}}, "z_4ef26b22018bd3be_ppo_py": {"hash": "ce0072e5695150dc2c5c88e88e644ba9", "index": {"url": "z_4ef26b22018bd3be_ppo_py.html", "file": "finrl_crypto\\agent\\ppo.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 229, "n_excluded": 0, "n_missing": 195, "n_branches": 36, "n_partial_branches": 0, "n_missing_branches": 36}}}, "z_4ef26b22018bd3be_sac_py": {"hash": "2ad4cc319f6ec79854e792bf7ada42fd", "index": {"url": "z_4ef26b22018bd3be_sac_py.html", "file": "finrl_crypto\\agent\\sac.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 200, "n_excluded": 0, "n_missing": 170, "n_branches": 26, "n_partial_branches": 0, "n_missing_branches": 26}}}, "z_58676b528f8eed05___init___py": {"hash": "95886fcd0ca64a920366f9604779a4a2", "index": {"url": "z_58676b528f8eed05___init___py.html", "file": "finrl_crypto\\environment\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_58676b528f8eed05_base_py": {"hash": "aff4202d525a6a5f1b90c48c5cada61d", "index": {"url": "z_58676b528f8eed05_base_py.html", "file": "finrl_crypto\\environment\\base.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 155, "n_excluded": 43, "n_missing": 116, "n_branches": 42, "n_partial_branches": 0, "n_missing_branches": 42}}}, "z_58676b528f8eed05_crypto_trading_py": {"hash": "0f6d01ad5e6fbd5626ed64b5db2d3932", "index": {"url": "z_58676b528f8eed05_crypto_trading_py.html", "file": "finrl_crypto\\environment\\crypto_trading.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 219, "n_excluded": 0, "n_missing": 177, "n_branches": 64, "n_partial_branches": 0, "n_missing_branches": 64}}}, "z_58676b528f8eed05_factory_py": {"hash": "18f591024c88c925866c33cab0650c5a", "index": {"url": "z_58676b528f8eed05_factory_py.html", "file": "finrl_crypto\\environment\\factory.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 111, "n_excluded": 0, "n_missing": 82, "n_branches": 48, "n_partial_branches": 0, "n_missing_branches": 48}}}, "z_58676b528f8eed05_portfolio_py": {"hash": "067ebb83833e47e6a320fd82e49ed4fe", "index": {"url": "z_58676b528f8eed05_portfolio_py.html", "file": "finrl_crypto\\environment\\portfolio.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 240, "n_excluded": 0, "n_missing": 201, "n_branches": 76, "n_partial_branches": 0, "n_missing_branches": 76}}}, "z_65d867bacf62c25a___init___py": {"hash": "24595590496c1f3075cf05d1f4b3744d", "index": {"url": "z_65d867bacf62c25a___init___py.html", "file": "finrl_crypto\\training\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_65d867bacf62c25a_base_py": {"hash": "04332f5bd685c5b6b7f751021e51f272", "index": {"url": "z_65d867bacf62c25a_base_py.html", "file": "finrl_crypto\\training\\base.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 173, "n_excluded": 28, "n_missing": 104, "n_branches": 38, "n_partial_branches": 0, "n_missing_branches": 38}}}, "z_65d867bacf62c25a_factory_py": {"hash": "340d1724a1c04a16699a41a7811e5f93", "index": {"url": "z_65d867bacf62c25a_factory_py.html", "file": "finrl_crypto\\training\\factory.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 98, "n_excluded": 0, "n_missing": 67, "n_branches": 32, "n_partial_branches": 0, "n_missing_branches": 32}}}, "z_65d867bacf62c25a_multi_asset_py": {"hash": "1121367788f0f2f909247092045d1e6e", "index": {"url": "z_65d867bacf62c25a_multi_asset_py.html", "file": "finrl_crypto\\training\\multi_asset.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 252, "n_excluded": 0, "n_missing": 230, "n_branches": 90, "n_partial_branches": 0, "n_missing_branches": 90}}}, "z_65d867bacf62c25a_portfolio_py": {"hash": "873ca61184ff6cf562b82c733c2befb5", "index": {"url": "z_65d867bacf62c25a_portfolio_py.html", "file": "finrl_crypto\\training\\portfolio.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 294, "n_excluded": 0, "n_missing": 268, "n_branches": 98, "n_partial_branches": 0, "n_missing_branches": 98}}}, "z_65d867bacf62c25a_single_asset_py": {"hash": "445806d3a63cf9f146d24edbd82dc353", "index": {"url": "z_65d867bacf62c25a_single_asset_py.html", "file": "finrl_crypto\\training\\single_asset.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 160, "n_excluded": 0, "n_missing": 141, "n_branches": 48, "n_partial_branches": 0, "n_missing_branches": 48}}}, "z_65d867bacf62c25a_utils_py": {"hash": "1308257de640a6e6ba3baeb1265a0e64", "index": {"url": "z_65d867bacf62c25a_utils_py.html", "file": "finrl_crypto\\training\\utils.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 395, "n_excluded": 0, "n_missing": 377, "n_branches": 160, "n_partial_branches": 0, "n_missing_branches": 160}}}}}