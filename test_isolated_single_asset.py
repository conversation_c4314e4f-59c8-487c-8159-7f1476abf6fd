#!/usr/bin/env python3
"""
隔离的SingleAssetTrainer pytest测试
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock

# 导入需要测试的类
from finrl_crypto.training.single_asset import SingleAssetTrainer
from finrl_crypto.training.base import TrainingConfig

@pytest.fixture
def mock_agent():
    """模拟智能体"""
    agent = Mock()
    agent.predict.return_value = [0.5, 0.3, 0.2]
    agent.train.return_value = {'loss': 0.1}
    return agent

@pytest.fixture
def mock_environment():
    """模拟环境"""
    env = Mock()
    env.reset.return_value = np.random.random(10)
    env.step.return_value = (np.random.random(10), 1.0, False, {})
    return env

@pytest.fixture
def mock_single_asset_data():
    """模拟单资产数据"""
    dates = pd.date_range('2023-01-01', periods=100, freq='h')
    return pd.DataFrame({
        'timestamp': dates,
        'open': np.random.uniform(100, 200, 100),
        'high': np.random.uniform(200, 300, 100),
        'low': np.random.uniform(50, 100, 100),
        'close': np.random.uniform(100, 200, 100),
        'volume': np.random.uniform(1000, 10000, 100)
    })

@pytest.fixture
def single_asset_trainer(mock_agent, mock_environment, mock_single_asset_data):
    """单资产训练器实例"""
    config = TrainingConfig()
    trainer = SingleAssetTrainer(
        agent=mock_agent,
        env=mock_environment,
        config=config
    )
    trainer.data = mock_single_asset_data
    trainer.symbol = "BTCUSDT"
    return trainer

def test_single_asset_initialization(single_asset_trainer):
    """测试单资产训练器初始化"""
    assert single_asset_trainer.symbol == "BTCUSDT"
    assert hasattr(single_asset_trainer, 'data')
    assert len(single_asset_trainer.data) == 100
    assert single_asset_trainer.agent is not None
    assert single_asset_trainer.env is not None
    assert single_asset_trainer.config is not None

def test_single_asset_attributes(single_asset_trainer):
    """测试单资产训练器属性"""
    # 检查继承自BaseTrainer的属性
    assert hasattr(single_asset_trainer, 'training_history')
    assert hasattr(single_asset_trainer, 'current_episode')
    
    # 检查SingleAssetTrainer特有的训练历史键
    assert 'portfolio_values' in single_asset_trainer.training_history
    assert 'positions' in single_asset_trainer.training_history
    assert 'trades' in single_asset_trainer.training_history
    assert 'returns' in single_asset_trainer.training_history
    assert 'sharpe_ratios' in single_asset_trainer.training_history
    assert 'max_drawdowns' in single_asset_trainer.training_history

if __name__ == "__main__":
    pytest.main([__file__, "-v"])