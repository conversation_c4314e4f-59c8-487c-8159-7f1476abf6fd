"""FinRL Crypto 环境模块

本模块提供用于加密货币交易的强化学习环境。

主要组件:
- BaseEnvironment: 环境基类
- CryptoTradingEnvironment: 加密货币交易环境
- PortfolioEnvironment: 投资组合环境
- EnvironmentFactory: 环境工厂

基本用法:
```python
from finrl_crypto.environment import create_trading_environment

# 创建交易环境
env = create_trading_environment(
    data=your_data,
    initial_amount=10000,
    transaction_cost_pct=0.001
)

# 使用环境
obs = env.reset()
for _ in range(100):
    action = env.action_space.sample()
    obs, reward, done, info = env.step(action)
    if done:
        break
```
"""

from .base import BaseEnvironment
from .crypto_trading import CryptoTradingEnvironment
from .portfolio import PortfolioEnvironment
from .factory import (
    EnvironmentFactory,
    create_trading_environment,
    create_portfolio_environment,
    get_supported_environments
)

__version__ = "0.1.0"

# 支持的环境类型
SUPPORTED_ENVIRONMENTS = [
    'crypto_trading',
    'portfolio'
]

# 快速创建函数
def create_environment(env_type: str, data, **kwargs):
    """快速创建环境
    
    Args:
        env_type: 环境类型 ('crypto_trading' 或 'portfolio')
        data: 交易数据
        **kwargs: 环境参数
        
    Returns:
        环境实例
    """
    return EnvironmentFactory.create_environment(env_type, data, **kwargs)


__all__ = [
    'BaseEnvironment',
    'CryptoTradingEnvironment', 
    'PortfolioEnvironment',
    'EnvironmentFactory',
    'create_environment',
    'create_trading_environment',
    'create_portfolio_environment',
    'get_supported_environments',
    'SUPPORTED_ENVIRONMENTS'
]