"""技术指标计算模块

提供各种技术指标的计算功能。
"""

import pandas as pd
import numpy as np
from typing import List, Optional, Dict, Any, Union
import logging

try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    logging.warning("TA-Lib未安装，将使用基础实现")

try:
    import stockstats
    STOCKSTATS_AVAILABLE = True
except ImportError:
    STOCKSTATS_AVAILABLE = False
    logging.warning("stockstats未安装，将使用基础实现")


class TechnicalIndicators:
    """技术指标计算器
    
    提供各种技术指标的计算功能，支持多种计算库。
    """
    
    def __init__(self, use_talib: bool = True, use_stockstats: bool = True):
        """初始化技术指标计算器
        
        Args:
            use_talib: 是否使用TA-Lib
            use_stockstats: 是否使用stockstats
        """
        self.use_talib = use_talib and TALIB_AVAILABLE
        self.use_stockstats = use_stockstats and STOCKSTATS_AVAILABLE
        self.logger = logging.getLogger(self.__class__.__name__)
        
        if not self.use_talib and not self.use_stockstats:
            self.logger.warning("未安装技术指标库，将使用基础实现")
    
    def add_indicators(self, 
                      df: pd.DataFrame, 
                      indicator_list: List[str],
                      **kwargs) -> pd.DataFrame:
        """添加技术指标
        
        Args:
            df: 数据DataFrame
            indicator_list: 指标列表
            **kwargs: 指标参数
            
        Returns:
            添加指标后的DataFrame
        """
        if df.empty:
            self.logger.error("数据为空，无法计算技术指标")
            return df
        
        result_df = pd.DataFrame()
        
        # 按股票分组计算
        if 'tic' in df.columns:
            for ticker in df['tic'].unique():
                ticker_df = df[df['tic'] == ticker].copy()
                ticker_df = self._calculate_indicators_for_ticker(
                    ticker_df, indicator_list, **kwargs
                )
                result_df = pd.concat([result_df, ticker_df], ignore_index=True)
        else:
            result_df = self._calculate_indicators_for_ticker(
                df, indicator_list, **kwargs
            )
        
        return result_df
    
    def _calculate_indicators_for_ticker(self, 
                                       df: pd.DataFrame, 
                                       indicator_list: List[str],
                                       **kwargs) -> pd.DataFrame:
        """为单个股票计算技术指标"""
        df = df.copy()
        
        # 确保数据按时间排序
        if 'time' in df.columns:
            df = df.sort_values('time')
        
        for indicator in indicator_list:
            try:
                df = self._add_single_indicator(df, indicator, **kwargs)
            except Exception as e:
                self.logger.error(f"计算指标{indicator}失败: {e}")
                continue
        
        return df
    
    def _add_single_indicator(self, 
                            df: pd.DataFrame, 
                            indicator: str,
                            **kwargs) -> pd.DataFrame:
        """添加单个技术指标"""
        indicator = indicator.lower()
        
        # 移动平均线
        if indicator.startswith('ma') or indicator.startswith('sma'):
            period = self._extract_period(indicator, default=20)
            df[indicator] = self._calculate_sma(df['close'], period)
        
        elif indicator.startswith('ema'):
            period = self._extract_period(indicator, default=20)
            df[indicator] = self._calculate_ema(df['close'], period)
        
        # RSI
        elif indicator == 'rsi' or indicator.startswith('rsi_'):
            period = self._extract_period(indicator, default=14)
            df[indicator] = self._calculate_rsi(df['close'], period)
        
        # MACD
        elif indicator == 'macd':
            macd_data = self._calculate_macd(df['close'])
            df['macd'] = macd_data['macd']
            df['macd_signal'] = macd_data['signal']
            df['macd_hist'] = macd_data['histogram']
        
        # 布林带
        elif indicator == 'bb' or indicator == 'bollinger':
            period = kwargs.get('bb_period', 20)
            std_dev = kwargs.get('bb_std', 2)
            bb_data = self._calculate_bollinger_bands(df['close'], period, std_dev)
            df['bb_upper'] = bb_data['upper']
            df['bb_middle'] = bb_data['middle']
            df['bb_lower'] = bb_data['lower']
        
        # KDJ
        elif indicator == 'kdj':
            kdj_data = self._calculate_kdj(df['high'], df['low'], df['close'])
            df['k'] = kdj_data['k']
            df['d'] = kdj_data['d']
            df['j'] = kdj_data['j']
        
        # CCI
        elif indicator == 'cci':
            period = kwargs.get('cci_period', 14)
            df[indicator] = self._calculate_cci(df['high'], df['low'], df['close'], period)
        
        # ATR
        elif indicator == 'atr':
            period = kwargs.get('atr_period', 14)
            df[indicator] = self._calculate_atr(df['high'], df['low'], df['close'], period)
        
        # 成交量指标
        elif indicator == 'obv':
            df[indicator] = self._calculate_obv(df['close'], df['volume'])
        
        # 威廉指标
        elif indicator == 'willr' or indicator == 'wr':
            period = kwargs.get('willr_period', 14)
            df[indicator] = self._calculate_willr(df['high'], df['low'], df['close'], period)
        
        # ROC
        elif indicator == 'roc':
            period = kwargs.get('roc_period', 10)
            df[indicator] = self._calculate_roc(df['close'], period)
        
        # 使用stockstats计算其他指标
        elif self.use_stockstats:
            df = self._calculate_with_stockstats(df, indicator)
        
        else:
            self.logger.warning(f"不支持的指标: {indicator}")
        
        return df
    
    def _extract_period(self, indicator: str, default: int = 20) -> int:
        """从指标名称中提取周期参数"""
        import re
        match = re.search(r'(\d+)', indicator)
        return int(match.group(1)) if match else default
    
    def _calculate_sma(self, series: pd.Series, period: int) -> pd.Series:
        """计算简单移动平均"""
        if self.use_talib:
            return pd.Series(talib.SMA(series.values, timeperiod=period), index=series.index)
        else:
            return series.rolling(window=period).mean()
    
    def _calculate_ema(self, series: pd.Series, period: int) -> pd.Series:
        """计算指数移动平均"""
        if self.use_talib:
            return pd.Series(talib.EMA(series.values, timeperiod=period), index=series.index)
        else:
            return series.ewm(span=period).mean()
    
    def _calculate_rsi(self, series: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI"""
        if self.use_talib:
            return pd.Series(talib.RSI(series.values, timeperiod=period), index=series.index)
        else:
            delta = series.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))
    
    def _calculate_macd(self, series: pd.Series, 
                       fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """计算MACD"""
        if self.use_talib:
            macd, signal_line, histogram = talib.MACD(
                series.values, fastperiod=fast, slowperiod=slow, signalperiod=signal
            )
            return {
                'macd': pd.Series(macd, index=series.index),
                'signal': pd.Series(signal_line, index=series.index),
                'histogram': pd.Series(histogram, index=series.index)
            }
        else:
            ema_fast = self._calculate_ema(series, fast)
            ema_slow = self._calculate_ema(series, slow)
            macd = ema_fast - ema_slow
            signal_line = self._calculate_ema(macd, signal)
            histogram = macd - signal_line
            
            return {
                'macd': macd,
                'signal': signal_line,
                'histogram': histogram
            }
    
    def _calculate_bollinger_bands(self, series: pd.Series, 
                                 period: int = 20, std_dev: float = 2) -> Dict[str, pd.Series]:
        """计算布林带"""
        if self.use_talib:
            upper, middle, lower = talib.BBANDS(
                series.values, timeperiod=period, nbdevup=std_dev, nbdevdn=std_dev
            )
            return {
                'upper': pd.Series(upper, index=series.index),
                'middle': pd.Series(middle, index=series.index),
                'lower': pd.Series(lower, index=series.index)
            }
        else:
            middle = series.rolling(window=period).mean()
            std = series.rolling(window=period).std()
            upper = middle + (std * std_dev)
            lower = middle - (std * std_dev)
            
            return {
                'upper': upper,
                'middle': middle,
                'lower': lower
            }
    
    def _calculate_kdj(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                      period: int = 9) -> Dict[str, pd.Series]:
        """计算KDJ指标"""
        if self.use_talib:
            k, d = talib.STOCH(high.values, low.values, close.values, 
                              fastk_period=period, slowk_period=3, slowd_period=3)
            k_series = pd.Series(k, index=close.index)
            d_series = pd.Series(d, index=close.index)
            j_series = 3 * k_series - 2 * d_series
            
            return {'k': k_series, 'd': d_series, 'j': j_series}
        else:
            lowest_low = low.rolling(window=period).min()
            highest_high = high.rolling(window=period).max()
            
            rsv = (close - lowest_low) / (highest_high - lowest_low) * 100
            k = rsv.ewm(alpha=1/3).mean()
            d = k.ewm(alpha=1/3).mean()
            j = 3 * k - 2 * d
            
            return {'k': k, 'd': d, 'j': j}
    
    def _calculate_cci(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                      period: int = 14) -> pd.Series:
        """计算CCI指标"""
        if self.use_talib:
            return pd.Series(talib.CCI(high.values, low.values, close.values, timeperiod=period), 
                           index=close.index)
        else:
            tp = (high + low + close) / 3
            ma = tp.rolling(window=period).mean()
            md = tp.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
            cci = (tp - ma) / (0.015 * md)
            return cci
    
    def _calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                      period: int = 14) -> pd.Series:
        """计算ATR指标"""
        if self.use_talib:
            return pd.Series(talib.ATR(high.values, low.values, close.values, timeperiod=period), 
                           index=close.index)
        else:
            tr1 = high - low
            tr2 = abs(high - close.shift())
            tr3 = abs(low - close.shift())
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            return tr.rolling(window=period).mean()
    
    def _calculate_obv(self, close: pd.Series, volume: pd.Series) -> pd.Series:
        """计算OBV指标"""
        if self.use_talib:
            return pd.Series(talib.OBV(close.values, volume.values), index=close.index)
        else:
            obv = pd.Series(index=close.index, dtype=float)
            obv.iloc[0] = volume.iloc[0]
            
            for i in range(1, len(close)):
                if close.iloc[i] > close.iloc[i-1]:
                    obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
                elif close.iloc[i] < close.iloc[i-1]:
                    obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
                else:
                    obv.iloc[i] = obv.iloc[i-1]
            
            return obv
    
    def _calculate_willr(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                        period: int = 14) -> pd.Series:
        """计算威廉指标"""
        if self.use_talib:
            return pd.Series(talib.WILLR(high.values, low.values, close.values, timeperiod=period), 
                           index=close.index)
        else:
            highest_high = high.rolling(window=period).max()
            lowest_low = low.rolling(window=period).min()
            willr = -100 * (highest_high - close) / (highest_high - lowest_low)
            return willr
    
    def _calculate_roc(self, close: pd.Series, period: int = 10) -> pd.Series:
        """计算ROC指标"""
        if self.use_talib:
            return pd.Series(talib.ROC(close.values, timeperiod=period), index=close.index)
        else:
            return ((close - close.shift(period)) / close.shift(period)) * 100
    
    def _calculate_with_stockstats(self, df: pd.DataFrame, indicator: str) -> pd.DataFrame:
        """使用stockstats计算指标"""
        try:
            # 准备stockstats格式的数据
            stock_df = df[['open', 'high', 'low', 'close', 'volume']].copy()
            stock = stockstats.StockDataFrame.retype(stock_df)
            
            # 计算指标
            if indicator in stock.columns or f'{indicator}' in dir(stock):
                result = stock[indicator]
                df[indicator] = result
            else:
                self.logger.warning(f"stockstats不支持指标: {indicator}")
                
        except Exception as e:
            self.logger.error(f"使用stockstats计算{indicator}失败: {e}")
        
        return df
    
    def get_supported_indicators(self) -> List[str]:
        """获取支持的指标列表"""
        basic_indicators = [
            'sma', 'ema', 'rsi', 'macd', 'bb', 'kdj', 'cci', 'atr', 
            'obv', 'willr', 'roc'
        ]
        
        talib_indicators = []
        if self.use_talib:
            talib_indicators = [
                'adx', 'aroon', 'bop', 'mom', 'ppo', 'trix', 'ultosc', 
                'dx', 'minus_di', 'plus_di', 'ht_dcphase'
            ]
        
        return basic_indicators + talib_indicators
    
    def clean_indicator_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理指标数据
        
        Args:
            df: 包含指标的DataFrame
            
        Returns:
            清理后的DataFrame
        """
        df = df.copy()
        
        # 处理无穷大值
        df = df.replace([np.inf, -np.inf], np.nan)
        
        # 删除全为NaN的列
        df = df.dropna(axis=1, how='all')
        
        # 前向填充NaN值（仅对指标列）
        indicator_cols = [col for col in df.columns 
                         if col not in ['time', 'tic', 'open', 'high', 'low', 'close', 'volume']]
        
        for col in indicator_cols:
            if col in df.columns:
                df[col] = df[col].fillna(method='ffill')
        
        return df