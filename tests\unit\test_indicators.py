"""技术指标单元测试

测试各种技术指标的计算正确性和边界情况处理。
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch

# 注意：这里假设技术指标模块的结构，实际实现时需要根据项目结构调整
# from finrl_crypto.indicators import (
#     sma, ema, rsi, macd, bollinger_bands, 
#     stochastic, williams_r, atr, adx
# )

# 临时实现一些基本指标用于测试示例
def sma(data: pd.Series, window: int) -> pd.Series:
    """简单移动平均"""
    return data.rolling(window=window).mean()

def ema(data: pd.Series, span: int) -> pd.Series:
    """指数移动平均"""
    return data.ewm(span=span).mean()

def rsi(data: pd.Series, window: int = 14) -> pd.Series:
    """相对强弱指数"""
    delta = data.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> dict:
    """MACD 指标"""
    ema_fast = ema(data, fast)
    ema_slow = ema(data, slow)
    macd_line = ema_fast - ema_slow
    signal_line = ema(macd_line, signal)
    histogram = macd_line - signal_line
    
    return {
        'macd': macd_line,
        'signal': signal_line,
        'histogram': histogram
    }

def bollinger_bands(data: pd.Series, window: int = 20, num_std: float = 2) -> dict:
    """布林带"""
    middle = sma(data, window)
    std = data.rolling(window=window).std()
    upper = middle + (std * num_std)
    lower = middle - (std * num_std)
    
    return {
        'upper': upper,
        'middle': middle,
        'lower': lower
    }

class TestSimpleMovingAverage:
    """简单移动平均测试类"""
    
    @pytest.fixture
    def sample_prices(self):
        """测试用价格数据"""
        return pd.Series([100, 102, 101, 103, 105, 104, 106, 108, 107, 109])
    
    def test_sma_basic_calculation(self, sample_prices):
        """测试基本 SMA 计算"""
        window = 3
        result = sma(sample_prices, window)
        
        # 检查结果长度
        assert len(result) == len(sample_prices)
        
        # 检查前几个值为 NaN
        assert pd.isna(result.iloc[:window-1]).all()
        
        # 检查具体计算值
        expected_first_value = sample_prices.iloc[:window].mean()
        assert abs(result.iloc[window-1] - expected_first_value) < 1e-10
        
        # 检查第二个值
        expected_second_value = sample_prices.iloc[1:window+1].mean()
        assert abs(result.iloc[window] - expected_second_value) < 1e-10
    
    def test_sma_edge_cases(self):
        """测试边界情况"""
        # 空序列
        empty_series = pd.Series([], dtype=float)
        result = sma(empty_series, 5)
        assert len(result) == 0
        
        # 窗口大于数据长度
        short_series = pd.Series([1, 2, 3])
        result = sma(short_series, 5)
        assert pd.isna(result).all()
        
        # 窗口为 1
        data = pd.Series([1, 2, 3, 4, 5])
        result = sma(data, 1)
        pd.testing.assert_series_equal(result, data, check_names=False, check_dtype=False)
        
        # 包含 NaN 的数据
        nan_series = pd.Series([1, np.nan, 3, 4, 5])
        result = sma(nan_series, 3)
        # 第一个有效值应该在索引 2（需要 3 个值）
        assert pd.isna(result.iloc[:2]).all()
    
    @pytest.mark.parametrize("window,expected_length", [
        (5, 10),
        (10, 10),
        (1, 10),
    ])
    def test_sma_different_windows(self, sample_prices, window, expected_length):
        """测试不同窗口大小"""
        result = sma(sample_prices, window)
        assert len(result) == expected_length
    
    def test_sma_with_negative_values(self):
        """测试包含负值的数据"""
        data = pd.Series([-1, -2, 3, 4, -5])
        result = sma(data, 3)
        
        # 检查第一个有效值
        expected = (-1 + -2 + 3) / 3
        assert abs(result.iloc[2] - expected) < 1e-10
    
    def test_sma_monotonic_data(self):
        """测试单调数据"""
        # 递增数据
        increasing_data = pd.Series(range(1, 11))
        result = sma(increasing_data, 3)
        
        # SMA 应该也是递增的（除了 NaN 值）
        valid_result = result.dropna()
        assert (valid_result.diff().dropna() > 0).all()
        
        # 递减数据
        decreasing_data = pd.Series(range(10, 0, -1))
        result = sma(decreasing_data, 3)
        
        # SMA 应该也是递减的（除了 NaN 值）
        valid_result = result.dropna()
        assert (valid_result.diff().dropna() < 0).all()

class TestExponentialMovingAverage:
    """指数移动平均测试类"""
    
    def test_ema_basic_calculation(self):
        """测试基本 EMA 计算"""
        data = pd.Series([100, 102, 101, 103, 105])
        result = ema(data, span=3)
        
        # EMA 不应该有 NaN 值（除非输入有）
        assert not result.isna().any()
        
        # 第一个值应该等于输入的第一个值
        assert result.iloc[0] == data.iloc[0]
        
        # EMA 应该对最近的值给予更多权重
        # 这里我们检查 EMA 是否在合理范围内
        assert result.min() >= data.min() * 0.9
        assert result.max() <= data.max() * 1.1
    
    def test_ema_vs_sma(self):
        """测试 EMA 与 SMA 的差异"""
        data = pd.Series([100, 110, 90, 120, 80, 130, 70])
        
        ema_result = ema(data, span=3)
        sma_result = sma(data, window=3)
        
        # EMA 应该对最近的变化更敏感
        # 在价格上涨时，EMA 通常会比 SMA 更快上升
        # 在价格下跌时，EMA 通常会比 SMA 更快下降
        
        # 检查最后几个值（跳过 NaN）
        valid_indices = ~sma_result.isna()
        ema_valid = ema_result[valid_indices]
        sma_valid = sma_result[valid_indices]
        
        # 两者应该都在合理范围内
        assert len(ema_valid) == len(sma_valid)
    
    def test_ema_edge_cases(self):
        """测试 EMA 边界情况"""
        # 单个值
        single_value = pd.Series([100])
        result = ema(single_value, span=3)
        assert result.iloc[0] == 100
        
        # 相同值
        constant_data = pd.Series([100] * 10)
        result = ema(constant_data, span=3)
        assert (result == 100).all()
        
        # 包含 NaN
        nan_data = pd.Series([100, np.nan, 102])
        result = ema(nan_data, span=3)
        # pandas EMA 会处理 NaN
        assert not result.isna().all()

class TestRSI:
    """RSI 测试类"""
    
    def test_rsi_bounds(self):
        """测试 RSI 值范围"""
        # 创建有明显趋势的数据
        uptrend_data = pd.Series(range(100, 120))  # 上升趋势
        downtrend_data = pd.Series(range(120, 100, -1))  # 下降趋势
        
        rsi_up = rsi(uptrend_data, 14)
        rsi_down = rsi(downtrend_data, 14)
        
        # RSI 应该在 0-100 之间
        valid_rsi_up = rsi_up.dropna()
        valid_rsi_down = rsi_down.dropna()
        
        assert (valid_rsi_up >= 0).all()
        assert (valid_rsi_up <= 100).all()
        assert (valid_rsi_down >= 0).all()
        assert (valid_rsi_down <= 100).all()
        
        # 上升趋势的 RSI 应该相对较高
        assert valid_rsi_up.mean() > 50
        
        # 下降趋势的 RSI 应该相对较低
        assert valid_rsi_down.mean() < 50
    
    def test_rsi_extreme_cases(self):
        """测试 RSI 极端情况"""
        # 持续上涨
        continuous_up = pd.Series([100 + i for i in range(30)])
        rsi_result = rsi(continuous_up, 14)
        
        # 持续上涨应该导致高 RSI
        valid_rsi = rsi_result.dropna()
        assert valid_rsi.iloc[-1] > 70  # 超买区域
        
        # 持续下跌
        continuous_down = pd.Series([100 - i for i in range(30)])
        rsi_result = rsi(continuous_down, 14)
        
        # 持续下跌应该导致低 RSI
        valid_rsi = rsi_result.dropna()
        assert valid_rsi.iloc[-1] < 30  # 超卖区域
    
    def test_rsi_sideways_market(self):
        """测试横盘市场的 RSI"""
        # 创建横盘数据
        sideways_data = pd.Series([100 + np.sin(i/5) for i in range(50)])
        rsi_result = rsi(sideways_data, 14)
        
        valid_rsi = rsi_result.dropna()
        
        # 横盘市场的 RSI 应该在 30-70 之间波动
        assert valid_rsi.mean() > 30
        assert valid_rsi.mean() < 70
    
    @pytest.mark.parametrize("window", [7, 14, 21, 28])
    def test_rsi_different_windows(self, window):
        """测试不同窗口期的 RSI"""
        data = pd.Series([100 + np.random.normal(0, 2) for _ in range(100)])
        result = rsi(data, window)
        
        # 检查前 window 个值为 NaN
        assert pd.isna(result.iloc[:window-1]).all()
        
        # 检查有效值在正确范围内
        valid_values = result.dropna()
        assert (valid_values >= 0).all()
        assert (valid_values <= 100).all()

class TestMACD:
    """MACD 测试类"""
    
    def test_macd_basic_calculation(self):
        """测试基本 MACD 计算"""
        # 创建有趋势的数据
        data = pd.Series([100 + i * 0.5 + np.sin(i/10) for i in range(100)])
        result = macd(data, fast=12, slow=26, signal=9)
        
        # 检查返回的字典结构
        assert 'macd' in result
        assert 'signal' in result
        assert 'histogram' in result
        
        # 检查数据长度
        assert len(result['macd']) == len(data)
        assert len(result['signal']) == len(data)
        assert len(result['histogram']) == len(data)
        
        # 检查 histogram = macd - signal
        macd_line = result['macd']
        signal_line = result['signal']
        histogram = result['histogram']
        
        # 跳过 NaN 值进行比较
        valid_mask = ~(macd_line.isna() | signal_line.isna() | histogram.isna())
        if valid_mask.any():
            expected_histogram = macd_line[valid_mask] - signal_line[valid_mask]
            actual_histogram = histogram[valid_mask]
            pd.testing.assert_series_equal(
                actual_histogram, expected_histogram, 
                check_names=False, atol=1e-10
            )
    
    def test_macd_trend_detection(self):
        """测试 MACD 趋势检测能力"""
        # 上升趋势
        uptrend = pd.Series([100 + i for i in range(50)])
        result_up = macd(uptrend)
        
        # 下降趋势
        downtrend = pd.Series([150 - i for i in range(50)])
        result_down = macd(downtrend)
        
        # 在明显的趋势中，MACD 应该显示相应的信号
        # 上升趋势：MACD 线应该在信号线上方
        macd_up = result_up['macd'].dropna()
        signal_up = result_up['signal'].dropna()
        
        if len(macd_up) > 10 and len(signal_up) > 10:
            # 检查最后几个值
            assert macd_up.iloc[-5:].mean() > signal_up.iloc[-5:].mean()
        
        # 下降趋势：MACD 线应该在信号线下方
        macd_down = result_down['macd'].dropna()
        signal_down = result_down['signal'].dropna()
        
        if len(macd_down) > 10 and len(signal_down) > 10:
            # 检查最后几个值
            assert macd_down.iloc[-5:].mean() < signal_down.iloc[-5:].mean()
    
    def test_macd_crossover_signals(self):
        """测试 MACD 交叉信号"""
        # 创建先下跌后上涨的数据
        data_part1 = [100 - i for i in range(30)]  # 下跌
        data_part2 = [70 + i for i in range(30)]   # 上涨
        data = pd.Series(data_part1 + data_part2)
        
        result = macd(data)
        macd_line = result['macd']
        signal_line = result['signal']
        histogram = result['histogram']
        
        # 检查是否有交叉点（histogram 从负变正或从正变负）
        valid_histogram = histogram.dropna()
        if len(valid_histogram) > 2:
            # 寻找符号变化
            sign_changes = (valid_histogram.shift(1) * valid_histogram < 0).sum()
            # 在这种数据模式下，应该至少有一次交叉
            assert sign_changes >= 0  # 至少不会出错

class TestBollingerBands:
    """布林带测试类"""
    
    def test_bollinger_bands_basic(self):
        """测试基本布林带计算"""
        data = pd.Series([100, 102, 101, 103, 105, 104, 106, 108, 107, 109] * 3)
        result = bollinger_bands(data, window=10, num_std=2)
        
        # 检查返回的字典结构
        assert 'upper' in result
        assert 'middle' in result
        assert 'lower' in result
        
        upper = result['upper']
        middle = result['middle']
        lower = result['lower']
        
        # 检查数据长度
        assert len(upper) == len(data)
        assert len(middle) == len(data)
        assert len(lower) == len(data)
        
        # 检查带的关系：upper > middle > lower
        valid_mask = ~(upper.isna() | middle.isna() | lower.isna())
        if valid_mask.any():
            assert (upper[valid_mask] >= middle[valid_mask]).all()
            assert (middle[valid_mask] >= lower[valid_mask]).all()
    
    def test_bollinger_bands_price_containment(self):
        """测试布林带价格包含性"""
        # 创建正态分布的价格数据
        np.random.seed(42)
        base_price = 100
        noise = np.random.normal(0, 2, 100)
        data = pd.Series([base_price + n for n in noise])
        
        result = bollinger_bands(data, window=20, num_std=2)
        upper = result['upper']
        lower = result['lower']
        
        # 大约 95% 的价格应该在布林带内（2 标准差）
        valid_mask = ~(upper.isna() | lower.isna())
        if valid_mask.any():
            within_bands = (
                (data[valid_mask] >= lower[valid_mask]) & 
                (data[valid_mask] <= upper[valid_mask])
            )
            containment_ratio = within_bands.mean()
            
            # 允许一定的误差，因为是有限样本
            assert containment_ratio > 0.85  # 至少 85% 在带内
    
    def test_bollinger_bands_volatility_expansion(self):
        """测试布林带波动率扩张"""
        # 低波动率数据
        low_vol_data = pd.Series([100 + 0.1 * np.sin(i/10) for i in range(50)])
        result_low = bollinger_bands(low_vol_data, window=20, num_std=2)
        
        # 高波动率数据
        high_vol_data = pd.Series([100 + 5 * np.sin(i/10) for i in range(50)])
        result_high = bollinger_bands(high_vol_data, window=20, num_std=2)
        
        # 高波动率的布林带应该更宽
        low_width = (result_low['upper'] - result_low['lower']).dropna()
        high_width = (result_high['upper'] - result_high['lower']).dropna()
        
        if len(low_width) > 0 and len(high_width) > 0:
            assert high_width.mean() > low_width.mean()
    
    @pytest.mark.parametrize("num_std", [1, 1.5, 2, 2.5, 3])
    def test_bollinger_bands_different_std(self, num_std):
        """测试不同标准差倍数的布林带"""
        data = pd.Series([100 + np.random.normal(0, 2) for _ in range(50)])
        result = bollinger_bands(data, window=20, num_std=num_std)
        
        upper = result['upper']
        middle = result['middle']
        lower = result['lower']
        
        # 检查带宽随标准差倍数增加
        valid_mask = ~(upper.isna() | middle.isna() | lower.isna())
        if valid_mask.any():
            band_width = (upper[valid_mask] - lower[valid_mask]).mean()
            # 带宽应该与标准差倍数成正比
            assert band_width > 0

class TestIndicatorPerformance:
    """指标性能测试类"""
    
    @pytest.mark.slow
    def test_sma_performance_large_dataset(self, performance_timer):
        """测试大数据集上的 SMA 性能"""
        # 创建大数据集
        large_data = pd.Series(np.random.random(100000))
        
        with performance_timer() as timer:
            result = sma(large_data, 50)
        
        # 检查计算时间（应该在合理范围内）
        assert timer.elapsed < 5.0  # 5秒内完成
        assert len(result) == len(large_data)
    
    @pytest.mark.slow
    def test_multiple_indicators_performance(self, performance_timer):
        """测试多个指标同时计算的性能"""
        data = pd.Series(np.random.random(10000))
        
        with performance_timer() as timer:
            sma_result = sma(data, 20)
            ema_result = ema(data, 20)
            rsi_result = rsi(data, 14)
            macd_result = macd(data)
            bb_result = bollinger_bands(data)
        
        # 所有计算应该在合理时间内完成
        assert timer.elapsed < 10.0  # 10秒内完成
        
        # 检查所有结果都有正确的长度
        assert len(sma_result) == len(data)
        assert len(ema_result) == len(data)
        assert len(rsi_result) == len(data)
        assert len(macd_result['macd']) == len(data)
        assert len(bb_result['middle']) == len(data)

class TestIndicatorIntegration:
    """指标集成测试类"""
    
    def test_indicators_with_real_data_pattern(self, sample_price_data):
        """使用真实数据模式测试指标"""
        # 使用 conftest.py 中的样本数据
        close_prices = sample_price_data['close']
        
        # 计算多个指标
        sma_20 = sma(close_prices, 20)
        ema_12 = ema(close_prices, 12)
        rsi_14 = rsi(close_prices, 14)
        macd_result = macd(close_prices)
        bb_result = bollinger_bands(close_prices)
        
        # 检查所有指标都有合理的值
        assert not sma_20.dropna().empty
        assert not ema_12.dropna().empty
        assert not rsi_14.dropna().empty
        assert not macd_result['macd'].dropna().empty
        assert not bb_result['middle'].dropna().empty
        
        # 检查 RSI 在合理范围内
        valid_rsi = rsi_14.dropna()
        assert (valid_rsi >= 0).all()
        assert (valid_rsi <= 100).all()
        
        # 检查布林带关系
        valid_mask = ~(
            bb_result['upper'].isna() | 
            bb_result['middle'].isna() | 
            bb_result['lower'].isna()
        )
        if valid_mask.any():
            assert (bb_result['upper'][valid_mask] >= bb_result['middle'][valid_mask]).all()
            assert (bb_result['middle'][valid_mask] >= bb_result['lower'][valid_mask]).all()
    
    def test_indicators_consistency(self):
        """测试指标一致性"""
        # 创建相同的数据
        data1 = pd.Series([100, 102, 101, 103, 105, 104, 106, 108, 107, 109])
        data2 = data1.copy()
        
        # 计算相同的指标
        sma1 = sma(data1, 5)
        sma2 = sma(data2, 5)
        
        # 结果应该完全相同
        pd.testing.assert_series_equal(sma1, sma2)
        
        # 测试 EMA 一致性
        ema1 = ema(data1, 5)
        ema2 = ema(data2, 5)
        
        pd.testing.assert_series_equal(ema1, ema2)
    
    def test_indicators_with_missing_data(self):
        """测试包含缺失数据的指标计算"""
        # 创建包含 NaN 的数据
        data_with_nan = pd.Series([100, np.nan, 102, 101, np.nan, 105, 104])
        
        # 计算指标（应该能处理 NaN）
        sma_result = sma(data_with_nan, 3)
        ema_result = ema(data_with_nan, 3)
        rsi_result = rsi(data_with_nan, 3)
        
        # 结果应该有合理的长度
        assert len(sma_result) == len(data_with_nan)
        assert len(ema_result) == len(data_with_nan)
        assert len(rsi_result) == len(data_with_nan)
        
        # 至少应该有一些非 NaN 的结果
        assert not sma_result.dropna().empty or sma_result.isna().all()
        assert not ema_result.dropna().empty or ema_result.isna().all()

# 参数化测试示例
class TestParametrizedIndicators:
    """参数化指标测试"""
    
    @pytest.mark.parametrize("window,data_length", [
        (5, 10),
        (10, 20),
        (20, 50),
        (50, 100),
    ])
    def test_sma_various_parameters(self, window, data_length):
        """测试各种参数组合的 SMA"""
        data = pd.Series(np.random.random(data_length))
        result = sma(data, window)
        
        assert len(result) == data_length
        
        if window <= data_length:
            # 应该有一些有效值
            valid_count = (~result.isna()).sum()
            expected_valid_count = data_length - window + 1
            assert valid_count == expected_valid_count
        else:
            # 窗口大于数据长度，所有值都应该是 NaN
            assert result.isna().all()
    
    @pytest.mark.parametrize("rsi_period", [7, 14, 21, 28])
    def test_rsi_various_periods(self, rsi_period):
        """测试各种周期的 RSI"""
        # 创建有趋势的数据
        data = pd.Series([100 + i * 0.5 for i in range(50)])
        result = rsi(data, rsi_period)
        
        # 检查有效值在正确范围内
        valid_values = result.dropna()
        if not valid_values.empty:
            assert (valid_values >= 0).all()
            assert (valid_values <= 100).all()
            
            # 上升趋势应该产生较高的 RSI
            assert valid_values.mean() > 50
    
    @pytest.mark.parametrize("fast,slow,signal", [
        (12, 26, 9),
        (8, 21, 5),
        (5, 13, 3),
    ])
    def test_macd_various_parameters(self, fast, slow, signal):
        """测试各种参数的 MACD"""
        data = pd.Series([100 + i * 0.1 for i in range(100)])
        result = macd(data, fast, slow, signal)
        
        # 检查返回结构
        assert 'macd' in result
        assert 'signal' in result
        assert 'histogram' in result
        
        # 检查长度
        assert len(result['macd']) == len(data)
        assert len(result['signal']) == len(data)
        assert len(result['histogram']) == len(data)
        
        # 快线应该比慢线更敏感（在趋势数据中）
        macd_line = result['macd'].dropna()
        if len(macd_line) > 10:
            # 在上升趋势中，MACD 应该为正
            assert macd_line.iloc[-5:].mean() > 0

# Mock 测试示例
class TestIndicatorMocks:
    """使用 Mock 的指标测试"""
    
    @patch('pandas.Series.rolling')
    def test_sma_with_mock(self, mock_rolling):
        """使用 Mock 测试 SMA"""
        # 设置 Mock
        mock_rolling_obj = Mock()
        mock_rolling_obj.mean.return_value = pd.Series([100, 101, 102])
        mock_rolling.return_value = mock_rolling_obj
        
        # 测试数据
        data = pd.Series([100, 101, 102])
        
        # 调用函数
        result = sma(data, 3)
        
        # 验证 Mock 被正确调用
        mock_rolling.assert_called_once_with(window=3)
        mock_rolling_obj.mean.assert_called_once()
        
        # 验证结果
        expected = pd.Series([100, 101, 102])
        pd.testing.assert_series_equal(result, expected)
    
    def test_indicator_error_handling(self):
        """测试指标错误处理"""
        # 测试无效输入类型
        try:
            result = sma("invalid_input", 5)
            # 如果没有抛出异常，检查结果是否合理
            assert result is None or len(result) == 0
        except (ValueError, TypeError, AttributeError):
            # 预期的异常
            pass
        
        # 测试无效窗口类型
        try:
            result = sma(pd.Series([1, 2, 3]), "invalid_window")
            assert result is None or len(result) == 0
        except (ValueError, TypeError, AttributeError):
            pass
        
        # 测试负窗口
        try:
            result = sma(pd.Series([1, 2, 3]), -5)
            assert result is None or len(result) == 0
        except (ValueError, TypeError, AttributeError):
            pass

# 基准测试
class TestIndicatorBenchmarks:
    """指标基准测试"""
    
    def test_sma_benchmark_against_pandas(self):
        """SMA 与 pandas 内置方法的基准测试"""
        data = pd.Series(np.random.random(1000))
        window = 20
        
        # 我们的实现
        our_result = sma(data, window)
        
        # pandas 内置实现
        pandas_result = data.rolling(window=window).mean()
        
        # 结果应该相同
        pd.testing.assert_series_equal(our_result, pandas_result)
    
    def test_ema_benchmark_against_pandas(self):
        """EMA 与 pandas 内置方法的基准测试"""
        data = pd.Series(np.random.random(1000))
        span = 20
        
        # 我们的实现
        our_result = ema(data, span)
        
        # pandas 内置实现
        pandas_result = data.ewm(span=span).mean()
        
        # 结果应该相同
        pd.testing.assert_series_equal(our_result, pandas_result)