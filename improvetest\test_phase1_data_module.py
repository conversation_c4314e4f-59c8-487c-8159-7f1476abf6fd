# -*- coding: utf-8 -*-
"""
第一阶段数据模块测试

测试finrl_crypto.data模块的所有功能，包括：
- BaseDataProcessor抽象基类
- 具体数据处理器实现
- 数据验证和处理功能
- 技术指标计算
- 数据管理器功能
"""

import unittest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from finrl_crypto.data.base import DataProcessor
    from finrl_crypto.data.processors import BinanceProcessor
    from finrl_crypto.data.indicators import TechnicalIndicators
    from finrl_crypto.data.manager import DataManager
except ImportError as e:
    print(f"导入错误: {e}")
    # 创建模拟类以便测试结构
    class DataProcessor:
        def __init__(self, data_source, start_date, end_date, **kwargs):
            self.data_source = data_source
            self.start_date = start_date
            self.end_date = end_date
            self.dataframe = pd.DataFrame()
    
    class BinanceProcessor(DataProcessor):
        pass
    
    class TechnicalIndicators:
        pass
    
    class DataManager:
        pass


class TestDataProcessor(unittest.TestCase):
    """测试DataProcessor抽象基类"""
    
    def setUp(self):
        """测试前准备"""
        self.start_date = '2023-01-01'
        self.end_date = '2023-12-31'
        self.data_source = 'test_source'
        
        # 创建一个具体的DataProcessor子类用于测试
        class ConcreteDataProcessor(DataProcessor):
            def download_data(self, symbols):
                return pd.DataFrame()
            
            def fetch_data(self, ticker_list=None):
                return pd.DataFrame()
                
        self.ConcreteDataProcessor = ConcreteDataProcessor
        
    def test_data_processor_initialization(self):
        """测试数据处理器初始化"""
        processor = self.ConcreteDataProcessor(
            data_source=self.data_source,
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        self.assertEqual(processor.data_source, self.data_source)
        self.assertEqual(processor.start_date, self.start_date)
        self.assertEqual(processor.end_date, self.end_date)
        
    def test_data_processor_with_optional_params(self):
        """测试带可选参数的数据处理器初始化"""
        processor = self.ConcreteDataProcessor(
            data_source=self.data_source,
            start_date=self.start_date,
            end_date=self.end_date,
            time_interval='1h',
            time_zone='Asia/Shanghai'
        )
        
        self.assertEqual(processor.data_source, self.data_source)
        
    def test_data_processor_abstract_methods(self):
        """测试抽象方法是否正确定义"""
        # 验证DataProcessor是抽象类
        with self.assertRaises(TypeError):
            DataProcessor(
                data_source=self.data_source,
                start_date=self.start_date,
                end_date=self.end_date
            )


class TestBinanceProcessor(unittest.TestCase):
    """测试BinanceProcessor具体实现"""
    
    def setUp(self):
        """测试前准备"""
        self.start_date = '2023-01-01'
        self.end_date = '2023-12-31'
        self.symbols = ['BTCUSDT', 'ETHUSDT']
        
        # 创建模拟数据
        self.mock_data = pd.DataFrame({
            'timestamp': pd.date_range(start=self.start_date, end=self.end_date, freq='D'),
            'symbol': 'BTCUSDT',
            'open': np.random.uniform(30000, 50000, 365),
            'high': np.random.uniform(30000, 50000, 365),
            'low': np.random.uniform(30000, 50000, 365),
            'close': np.random.uniform(30000, 50000, 365),
            'volume': np.random.uniform(1000, 10000, 365)
        })
        
    @patch('finrl_crypto.data.processors.Client')
    def test_binance_processor_initialization(self, mock_client):
        """测试Binance处理器初始化"""
        processor = BinanceProcessor(
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        self.assertEqual(processor.data_source, 'binance')
        self.assertEqual(processor.start_date, self.start_date)
        self.assertEqual(processor.end_date, self.end_date)
        
    @patch('finrl_crypto.data.processors.Client')
    def test_fetch_data(self, mock_client):
        """测试数据获取功能"""
        # 模拟Binance客户端响应
        mock_instance = Mock()
        mock_instance.get_historical_klines.return_value = [
            [1640995200000, "46000.00", "47000.00", "45000.00", "46500.00", "1000.00", 
             1640995259999, "46500000.00", 100, "500.00", "23000000.00", "0"],
            [1640995260000, "46500.00", "47500.00", "46000.00", "47000.00", "1200.00", 
             1640995319999, "47000000.00", 120, "600.00", "28000000.00", "0"]
        ]
        mock_client.return_value = mock_instance
        
        processor = BinanceProcessor(
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        # 测试数据获取
        data = processor.download_data(['BTCUSDT'])
        self.assertIsInstance(data, pd.DataFrame)
        self.assertGreater(len(data), 0)
        
    def test_data_validation(self):
        """测试数据验证功能"""
        processor = BinanceProcessor(
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        # 测试有效数据
        is_valid = processor.validate_data(self.mock_data)
        self.assertTrue(is_valid)
        
        # 测试无效数据（缺少必要列）
        invalid_data = self.mock_data.drop(columns=['close'])
        is_valid = processor.validate_data(invalid_data)
        self.assertFalse(is_valid)
        
    def test_data_preprocessing(self):
        """测试数据预处理功能"""
        processor = BinanceProcessor(
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        processed_data = processor.preprocess_data(self.mock_data)
        
        # 验证预处理后的数据结构
        self.assertIsInstance(processed_data, pd.DataFrame)
        self.assertIn('timestamp', processed_data.columns)
        self.assertIn('close', processed_data.columns)


class TestTechnicalIndicators(unittest.TestCase):
    """测试技术指标计算"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟价格数据
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        self.price_data = pd.DataFrame({
            'timestamp': dates,
            'open': np.random.uniform(100, 200, len(dates)),
            'high': np.random.uniform(100, 200, len(dates)),
            'low': np.random.uniform(100, 200, len(dates)),
            'close': np.random.uniform(100, 200, len(dates)),
            'volume': np.random.uniform(1000, 10000, len(dates))
        })
        
        self.indicators = TechnicalIndicators()
        
    def test_sma_calculation(self):
        """测试简单移动平均线计算"""
        sma = self.indicators._calculate_sma(self.price_data['close'], period=20)
        
        self.assertEqual(len(sma), len(self.price_data))
        self.assertTrue(pd.isna(sma.iloc[:19]).all())  # 前19个值应该是NaN
        self.assertFalse(pd.isna(sma.iloc[19:]).any())  # 后续值不应该是NaN
        
    def test_ema_calculation(self):
        """测试指数移动平均线计算"""
        ema = self.indicators._calculate_ema(self.price_data['close'], period=20)
        
        self.assertEqual(len(ema), len(self.price_data))
        self.assertFalse(pd.isna(ema.iloc[19:]).any())
        
    def test_rsi_calculation(self):
        """测试RSI指标计算"""
        rsi = self.indicators._calculate_rsi(self.price_data['close'], period=14)
        
        self.assertEqual(len(rsi), len(self.price_data))
        # RSI值应该在0-100之间（忽略NaN值）
        valid_rsi = rsi.dropna()
        if len(valid_rsi) > 0:
            self.assertTrue((valid_rsi >= 0).all() and (valid_rsi <= 100).all())
        
    def test_macd_calculation(self):
        """测试MACD指标计算"""
        macd_data = self.indicators._calculate_macd(self.price_data['close'])
        
        self.assertIsInstance(macd_data, dict)
        self.assertIn('macd', macd_data)
        self.assertIn('signal', macd_data)
        self.assertIn('histogram', macd_data)
        
    def test_bollinger_bands_calculation(self):
        """测试布林带计算"""
        bb_data = self.indicators._calculate_bollinger_bands(
            self.price_data['close'], period=20, std_dev=2
        )
        
        self.assertIsInstance(bb_data, dict)
        self.assertIn('upper', bb_data)
        self.assertIn('middle', bb_data)
        self.assertIn('lower', bb_data)
        
        upper, middle, lower = bb_data['upper'], bb_data['middle'], bb_data['lower']
        # 验证布林带的关系：upper > middle > lower
        valid_indices = ~(pd.isna(upper) | pd.isna(middle) | pd.isna(lower))
        self.assertTrue((upper[valid_indices] >= middle[valid_indices]).all())
        self.assertTrue((middle[valid_indices] >= lower[valid_indices]).all())


class TestDataManager(unittest.TestCase):
    """测试数据管理器"""
    
    def setUp(self):
        """测试前准备"""
        self.data_manager = DataManager(
            data_source='binance',
            start_date='2023-01-01',
            end_date='2023-12-31'
        )
        
        # 创建模拟数据
        self.mock_data = pd.DataFrame({
            'timestamp': pd.date_range(start='2023-01-01', end='2023-12-31', freq='D'),
            'symbol': 'BTCUSDT',
            'close': np.random.uniform(30000, 50000, 365),
            'volume': np.random.uniform(1000, 10000, 365)
        })
        
    def test_data_manager_initialization(self):
        """测试数据管理器初始化"""
        self.assertIsInstance(self.data_manager, DataManager)
        
    def test_add_data_source(self):
        """测试添加数据源"""
        processor = Mock(spec=DataProcessor)
        processor.data_source = 'test_source'
        
        self.data_manager.add_data_source('test_source', processor)
        
        self.assertIn('test_source', self.data_manager.data_sources)
        
    def test_get_data(self):
        """测试获取数据"""
        # 模拟数据处理器
        mock_processor = Mock(spec=DataProcessor)
        mock_processor.fetch_data.return_value = self.mock_data
        mock_processor.download_data.return_value = self.mock_data
        mock_processor.clean_data.return_value = self.mock_data
        mock_processor.dataframe = self.mock_data
        
        self.data_manager.add_data_source('test_source', mock_processor)
        
        data = self.data_manager.get_data('test_source', ['BTCUSDT'])
        
        self.assertIsInstance(data, pd.DataFrame)
        mock_processor.download_data.assert_called_once()
        
    def test_merge_data_sources(self):
        """测试合并多个数据源"""
        # 创建两个模拟数据源
        data1 = self.mock_data.copy()
        data1['source'] = 'source1'
        
        data2 = self.mock_data.copy()
        data2['source'] = 'source2'
        data2['timestamp'] = data2['timestamp'] + timedelta(days=1)
        
        mock_processor1 = Mock(spec=DataProcessor)
        mock_processor1.fetch_data.return_value = data1
        
        mock_processor2 = Mock(spec=DataProcessor)
        mock_processor2.fetch_data.return_value = data2
        
        self.data_manager.add_data_source('source1', mock_processor1)
        self.data_manager.add_data_source('source2', mock_processor2)
        
        merged_data = self.data_manager.merge_data_sources(['source1', 'source2'])
        
        self.assertIsInstance(merged_data, pd.DataFrame)
        self.assertGreater(len(merged_data), len(data1))


class TestDataModuleIntegration(unittest.TestCase):
    """数据模块集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.start_date = '2023-01-01'
        self.end_date = '2023-01-31'
        self.symbols = ['BTCUSDT']
        
    @patch('finrl_crypto.data.processors.Client')
    def test_end_to_end_data_pipeline(self, mock_client):
        """测试端到端数据处理流程"""
        # 模拟Binance客户端响应
        mock_instance = Mock()
        mock_instance.get_historical_klines.return_value = [
            [1640995200000, "46000.00", "47000.00", "45000.00", "46500.00", "1000.00", 
             1640995259999, "46500000.00", 100, "500.00", "23000000.00", "0"],
            [1641081600000, "46500.00", "47500.00", "45500.00", "47000.00", "1100.00", 
             1641081659999, "47000000.00", 110, "550.00", "25000000.00", "0"]
        ]
        mock_client.return_value = mock_instance
        
        # 1. 创建数据处理器
        processor = BinanceProcessor(
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        # 2. 获取数据
        raw_data = processor.download_data(self.symbols)
        self.assertIsInstance(raw_data, pd.DataFrame)
        
        # 3. 创建DataManager进行验证和预处理
        from finrl_crypto.data.manager import DataManager
        manager = DataManager(
            data_source='binance',
            start_date=self.start_date,
            end_date=self.end_date
        )
        manager.processed_data = raw_data
        
        # 验证数据
        validation_result = manager.validate_data()
        self.assertIsInstance(validation_result, dict)
        
        # 预处理数据（使用clean_data方法）
        processed_data = manager.processed_data
        self.assertIsInstance(processed_data, pd.DataFrame)
        
        # 5. 计算技术指标
        indicators = TechnicalIndicators()
        if len(processed_data) > 20:  # 确保有足够的数据计算指标
            sma = indicators.calculate_sma(processed_data['close'], window=20)
            self.assertEqual(len(sma), len(processed_data))
        
        # 6. 使用数据管理器
        data_manager = DataManager(
            data_source='binance',
            start_date=self.start_date,
            end_date=self.end_date
        )
        data_manager.add_data_source('binance', processor)
        
        managed_data = data_manager.get_data('binance', self.symbols)
        self.assertIsInstance(managed_data, pd.DataFrame)
        
    def test_data_quality_checks(self):
        """测试数据质量检查"""
        # 创建包含质量问题的数据
        problematic_data = pd.DataFrame({
            'timestamp': pd.date_range(start='2023-01-01', end='2023-01-10', freq='D'),
            'open': [100, np.nan, 102, 103, 104, 105, 106, 107, 108, 109],
            'high': [105, 106, np.inf, 108, 109, 110, 111, 112, 113, 114],
            'low': [95, 96, 97, 98, 99, 100, 101, 102, 103, 104],
            'close': [102, 103, 104, 105, 106, 107, 108, 109, 110, 111],
            'volume': [1000, 1100, 1200, 0, 1400, 1500, 1600, 1700, 1800, 1900]
        })
        
        processor = BinanceProcessor(
            data_source='test',
            start_date='2023-01-01',
            end_date='2023-01-10',
            symbols=['TEST']
        )
        
        # 测试数据质量检查
        quality_report = processor.check_data_quality(problematic_data)
        
        self.assertIsInstance(quality_report, dict)
        self.assertIn('missing_values', quality_report)
        self.assertIn('infinite_values', quality_report)
        self.assertIn('zero_volume', quality_report)


if __name__ == '__main__':
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestDataProcessor,
        TestBinanceProcessor,
        TestTechnicalIndicators,
        TestDataManager,
        TestDataModuleIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print(f"\n测试结果:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.2f}%")