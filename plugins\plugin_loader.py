"""插件加载器模块

负责从文件系统动态加载插件类。
"""

import importlib.util
import inspect
import logging
import sys
from pathlib import Path
from typing import List, Optional, Type

from .plugin_interface import IPlugin


class PluginLoader:
    """插件加载器
    
    负责从文件系统动态加载插件类。
    """
    
    def __init__(self):
        self._logger = logging.getLogger(__name__)
    
    def load_from_file(self, plugin_path: Path) -> Optional[Type[IPlugin]]:
        """从文件加载插件类
        
        Args:
            plugin_path: 插件文件路径
            
        Returns:
            Optional[Type[IPlugin]]: 插件类，如果加载失败则返回None
        """
        try:
            if not plugin_path.exists() or not plugin_path.is_file():
                self._logger.error(f"插件文件不存在: {plugin_path}")
                return None
            
            if plugin_path.suffix != '.py':
                self._logger.error(f"插件文件必须是Python文件: {plugin_path}")
                return None
            
            # 生成模块名
            module_name = f"plugin_{plugin_path.stem}_{id(plugin_path)}"
            
            # 加载模块
            spec = importlib.util.spec_from_file_location(module_name, plugin_path)
            if spec is None or spec.loader is None:
                self._logger.error(f"无法创建模块规范: {plugin_path}")
                return None
            
            module = importlib.util.module_from_spec(spec)
            sys.modules[module_name] = module
            spec.loader.exec_module(module)
            
            # 查找插件类
            plugin_class = self._find_plugin_class(module)
            if plugin_class:
                self._logger.info(f"成功加载插件: {plugin_class.__name__} from {plugin_path}")
                return plugin_class
            else:
                self._logger.error(f"在文件中未找到有效的插件类: {plugin_path}")
                return None
                
        except Exception as e:
            self._logger.error(f"加载插件文件失败 {plugin_path}: {e}")
            return None
    
    def load_from_directory(self, directory: Path) -> List[Type[IPlugin]]:
        """从目录加载所有插件
        
        Args:
            directory: 插件目录路径
            
        Returns:
            List[Type[IPlugin]]: 插件类列表
        """
        plugin_classes = []
        
        try:
            if not directory.exists() or not directory.is_dir():
                self._logger.error(f"插件目录不存在: {directory}")
                return plugin_classes
            
            # 递归查找所有Python文件
            python_files = list(directory.rglob("*.py"))
            
            for python_file in python_files:
                # 跳过__init__.py和以_开头的文件
                if python_file.name.startswith('_') or python_file.name == '__init__.py':
                    continue
                
                plugin_class = self.load_from_file(python_file)
                if plugin_class:
                    plugin_classes.append(plugin_class)
            
            self._logger.info(f"从目录 {directory} 加载了 {len(plugin_classes)} 个插件")
            
        except Exception as e:
            self._logger.error(f"从目录加载插件失败 {directory}: {e}")
        
        return plugin_classes
    
    def _find_plugin_class(self, module) -> Optional[Type[IPlugin]]:
        """在模块中查找插件类
        
        Args:
            module: Python模块
            
        Returns:
            Optional[Type[IPlugin]]: 插件类，如果未找到则返回None
        """
        try:
            # 获取模块中的所有类
            for name, obj in inspect.getmembers(module, inspect.isclass):
                # 检查是否是IPlugin的子类（但不是IPlugin本身）
                if (issubclass(obj, IPlugin) and 
                    obj is not IPlugin and 
                    obj.__module__ == module.__name__):
                    
                    # 验证类是否实现了必要的抽象方法
                    if self._validate_plugin_class(obj):
                        return obj
            
            return None
            
        except Exception as e:
            self._logger.error(f"查找插件类失败: {e}")
            return None
    
    def _validate_plugin_class(self, plugin_class: Type[IPlugin]) -> bool:
        """验证插件类是否正确实现了接口
        
        Args:
            plugin_class: 插件类
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 检查是否可以实例化（即没有未实现的抽象方法）
            required_methods = ['metadata', 'initialize', 'execute', 'cleanup']
            
            for method_name in required_methods:
                if not hasattr(plugin_class, method_name):
                    self._logger.error(f"插件类 {plugin_class.__name__} 缺少必需的方法: {method_name}")
                    return False
                
                method = getattr(plugin_class, method_name)
                if callable(method) or isinstance(method, property):
                    continue
                else:
                    self._logger.error(f"插件类 {plugin_class.__name__} 的 {method_name} 不是可调用的")
                    return False
            
            # 尝试创建实例来检查抽象方法
            try:
                temp_instance = plugin_class()
                # 检查metadata属性
                metadata = temp_instance.metadata
                if not hasattr(metadata, 'name') or not hasattr(metadata, 'version'):
                    self._logger.error(f"插件类 {plugin_class.__name__} 的metadata不完整")
                    return False
                    
            except TypeError as e:
                if "abstract" in str(e).lower():
                    self._logger.error(f"插件类 {plugin_class.__name__} 有未实现的抽象方法: {e}")
                    return False
                # 其他TypeError可能是由于构造函数参数导致的，这是可以接受的
            
            return True
            
        except Exception as e:
            self._logger.error(f"验证插件类失败 {plugin_class.__name__}: {e}")
            return False
    
    def validate_plugin_file(self, plugin_path: Path) -> bool:
        """验证插件文件是否有效
        
        Args:
            plugin_path: 插件文件路径
            
        Returns:
            bool: 文件是否有效
        """
        try:
            plugin_class = self.load_from_file(plugin_path)
            return plugin_class is not None
        except Exception:
            return False
    
    def get_plugin_info(self, plugin_path: Path) -> Optional[dict]:
        """获取插件文件信息
        
        Args:
            plugin_path: 插件文件路径
            
        Returns:
            Optional[dict]: 插件信息，如果获取失败则返回None
        """
        try:
            plugin_class = self.load_from_file(plugin_path)
            if plugin_class:
                temp_instance = plugin_class()
                metadata = temp_instance.metadata
                return {
                    'name': metadata.name,
                    'version': metadata.version,
                    'description': metadata.description,
                    'author': metadata.author,
                    'type': metadata.plugin_type.value,
                    'dependencies': metadata.dependencies,
                    'file_path': str(plugin_path),
                    'class_name': plugin_class.__name__
                }
            return None
        except Exception as e:
            self._logger.error(f"获取插件信息失败 {plugin_path}: {e}")
            return None