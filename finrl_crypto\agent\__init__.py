"""FinRL Crypto 智能体模块

本模块提供用于加密货币交易的强化学习智能体。

主要组件:
- BaseAgent: 智能体基类
- DQNAgent: Deep Q-Network 智能体
- PPOAgent: Proximal Policy Optimization 智能体
- A2CAgent: Advantage Actor-Critic 智能体
- SACAgent: Soft Actor-Critic 智能体
- AgentFactory: 智能体工厂

基本用法:
```python
from finrl_crypto.agent import create_agent

# 创建PPO智能体
agent = create_agent(
    agent_type='ppo',
    state_dim=100,
    action_dim=10,
    learning_rate=3e-4
)

# 训练智能体
for episode in range(1000):
    state = env.reset()
    done = False
    while not done:
        action = agent.act(state)
        next_state, reward, done, info = env.step(action)
        agent.learn(state, action, reward, next_state, done)
        state = next_state
```
"""

from .base import BaseAgent
from .dqn import DQNAgent
from .ppo import PPOAgent
from .a2c import A2CAgent
from .sac import SACAgent
from .factory import (
    AgentFactory,
    create_agent,
    get_available_agents,
    get_agent_info,
)

__version__ = "0.1.0"

# 支持的智能体类型
SUPPORTED_AGENTS = [
    'dqn',
    'ppo',
    'a2c',
    'sac'
]

# 快速创建函数
def create_trading_agent(agent_type: str, state_dim: int, action_dim: int, **kwargs):
    """快速创建交易智能体
    
    Args:
        agent_type: 智能体类型
        state_dim: 状态维度
        action_dim: 动作维度
        **kwargs: 智能体参数
        
    Returns:
        智能体实例
    """
    return AgentFactory.create_agent(agent_type, state_dim, action_dim, **kwargs)


__all__ = [
    'BaseAgent',
    'DQNAgent',
    'PPOAgent',
    'A2CAgent',
    'SACAgent',
    'AgentFactory',
    'create_agent',
    'create_trading_agent',
    'get_supported_agents',
    'SUPPORTED_AGENTS'
]