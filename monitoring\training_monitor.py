"""训练监控模块

监控DRL训练过程中的各种指标和状态。
"""

import logging
import threading
import time
from typing import Dict, List, Optional, Callable, Any, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum


class TrainingPhase(Enum):
    """训练阶段枚举"""
    INITIALIZATION = "initialization"
    TRAINING = "training"
    VALIDATION = "validation"
    TESTING = "testing"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"


@dataclass
class TrainingMetrics:
    """训练指标数据类"""
    timestamp: datetime
    episode: int
    step: int
    phase: TrainingPhase
    reward: Optional[float] = None
    loss: Optional[float] = None
    learning_rate: Optional[float] = None
    epsilon: Optional[float] = None
    portfolio_value: Optional[float] = None
    sharpe_ratio: Optional[float] = None
    max_drawdown: Optional[float] = None
    win_rate: Optional[float] = None
    custom_metrics: Dict[str, Any] = field(default_factory=dict)
    

@dataclass
class TrainingSession:
    """训练会话信息"""
    session_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    total_episodes: Optional[int] = None
    current_episode: int = 0
    current_step: int = 0
    phase: TrainingPhase = TrainingPhase.INITIALIZATION
    config: Dict[str, Any] = field(default_factory=dict)
    metrics_history: List[TrainingMetrics] = field(default_factory=list)
    

class TrainingMonitor:
    """训练监控器
    
    监控DRL训练过程，收集和分析训练指标。
    """
    
    def __init__(self, session_id: Optional[str] = None):
        """
        Args:
            session_id: 训练会话ID，如果为None则自动生成
        """
        self.session_id = session_id or f"session_{int(time.time())}"
        self._session = TrainingSession(
            session_id=self.session_id,
            start_time=datetime.now()
        )
        
        self._is_monitoring = False
        self._callbacks: List[Callable[[TrainingMetrics], None]] = []
        self._anomaly_callbacks: List[Callable[[str, TrainingMetrics], None]] = []
        self._lock = threading.RLock()
        self._logger = logging.getLogger(__name__)
        
        # 异常检测阈值
        self._anomaly_thresholds = {
            'reward_drop_threshold': 0.5,  # 奖励下降50%视为异常
            'loss_spike_threshold': 2.0,   # 损失增加200%视为异常
            'no_progress_episodes': 100,   # 100个episode无进展视为异常
        }
        
        # 统计信息
        self._best_reward = float('-inf')
        self._worst_reward = float('inf')
        self._total_steps = 0
        self._last_progress_episode = 0
    
    def start_session(self, config: Optional[Dict[str, Any]] = None, total_episodes: Optional[int] = None) -> None:
        """开始训练会话
        
        Args:
            config: 训练配置
            total_episodes: 总episode数
        """
        with self._lock:
            self._session.config = config or {}
            self._session.total_episodes = total_episodes
            self._session.phase = TrainingPhase.TRAINING
            self._is_monitoring = True
            
            self._logger.info(f"训练会话 {self.session_id} 已开始")
    
    def end_session(self, success: bool = True) -> None:
        """结束训练会话
        
        Args:
            success: 训练是否成功完成
        """
        with self._lock:
            self._session.end_time = datetime.now()
            self._session.phase = TrainingPhase.COMPLETED if success else TrainingPhase.FAILED
            self._is_monitoring = False
            
            duration = self._session.end_time - self._session.start_time
            self._logger.info(f"训练会话 {self.session_id} 已结束，持续时间: {duration}")
    
    def log_metrics(self, 
                   episode: int,
                   step: int,
                   reward: Optional[float] = None,
                   loss: Optional[float] = None,
                   **kwargs) -> None:
        """记录训练指标
        
        Args:
            episode: 当前episode
            step: 当前step
            reward: 奖励值
            loss: 损失值
            **kwargs: 其他自定义指标
        """
        if not self._is_monitoring:
            return
        
        with self._lock:
            # 更新会话状态
            self._session.current_episode = episode
            self._session.current_step = step
            self._total_steps += 1
            
            # 创建指标对象
            metrics = TrainingMetrics(
                timestamp=datetime.now(),
                episode=episode,
                step=step,
                phase=self._session.phase,
                reward=reward,
                loss=loss,
                custom_metrics=kwargs
            )
            
            # 计算衍生指标
            self._calculate_derived_metrics(metrics)
            
            # 存储指标
            self._session.metrics_history.append(metrics)
            
            # 更新统计信息
            self._update_statistics(metrics)
            
            # 异常检测
            self._detect_anomalies(metrics)
            
            # 调用回调函数
            for callback in self._callbacks:
                try:
                    callback(metrics)
                except Exception as e:
                    self._logger.error(f"训练监控回调函数执行失败: {e}")
    
    def add_callback(self, callback: Callable[[TrainingMetrics], None]) -> None:
        """添加监控回调函数
        
        Args:
            callback: 回调函数，接收TrainingMetrics参数
        """
        with self._lock:
            self._callbacks.append(callback)
    
    def add_anomaly_callback(self, callback: Callable[[str, TrainingMetrics], None]) -> None:
        """添加异常检测回调函数
        
        Args:
            callback: 回调函数，接收异常类型和TrainingMetrics参数
        """
        with self._lock:
            self._anomaly_callbacks.append(callback)
    
    def get_current_metrics(self) -> Optional[TrainingMetrics]:
        """获取最新的训练指标
        
        Returns:
            Optional[TrainingMetrics]: 最新指标，如果没有则返回None
        """
        with self._lock:
            if self._session.metrics_history:
                return self._session.metrics_history[-1]
            return None
    
    def get_metrics_history(self, limit: Optional[int] = None) -> List[TrainingMetrics]:
        """获取训练指标历史
        
        Args:
            limit: 返回的最大记录数，None表示返回所有
            
        Returns:
            List[TrainingMetrics]: 指标历史
        """
        with self._lock:
            if limit is None:
                return self._session.metrics_history.copy()
            else:
                return self._session.metrics_history[-limit:].copy()
    
    def get_session_summary(self) -> Dict[str, Any]:
        """获取训练会话摘要
        
        Returns:
            Dict[str, Any]: 会话摘要信息
        """
        with self._lock:
            duration = None
            if self._session.end_time:
                duration = (self._session.end_time - self._session.start_time).total_seconds()
            elif self._is_monitoring:
                duration = (datetime.now() - self._session.start_time).total_seconds()
            
            # 计算平均指标
            avg_reward = None
            avg_loss = None
            if self._session.metrics_history:
                rewards = [m.reward for m in self._session.metrics_history if m.reward is not None]
                losses = [m.loss for m in self._session.metrics_history if m.loss is not None]
                
                if rewards:
                    avg_reward = sum(rewards) / len(rewards)
                if losses:
                    avg_loss = sum(losses) / len(losses)
            
            return {
                'session_id': self.session_id,
                'start_time': self._session.start_time.isoformat(),
                'end_time': self._session.end_time.isoformat() if self._session.end_time else None,
                'duration_seconds': duration,
                'phase': self._session.phase.value,
                'total_episodes': self._session.total_episodes,
                'current_episode': self._session.current_episode,
                'current_step': self._session.current_step,
                'total_steps': self._total_steps,
                'metrics_count': len(self._session.metrics_history),
                'best_reward': self._best_reward if self._best_reward != float('-inf') else None,
                'worst_reward': self._worst_reward if self._worst_reward != float('inf') else None,
                'average_reward': avg_reward,
                'average_loss': avg_loss,
                'config': self._session.config
            }
    
    def get_performance_stats(self, window_size: int = 100) -> Dict[str, Any]:
        """获取性能统计信息
        
        Args:
            window_size: 统计窗口大小（最近N个episode）
            
        Returns:
            Dict[str, Any]: 性能统计
        """
        with self._lock:
            if not self._session.metrics_history:
                return {}
            
            recent_metrics = self._session.metrics_history[-window_size:]
            
            # 奖励统计
            rewards = [m.reward for m in recent_metrics if m.reward is not None]
            reward_stats = {}
            if rewards:
                reward_stats = {
                    'mean': sum(rewards) / len(rewards),
                    'min': min(rewards),
                    'max': max(rewards),
                    'std': self._calculate_std(rewards)
                }
            
            # 损失统计
            losses = [m.loss for m in recent_metrics if m.loss is not None]
            loss_stats = {}
            if losses:
                loss_stats = {
                    'mean': sum(losses) / len(losses),
                    'min': min(losses),
                    'max': max(losses),
                    'std': self._calculate_std(losses)
                }
            
            return {
                'window_size': len(recent_metrics),
                'reward_stats': reward_stats,
                'loss_stats': loss_stats,
                'episodes_analyzed': len(set(m.episode for m in recent_metrics))
            }
    
    def _calculate_derived_metrics(self, metrics: TrainingMetrics) -> None:
        """计算衍生指标
        
        Args:
            metrics: 训练指标对象
        """
        # 这里可以添加更多衍生指标的计算
        # 例如：移动平均奖励、趋势分析等
        pass
    
    def _update_statistics(self, metrics: TrainingMetrics) -> None:
        """更新统计信息
        
        Args:
            metrics: 训练指标
        """
        if metrics.reward is not None:
            if metrics.reward > self._best_reward:
                self._best_reward = metrics.reward
                self._last_progress_episode = metrics.episode
            
            if metrics.reward < self._worst_reward:
                self._worst_reward = metrics.reward
    
    def _detect_anomalies(self, metrics: TrainingMetrics) -> None:
        """检测训练异常
        
        Args:
            metrics: 当前训练指标
        """
        try:
            # 检测奖励异常下降
            if (metrics.reward is not None and 
                self._best_reward != float('-inf') and
                metrics.reward < self._best_reward * (1 - self._anomaly_thresholds['reward_drop_threshold'])):
                self._trigger_anomaly("reward_drop", metrics)
            
            # 检测长时间无进展
            episodes_since_progress = metrics.episode - self._last_progress_episode
            if episodes_since_progress > self._anomaly_thresholds['no_progress_episodes']:
                self._trigger_anomaly("no_progress", metrics)
            
            # 检测损失异常增加
            if len(self._session.metrics_history) > 10:
                recent_losses = [m.loss for m in self._session.metrics_history[-10:] if m.loss is not None]
                if (recent_losses and metrics.loss is not None and 
                    metrics.loss > sum(recent_losses) / len(recent_losses) * self._anomaly_thresholds['loss_spike_threshold']):
                    self._trigger_anomaly("loss_spike", metrics)
                    
        except Exception as e:
            self._logger.error(f"异常检测失败: {e}")
    
    def _trigger_anomaly(self, anomaly_type: str, metrics: TrainingMetrics) -> None:
        """触发异常回调
        
        Args:
            anomaly_type: 异常类型
            metrics: 相关指标
        """
        self._logger.warning(f"检测到训练异常: {anomaly_type} at episode {metrics.episode}")
        
        for callback in self._anomaly_callbacks:
            try:
                callback(anomaly_type, metrics)
            except Exception as e:
                self._logger.error(f"异常回调函数执行失败: {e}")
    
    def _calculate_std(self, values: List[float]) -> float:
        """计算标准差
        
        Args:
            values: 数值列表
            
        Returns:
            float: 标准差
        """
        if len(values) < 2:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
        return variance ** 0.5
    
    def set_anomaly_threshold(self, threshold_name: str, value: float) -> None:
        """设置异常检测阈值
        
        Args:
            threshold_name: 阈值名称
            value: 阈值
        """
        if threshold_name in self._anomaly_thresholds:
            self._anomaly_thresholds[threshold_name] = value
            self._logger.info(f"异常检测阈值 {threshold_name} 已设置为 {value}")
        else:
            self._logger.warning(f"未知的异常检测阈值: {threshold_name}")
    
    def pause_monitoring(self) -> None:
        """暂停监控"""
        with self._lock:
            self._session.phase = TrainingPhase.PAUSED
            self._logger.info("训练监控已暂停")
    
    def resume_monitoring(self) -> None:
        """恢复监控"""
        with self._lock:
            self._session.phase = TrainingPhase.TRAINING
            self._logger.info("训练监控已恢复")
    
    def is_monitoring(self) -> bool:
        """检查是否正在监控
        
        Returns:
            bool: 是否正在监控
        """
        return self._is_monitoring
    
    def log_episode(self, episode: int, reward: float, loss: float, custom_metrics: Optional[Dict[str, Any]] = None) -> None:
        """记录episode训练数据
        
        Args:
            episode: episode编号
            reward: 奖励值
            loss: 损失值
            custom_metrics: 自定义指标
        """
        with self._lock:
            metrics = TrainingMetrics(
                timestamp=datetime.now(),
                episode=episode,
                step=self._total_steps,
                phase=self._session.phase,
                reward=reward,
                loss=loss,
                custom_metrics=custom_metrics or {}
            )
            
            self._session.metrics_history.append(metrics)
            self._session.current_episode = episode
            self._total_steps += 1
            
            # 更新最佳/最差奖励
            if reward > self._best_reward:
                self._best_reward = reward
                self._last_progress_episode = episode
            if reward < self._worst_reward:
                self._worst_reward = reward
            
            # 触发回调
            for callback in self._callbacks:
                try:
                    callback(metrics)
                except Exception as e:
                    self._logger.error(f"回调执行失败: {e}")
            
            # 异常检测
            anomalies = self.detect_anomalies(metrics)
            for anomaly in anomalies:
                for callback in self._anomaly_callbacks:
                    try:
                        callback(anomaly, metrics)
                    except Exception as e:
                        self._logger.error(f"异常回调执行失败: {e}")
    
    def get_training_summary(self) -> Dict[str, Any]:
        """获取训练摘要
        
        Returns:
            Dict[str, Any]: 训练摘要信息
        """
        with self._lock:
            if not self._session.metrics_history:
                return {
                    'total_episodes': 0,
                    'total_steps': 0,
                    'best_reward': None,
                    'worst_reward': None,
                    'average_reward': None,
                    'average_loss': None
                }
            
            rewards = [m.reward for m in self._session.metrics_history if m.reward is not None]
            losses = [m.loss for m in self._session.metrics_history if m.loss is not None]
            
            return {
                'session_id': self.session_id,
                'total_episodes': len(self._session.metrics_history),
                'total_steps': self._total_steps,
                'best_reward': max(rewards) if rewards else None,
                'worst_reward': min(rewards) if rewards else None,
                'average_reward': sum(rewards) / len(rewards) if rewards else None,
                'average_loss': sum(losses) / len(losses) if losses else None,
                'current_phase': self._session.phase.value,
                'start_time': self._session.start_time.isoformat(),
                'end_time': self._session.end_time.isoformat() if self._session.end_time else None
            }
    
    def detect_anomalies(self, current_metrics: Optional[TrainingMetrics] = None) -> List[str]:
        """检测训练异常
        
        Args:
            current_metrics: 当前训练指标，如果为None则使用最新的指标
            
        Returns:
            List[str]: 检测到的异常列表
        """
        anomalies = []
        
        with self._lock:
            if len(self._session.metrics_history) < 2:
                return anomalies
            
            # 如果没有提供当前指标，使用最新的指标
            if current_metrics is None:
                current_metrics = self._session.metrics_history[-1]
            
            # 检查奖励下降
            if current_metrics.reward is not None and self._best_reward != float('-inf'):
                reward_drop = (self._best_reward - current_metrics.reward) / abs(self._best_reward)
                if reward_drop > self._anomaly_thresholds['reward_drop_threshold']:
                    anomalies.append(f"奖励大幅下降: {reward_drop:.2%}")
            
            # 检查损失激增
            recent_losses = [m.loss for m in self._session.metrics_history[-10:] if m.loss is not None]
            if current_metrics.loss is not None and recent_losses:
                avg_recent_loss = sum(recent_losses) / len(recent_losses)
                if current_metrics.loss > avg_recent_loss * self._anomaly_thresholds['loss_spike_threshold']:
                    anomalies.append(f"损失激增: {current_metrics.loss:.4f} vs 平均 {avg_recent_loss:.4f}")
            
            # 检查无进展
            episodes_since_progress = current_metrics.episode - self._last_progress_episode
            if episodes_since_progress > self._anomaly_thresholds['no_progress_episodes']:
                anomalies.append(f"长期无进展: {episodes_since_progress} episodes")
        
        return anomalies
    
    def export_metrics(self, format_type: str = 'json') -> Union[str, Dict[str, Any]]:
        """导出训练指标
        
        Args:
            format_type: 导出格式 ('json', 'dict')
            
        Returns:
            Union[str, Dict[str, Any]]: 导出的数据
        """
        with self._lock:
            data = {
                'session': {
                    'session_id': self.session_id,
                    'start_time': self._session.start_time.isoformat(),
                    'end_time': self._session.end_time.isoformat() if self._session.end_time else None,
                    'phase': self._session.phase.value,
                    'config': self._session.config
                },
                'metrics': [
                    {
                        'timestamp': m.timestamp.isoformat(),
                        'episode': m.episode,
                        'step': m.step,
                        'phase': m.phase.value,
                        'reward': m.reward,
                        'loss': m.loss,
                        'learning_rate': m.learning_rate,
                        'epsilon': m.epsilon,
                        'portfolio_value': m.portfolio_value,
                        'sharpe_ratio': m.sharpe_ratio,
                        'max_drawdown': m.max_drawdown,
                        'win_rate': m.win_rate,
                        'custom_metrics': m.custom_metrics
                    }
                    for m in self._session.metrics_history
                ]
            }
            
            if format_type == 'json':
                import json
                return json.dumps(data, indent=2)
            else:
                return data