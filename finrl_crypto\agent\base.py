"""智能体基类模块

定义所有强化学习智能体的通用接口和基础功能。
"""

import numpy as np
import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Any, Optional, Union
import logging
import os
import pickle
from collections import deque


class BaseAgent(ABC):
    """智能体基类
    
    定义所有强化学习智能体的通用接口。
    """
    
    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 learning_rate: float = 3e-4,
                 device: str = 'auto',
                 seed: Optional[int] = None,
                 **kwargs):
        """初始化智能体
        
        Args:
            state_dim: 状态空间维度
            action_dim: 动作空间维度
            learning_rate: 学习率
            device: 计算设备 ('cpu', 'cuda', 'auto')
            seed: 随机种子
            **kwargs: 其他参数
        """
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.learning_rate = learning_rate
        
        # 设置设备
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        # 设置随机种子
        if seed is not None:
            self.set_seed(seed)
        
        # 训练状态
        self.training_step = 0
        self.episode_count = 0
        
        # 性能记录
        self.episode_rewards = deque(maxlen=100)
        self.episode_lengths = deque(maxlen=100)
        self.losses = deque(maxlen=1000)
        
        # 日志记录
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化网络和优化器
        self._build_networks()
        self._build_optimizers()
    
    @abstractmethod
    def _build_networks(self):
        """构建神经网络"""
        pass
    
    @abstractmethod
    def _build_optimizers(self):
        """构建优化器"""
        pass
    
    @abstractmethod
    def act(self, state: np.ndarray, training: bool = True) -> np.ndarray:
        """根据状态选择动作
        
        Args:
            state: 当前状态
            training: 是否为训练模式
            
        Returns:
            选择的动作
        """
        pass
    
    @abstractmethod
    def learn(self,
              state: np.ndarray,
              action: np.ndarray,
              reward: float,
              next_state: np.ndarray,
              done: bool) -> Dict[str, float]:
        """学习更新
        
        Args:
            state: 当前状态
            action: 执行的动作
            reward: 获得的奖励
            next_state: 下一状态
            done: 是否结束
            
        Returns:
            学习统计信息
        """
        pass
    
    def set_seed(self, seed: int):
        """设置随机种子
        
        Args:
            seed: 随机种子
        """
        np.random.seed(seed)
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)
        
        # 确保结果可重现
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    
    def save(self, filepath: str):
        """保存智能体
        
        Args:
            filepath: 保存路径
        """
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # 保存模型状态
        save_dict = {
            'state_dim': self.state_dim,
            'action_dim': self.action_dim,
            'learning_rate': self.learning_rate,
            'training_step': self.training_step,
            'episode_count': self.episode_count,
            'episode_rewards': list(self.episode_rewards),
            'episode_lengths': list(self.episode_lengths),
            'losses': list(self.losses),
        }
        
        # 添加网络状态
        save_dict.update(self._get_save_dict())
        
        torch.save(save_dict, filepath)
        self.logger.info(f"智能体已保存到: {filepath}")
    
    def load(self, filepath: str):
        """加载智能体
        
        Args:
            filepath: 加载路径
        """
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"文件不存在: {filepath}")
        
        save_dict = torch.load(filepath, map_location=self.device)
        
        # 恢复基本属性
        self.training_step = save_dict.get('training_step', 0)
        self.episode_count = save_dict.get('episode_count', 0)
        self.episode_rewards = deque(save_dict.get('episode_rewards', []), maxlen=100)
        self.episode_lengths = deque(save_dict.get('episode_lengths', []), maxlen=100)
        self.losses = deque(save_dict.get('losses', []), maxlen=1000)
        
        # 恢复网络状态
        self._load_from_dict(save_dict)
        
        self.logger.info(f"智能体已从 {filepath} 加载")
    
    @abstractmethod
    def _get_save_dict(self) -> Dict[str, Any]:
        """获取保存字典
        
        Returns:
            保存字典
        """
        pass
    
    @abstractmethod
    def _load_from_dict(self, save_dict: Dict[str, Any]):
        """从保存字典加载
        
        Args:
            save_dict: 保存字典
        """
        pass
    
    def update_episode_stats(self, episode_reward: float, episode_length: int):
        """更新回合统计
        
        Args:
            episode_reward: 回合奖励
            episode_length: 回合长度
        """
        self.episode_rewards.append(episode_reward)
        self.episode_lengths.append(episode_length)
        self.episode_count += 1
    
    def get_stats(self) -> Dict[str, float]:
        """获取统计信息
        
        Returns:
            统计信息字典
        """
        stats = {
            'training_step': self.training_step,
            'episode_count': self.episode_count,
        }
        
        if self.episode_rewards:
            stats.update({
                'avg_episode_reward': np.mean(self.episode_rewards),
                'max_episode_reward': np.max(self.episode_rewards),
                'min_episode_reward': np.min(self.episode_rewards),
                'std_episode_reward': np.std(self.episode_rewards),
            })
        
        if self.episode_lengths:
            stats.update({
                'avg_episode_length': np.mean(self.episode_lengths),
                'max_episode_length': np.max(self.episode_lengths),
                'min_episode_length': np.min(self.episode_lengths),
            })
        
        if self.losses:
            stats.update({
                'avg_loss': np.mean(self.losses),
                'recent_loss': self.losses[-1] if self.losses else 0.0,
            })
        
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self.episode_rewards.clear()
        self.episode_lengths.clear()
        self.losses.clear()
        self.training_step = 0
        self.episode_count = 0
    
    def set_training_mode(self, training: bool = True):
        """设置训练模式
        
        Args:
            training: 是否为训练模式
        """
        # 子类可以重写此方法来设置网络的训练/评估模式
        pass
    
    def get_action_distribution(self, state: np.ndarray) -> Dict[str, Any]:
        """获取动作分布（用于分析）
        
        Args:
            state: 状态
            
        Returns:
            动作分布信息
        """
        # 默认实现，子类可以重写
        action = self.act(state, training=False)
        return {
            'action': action,
            'action_type': 'deterministic'
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息
        
        Returns:
            模型信息字典
        """
        return {
            'agent_type': self.__class__.__name__,
            'state_dim': self.state_dim,
            'action_dim': self.action_dim,
            'learning_rate': self.learning_rate,
            'device': str(self.device),
            'training_step': self.training_step,
            'episode_count': self.episode_count,
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(state_dim={self.state_dim}, action_dim={self.action_dim})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()


class ReplayBuffer:
    """经验回放缓冲区
    
    用于存储和采样经验数据。
    """
    
    def __init__(self, capacity: int, state_dim: int, action_dim: int):
        """初始化回放缓冲区
        
        Args:
            capacity: 缓冲区容量
            state_dim: 状态维度
            action_dim: 动作维度
        """
        self.capacity = capacity
        self.size = 0
        self.ptr = 0
        
        # 预分配内存
        self.states = np.zeros((capacity, state_dim), dtype=np.float32)
        self.actions = np.zeros((capacity, action_dim), dtype=np.float32)
        self.rewards = np.zeros(capacity, dtype=np.float32)
        self.next_states = np.zeros((capacity, state_dim), dtype=np.float32)
        self.dones = np.zeros(capacity, dtype=np.bool_)
    
    def add(self,
            state: np.ndarray,
            action: np.ndarray,
            reward: float,
            next_state: np.ndarray,
            done: bool):
        """添加经验
        
        Args:
            state: 状态
            action: 动作
            reward: 奖励
            next_state: 下一状态
            done: 是否结束
        """
        self.states[self.ptr] = state
        self.actions[self.ptr] = action
        self.rewards[self.ptr] = reward
        self.next_states[self.ptr] = next_state
        self.dones[self.ptr] = done
        
        self.ptr = (self.ptr + 1) % self.capacity
        self.size = min(self.size + 1, self.capacity)
    
    def sample(self, batch_size: int) -> Tuple[np.ndarray, ...]:
        """采样批次数据
        
        Args:
            batch_size: 批次大小
            
        Returns:
            采样的批次数据
        """
        indices = np.random.choice(self.size, batch_size, replace=False)
        
        return (
            self.states[indices],
            self.actions[indices],
            self.rewards[indices],
            self.next_states[indices],
            self.dones[indices]
        )
    
    def __len__(self) -> int:
        """获取缓冲区大小"""
        return self.size
    
    def is_ready(self, batch_size: int) -> bool:
        """检查是否有足够数据进行采样
        
        Args:
            batch_size: 批次大小
            
        Returns:
            是否准备好
        """
        return self.size >= batch_size


def create_mlp(input_dim: int,
               output_dim: int,
               hidden_dims: List[int],
               activation: str = 'relu',
               output_activation: Optional[str] = None,
               dropout: float = 0.0) -> nn.Module:
    """创建多层感知机
    
    Args:
        input_dim: 输入维度
        output_dim: 输出维度
        hidden_dims: 隐藏层维度列表
        activation: 激活函数
        output_activation: 输出激活函数
        dropout: Dropout概率
        
    Returns:
        MLP网络
    """
    # 激活函数映射
    activation_map = {
        'relu': nn.ReLU,
        'tanh': nn.Tanh,
        'sigmoid': nn.Sigmoid,
        'leaky_relu': nn.LeakyReLU,
        'elu': nn.ELU,
    }
    
    if activation not in activation_map:
        raise ValueError(f"不支持的激活函数: {activation}")
    
    layers = []
    dims = [input_dim] + hidden_dims + [output_dim]
    
    for i in range(len(dims) - 1):
        layers.append(nn.Linear(dims[i], dims[i + 1]))
        
        # 添加激活函数（除了最后一层）
        if i < len(dims) - 2:
            layers.append(activation_map[activation]())
            if dropout > 0:
                layers.append(nn.Dropout(dropout))
    
    # 输出激活函数
    if output_activation is not None:
        if output_activation in activation_map:
            layers.append(activation_map[output_activation]())
        else:
            raise ValueError(f"不支持的输出激活函数: {output_activation}")
    
    return nn.Sequential(*layers)