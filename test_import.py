#!/usr/bin/env python3
"""
测试SingleAssetTrainer的导入和基本功能
"""

try:
    from finrl_crypto.training.single_asset import SingleAssetTrainer
    from finrl_crypto.training.base import TrainingConfig
    from finrl_crypto.agent.base import BaseAgent
    from finrl_crypto.environment.base import BaseEnvironment
    import pandas as pd
    print("✓ 所有导入成功")
    
    # 创建mock对象
    class MockAgent(BaseAgent):
        def __init__(self):
            super().__init__(state_dim=10, action_dim=3)
        
        def _build_networks(self):
            pass
        
        def _build_optimizers(self):
            pass
        
        def _get_save_dict(self):
            return {}
        
        def _load_from_dict(self, data):
            pass
        
        def act(self, observation, deterministic=True):
            return [0.5, 0.3, 0.2]
        
        def predict(self, observation):
            return [0.5, 0.3, 0.2]
        
        def learn(self, *args, **kwargs):
            pass
        
        def save(self, path):
            pass
        
        def load(self, path):
            pass
    
    class MockEnvironment(BaseEnvironment):
        def __init__(self):
            # 创建简单的mock数据
            mock_data = pd.DataFrame({
                'timestamp': pd.date_range('2023-01-01', periods=10, freq='h'),
                'open': [100] * 10,
                'high': [105] * 10,
                'low': [95] * 10,
                'close': [102] * 10,
                'volume': [1000] * 10
            })
            super().__init__(data=mock_data)
        
        def _setup_spaces(self, state_space, action_space):
            pass
        
        def _reset_specific_state(self):
            pass
        
        def _get_state(self):
            return [1.0, 2.0, 3.0]
        
        def _execute_action(self, action):
            return True
        
        def _calculate_reward(self):
            return 0.1
        
        def reset(self):
            return [1.0, 2.0, 3.0]
        
        def step(self, action):
            return [1.0, 2.0, 3.0], 0.1, False, {}
        
        def render(self):
            pass
    
    # 测试初始化
    config = TrainingConfig()
    agent = MockAgent()
    env = MockEnvironment()
    
    trainer = SingleAssetTrainer(agent=agent, env=env, config=config)
    print("✓ SingleAssetTrainer初始化成功")
    
    # 检查属性
    print(f"✓ training_history类型: {type(trainer.training_history)}")
    print(f"✓ training_history键: {list(trainer.training_history.keys())}")
    
    # 检查特定键
    expected_keys = ['portfolio_values', 'positions', 'trades', 'returns', 'sharpe_ratios', 'max_drawdowns']
    for key in expected_keys:
        if key in trainer.training_history:
            print(f"✓ {key}: 存在")
        else:
            print(f"✗ {key}: 缺失")
    
except Exception as e:
    print(f"✗ 错误: {e}")
    import traceback
    traceback.print_exc()