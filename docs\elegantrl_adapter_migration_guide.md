# ElegantRL抽象层迁移指南

## 概述

本指南将帮助您从直接使用ElegantRL框架迁移到新的ElegantRL抽象层。新的抽象层提供了统一的接口，使代码更加模块化和可维护。

## 迁移前后对比

### 迁移前（直接使用ElegantRL）

```python
# 旧的方式 - 直接使用ElegantRL
from train.config import Arguments
from train.run import train_and_evaluate, init_agent
from drl_agents.agents import AgentSAC
from env.env_stocktrading import StockTradingEnv

# 创建环境
env = StockTradingEnv(config=env_config, env_params=env_params)

# 创建Arguments对象
args = Arguments(agent=AgentSAC, env=env)
args.cwd = './models/sac'
args.break_step = 5000
args.learner_gpus = 0

# 训练
train_and_evaluate(args)

# 预测
agent = init_agent(args, gpu_id=0)
state = env.reset()
action = agent.act(torch.as_tensor((state,), device=agent.device))
```

### 迁移后（使用抽象层）

```python
# 新的方式 - 使用抽象层
from adapters import create_trainer
from env.env_stocktrading import StockTradingEnv

# 创建训练器
trainer = create_trainer('elegantrl')

# 配置参数
agent_config = {
    'model_name': 'sac',
    'model_kwargs': {
        'learning_rate': 0.0003,
        'batch_size': 256,
        'gamma': 0.99
    }
}

env_config = {
    'env_class': StockTradingEnv,
    'price_array': price_array,
    'tech_array': tech_array,
    'env_params': env_params
}

training_config = {
    'cwd': './models/sac',
    'total_timesteps': 5000,
    'gpu_id': 0
}

# 训练
model_path = trainer.train(agent_config, env_config, training_config)

# 预测
prediction_config = {
    'model_name': 'sac',
    'net_dimension': 512,
    'gpu_id': 0
}

episode_assets = trainer.predict(model_path, env_config, prediction_config)
```

## 详细迁移步骤

### 1. 导入更改

**旧的导入：**
```python
from train.config import Arguments
from train.run import train_and_evaluate, init_agent
from drl_agents.agents import AgentSAC, AgentDDPG, AgentPPO
```

**新的导入：**
```python
from adapters import create_trainer, get_available_trainers
from adapters.trainer_factory import TrainerFactory
```

### 2. 训练代码迁移

#### 步骤2.1：创建训练器
```python
# 创建ElegantRL训练器
trainer = create_trainer('elegantrl')

# 或者使用工厂类
trainer = TrainerFactory.create_trainer('elegantrl')
```

#### 步骤2.2：配置参数结构化

**代理配置：**
```python
agent_config = {
    'model_name': 'sac',  # 支持: 'ddpg', 'td3', 'sac', 'ppo', 'a2c'
    'model_kwargs': {
        'learning_rate': 0.0003,
        'batch_size': 256,
        'gamma': 0.99,
        'net_dimension': 512,
        'target_step': 1000
    }
}
```

**环境配置：**
```python
env_config = {
    'env_class': StockTradingEnv,  # 环境类
    'price_array': price_array,   # 价格数据
    'tech_array': tech_array,     # 技术指标数据
    'env_params': env_params,     # 环境参数
    'if_log': True               # 是否记录日志
}
```

**训练配置：**
```python
training_config = {
    'cwd': './models/sac',        # 模型保存路径
    'total_timesteps': 5000,      # 训练步数
    'gpu_id': 0                   # GPU ID
}
```

#### 步骤2.3：执行训练
```python
# 验证配置（可选）
if trainer.validate_config(agent_config):
    model_path = trainer.train(agent_config, env_config, training_config)
    print(f"训练完成，模型保存在: {model_path}")
else:
    print("配置验证失败")
```

### 3. 预测代码迁移

#### 步骤3.1：配置预测参数
```python
prediction_config = {
    'model_name': 'sac',          # 模型名称
    'net_dimension': 512,         # 网络维度
    'gpu_id': 0                   # GPU ID
}
```

#### 步骤3.2：执行预测
```python
episode_assets = trainer.predict(model_path, env_config, prediction_config)
print(f"预测完成，获得 {len(episode_assets)} 个时间步的资产数据")
```

### 4. 评估代码迁移

```python
evaluation_config = {
    'model_name': 'sac',
    'net_dimension': 512,
    'gpu_id': 0
}

metrics = trainer.evaluate(model_path, env_config, evaluation_config)
print("评估结果:")
for metric, value in metrics.items():
    print(f"  {metric}: {value:.4f}")
```

## 配置参数映射表

### Arguments对象到新配置的映射

| 旧参数 (Arguments) | 新配置位置 | 说明 |
|-------------------|-----------|------|
| `args.cwd` | `training_config['cwd']` | 模型保存路径 |
| `args.break_step` | `training_config['total_timesteps']` | 训练步数 |
| `args.learner_gpus` | `training_config['gpu_id']` | GPU设备 |
| `args.learning_rate` | `agent_config['model_kwargs']['learning_rate']` | 学习率 |
| `args.batch_size` | `agent_config['model_kwargs']['batch_size']` | 批次大小 |
| `args.gamma` | `agent_config['model_kwargs']['gamma']` | 折扣因子 |
| `args.net_dim` | `agent_config['model_kwargs']['net_dimension']` | 网络维度 |
| `args.target_step` | `agent_config['model_kwargs']['target_step']` | 目标步数 |

### 模型名称映射

| 旧代理类 | 新模型名称 | 策略类型 |
|---------|-----------|----------|
| `AgentDDPG` | `'ddpg'` | off_policy |
| `AgentTD3` | `'td3'` | off_policy |
| `AgentSAC` | `'sac'` | off_policy |
| `AgentPPO` | `'ppo'` | on_policy |
| `AgentA2C` | `'a2c'` | on_policy |

## 错误处理

新的抽象层提供了更好的错误处理机制：

```python
from interfaces.trainer_interface import (
    TrainingError, EvaluationError, PredictionError,
    ModelNotFoundError, ConfigurationError
)

try:
    model_path = trainer.train(agent_config, env_config, training_config)
except ConfigurationError as e:
    print(f"配置错误: {e}")
except TrainingError as e:
    print(f"训练错误: {e}")
except Exception as e:
    print(f"未知错误: {e}")
```

## 高级功能

### 1. 获取支持的模型
```python
supported_models = trainer.get_supported_models()
print(f"支持的模型: {supported_models}")
```

### 2. 获取模型信息
```python
model_info = trainer.get_model_info('sac')
print(f"SAC模型信息: {model_info}")
```

### 3. 配置验证
```python
is_valid = trainer.validate_config(agent_config)
if not is_valid:
    print("配置无效，请检查参数")
```

### 4. 工厂模式使用
```python
# 获取所有可用的训练器
available_trainers = get_available_trainers()
print(f"可用训练器: {available_trainers}")

# 检查训练器是否支持
if TrainerFactory.is_trainer_supported('elegantrl'):
    trainer = TrainerFactory.create_trainer('elegantrl')
```

## 完整示例

以下是一个完整的迁移示例：

```python
#!/usr/bin/env python3
"""
完整的迁移示例
"""

import numpy as np
from adapters import create_trainer
from env.env_stocktrading import StockTradingEnv

def main():
    # 1. 创建训练器
    trainer = create_trainer('elegantrl')
    
    # 2. 准备数据（示例数据）
    time_steps = 100
    num_stocks = 3
    price_array = np.random.rand(time_steps, num_stocks) * 100
    tech_array = np.random.rand(time_steps, 5)
    
    env_params = {
        'initial_amount': 100000,
        'transaction_fee_percent': 0.001,
        'tech_indicator_list': ['macd', 'rsi', 'cci', 'adx', 'boll'],
        'max_stock': 100
    }
    
    # 3. 配置参数
    agent_config = {
        'model_name': 'sac',
        'model_kwargs': {
            'learning_rate': 0.0003,
            'batch_size': 256,
            'gamma': 0.99,
            'net_dimension': 512
        }
    }
    
    env_config = {
        'env_class': StockTradingEnv,
        'price_array': price_array,
        'tech_array': tech_array,
        'env_params': env_params
    }
    
    training_config = {
        'cwd': './models/sac_migrated',
        'total_timesteps': 2000,
        'gpu_id': 0
    }
    
    # 4. 训练
    print("开始训练...")
    model_path = trainer.train(agent_config, env_config, training_config)
    print(f"训练完成: {model_path}")
    
    # 5. 评估
    evaluation_config = {
        'model_name': 'sac',
        'net_dimension': 512,
        'gpu_id': 0
    }
    
    metrics = trainer.evaluate(model_path, env_config, evaluation_config)
    print(f"评估结果: {metrics}")
    
    # 6. 预测
    episode_assets = trainer.predict(model_path, env_config, evaluation_config)
    print(f"预测完成，总收益率: {(episode_assets[-1] - episode_assets[0]) / episode_assets[0] * 100:.2f}%")

if __name__ == '__main__':
    main()
```

## 注意事项

1. **配置验证**：新系统提供了配置验证功能，建议在训练前验证配置。

2. **错误处理**：使用新的异常类型进行更精确的错误处理。

3. **模型名称**：使用字符串形式的模型名称而不是类对象。

4. **路径管理**：新系统会自动创建必要的目录。

5. **GPU设置**：GPU配置现在通过配置字典传递，而不是直接设置Arguments属性。

6. **扩展性**：新的抽象层设计为可扩展的，未来可以轻松添加其他RL框架的支持。

## 迁移检查清单

- [ ] 更新导入语句
- [ ] 将Arguments配置转换为结构化配置字典
- [ ] 使用create_trainer创建训练器
- [ ] 更新训练代码使用trainer.train()
- [ ] 更新预测代码使用trainer.predict()
- [ ] 添加适当的错误处理
- [ ] 测试迁移后的代码
- [ ] 验证结果与原始实现一致

## 获取帮助

如果在迁移过程中遇到问题，可以：

1. 查看 `examples/elegantrl_adapter_example.py` 中的完整示例
2. 运行 `improvetest/test_stage1_4_elegantrl_adapter.py` 中的测试
3. 使用 `trainer.validate_config()` 验证配置
4. 查看 `trainer.get_model_info()` 获取模型信息

迁移完成后，您的代码将更加模块化、可维护，并且为未来的扩展做好了准备。