#!/usr/bin/env python3
"""
简化的SingleAssetTrainer测试
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock

# 导入需要测试的类
from finrl_crypto.training.single_asset import SingleAssetTrainer
from finrl_crypto.training.base import TrainingConfig

def test_single_asset_trainer_creation():
    """测试SingleAssetTrainer的创建"""
    # 创建mock对象
    mock_agent = Mock()
    mock_agent.predict.return_value = [0.5, 0.3, 0.2]
    mock_agent.train.return_value = {'loss': 0.1}
    
    mock_env = Mock()
    mock_env.reset.return_value = np.random.random(10)
    mock_env.step.return_value = (np.random.random(10), 1.0, False, {})
    
    # 创建配置
    config = TrainingConfig()
    
    # 创建SingleAssetTrainer
    trainer = SingleAssetTrainer(
        agent=mock_agent,
        env=mock_env,
        config=config
    )
    
    # 验证创建成功
    assert trainer is not None
    assert trainer.agent == mock_agent
    assert trainer.env == mock_env
    assert trainer.config == config
    
    print("✓ SingleAssetTrainer创建成功")

def test_single_asset_trainer_with_data():
    """测试SingleAssetTrainer与数据的交互"""
    # 创建mock对象
    mock_agent = Mock()
    mock_agent.predict.return_value = [0.5, 0.3, 0.2]
    mock_agent.train.return_value = {'loss': 0.1}
    
    mock_env = Mock()
    mock_env.reset.return_value = np.random.random(10)
    mock_env.step.return_value = (np.random.random(10), 1.0, False, {})
    
    # 创建配置
    config = TrainingConfig()
    
    # 创建SingleAssetTrainer
    trainer = SingleAssetTrainer(
        agent=mock_agent,
        env=mock_env,
        config=config
    )
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=100, freq='h')
    test_data = pd.DataFrame({
        'timestamp': dates,
        'open': np.random.uniform(100, 200, 100),
        'high': np.random.uniform(200, 300, 100),
        'low': np.random.uniform(50, 100, 100),
        'close': np.random.uniform(100, 200, 100),
        'volume': np.random.uniform(1000, 10000, 100)
    })
    
    # 设置数据和symbol
    trainer.data = test_data
    trainer.symbol = "BTCUSDT"
    
    # 验证
    assert hasattr(trainer, 'data')
    assert hasattr(trainer, 'symbol')
    assert trainer.symbol == "BTCUSDT"
    assert len(trainer.data) == 100
    
    print("✓ SingleAssetTrainer数据设置成功")

if __name__ == "__main__":
    test_single_asset_trainer_creation()
    test_single_asset_trainer_with_data()
    print("\n所有测试通过！")