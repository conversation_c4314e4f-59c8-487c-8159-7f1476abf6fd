#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件：第一阶段第一个子项 - 创建共享工具模块
文件名：test_stage1_1_shared_utils.py
目标：测试OptimizationUtils类的各种功能
"""

import unittest
import tempfile
import os
import pickle
import joblib
from unittest.mock import Mock, patch, MagicMock
import sys
import pandas as pd
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 简单的Trial类，用于测试pickle功能
class SimpleTrial:
    def __init__(self):
        self.user_attrs = {
            'name_folder': 'test_folder',
            'name_test': 'test_name'
        }
        self.number = 1

class TestOptimizationUtils(unittest.TestCase):
    """测试OptimizationUtils类的所有功能"""
    
    def setUp(self):
        """测试前的设置"""
        # 创建临时目录用于测试
        self.temp_dir = tempfile.mkdtemp()
        self.test_trial = Mock()
        self.test_trial.number = 1
        self.test_trial.user_attrs = {
            'name_folder': 'test_folder',
            'name_test': 'test_name'
        }
        
    def tearDown(self):
        """测试后的清理"""
        # 清理临时目录
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_sample_hyperparams_structure(self):
        """测试sample_hyperparams返回正确的结构"""
        from utils.optimization_utils import OptimizationUtils
        
        # 模拟trial对象
        mock_trial = Mock()
        mock_trial.suggest_categorical.return_value = 0.001
        
        # 测试配置
        config = {
            'no_candles_for_train': 1000,
            'learning_rates': [3e-2, 2.3e-2, 1.5e-2],
            'batch_sizes': [512, 1280, 2048],
            'gammas': [0.85, 0.99, 0.999]
        }
        
        erl_params, env_params = OptimizationUtils.sample_hyperparams(mock_trial, config)
        
        # 验证返回结构
        self.assertIsInstance(erl_params, dict)
        self.assertIsInstance(env_params, dict)
        
        # 验证必要的键存在
        required_erl_keys = ['learning_rate', 'batch_size', 'gamma', 'net_dimension', 'target_step']
        required_env_keys = ['lookback', 'norm_cash', 'norm_stocks', 'norm_tech']
        
        for key in required_erl_keys:
            self.assertIn(key, erl_params)
        
        for key in required_env_keys:
            self.assertIn(key, env_params)
    
    def test_set_trial_attributes(self):
        """测试set_trial_attributes正确设置属性"""
        from utils.optimization_utils import OptimizationUtils
        
        mock_trial = Mock()
        mock_study = Mock()
        
        config = {
            'model_name': 'test_model',
            'timeframe': '1h',
            'train_start_date': '2023-01-01',
            'train_end_date': '2023-06-01',
            'val_start_date': '2023-06-01',
            'val_end_date': '2023-12-01',
            'ticker_list': ['BTCUSDT', 'ETHUSDT'],
            'technical_indicators': ['RSI', 'MACD']
        }
        
        name_folder = 'test_folder'
        name_test = 'test_name'
        
        with patch('joblib.dump') as mock_dump:
            OptimizationUtils.set_trial_attributes(
                mock_trial, config, name_folder, name_test, mock_study
            )
        
        # 验证所有属性都被设置
        expected_calls = [
            ('model_name', 'test_model'),
            ('timeframe', '1h'),
            ('train_start_date', '2023-01-01'),
            ('train_end_date', '2023-06-01'),
            ('test_start_date', '2023-06-01'),
            ('test_end_date', '2023-12-01'),
            ('ticker_list', ['BTCUSDT', 'ETHUSDT']),
            ('technical_indicator_list', ['RSI', 'MACD']),
            ('name_folder', 'test_folder'),
            ('name_test', 'test_name')
        ]
        
        for attr_name, attr_value in expected_calls:
            mock_trial.set_user_attr.assert_any_call(attr_name, attr_value)
        
        # 验证study被保存
        mock_dump.assert_called_once()
    
    def test_load_saved_data(self):
        """测试load_saved_data正确加载数据"""
        from utils.optimization_utils import OptimizationUtils
        
        # 创建测试数据
        test_data = {
            'data_from_processor': pd.DataFrame({'test': [1, 2, 3]}),
            'price_array': [1.0, 2.0, 3.0],
            'tech_array': [[1, 2], [3, 4]],
            'time_array': ['2023-01-01', '2023-01-02']
        }
        
        # 创建测试文件
        data_folder = os.path.join(self.temp_dir, '1h_1500')
        os.makedirs(data_folder, exist_ok=True)
        
        for key, value in test_data.items():
            with open(os.path.join(data_folder, key), 'wb') as f:
                pickle.dump(value, f)
        
        # 测试加载
        with patch('utils.optimization_utils.OptimizationUtils._get_data_folder_path') as mock_path:
            mock_path.return_value = data_folder
            
            loaded_data = OptimizationUtils.load_saved_data('1h', 1000, 500)
        
        # 验证数据正确加载
        self.assertEqual(len(loaded_data), 4)
        data_from_processor, price_array, tech_array, time_array = loaded_data
        
        pd.testing.assert_frame_equal(data_from_processor, test_data['data_from_processor'])
        self.assertEqual(price_array, test_data['price_array'])
        self.assertEqual(tech_array, test_data['tech_array'])
        self.assertEqual(time_array, test_data['time_array'])
    
    def test_write_logs(self):
        """测试write_logs正确写入日志"""
        from utils.optimization_utils import OptimizationUtils
        
        # 创建测试目录
        name_folder = 'test_folder'
        logs_dir = os.path.join(self.temp_dir, 'train_results')
        os.makedirs(logs_dir, exist_ok=True)
        
        mock_trial = Mock()
        mock_trial.number = 42
        
        config = {
            'model_name': 'test_model',
            'cwd': '/test/cwd',
            'erl_params': {'learning_rate': 0.001},
            'env_params': {'lookback': 1}
        }
        
        with patch('utils.optimization_utils.OptimizationUtils._get_train_results_path') as mock_path:
            mock_path.return_value = logs_dir
            
            log_path = OptimizationUtils.write_logs(name_folder, config, mock_trial)
        
        # 验证日志文件被创建
        expected_log_path = os.path.join(logs_dir, name_folder, 'logs.txt')
        self.assertTrue(os.path.exists(expected_log_path))
        
        # 验证日志内容
        with open(expected_log_path, 'r') as f:
            log_content = f.read()
        
        self.assertIn('MODEL NAME: test_model', log_content)
        self.assertIn('TRIAL NUMBER: 42', log_content)
        self.assertIn('CWD: /test/cwd', log_content)
        self.assertIn("{'learning_rate': 0.001}", log_content)
        self.assertIn("{'lookback': 1}", log_content)
    
    def test_save_best_agent(self):
        """测试save_best_agent正确保存最佳代理"""
        from utils.optimization_utils import OptimizationUtils
        
        # 创建测试目录结构
        source_dir = os.path.join(self.temp_dir, 'train_results', 'cwd_tests', 'test_name')
        target_dir = os.path.join(self.temp_dir, 'train_results', 'test_folder', 'stored_agent')
        
        os.makedirs(source_dir, exist_ok=True)
        
        # 创建测试文件
        test_file = os.path.join(source_dir, 'test_agent.pkl')
        with open(test_file, 'w') as f:
            f.write('test agent data')
        
        # 创建一个简单的trial对象，避免Mock对象的pickle问题
        simple_trial = SimpleTrial()
        
        with patch('utils.optimization_utils.OptimizationUtils._get_train_results_path') as mock_path:
            mock_path.return_value = os.path.join(self.temp_dir, 'train_results')
            
            OptimizationUtils.save_best_agent(simple_trial)
        
        # 验证目标目录被创建
        self.assertTrue(os.path.exists(target_dir))
        
        # 验证文件被复制
        copied_file = os.path.join(target_dir, 'test_agent.pkl')
        self.assertTrue(os.path.exists(copied_file))
        
        # 验证trial被保存
        trial_file = os.path.join(self.temp_dir, 'train_results', 'test_folder', 'best_trial')
        self.assertTrue(os.path.exists(trial_file))

if __name__ == '__main__':
    unittest.main()