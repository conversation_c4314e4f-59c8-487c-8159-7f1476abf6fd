# FinRL-Crypto: 深度强化学习加密货币交易系统

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Python](https://img.shields.io/badge/python-3.8+-green.svg)
![PyTorch](https://img.shields.io/badge/pytorch-1.9.1-orange.svg)
![Status](https://img.shields.io/badge/status-stable-brightgreen.svg)

> 🚀 基于AAAI'23论文的防过拟合深度强化学习加密货币交易系统

## 📋 项目简介

FinRL-Crypto是一个先进的加密货币交易系统，采用深度强化学习(DRL)技术，专门解决金融强化学习中的过拟合问题。该系统通过创新的组合净化交叉验证(CPCV)方法，相比传统方法减少46%的过拟合风险，在10种不同加密货币和市场崩盘期间均表现出色。

### 🎯 核心特性

- **🛡️ 防过拟合技术**: 实现CPCV、K折交叉验证、滑窗验证三种验证方法
- **🤖 多算法支持**: 集成PPO、SAC、DDPG、TD3、A2C等主流DRL算法
- **📊 实时数据**: 支持Binance、Yahoo Finance等多数据源
- **⚡ 自动优化**: 基于Optuna的超参数自动调优
- **📈 全面回测**: 完整的回测框架和金融指标分析
- **🔍 PBO检验**: 概率回测过拟合(Probability of Backtest Overfitting)分析

### 🏆 性能表现

- ✅ 在10种加密货币上验证有效
- ✅ 市场崩盘期间表现优于基准
- ✅ 过拟合风险降低46%
- ✅ 发表于AAAI'23顶级会议

## 🚀 快速开始

### 环境要求

- Python 3.8+
- CUDA 10.2+ (可选，用于GPU加速)
- 8GB+ RAM
- 稳定的网络连接

### 安装步骤

1. **克隆仓库**
```bash
git clone https://github.com/your-username/FinRL-Crypto.git
cd FinRL-Crypto
```

2. **创建虚拟环境**
```bash
python -m venv finrl_env
source finrl_env/bin/activate  # Linux/Mac
# 或
finrl_env\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置API密钥**
```bash
# 创建环境变量文件
cp config_api_template.py config_api.py
# 编辑config_api.py，填入您的API密钥
```

### 🎮 快速上手示例

#### 基础使用流程

```python
# 1. 配置交易参数
from config_main import *

# 设置交易时间范围
trade_start_date = '2022-04-30 00:00:00'
trade_end_date = '2022-06-27 00:00:00'

# 选择加密货币
TICKER_LIST = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']

# 2. 下载数据
python 0_dl_trainval_data.py  # 下载训练验证数据
python 0_dl_trade_data.py     # 下载交易数据

# 3. 训练模型（选择一种验证方法）
python 1_optimize_cpcv.py     # 推荐：CPCV验证
# 或
python 1_optimize_kcv.py      # K折交叉验证
# 或
python 1_optimize_wf.py       # 滑窗验证

# 4. 验证结果
python 2_validate.py

# 5. 回测分析
python 4_backtest.py

# 6. PBO检验
python 5_pbo.py
```

#### 自定义配置示例

```python
# config_main.py 关键配置

# 交易设置
INITIAL_CAPITAL = 1000000  # 初始资金
BUY_COST_PCT = 0.003       # 买入手续费
SELL_COST_PCT = 0.003      # 卖出手续费

# 训练设置
no_candles_for_train = 20000  # 训练数据点数
no_candles_for_val = 5000     # 验证数据点数
TIMEFRAME = '5m'              # 时间框架

# 技术指标
TECHNICAL_INDICATORS_LIST = [
    'macd', 'rsi_30', 'cci_30', 'dx_30',
    'close_30_sma', 'close_60_sma'
]

# DRL算法选择
MODEL_NAME = 'ppo'  # 可选: ppo, sac, ddpg, td3, a2c
```

## 🏗️ 系统架构

```mermaid
graph TB
    subgraph "数据层 Data Layer"
        A[Binance API] --> B[数据处理器]
        C[Yahoo Finance] --> B
        B --> D[技术指标计算]
    end
    
    subgraph "环境层 Environment Layer"
        E[加密货币交易环境]
        F[状态空间构建]
        G[动作空间定义]
        D --> E
        E --> F
        E --> G
    end
    
    subgraph "算法层 Algorithm Layer"
        H[PPO Agent]
        I[SAC Agent]
        J[DDPG Agent]
        K[TD3 Agent]
        L[A2C Agent]
    end
    
    subgraph "训练层 Training Layer"
        M[ElegantRL框架]
        N[Optuna优化]
        O[CPCV验证]
        P[超参数调优]
    end
    
    subgraph "评估层 Evaluation Layer"
        Q[回测引擎]
        R[性能指标]
        S[PBO分析]
        T[可视化报告]
    end
    
    F --> H
    F --> I
    F --> J
    F --> K
    F --> L
    
    H --> M
    I --> M
    J --> M
    K --> M
    L --> M
    
    M --> N
    M --> O
    N --> P
    
    M --> Q
    Q --> R
    R --> S
    S --> T
```

## 📊 核心模块说明

### 数据处理模块
- **BinanceProcessor**: Binance数据获取和预处理
- **YahooProcessor**: Yahoo Finance数据获取
- **技术指标计算**: 基于TA-Lib的技术分析

### 强化学习模块
- **环境定义**: 符合OpenAI Gym标准的交易环境
- **多算法支持**: PPO、SAC、DDPG、TD3、A2C
- **ElegantRL集成**: 高效的DRL训练框架

### 验证模块
- **CPCV**: 组合净化交叉验证，防止数据泄露
- **K-Fold CV**: 传统K折交叉验证
- **Walk-Forward**: 滑窗验证方法

### 评估模块
- **回测引擎**: 完整的历史数据回测
- **金融指标**: 夏普比率、最大回撤、年化收益等
- **PBO分析**: 概率回测过拟合检验

## 📈 使用案例

### 案例1：比特币交易策略
```python
# 专注比特币的高频交易策略
TICKER_LIST = ['BTCUSDT']
TIMEFRAME = '1m'
no_candles_for_train = 50000
MODEL_NAME = 'sac'  # SAC适合连续动作空间
```

### 案例2：多币种投资组合
```python
# 分散投资多种主流加密货币
TICKER_LIST = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'DOTUSDT']
TIMEFRAME = '1h'
no_candles_for_train = 10000
MODEL_NAME = 'ppo'  # PPO稳定性好
```

### 案例3：市场崩盘期间的防御策略
```python
# 针对熊市的保守策略
trade_start_date = '2022-05-01 00:00:00'  # 市场下跌期
trade_end_date = '2022-07-01 00:00:00'
BUY_COST_PCT = 0.001  # 降低交易成本
SELL_COST_PCT = 0.001
```

## 🔧 高级配置

### 超参数调优
```python
# 在1_optimize_*.py中自定义搜索空间
def sample_hyperparams(trial):
    return {
        'learning_rate': trial.suggest_loguniform('lr', 1e-5, 1e-2),
        'batch_size': trial.suggest_categorical('batch_size', [128, 256, 512]),
        'net_dim': trial.suggest_categorical('net_dim', [128, 256, 512]),
        'gamma': trial.suggest_uniform('gamma', 0.9, 0.999)
    }
```

### 自定义技术指标
```python
# 添加新的技术指标
TECHNICAL_INDICATORS_LIST = [
    'macd', 'rsi_30', 'cci_30', 'dx_30',
    'close_30_sma', 'close_60_sma',
    'bb_upper', 'bb_lower',  # 布林带
    'atr_14',                # 平均真实波幅
    'obv'                    # 成交量平衡指标
]
```

## 📋 API参考

### 核心类

#### CryptoEnvAlpaca
```python
class CryptoEnvAlpaca:
    def __init__(self, config, env_params, initial_capital=1000000):
        """初始化交易环境"""
    
    def reset(self):
        """重置环境到初始状态"""
    
    def step(self, actions):
        """执行交易动作"""
        return state, reward, done, info
```

#### DRLAgent
```python
class DRLAgent:
    def __init__(self, env, price_array, tech_array, env_params):
        """初始化DRL代理"""
    
    def get_model(self, model_name, model_kwargs):
        """获取指定的DRL模型"""
    
    def train_model(self, model, cwd, total_timesteps):
        """训练模型"""
```

## 🧪 测试

```bash
# 运行单元测试
python -m pytest tests/

# 运行集成测试
python tests/integration_test.py

# 性能基准测试
python tests/benchmark_test.py
```

## 📊 性能监控

### 实时监控指标
- 累计收益率
- 夏普比率
- 最大回撤
- 胜率
- 平均持仓时间

### 日志分析
```bash
# 查看训练日志
tail -f logs/training.log

# 分析性能指标
python utils/analyze_logs.py --log-dir logs/
```

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

### 开发环境设置
1. Fork本仓库
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 安装开发依赖: `pip install -r requirements-dev.txt`
4. 运行预提交检查: `pre-commit install`

### 代码规范
- 遵循PEP 8代码风格
- 添加类型注解
- 编写单元测试
- 更新文档

### 提交流程
1. 提交更改: `git commit -m 'Add amazing feature'`
2. 推送分支: `git push origin feature/amazing-feature`
3. 创建Pull Request

### 贡献类型
- 🐛 Bug修复
- ✨ 新功能
- 📚 文档改进
- 🎨 代码重构
- ⚡ 性能优化
- 🧪 测试覆盖

## 📄 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 📞 联系我们

- **作者**: Berend Gort, Xiao-Yang Liu
- **论文**: [Deep reinforcement learning for cryptocurrency trading: Practical approach to address backtest overfitting](https://arxiv.org/abs/2209.05559)
- **会议**: AAAI'23 Bridge on AI for Financial Services
- **社区**: [AI4Finance Foundation](https://github.com/AI4Finance-Foundation)

## 🙏 致谢

- [ElegantRL](https://github.com/AI4Finance-Foundation/ElegantRL) - 强化学习框架
- [FinRL](https://github.com/AI4Finance-Foundation/FinRL) - 金融强化学习库
- [Optuna](https://optuna.org/) - 超参数优化框架
- [TA-Lib](https://ta-lib.org/) - 技术分析库

## 📚 相关资源

- [论文原文](https://arxiv.org/abs/2209.05559)
- [AAAI'23会议](https://aaai.org/Conferences/AAAI-23/)
- [AI4Finance社区](https://github.com/AI4Finance-Foundation)
- [强化学习教程](https://spinningup.openai.com/)

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！

📈 **免责声明**: 本软件仅用于研究和教育目的。加密货币交易存在高风险，请谨慎投资。