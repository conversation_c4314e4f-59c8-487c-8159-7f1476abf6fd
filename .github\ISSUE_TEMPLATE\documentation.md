---
name: Documentation
about: 报告文档问题或建议文档改进
title: '[DOCS] '
labels: 'documentation'
assignees: ''
---

# 📚 文档问题/改进

## 📋 问题类型

- [ ] 文档错误
- [ ] 文档缺失
- [ ] 文档过时
- [ ] 文档不清楚
- [ ] 文档改进建议
- [ ] 新文档请求

## 📍 文档位置

**文件路径或URL：**

**章节/段落：**

## 🔍 问题描述

### 当前状态
描述当前文档的问题或不足：

### 期望状态
描述你期望的文档应该是什么样的：

## 📝 建议的改进

### 内容改进
```markdown
建议的文档内容（如果适用）
```

### 结构改进
- 
- 
- 

## 👥 目标受众

这个文档改进主要针对哪些用户？

- [ ] 新用户/初学者
- [ ] 有经验的用户
- [ ] 开发者
- [ ] 系统管理员
- [ ] API用户
- [ ] 贡献者

## 📊 优先级

- [ ] 高（阻塞用户使用）
- [ ] 中（影响用户体验）
- [ ] 低（改进建议）

## 🔗 相关资源

- 相关代码：
- 相关Issue：
- 参考文档：
- 外部资源：

## 📋 文档类型

- [ ] README
- [ ] API文档
- [ ] 用户指南
- [ ] 开发者文档
- [ ] 安装指南
- [ ] 配置文档
- [ ] 故障排除
- [ ] 示例/教程
- [ ] 变更日志
- [ ] 贡献指南

## 🎯 具体改进建议

### 添加内容
- 
- 
- 

### 修改内容
- 
- 
- 

### 删除内容
- 
- 
- 

### 重组结构
- 
- 
- 

## 📸 截图/示例

如果适用，请提供截图或示例来说明问题：

## 🌍 国际化考虑

- [ ] 需要多语言支持
- [ ] 文化敏感性考虑
- [ ] 本地化需求

## ♿ 可访问性考虑

- [ ] 屏幕阅读器友好
- [ ] 色彩对比度
- [ ] 字体大小
- [ ] 键盘导航

## 📱 响应式设计

- [ ] 移动设备友好
- [ ] 平板设备优化
- [ ] 桌面设备优化

## 🔍 SEO考虑

- [ ] 搜索引擎优化
- [ ] 关键词优化
- [ ] 元数据改进

## 📊 分析和指标

如何衡量文档改进的效果？

- [ ] 用户反馈
- [ ] 页面访问量
- [ ] 搜索查询
- [ ] 支持请求减少
- [ ] 用户完成率

## 🧪 测试计划

如何验证文档改进？

- [ ] 用户测试
- [ ] 同行评审
- [ ] 技术审查
- [ ] 可用性测试

## 📅 时间估算

预估的文档工作量：
- [ ] 1-2小时
- [ ] 半天
- [ ] 1天
- [ ] 2-3天
- [ ] 1周以上

## 📝 额外上下文

任何其他相关信息：

## ✅ 检查清单

- [ ] 我已经搜索了现有的文档issues
- [ ] 我已经检查了最新版本的文档
- [ ] 我已经清楚地描述了问题或改进建议
- [ ] 我已经提供了具体的改进方案
- [ ] 我已经考虑了目标受众

## 🏷️ 相关标签

- [ ] api-docs
- [ ] user-guide
- [ ] developer-docs
- [ ] tutorial
- [ ] examples
- [ ] installation
- [ ] configuration
- [ ] troubleshooting
- [ ] contributing
- [ ] changelog