# Pre-commit hooks配置
# 安装: pip install pre-commit
# 设置: pre-commit install
# 运行: pre-commit run --all-files

repos:
  # 基础代码质量检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        args: [--markdown-linebreak-ext=md]
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-symlinks
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable
      - id: debug-statements
      - id: detect-aws-credentials
      - id: detect-private-key
      - id: fix-byte-order-marker
      - id: mixed-line-ending
        args: ['--fix=lf']
      - id: requirements-txt-fixer
      - id: sort-simple-yaml
      - id: file-contents-sorter
        files: '^(.*\.gitignore|.*\.dockerignore)$'

  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        language_version: python3
        args: ['--line-length=88', '--target-version=py38']

  # Python导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ['--profile=black', '--line-length=88']

  # Python代码检查
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: ['--max-line-length=88', '--extend-ignore=E203,W503']
        additional_dependencies:
          - flake8-docstrings
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-simplify
          - flake8-bandit
          - flake8-pytest-style
          - flake8-return
          - flake8-use-fstring
          - flake8-cognitive-complexity
          - flake8-expression-complexity
          - flake8-functions
          - flake8-variables-names
          - flake8-annotations
          - flake8-broken-line
          - flake8-commas
          - flake8-quotes
          - flake8-string-format
          - flake8-tidy-imports
          - flake8-type-checking
          - flake8-unused-arguments
          - flake8-pie
          - flake8-print
          - flake8-debugger
          - flake8-eradicate
          - flake8-fixme
          - flake8-todo
          - flake8-logging-format
          - flake8-secure-coding-standard
          - flake8-sql
          - flake8-requirements
          - flake8-mock
          - flake8-datetime
          - flake8-encodings
          - flake8-pep3101
          - flake8-rst-docstrings
          - flake8-markdown

  # Python类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.5.1
    hooks:
      - id: mypy
        args: ['--ignore-missing-imports', '--no-strict-optional']
        additional_dependencies:
          - types-requests
          - types-PyYAML
          - types-python-dateutil
          - types-setuptools

  # Python安全检查
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: ['-r', '.', '-f', 'json', '-o', 'bandit-report.json']
        exclude: ^tests/

  # Python依赖安全检查
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check
        files: requirements.*\.txt$

  # Python文档字符串检查
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        args: ['--convention=google']
        exclude: ^(tests/|docs/|examples/)

  # Python复杂度检查
  - repo: https://github.com/xenon-python/xenon
    rev: v0.9.0
    hooks:
      - id: xenon
        args: ['--max-average=A', '--max-modules=B', '--max-absolute=B']

  # Jupyter Notebook清理
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.7.0
    hooks:
      - id: nbqa-black
        additional_dependencies: [black==23.7.0]
      - id: nbqa-isort
        additional_dependencies: [isort==5.12.0]
      - id: nbqa-flake8
        additional_dependencies: [flake8==6.0.0]

  # 清理Jupyter Notebook输出
  - repo: https://github.com/kynan/nbstripout
    rev: 0.6.1
    hooks:
      - id: nbstripout

  # YAML格式化
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0
    hooks:
      - id: prettier
        types_or: [yaml, markdown, json]
        exclude: ^(.*\.md|.*\.json)$

  # Markdown检查
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.35.0
    hooks:
      - id: markdownlint
        args: ['--fix']

  # 提交消息检查
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.6.0
    hooks:
      - id: commitizen
        stages: [commit-msg]

  # 密钥检测
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']
        exclude: package.lock.json

  # Shell脚本检查
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.5
    hooks:
      - id: shellcheck

  # Dockerfile检查
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint

  # 许可证检查
  - repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.5.4
    hooks:
      - id: insert-license
        files: \.py$
        args:
          - --license-filepath
          - LICENSE_HEADER.txt
          - --comment-style
          - '#'

# 全局配置
default_stages: [commit]
fail_fast: false
minimum_pre_commit_version: '2.20.0'

# CI配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false