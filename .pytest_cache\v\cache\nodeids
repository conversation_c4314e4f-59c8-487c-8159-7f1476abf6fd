["chonggoutest/test_backtest_module.py::TestBacktestComparison::test_performance_ranking", "chonggoutest/test_backtest_module.py::TestBacktestComparison::test_strategy_comparison", "chonggoutest/test_backtest_module.py::TestBacktestMetrics::test_advanced_metrics_calculation", "chonggoutest/test_backtest_module.py::TestBacktestMetrics::test_basic_metrics_calculation", "chonggoutest/test_backtest_module.py::TestBacktestMetrics::test_benchmark_comparison_metrics", "chonggoutest/test_backtest_module.py::TestBacktestMetrics::test_risk_metrics_calculation", "chonggoutest/test_backtest_module.py::TestBacktestOptimization::test_monte_carlo_simulation", "chonggoutest/test_backtest_module.py::TestBacktestOptimization::test_walk_forward_analysis", "chonggoutest/test_backtest_module.py::TestBacktestResult::test_backtest_result_creation", "chonggoutest/test_backtest_module.py::TestBacktestResult::test_drawdown_analysis", "chonggoutest/test_backtest_module.py::TestBacktestResult::test_monthly_returns", "chonggoutest/test_backtest_module.py::TestBacktestResult::test_performance_metrics_calculation", "chonggoutest/test_backtest_module.py::TestBacktestResult::test_trade_analysis", "chonggoutest/test_backtest_module.py::TestBacktestVisualization::test_drawdown_plot", "chonggoutest/test_backtest_module.py::TestBacktestVisualization::test_portfolio_value_plot", "chonggoutest/test_backtest_module.py::TestBacktestVisualization::test_returns_distribution_plot", "chonggoutest/test_backtest_module.py::TestBacktester::test_backtest_execution", "chonggoutest/test_backtest_module.py::TestBacktester::test_backtester_creation", "chonggoutest/test_backtest_module.py::TestBacktester::test_commission_and_slippage", "chonggoutest/test_backtest_module.py::TestBacktester::test_position_sizing", "chonggoutest/test_backtest_module.py::TestBacktester::test_risk_management", "chonggoutest/test_risk_module.py::TestPortfolioRisk::test_component_risk_contribution", "chonggoutest/test_risk_module.py::TestPortfolioRisk::test_diversification_ratio", "chonggoutest/test_risk_module.py::TestPortfolioRisk::test_marginal_risk_contribution", "chonggoutest/test_risk_module.py::TestPortfolioRisk::test_portfolio_risk_creation", "chonggoutest/test_risk_module.py::TestPortfolioRisk::test_portfolio_var", "chonggoutest/test_risk_module.py::TestPortfolioRisk::test_portfolio_volatility", "chonggoutest/test_risk_module.py::TestPositionSizer::test_kelly_criterion", "chonggoutest/test_risk_module.py::TestPositionSizer::test_position_size_constraints", "chonggoutest/test_risk_module.py::TestPositionSizer::test_position_sizer_creation", "chonggoutest/test_risk_module.py::TestPositionSizer::test_risk_parity_sizing", "chonggoutest/test_risk_module.py::TestPositionSizer::test_volatility_targeting", "chonggoutest/test_risk_module.py::TestRiskManager::test_correlation_check", "chonggoutest/test_risk_module.py::TestRiskManager::test_portfolio_risk_check", "chonggoutest/test_risk_module.py::TestRiskManager::test_position_risk_check", "chonggoutest/test_risk_module.py::TestRiskManager::test_risk_manager_creation", "chonggoutest/test_risk_module.py::TestRiskManager::test_stop_loss_take_profit", "chonggoutest/test_risk_module.py::TestRiskMetrics::test_cvar_calculation", "chonggoutest/test_risk_module.py::TestRiskMetrics::test_downside_risk_metrics", "chonggoutest/test_risk_module.py::TestRiskMetrics::test_maximum_drawdown", "chonggoutest/test_risk_module.py::TestRiskMetrics::test_tail_risk_metrics", "chonggoutest/test_risk_module.py::TestRiskMetrics::test_var_calculation", "chonggoutest/test_risk_module.py::TestRiskReporting::test_risk_alerts", "chonggoutest/test_risk_module.py::TestRiskReporting::test_risk_dashboard", "chonggoutest/test_risk_module.py::TestStressTest::test_historical_stress_test", "chonggoutest/test_risk_module.py::TestStressTest::test_monte_carlo_stress_test", "chonggoutest/test_risk_module.py::TestStressTest::test_scenario_stress_test", "chonggoutest/test_risk_module.py::TestStressTest::test_stress_test_creation", "chonggoutest/test_risk_module.py::TestStressTest::test_stress_test_reporting", "chonggoutest/test_strategy_module.py::TestBaseStrategy::test_base_strategy_interface", "chonggoutest/test_strategy_module.py::TestBaseStrategy::test_signal_generation_interface", "chonggoutest/test_strategy_module.py::TestBaseStrategy::test_strategy_initialization", "chonggoutest/test_strategy_module.py::TestMovingAverageStrategy::test_moving_average_parameter_update", "chonggoutest/test_strategy_module.py::TestMovingAverageStrategy::test_moving_average_signal_generation", "chonggoutest/test_strategy_module.py::TestMovingAverageStrategy::test_moving_average_strategy_creation", "chonggoutest/test_strategy_module.py::TestRLStrategy::test_rl_model_integration", "chonggoutest/test_strategy_module.py::TestRLStrategy::test_rl_strategy_creation", "chonggoutest/test_strategy_module.py::TestRSIStrategy::test_rsi_signal_generation", "chonggoutest/test_strategy_module.py::TestRSIStrategy::test_rsi_strategy_creation", "chonggoutest/test_strategy_module.py::TestStrategyComposition::test_composite_signal_generation", "chonggoutest/test_strategy_module.py::TestStrategyComposition::test_strategy_composition_creation", "chonggoutest/test_strategy_module.py::TestStrategyFactory::test_strategy_creation_from_factory", "chonggoutest/test_strategy_module.py::TestStrategyFactory::test_strategy_factory_creation", "chonggoutest/test_strategy_module.py::TestStrategyOptimization::test_parameter_optimization", "chonggoutest/test_strategy_module.py::TestStrategyOptimization::test_strategy_optimizer_creation", "chonggoutest/test_strategy_module.py::TestStrategyPerformanceMetrics::test_benchmark_comparison", "chonggoutest/test_strategy_module.py::TestStrategyPerformanceMetrics::test_strategy_performance_calculation", "chonggoutest/test_visualization_module.py::TestChartGenerator::test_candlestick_chart", "chonggoutest/test_visualization_module.py::TestChartGenerator::test_chart_customization", "chonggoutest/test_visualization_module.py::TestChartGenerator::test_chart_generator_creation", "chonggoutest/test_visualization_module.py::TestChartGenerator::test_heatmap", "chonggoutest/test_visualization_module.py::TestChartGenerator::test_line_chart", "chonggoutest/test_visualization_module.py::TestChartGenerator::test_scatter_plot", "chonggoutest/test_visualization_module.py::TestInteractiveDashboard::test_dashboard_components", "chonggoutest/test_visualization_module.py::TestInteractiveDashboard::test_dashboard_creation", "chonggoutest/test_visualization_module.py::TestInteractiveDashboard::test_dashboard_export", "chonggoutest/test_visualization_module.py::TestInteractiveDashboard::test_real_time_updates", "chonggoutest/test_visualization_module.py::TestPerformanceVisualizer::test_cumulative_returns_plot", "chonggoutest/test_visualization_module.py::TestPerformanceVisualizer::test_drawdown_plot", "chonggoutest/test_visualization_module.py::TestPerformanceVisualizer::test_monthly_returns_heatmap", "chonggoutest/test_visualization_module.py::TestPerformanceVisualizer::test_performance_metrics_table", "chonggoutest/test_visualization_module.py::TestPerformanceVisualizer::test_performance_visualizer_creation", "chonggoutest/test_visualization_module.py::TestPerformanceVisualizer::test_returns_distribution_plot", "chonggoutest/test_visualization_module.py::TestPerformanceVisualizer::test_rolling_metrics_plot", "chonggoutest/test_visualization_module.py::TestPortfolioVisualizer::test_asset_allocation_over_time", "chonggoutest/test_visualization_module.py::TestPortfolioVisualizer::test_correlation_network_plot", "chonggoutest/test_visualization_module.py::TestPortfolioVisualizer::test_efficient_frontier_plot", "chonggoutest/test_visualization_module.py::TestPortfolioVisualizer::test_portfolio_composition_plot", "chonggoutest/test_visualization_module.py::TestPortfolioVisualizer::test_portfolio_performance_attribution", "chonggoutest/test_visualization_module.py::TestPortfolioVisualizer::test_portfolio_visualizer_creation", "chonggoutest/test_visualization_module.py::TestRiskVisualizer::test_correlation_heatmap", "chonggoutest/test_visualization_module.py::TestRiskVisualizer::test_risk_contribution_plot", "chonggoutest/test_visualization_module.py::TestRiskVisualizer::test_risk_visualizer_creation", "chonggoutest/test_visualization_module.py::TestRiskVisualizer::test_stress_test_visualization", "chonggoutest/test_visualization_module.py::TestRiskVisualizer::test_tail_risk_visualization", "chonggoutest/test_visualization_module.py::TestRiskVisualizer::test_var_plot", "chonggoutest/test_visualization_module.py::TestVisualizationUtils::test_chart_annotations", "chonggoutest/test_visualization_module.py::TestVisualizationUtils::test_chart_styling", "chonggoutest/test_visualization_module.py::TestVisualizationUtils::test_color_palette_generation", "chonggoutest/test_visualization_module.py::TestVisualizationUtils::test_data_formatting", "improvetest/test_config_manager_unit.py::TestConfigLoadError::test_config_load_error_creation", "improvetest/test_config_manager_unit.py::TestConfigLoader::test_abstract_base_class", "improvetest/test_config_manager_unit.py::TestConfigLoader::test_load_empty_config", "improvetest/test_config_manager_unit.py::TestConfigLoader::test_load_invalid_config", "improvetest/test_config_manager_unit.py::TestConfigLoader::test_load_valid_config", "improvetest/test_config_manager_unit.py::TestConfigManagerIntegration::test_environment_variable_override", "improvetest/test_config_manager_unit.py::TestConfigManagerIntegration::test_json_config_loading", "improvetest/test_config_manager_unit.py::TestConfigManagerIntegration::test_yaml_config_loading", "improvetest/test_config_manager_unit.py::TestConfigMerging::test_deep_merge_configs", "improvetest/test_config_manager_unit.py::TestConfigValidation::test_schema_validation_failure", "improvetest/test_config_manager_unit.py::TestConfigValidation::test_schema_validation_success", "improvetest/test_config_manager_unit.py::TestConfigValidationError::test_config_validation_error_creation", "improvetest/test_dependency_injection_unit.py::TestDependencyInjectionContainer::test_circular_dependency_detection", "improvetest/test_dependency_injection_unit.py::TestDependencyInjectionContainer::test_dependency_injection", "improvetest/test_dependency_injection_unit.py::TestDependencyInjectionContainer::test_service_registration", "improvetest/test_dependency_injection_unit.py::TestDependencyInjectionContainer::test_singleton_lifetime", "improvetest/test_dependency_injection_unit.py::TestDependencyInjectionContainer::test_thread_safety", "improvetest/test_dependency_injection_unit.py::TestDependencyInjectionContainer::test_transient_lifetime", "improvetest/test_dependency_injection_unit.py::TestServiceConfiguration::test_configuration_based_registration", "improvetest/test_dependency_injection_unit.py::TestServiceDescriptor::test_service_descriptor_creation", "improvetest/test_dependency_injection_unit.py::TestServiceDescriptor::test_service_descriptor_with_interface", "improvetest/test_dependency_injection_unit.py::TestServiceLifetime::test_all_lifetimes_exist", "improvetest/test_dependency_injection_unit.py::TestServiceLifetime::test_scoped_lifetime", "improvetest/test_dependency_injection_unit.py::TestServiceLifetime::test_singleton_lifetime", "improvetest/test_dependency_injection_unit.py::TestServiceLifetime::test_transient_lifetime", "improvetest/test_phase1_agent_module.py::TestA2CAgent::test_a2c_actor_critic_networks", "improvetest/test_phase1_agent_module.py::TestA2CAgent::test_a2c_agent_initialization", "improvetest/test_phase1_agent_module.py::TestA2CAgent::test_a2c_policy_gradient", "improvetest/test_phase1_agent_module.py::TestA2CAgent::test_a2c_value_estimation", "improvetest/test_phase1_agent_module.py::TestAgentFactory::test_agent_with_custom_config", "improvetest/test_phase1_agent_module.py::TestAgentFactory::test_create_a2c_agent", "improvetest/test_phase1_agent_module.py::TestAgentFactory::test_create_ppo_agent", "improvetest/test_phase1_agent_module.py::TestAgentFactory::test_create_sac_agent", "improvetest/test_phase1_agent_module.py::TestAgentFactory::test_invalid_agent_type", "improvetest/test_phase1_agent_module.py::TestAgentModuleIntegration::test_agent_evaluation", "improvetest/test_phase1_agent_module.py::TestAgentModuleIntegration::test_complete_training_pipeline", "improvetest/test_phase1_agent_module.py::TestAgentModuleIntegration::test_multi_agent_training", "improvetest/test_phase1_agent_module.py::TestBaseAgent::test_agent_configuration", "improvetest/test_phase1_agent_module.py::TestBaseAgent::test_base_agent_abstract_methods", "improvetest/test_phase1_agent_module.py::TestBaseAgent::test_base_agent_initialization", "improvetest/test_phase1_agent_module.py::TestModelManager::test_load_model", "improvetest/test_phase1_agent_module.py::TestModelManager::test_model_manager_initialization", "improvetest/test_phase1_agent_module.py::TestModelManager::test_model_metadata", "improvetest/test_phase1_agent_module.py::TestModelManager::test_model_versioning", "improvetest/test_phase1_agent_module.py::TestModelManager::test_save_model", "improvetest/test_phase1_agent_module.py::TestMultiAgentManager::test_add_agent", "improvetest/test_phase1_agent_module.py::TestMultiAgentManager::test_agent_coordination", "improvetest/test_phase1_agent_module.py::TestMultiAgentManager::test_agent_performance_tracking", "improvetest/test_phase1_agent_module.py::TestMultiAgentManager::test_ensemble_prediction", "improvetest/test_phase1_agent_module.py::TestMultiAgentManager::test_get_agent_predictions", "improvetest/test_phase1_agent_module.py::TestMultiAgentManager::test_multi_agent_manager_initialization", "improvetest/test_phase1_agent_module.py::TestMultiAgentManager::test_remove_agent", "improvetest/test_phase1_agent_module.py::TestPPOAgent::test_ppo_advantage_calculation", "improvetest/test_phase1_agent_module.py::TestPPOAgent::test_ppo_agent_initialization", "improvetest/test_phase1_agent_module.py::TestPPOAgent::test_ppo_policy_update", "improvetest/test_phase1_agent_module.py::TestPPOAgent::test_ppo_prediction", "improvetest/test_phase1_agent_module.py::TestPPOAgent::test_ppo_training", "improvetest/test_phase1_agent_module.py::TestSACAgent::test_sac_agent_initialization", "improvetest/test_phase1_agent_module.py::TestSACAgent::test_sac_entropy_regularization", "improvetest/test_phase1_agent_module.py::TestSACAgent::test_sac_soft_q_learning", "improvetest/test_phase1_agent_module.py::TestSACAgent::test_sac_temperature_parameter", "improvetest/test_phase1_data_module.py::TestBinanceProcessor::test_binance_processor_initialization", "improvetest/test_phase1_data_module.py::TestBinanceProcessor::test_data_preprocessing", "improvetest/test_phase1_data_module.py::TestBinanceProcessor::test_data_validation", "improvetest/test_phase1_data_module.py::TestBinanceProcessor::test_fetch_data", "improvetest/test_phase1_data_module.py::TestDataManager::test_add_data_source", "improvetest/test_phase1_data_module.py::TestDataManager::test_data_manager_initialization", "improvetest/test_phase1_data_module.py::TestDataManager::test_get_data", "improvetest/test_phase1_data_module.py::TestDataManager::test_merge_data_sources", "improvetest/test_phase1_data_module.py::TestDataModuleIntegration::test_data_quality_checks", "improvetest/test_phase1_data_module.py::TestDataModuleIntegration::test_end_to_end_data_pipeline", "improvetest/test_phase1_data_module.py::TestDataProcessor::test_data_processor_abstract_methods", "improvetest/test_phase1_data_module.py::TestDataProcessor::test_data_processor_initialization", "improvetest/test_phase1_data_module.py::TestDataProcessor::test_data_processor_with_optional_params", "improvetest/test_phase1_data_module.py::TestTechnicalIndicators::test_bollinger_bands_calculation", "improvetest/test_phase1_data_module.py::TestTechnicalIndicators::test_ema_calculation", "improvetest/test_phase1_data_module.py::TestTechnicalIndicators::test_macd_calculation", "improvetest/test_phase1_data_module.py::TestTechnicalIndicators::test_rsi_calculation", "improvetest/test_phase1_data_module.py::TestTechnicalIndicators::test_sma_calculation", "improvetest/test_phase1_environment_module.py::TestBaseEnvironment::test_base_environment_initialization", "improvetest/test_phase1_environment_module.py::TestBaseEnvironment::test_base_environment_with_optional_params", "improvetest/test_phase1_environment_module.py::TestBaseEnvironment::test_gym_env_inheritance", "improvetest/test_phase1_environment_module.py::TestCryptoTradingEnvironment::test_action_space_definition", "improvetest/test_phase1_environment_module.py::TestCryptoTradingEnvironment::test_observation_space_definition", "improvetest/test_phase1_environment_module.py::TestCryptoTradingEnvironment::test_reset_functionality", "improvetest/test_phase1_environment_module.py::TestCryptoTradingEnvironment::test_reward_calculation", "improvetest/test_phase1_environment_module.py::TestCryptoTradingEnvironment::test_step_functionality", "improvetest/test_phase1_environment_module.py::TestCryptoTradingEnvironment::test_trading_environment_initialization", "improvetest/test_phase1_environment_module.py::TestCryptoTradingEnvironment::test_transaction_cost_calculation", "improvetest/test_phase1_environment_module.py::TestEnvironmentIntegration::test_complete_trading_episode", "improvetest/test_phase1_environment_module.py::TestEnvironmentIntegration::test_environment_consistency", "improvetest/test_phase1_environment_module.py::TestEnvironmentIntegration::test_environment_with_different_configs", "improvetest/test_phase1_environment_module.py::TestEnvironmentStateManagement::test_episode_termination", "improvetest/test_phase1_environment_module.py::TestEnvironmentStateManagement::test_state_normalization", "improvetest/test_phase1_environment_module.py::TestEnvironmentStateManagement::test_state_representation", "improvetest/test_phase1_environment_module.py::TestPortfolioEnvironment::test_multi_asset_action_space", "improvetest/test_phase1_environment_module.py::TestPortfolioEnvironment::test_portfolio_environment_initialization", "improvetest/test_phase1_environment_module.py::TestPortfolioEnvironment::test_portfolio_rebalancing", "improvetest/test_phase1_environment_module.py::TestPortfolioEnvironment::test_portfolio_value_calculation", "improvetest/test_phase1_training_module.py::TestBaseTrainer::test_evaluate", "improvetest/test_phase1_training_module.py::TestBaseTrainer::test_train_episode", "improvetest/test_phase1_training_module.py::TestBaseTrainer::test_train_multiple_episodes", "improvetest/test_phase1_training_module.py::TestBaseTrainer::test_trainer_initialization", "improvetest/test_phase1_training_module.py::TestBaseTrainer::test_training_callbacks", "improvetest/test_phase1_training_module.py::TestMultiAssetTrainer::test_correlation_analysis", "improvetest/test_phase1_training_module.py::TestMultiAssetTrainer::test_data_synchronization", "improvetest/test_phase1_training_module.py::TestMultiAssetTrainer::test_evaluate", "improvetest/test_phase1_training_module.py::TestMultiAssetTrainer::test_multi_asset_initialization", "improvetest/test_phase1_training_module.py::TestMultiAssetTrainer::test_portfolio_allocation", "improvetest/test_phase1_training_module.py::TestMultiAssetTrainer::test_train_episode", "improvetest/test_phase1_training_module.py::TestMultiAssetTrainer::test_train_multiple_episodes", "improvetest/test_phase1_training_module.py::TestMultiAssetTrainer::test_trainer_initialization", "improvetest/test_phase1_training_module.py::TestMultiAssetTrainer::test_training_callbacks", "improvetest/test_phase1_training_module.py::TestPortfolioTrainer::test_evaluate", "improvetest/test_phase1_training_module.py::TestPortfolioTrainer::test_performance_tracking", "improvetest/test_phase1_training_module.py::TestPortfolioTrainer::test_portfolio_initialization", "improvetest/test_phase1_training_module.py::TestPortfolioTrainer::test_position_management", "improvetest/test_phase1_training_module.py::TestPortfolioTrainer::test_risk_management", "improvetest/test_phase1_training_module.py::TestPortfolioTrainer::test_train_episode", "improvetest/test_phase1_training_module.py::TestPortfolioTrainer::test_train_multiple_episodes", "improvetest/test_phase1_training_module.py::TestPortfolioTrainer::test_trainer_initialization", "improvetest/test_phase1_training_module.py::TestPortfolioTrainer::test_training_callbacks", "improvetest/test_phase1_training_module.py::TestSingleAssetTrainer::test_data_preprocessing", "improvetest/test_phase1_training_module.py::TestSingleAssetTrainer::test_evaluate", "improvetest/test_phase1_training_module.py::TestSingleAssetTrainer::test_feature_engineering", "improvetest/test_phase1_training_module.py::TestSingleAssetTrainer::test_single_asset_initialization", "improvetest/test_phase1_training_module.py::TestSingleAssetTrainer::test_train_episode", "improvetest/test_phase1_training_module.py::TestSingleAssetTrainer::test_train_multiple_episodes", "improvetest/test_phase1_training_module.py::TestSingleAssetTrainer::test_trainer_initialization", "improvetest/test_phase1_training_module.py::TestSingleAssetTrainer::test_training_callbacks", "improvetest/test_phase1_training_module.py::TestSingleAssetTrainer::test_training_with_validation", "improvetest/test_phase1_training_module.py::TestTrainerFactory::test_create_multi_asset_trainer", "improvetest/test_phase1_training_module.py::TestTrainerFactory::test_create_portfolio_trainer", "improvetest/test_phase1_training_module.py::TestTrainerFactory::test_create_single_asset_trainer", "improvetest/test_phase1_training_module.py::TestTrainerFactory::test_invalid_trainer_type", "improvetest/test_phase1_training_module.py::TestTrainingUtils::test_calculate_max_drawdown", "improvetest/test_phase1_training_module.py::TestTrainingUtils::test_calculate_sharpe_ratio", "improvetest/test_phase1_training_module.py::TestTrainingUtils::test_calculate_volatility", "improvetest/test_phase1_training_module.py::TestTrainingUtils::test_normalize_features", "improvetest/test_phase1_training_module.py::TestTrainingUtils::test_prepare_training_data", "improvetest/test_phase1_training_module.py::TestTrainingUtils::test_save_load_training_state", "improvetest/test_phase1_training_module.py::TestTrainingUtils::test_split_data", "improvetest/test_stage1_1_shared_utils.py::TestOptimizationUtils::test_load_saved_data", "improvetest/test_stage1_1_shared_utils.py::TestOptimizationUtils::test_sample_hyperparams_structure", "improvetest/test_stage1_1_shared_utils.py::TestOptimizationUtils::test_save_best_agent", "improvetest/test_stage1_1_shared_utils.py::TestOptimizationUtils::test_set_trial_attributes", "improvetest/test_stage1_1_shared_utils.py::TestOptimizationUtils::test_write_logs", "improvetest/test_stage1_2_refactor_optimization.py::TestRefactoredOptimization::test_code_duplication_elimination", "improvetest/test_stage1_2_refactor_optimization.py::TestRefactoredOptimization::test_performance_impact", "improvetest/test_stage1_2_refactor_optimization.py::TestRefactoredOptimization::test_refactored_cpcv_imports_utils", "improvetest/test_stage1_2_refactor_optimization.py::TestRefactoredOptimization::test_refactored_cpcv_uses_shared_data_loading", "improvetest/test_stage1_2_refactor_optimization.py::TestRefactoredOptimization::test_refactored_cpcv_uses_shared_hyperparams", "improvetest/test_stage1_2_refactor_optimization.py::TestRefactoredOptimization::test_refactored_cpcv_uses_shared_logging", "improvetest/test_stage1_2_refactor_optimization.py::TestRefactoredOptimization::test_refactored_kcv_imports_utils", "improvetest/test_stage1_2_refactor_optimization.py::TestRefactoredOptimization::test_refactored_kcv_uses_shared_functions", "improvetest/test_stage1_2_refactor_optimization.py::TestRefactoredOptimization::test_refactored_scripts_maintain_functionality", "improvetest/test_stage1_3_config_manager.py::TestConfigManager::test_computed_config", "improvetest/test_stage1_3_config_manager.py::TestConfigManager::test_config_get_set", "improvetest/test_stage1_3_config_manager.py::TestConfigManager::test_config_validation", "improvetest/test_stage1_3_config_manager.py::TestConfigManager::test_date_calculation", "improvetest/test_stage1_3_config_manager.py::TestConfigManager::test_default_config_loading", "improvetest/test_stage1_3_config_manager.py::TestConfigManager::test_error_handling", "improvetest/test_stage1_3_config_manager.py::TestConfigManager::test_get_all_config", "improvetest/test_stage1_3_config_manager.py::TestConfigManager::test_global_config_manager", "improvetest/test_stage1_3_config_manager.py::TestConfigManager::test_json_config_file", "improvetest/test_stage1_3_config_manager.py::TestConfigManager::test_nCr_calculation", "improvetest/test_stage1_3_config_manager.py::TestConfigManager::test_yaml_config_file", "improvetest/test_stage1_3_config_manager.py::test_config_manager_integration", "improvetest/test_stage1_4_elegantrl_adapter.py::TestConvenienceFunctions::test_create_trainer_function", "improvetest/test_stage1_4_elegantrl_adapter.py::TestConvenienceFunctions::test_get_available_trainers_function", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLAdapter::test_adapter_implements_interface", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLAdapter::test_get_model_info", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLAdapter::test_get_model_info_invalid", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLAdapter::test_get_supported_models", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLAdapter::test_model_classification", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLAdapter::test_train_method_structure", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLAdapter::test_validate_config_invalid", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLAdapter::test_validate_config_valid", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLAdapter::test_validate_training_config", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLModel::test_load_method_invalid_path", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLModel::test_load_method_valid_path", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLModel::test_model_implements_interface", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLModel::test_model_initialization", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLModel::test_predict_method", "improvetest/test_stage1_4_elegantrl_adapter.py::TestElegantRLModel::test_save_method", "improvetest/test_stage1_4_elegantrl_adapter.py::TestIntegration::test_full_workflow_structure", "improvetest/test_stage1_4_elegantrl_adapter.py::TestTrainerFactory::test_create_trainer_elegantrl", "improvetest/test_stage1_4_elegantrl_adapter.py::TestTrainerFactory::test_create_trainer_invalid", "improvetest/test_stage1_4_elegantrl_adapter.py::TestTrainerFactory::test_get_supported_trainers", "improvetest/test_stage1_4_elegantrl_adapter.py::TestTrainerFactory::test_get_trainer_info", "improvetest/test_stage1_4_elegantrl_adapter.py::TestTrainerFactory::test_get_trainer_info_invalid", "improvetest/test_stage1_4_elegantrl_adapter.py::TestTrainerFactory::test_is_trainer_supported", "improvetest/test_stage1_4_elegantrl_adapter.py::TestTrainerFactory::test_register_trainer", "improvetest/test_stage1_4_elegantrl_adapter.py::TestTrainerFactory::test_register_trainer_invalid_class", "improvetest/test_stage1_4_elegantrl_adapter.py::TestTrainerInterface::test_custom_exceptions_exist", "improvetest/test_stage1_4_elegantrl_adapter.py::TestTrainerInterface::test_model_interface_is_abstract", "improvetest/test_stage1_4_elegantrl_adapter.py::TestTrainerInterface::test_trainer_interface_is_abstract", "improvetest/test_stage2_1_dependency_injection.py::TestConfigurationDrivenDI::test_load_from_config", "improvetest/test_stage2_1_dependency_injection.py::TestConfigurationDrivenDI::test_validate_configuration", "improvetest/test_stage2_1_dependency_injection.py::TestDIContainer::test_circular_dependency_detection", "improvetest/test_stage2_1_dependency_injection.py::TestDIContainer::test_container_initialization", "improvetest/test_stage2_1_dependency_injection.py::TestDIContainer::test_dependency_injection", "improvetest/test_stage2_1_dependency_injection.py::TestDIContainer::test_register_singleton_service", "improvetest/test_stage2_1_dependency_injection.py::TestDIContainer::test_register_transient_service", "improvetest/test_stage2_1_dependency_injection.py::TestDIContainer::test_register_with_factory", "improvetest/test_stage2_1_dependency_injection.py::TestDIContainer::test_register_with_interface", "improvetest/test_stage2_1_dependency_injection.py::TestDIContainer::test_service_not_found", "improvetest/test_stage2_1_dependency_injection.py::TestDIContainer::test_thread_safety", "improvetest/test_stage2_1_dependency_injection.py::TestDIContainerIntegration::test_complex_dependency_graph", "improvetest/test_stage2_1_dependency_injection.py::TestDIContainerIntegration::test_mixed_lifetimes", "improvetest/test_stage2_1_dependency_injection.py::TestDIContainerPerformance::test_concurrent_access_performance", "improvetest/test_stage2_1_dependency_injection.py::TestDIContainerPerformance::test_service_resolution_performance", "improvetest/test_stage2_1_dependency_injection.py::TestServiceDescriptor::test_service_descriptor_creation", "improvetest/test_stage2_1_dependency_injection.py::TestServiceDescriptor::test_service_descriptor_with_factory", "improvetest/test_stage2_2_unified_config.py::TestConfigIntegration::test_complete_config_workflow", "improvetest/test_stage2_2_unified_config.py::TestConfigIntegration::test_config_backup_and_restore", "improvetest/test_stage2_2_unified_config.py::TestConfigIntegration::test_config_hot_reload", "improvetest/test_stage2_2_unified_config.py::TestConfigLoaders::test_config_loader_error_handling", "improvetest/test_stage2_2_unified_config.py::TestConfigLoaders::test_environment_config_loader", "improvetest/test_stage2_2_unified_config.py::TestConfigLoaders::test_file_config_loader", "improvetest/test_stage2_2_unified_config.py::TestConfigPerformance::test_concurrent_config_access", "improvetest/test_stage2_2_unified_config.py::TestConfigPerformance::test_large_config_loading_performance", "improvetest/test_stage2_2_unified_config.py::TestConfigValidation::test_config_manager_with_validation", "improvetest/test_stage2_2_unified_config.py::TestConfigValidation::test_custom_validation_rules", "improvetest/test_stage2_2_unified_config.py::TestConfigValidation::test_schema_validation_failure", "improvetest/test_stage2_2_unified_config.py::TestConfigValidation::test_schema_validation_success", "improvetest/test_stage2_2_unified_config.py::TestUnifiedConfigManager::test_config_manager_initialization", "improvetest/test_stage2_2_unified_config.py::TestUnifiedConfigManager::test_config_not_found", "improvetest/test_stage2_2_unified_config.py::TestUnifiedConfigManager::test_environment_variable_override", "improvetest/test_stage2_2_unified_config.py::TestUnifiedConfigManager::test_invalid_file_format", "improvetest/test_stage2_2_unified_config.py::TestUnifiedConfigManager::test_load_json_config", "improvetest/test_stage2_2_unified_config.py::TestUnifiedConfigManager::test_load_multiple_configs", "improvetest/test_stage2_2_unified_config.py::TestUnifiedConfigManager::test_load_yaml_config", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentComponents::test_agent_factory_standalone", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentComponents::test_model_builder_standalone", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentComponents::test_prediction_service_standalone", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentComponents::test_training_service_standalone", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentRefactor::test_agent_factory_creation", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentRefactor::test_agent_lifecycle_management", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentRefactor::test_backward_compatibility", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentRefactor::test_configuration_validation", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentRefactor::test_dependency_injection_integration", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentRefactor::test_error_handling", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentRefactor::test_model_builder_creation", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentRefactor::test_performance_monitoring", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentRefactor::test_prediction_service_creation", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentRefactor::test_refactored_drl_agent_integration", "improvetest/test_stage2_3_drl_agent_refactor.py::TestDRLAgentRefactor::test_training_service_creation", "improvetest/test_stage3_extensibility.py::TestIntegration::test_full_system_integration", "improvetest/test_stage3_extensibility.py::TestIntegration::test_logging_with_optimization", "improvetest/test_stage3_extensibility.py::TestIntegration::test_plugin_with_monitoring", "improvetest/test_stage3_extensibility.py::TestMonitoringSystem::test_anomaly_detection", "improvetest/test_stage3_extensibility.py::TestMonitoringSystem::test_cpu_monitoring", "improvetest/test_stage3_extensibility.py::TestMonitoringSystem::test_memory_monitoring", "improvetest/test_stage3_extensibility.py::TestMonitoringSystem::test_system_monitor_creation", "improvetest/test_stage3_extensibility.py::TestMonitoringSystem::test_training_metrics_logging", "improvetest/test_stage3_extensibility.py::TestMonitoringSystem::test_training_monitor_creation", "improvetest/test_stage3_extensibility.py::TestPerformanceOptimization::test_compute_optimizer_creation", "improvetest/test_stage3_extensibility.py::TestPerformanceOptimization::test_concurrency_optimizer_creation", "improvetest/test_stage3_extensibility.py::TestPerformanceOptimization::test_device_selection", "improvetest/test_stage3_extensibility.py::TestPerformanceOptimization::test_memory_optimization", "improvetest/test_stage3_extensibility.py::TestPerformanceOptimization::test_memory_optimizer_creation", "improvetest/test_stage3_extensibility.py::TestPerformanceOptimization::test_memory_usage_check", "improvetest/test_stage3_extensibility.py::TestPerformanceOptimization::test_parallel_execution", "improvetest/test_stage3_extensibility.py::TestPluginArchitecture::test_plugin_execution", "improvetest/test_stage3_extensibility.py::TestPluginArchitecture::test_plugin_interface_definition", "improvetest/test_stage3_extensibility.py::TestPluginArchitecture::test_plugin_loading", "improvetest/test_stage3_extensibility.py::TestPluginArchitecture::test_plugin_manager_creation", "improvetest/test_stage3_extensibility.py::TestPluginArchitecture::test_plugin_unloading", "improvetest/test_stage3_extensibility.py::TestUnifiedLogging::test_log_rotation", "improvetest/test_stage3_extensibility.py::TestUnifiedLogging::test_logger_config_creation", "improvetest/test_stage3_extensibility.py::TestUnifiedLogging::test_logger_setup", "improvetest/test_stage3_extensibility.py::TestUnifiedLogging::test_structured_logger_creation", "improvetest/test_stage3_extensibility.py::TestUnifiedLogging::test_structured_logging", "test_isolated_single_asset.py::test_single_asset_attributes", "test_isolated_single_asset.py::test_single_asset_initialization", "test_simple_single_asset.py::test_single_asset_trainer_creation", "test_simple_single_asset.py::test_single_asset_trainer_with_data", "tests/e2e/test_complete_workflow.py::TestCompleteWorkflow::test_extended_workflow_simulation", "tests/e2e/test_complete_workflow.py::TestCompleteWorkflow::test_full_pipeline_data_to_deployment", "tests/e2e/test_complete_workflow.py::TestCompleteWorkflow::test_workflow_data_integrity", "tests/e2e/test_complete_workflow.py::TestCompleteWorkflow::test_workflow_error_handling", "tests/e2e/test_complete_workflow.py::TestCompleteWorkflow::test_workflow_performance_benchmarks", "tests/e2e/test_complete_workflow.py::TestWorkflowIntegration::test_component_integration", "tests/e2e/test_complete_workflow.py::TestWorkflowIntegration::test_end_to_end_data_flow", "tests/e2e/test_complete_workflow.py::TestWorkflowIntegration::test_monitoring_integration", "tests/e2e/test_complete_workflow.py::TestWorkflowIntegration::test_strategy_risk_integration", "tests/e2e/test_complete_workflow.py::TestWorkflowScalability::test_concurrent_operations", "tests/e2e/test_complete_workflow.py::TestWorkflowScalability::test_high_frequency_data_handling", "tests/e2e/test_complete_workflow.py::TestWorkflowScalability::test_memory_efficiency_large_datasets", "tests/e2e/test_complete_workflow.py::TestWorkflowScalability::test_multi_symbol_scalability[1]", "tests/e2e/test_complete_workflow.py::TestWorkflowScalability::test_multi_symbol_scalability[3]", "tests/e2e/test_complete_workflow.py::TestWorkflowScalability::test_multi_symbol_scalability[5]", "tests/integration/test_training_pipeline.py::TestParametrizedTraining::test_different_algorithms_training[A2C-1000]", "tests/integration/test_training_pipeline.py::TestParametrizedTraining::test_different_algorithms_training[PPO-1000]", "tests/integration/test_training_pipeline.py::TestParametrizedTraining::test_different_algorithms_training[SAC-1000]", "tests/integration/test_training_pipeline.py::TestParametrizedTraining::test_different_initial_amounts[100000-80000]", "tests/integration/test_training_pipeline.py::TestParametrizedTraining::test_different_initial_amounts[200000-160000]", "tests/integration/test_training_pipeline.py::TestParametrizedTraining::test_different_initial_amounts[50000-40000]", "tests/integration/test_training_pipeline.py::TestTrainingIntegrationWithBacktest::test_strategy_comparison", "tests/integration/test_training_pipeline.py::TestTrainingIntegrationWithBacktest::test_train_and_backtest_pipeline", "tests/integration/test_training_pipeline.py::TestTrainingMetrics::test_training_convergence_detection", "tests/integration/test_training_pipeline.py::TestTrainingMetrics::test_training_metrics_collection", "tests/integration/test_training_pipeline.py::TestTrainingMetrics::test_training_performance_tracking", "tests/integration/test_training_pipeline.py::TestTrainingPipeline::test_basic_training_pipeline", "tests/integration/test_training_pipeline.py::TestTrainingPipeline::test_long_training_session", "tests/integration/test_training_pipeline.py::TestTrainingPipeline::test_memory_usage_during_training", "tests/integration/test_training_pipeline.py::TestTrainingPipeline::test_multi_asset_training", "tests/integration/test_training_pipeline.py::TestTrainingPipeline::test_training_error_handling", "tests/integration/test_training_pipeline.py::TestTrainingPipeline::test_training_interruption_and_resume", "tests/integration/test_training_pipeline.py::TestTrainingPipeline::test_training_reproducibility", "tests/integration/test_training_pipeline.py::TestTrainingPipeline::test_training_with_different_algorithms", "tests/integration/test_training_pipeline.py::TestTrainingPipeline::test_training_with_validation", "tests/unit/test_indicators.py::TestBollingerBands::test_bollinger_bands_basic", "tests/unit/test_indicators.py::TestBollingerBands::test_bollinger_bands_different_std[1.5]", "tests/unit/test_indicators.py::TestBollingerBands::test_bollinger_bands_different_std[1]", "tests/unit/test_indicators.py::TestBollingerBands::test_bollinger_bands_different_std[2.5]", "tests/unit/test_indicators.py::TestBollingerBands::test_bollinger_bands_different_std[2]", "tests/unit/test_indicators.py::TestBollingerBands::test_bollinger_bands_different_std[3]", "tests/unit/test_indicators.py::TestBollingerBands::test_bollinger_bands_price_containment", "tests/unit/test_indicators.py::TestBollingerBands::test_bollinger_bands_volatility_expansion", "tests/unit/test_indicators.py::TestExponentialMovingAverage::test_ema_basic_calculation", "tests/unit/test_indicators.py::TestExponentialMovingAverage::test_ema_edge_cases", "tests/unit/test_indicators.py::TestExponentialMovingAverage::test_ema_vs_sma", "tests/unit/test_indicators.py::TestIndicatorBenchmarks::test_ema_benchmark_against_pandas", "tests/unit/test_indicators.py::TestIndicatorBenchmarks::test_sma_benchmark_against_pandas", "tests/unit/test_indicators.py::TestIndicatorIntegration::test_indicators_consistency", "tests/unit/test_indicators.py::TestIndicatorIntegration::test_indicators_with_missing_data", "tests/unit/test_indicators.py::TestIndicatorIntegration::test_indicators_with_real_data_pattern", "tests/unit/test_indicators.py::TestIndicatorMocks::test_indicator_error_handling", "tests/unit/test_indicators.py::TestIndicatorMocks::test_sma_with_mock", "tests/unit/test_indicators.py::TestIndicatorPerformance::test_multiple_indicators_performance", "tests/unit/test_indicators.py::TestIndicatorPerformance::test_sma_performance_large_dataset", "tests/unit/test_indicators.py::TestMACD::test_macd_basic_calculation", "tests/unit/test_indicators.py::TestMACD::test_macd_crossover_signals", "tests/unit/test_indicators.py::TestMACD::test_macd_trend_detection", "tests/unit/test_indicators.py::TestParametrizedIndicators::test_macd_various_parameters[12-26-9]", "tests/unit/test_indicators.py::TestParametrizedIndicators::test_macd_various_parameters[5-13-3]", "tests/unit/test_indicators.py::TestParametrizedIndicators::test_macd_various_parameters[8-21-5]", "tests/unit/test_indicators.py::TestParametrizedIndicators::test_rsi_various_periods[14]", "tests/unit/test_indicators.py::TestParametrizedIndicators::test_rsi_various_periods[21]", "tests/unit/test_indicators.py::TestParametrizedIndicators::test_rsi_various_periods[28]", "tests/unit/test_indicators.py::TestParametrizedIndicators::test_rsi_various_periods[7]", "tests/unit/test_indicators.py::TestParametrizedIndicators::test_sma_various_parameters[10-20]", "tests/unit/test_indicators.py::TestParametrizedIndicators::test_sma_various_parameters[20-50]", "tests/unit/test_indicators.py::TestParametrizedIndicators::test_sma_various_parameters[5-10]", "tests/unit/test_indicators.py::TestParametrizedIndicators::test_sma_various_parameters[50-100]", "tests/unit/test_indicators.py::TestRSI::test_rsi_bounds", "tests/unit/test_indicators.py::TestRSI::test_rsi_different_windows[14]", "tests/unit/test_indicators.py::TestRSI::test_rsi_different_windows[21]", "tests/unit/test_indicators.py::TestRSI::test_rsi_different_windows[28]", "tests/unit/test_indicators.py::TestRSI::test_rsi_different_windows[7]", "tests/unit/test_indicators.py::TestRSI::test_rsi_extreme_cases", "tests/unit/test_indicators.py::TestRSI::test_rsi_sideways_market", "tests/unit/test_indicators.py::TestSimpleMovingAverage::test_sma_basic_calculation", "tests/unit/test_indicators.py::TestSimpleMovingAverage::test_sma_different_windows[1-10]", "tests/unit/test_indicators.py::TestSimpleMovingAverage::test_sma_different_windows[10-10]", "tests/unit/test_indicators.py::TestSimpleMovingAverage::test_sma_different_windows[5-10]", "tests/unit/test_indicators.py::TestSimpleMovingAverage::test_sma_edge_cases", "tests/unit/test_indicators.py::TestSimpleMovingAverage::test_sma_monotonic_data", "tests/unit/test_indicators.py::TestSimpleMovingAverage::test_sma_with_negative_values", "tests/unit/test_training_module.py::TestEvaluationMetrics::test_evaluation_metrics_creation", "tests/unit/test_training_module.py::TestEvaluationMetrics::test_evaluation_metrics_defaults", "tests/unit/test_training_module.py::TestTrainingConfig::test_training_config_creation", "tests/unit/test_training_module.py::TestTrainingConfig::test_training_config_defaults", "tests/unit/test_training_module.py::TestTrainingModuleIntegration::test_correlation_calculation", "tests/unit/test_training_module.py::TestTrainingModuleIntegration::test_evaluation_metrics_calculation", "tests/unit/test_training_module.py::TestTrainingModuleIntegration::test_max_drawdown_calculation", "tests/unit/test_training_module.py::TestTrainingModuleIntegration::test_multi_asset_portfolio_structure", "tests/unit/test_training_module.py::TestTrainingModuleIntegration::test_performance_ratios", "tests/unit/test_training_module.py::TestTrainingModuleIntegration::test_portfolio_weight_validation", "tests/unit/test_training_module.py::TestTrainingModuleIntegration::test_risk_metrics_calculation", "tests/unit/test_training_module.py::TestTrainingModuleIntegration::test_sharpe_ratio_calculation", "tests/unit/test_training_module.py::TestTrainingModuleIntegration::test_training_config_validation", "tests/unit/test_training_module.py::TestTrainingModuleIntegration::test_training_history_structure", "tests/unit/test_training_module.py::TestTrainingUtilities::test_configuration_management", "tests/unit/test_training_module.py::TestTrainingUtilities::test_data_validation", "tests/unit/test_training_module.py::TestTrainingUtilities::test_performance_metrics_edge_cases", "tests/unit/test_training_module.py::TestTrainingUtilities::test_statistical_calculations"]