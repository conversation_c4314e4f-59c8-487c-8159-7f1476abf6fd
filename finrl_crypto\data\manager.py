"""数据管理器模块

提供统一的数据管理接口，协调各种数据处理器的工作。
"""

import pandas as pd
import numpy as np
from typing import List, Optional, Dict, Any, Union
from pathlib import Path
import logging
from datetime import datetime

from .processors import DataProcessorFactory, DataProcessor
from .indicators import TechnicalIndicators


class DataManager:
    """数据管理器
    
    统一管理数据的获取、处理、存储和检索。
    """
    
    def __init__(self, 
                 data_source: str,
                 start_date: str,
                 end_date: str,
                 time_interval: str = '1d',
                 cache_dir: Optional[str] = None,
                 **kwargs):
        """初始化数据管理器
        
        Args:
            data_source: 数据源名称
            start_date: 开始日期
            end_date: 结束日期
            time_interval: 时间间隔
            cache_dir: 缓存目录
            **kwargs: 其他参数
        """
        self.data_source = data_source
        self.start_date = start_date
        self.end_date = end_date
        self.time_interval = time_interval
        self.cache_dir = Path(cache_dir) if cache_dir else Path('./data_cache')
        self.cache_dir.mkdir(exist_ok=True)
        
        # 初始化日志
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 创建数据处理器
        self.processor = DataProcessorFactory.create_processor(
            data_source=data_source,
            start_date=start_date,
            end_date=end_date,
            time_interval=time_interval,
            **kwargs
        )
        
        # 初始化技术指标计算器
        self.indicators = TechnicalIndicators()
        
        # 数据存储
        self.raw_data: Optional[pd.DataFrame] = None
        self.processed_data: Optional[pd.DataFrame] = None
        
        # 数据源管理
        self.data_sources: Dict[str, DataProcessor] = {}
        
    def add_data_source(self, name: str, processor: DataProcessor) -> None:
        """添加数据源
        
        Args:
            name: 数据源名称
            processor: 数据处理器实例
        """
        self.data_sources[name] = processor
        self.logger.info(f"已添加数据源: {name}")
    
    def merge_data_sources(self, source_names: List[str]) -> pd.DataFrame:
        """合并多个数据源的数据
        
        Args:
            source_names: 要合并的数据源名称列表
            
        Returns:
            合并后的DataFrame
        """
        merged_data = pd.DataFrame()
        
        for source_name in source_names:
            if source_name not in self.data_sources:
                raise ValueError(f"数据源 '{source_name}' 不存在")
            
            processor = self.data_sources[source_name]
            source_data = processor.fetch_data(['BTCUSDT'])  # 使用默认交易对
            
            if merged_data.empty:
                merged_data = source_data.copy()
            else:
                merged_data = pd.concat([merged_data, source_data], ignore_index=True)
        
        self.logger.info(f"已合并 {len(source_names)} 个数据源的数据")
        return merged_data
    
    def get_data(self, 
                 source_name_or_ticker_list: Union[str, List[str]],
                 ticker_list: Optional[List[str]] = None,
                 use_cache: bool = True,
                 force_refresh: bool = False) -> pd.DataFrame:
        """获取数据
        
        Args:
            source_name_or_ticker_list: 数据源名称或股票代码列表
            ticker_list: 股票/加密货币代码列表（当第一个参数是数据源名称时使用）
            use_cache: 是否使用缓存
            force_refresh: 是否强制刷新
            
        Returns:
            处理后的数据DataFrame
        """
        # 判断是使用数据源还是直接使用ticker列表
        if isinstance(source_name_or_ticker_list, str) and ticker_list is not None:
            # 使用指定的数据源
            if source_name_or_ticker_list not in self.data_sources:
                raise ValueError(f"数据源 '{source_name_or_ticker_list}' 不存在")
            processor = self.data_sources[source_name_or_ticker_list]
            actual_ticker_list = ticker_list
        else:
            # 使用默认处理器
            processor = self.processor
            actual_ticker_list = source_name_or_ticker_list if isinstance(source_name_or_ticker_list, list) else [source_name_or_ticker_list]
        
        cache_key = self._generate_cache_key(actual_ticker_list)
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        
        # 检查缓存
        if use_cache and not force_refresh and cache_file.exists():
            try:
                self.logger.info(f"从缓存加载数据: {cache_file}")
                self.processed_data = pd.read_pickle(cache_file)
                return self.processed_data
            except Exception as e:
                self.logger.warning(f"缓存加载失败: {e}，重新下载数据")
        
        # 下载原始数据
        self.logger.info("开始下载原始数据")
        self.raw_data = processor.download_data(actual_ticker_list)
        
        # 设置处理器的数据
        processor.dataframe = self.raw_data
        
        # 数据清洗
        self.logger.info("开始数据清洗")
        self.processed_data = processor.clean_data()
        
        # 保存到缓存
        if use_cache:
            try:
                self.processed_data.to_pickle(cache_file)
                self.logger.info(f"数据已保存到缓存: {cache_file}")
            except Exception as e:
                self.logger.warning(f"缓存保存失败: {e}")
        
        return self.processed_data
    
    def add_technical_indicators(self, 
                               indicator_list: List[str],
                               **kwargs) -> pd.DataFrame:
        """添加技术指标
        
        Args:
            indicator_list: 技术指标列表
            **kwargs: 指标参数
            
        Returns:
            添加指标后的DataFrame
        """
        if self.processed_data is None:
            raise ValueError("请先获取数据")
        
        self.logger.info(f"添加技术指标: {indicator_list}")
        
        # 使用处理器的指标计算方法
        if hasattr(self.processor, 'add_technical_indicators'):
            self.processed_data = self.processor.add_technical_indicators(
                indicator_list, **kwargs
            )
        else:
            # 使用通用指标计算器
            self.processed_data = self.indicators.add_indicators(
                self.processed_data, indicator_list, **kwargs
            )
        
        return self.processed_data
    
    def add_turbulence(self) -> pd.DataFrame:
        """添加市场波动指标
        
        Returns:
            添加波动指标后的DataFrame
        """
        if self.processed_data is None:
            raise ValueError("请先获取数据")
        
        self.logger.info("添加市场波动指标")
        
        if hasattr(self.processor, 'add_turbulence'):
            self.processed_data = self.processor.add_turbulence()
        else:
            self.logger.warning(f"数据源{self.data_source}不支持波动指标")
        
        return self.processed_data
    
    def add_vix(self) -> pd.DataFrame:
        """添加VIX指标
        
        Returns:
            添加VIX指标后的DataFrame
        """
        if self.processed_data is None:
            raise ValueError("请先获取数据")
        
        self.logger.info("添加VIX指标")
        
        if hasattr(self.processor, 'add_vix'):
            self.processed_data = self.processor.add_vix()
        else:
            self.logger.warning(f"数据源{self.data_source}不支持VIX指标")
        
        return self.processed_data
    
    def get_trading_days(self) -> List[str]:
        """获取交易日列表
        
        Returns:
            交易日列表
        """
        if hasattr(self.processor, 'get_trading_days'):
            return self.processor.get_trading_days()
        else:
            self.logger.warning(f"数据源{self.data_source}不支持获取交易日")
            return []
    
    def to_array(self, 
                 features: Optional[List[str]] = None,
                 if_vix: bool = False) -> np.ndarray:
        """转换为numpy数组
        
        Args:
            features: 特征列表
            if_vix: 是否包含VIX
            
        Returns:
            numpy数组
        """
        if self.processed_data is None:
            raise ValueError("请先获取数据")
        
        return self.processor.df_to_array(
            features=features, 
            if_vix=if_vix
        )
    
    def get_data_info(self) -> Dict[str, Any]:
        """获取数据信息
        
        Returns:
            数据信息字典
        """
        if self.processed_data is None:
            return {}
        
        info = {
            'shape': self.processed_data.shape,
            'columns': list(self.processed_data.columns),
            'tickers': list(self.processed_data['tic'].unique()) if 'tic' in self.processed_data.columns else [],
            'date_range': {
                'start': self.processed_data['time'].min() if 'time' in self.processed_data.columns else None,
                'end': self.processed_data['time'].max() if 'time' in self.processed_data.columns else None
            },
            'missing_values': self.processed_data.isnull().sum().to_dict(),
            'data_types': self.processed_data.dtypes.to_dict()
        }
        
        return info
    
    def validate_data(self) -> Dict[str, Any]:
        """验证数据质量
        
        Returns:
            验证结果字典
        """
        if self.processed_data is None:
            return {'valid': False, 'errors': ['数据为空']}
        
        errors = []
        warnings = []
        
        # 检查必需列
        required_columns = ['time', 'tic', 'close']
        missing_columns = [col for col in required_columns if col not in self.processed_data.columns]
        if missing_columns:
            errors.append(f"缺少必需列: {missing_columns}")
        
        # 检查数据完整性
        if self.processed_data.empty:
            errors.append("数据为空")
        
        # 检查缺失值
        missing_ratio = self.processed_data.isnull().sum() / len(self.processed_data)
        high_missing = missing_ratio[missing_ratio > 0.1].to_dict()
        if high_missing:
            warnings.append(f"高缺失值列: {high_missing}")
        
        # 检查重复数据
        if 'time' in self.processed_data.columns and 'tic' in self.processed_data.columns:
            duplicates = self.processed_data.duplicated(subset=['time', 'tic']).sum()
            if duplicates > 0:
                warnings.append(f"发现{duplicates}行重复数据")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings,
            'info': self.get_data_info()
        }
    
    def save_data(self, 
                  file_path: str, 
                  format: str = 'csv') -> None:
        """保存数据
        
        Args:
            file_path: 文件路径
            format: 文件格式（csv, pickle, parquet）
        """
        if self.processed_data is None:
            raise ValueError("没有数据可保存")
        
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        if format.lower() == 'csv':
            self.processed_data.to_csv(file_path, index=False)
        elif format.lower() == 'pickle':
            self.processed_data.to_pickle(file_path)
        elif format.lower() == 'parquet':
            self.processed_data.to_parquet(file_path, index=False)
        else:
            raise ValueError(f"不支持的文件格式: {format}")
        
        self.logger.info(f"数据已保存到: {file_path}")
    
    def load_data(self, 
                  file_path: str, 
                  format: str = 'csv') -> pd.DataFrame:
        """加载数据
        
        Args:
            file_path: 文件路径
            format: 文件格式
            
        Returns:
            加载的DataFrame
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        if format.lower() == 'csv':
            self.processed_data = pd.read_csv(file_path)
        elif format.lower() == 'pickle':
            self.processed_data = pd.read_pickle(file_path)
        elif format.lower() == 'parquet':
            self.processed_data = pd.read_parquet(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {format}")
        
        self.logger.info(f"数据已从{file_path}加载")
        return self.processed_data
    
    def _generate_cache_key(self, ticker_list: List[str]) -> str:
        """生成缓存键
        
        Args:
            ticker_list: 股票代码列表
            
        Returns:
            缓存键字符串
        """
        import hashlib
        
        key_components = [
            self.data_source,
            self.start_date,
            self.end_date,
            self.time_interval,
            '_'.join(sorted(ticker_list))
        ]
        
        key_string = '_'.join(key_components)
        return hashlib.md5(key_string.encode()).hexdigest()[:16]
    
    def clear_cache(self) -> None:
        """清理缓存"""
        import shutil
        
        if self.cache_dir.exists():
            shutil.rmtree(self.cache_dir)
            self.cache_dir.mkdir(exist_ok=True)
            self.logger.info("缓存已清理")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息
        
        Returns:
            缓存信息字典
        """
        if not self.cache_dir.exists():
            return {'cache_dir': str(self.cache_dir), 'files': [], 'total_size': 0}
        
        cache_files = list(self.cache_dir.glob('*.pkl'))
        total_size = sum(f.stat().st_size for f in cache_files)
        
        return {
            'cache_dir': str(self.cache_dir),
            'files': [f.name for f in cache_files],
            'file_count': len(cache_files),
            'total_size': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2)
        }