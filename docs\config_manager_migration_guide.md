# 配置管理器迁移指南

## 概述

本指南介绍如何从原有的全局变量配置方式迁移到新的配置管理器系统。新的配置管理器提供了更好的可维护性、灵活性和可扩展性。

## 迁移前后对比

### 旧方式（全局变量导入）

```python
# 旧方式：直接从config_main导入全局变量
from config_main import (
    TIMEFRAME,
    TICKER_LIST,
    no_candles_for_train,
    no_candles_for_val,
    TRAIN_START_DATE,
    TRAIN_END_DATE,
    VAL_START_DATE,
    VAL_END_DATE,
    H_TRIALS,
    SEED_CFG
)

# 使用配置
print(f"时间框架: {TIMEFRAME}")
print(f"训练蜡烛数: {no_candles_for_train}")
print(f"交易对: {TICKER_LIST}")
```

### 新方式（配置管理器）

```python
# 新方式：使用配置管理器
from utils.config_manager import get_config, get_config_manager

# 方式1：使用便捷函数
print(f"时间框架: {get_config('timeframe')}")
print(f"训练蜡烛数: {get_config('no_candles_for_train')}")
print(f"交易对: {get_config('ticker_list')}")

# 方式2：使用配置管理器实例
config_manager = get_config_manager()
print(f"训练开始日期: {config_manager.get('train_start_date')}")
print(f"验证结束日期: {config_manager.get('val_end_date')}")
```

## 配置键名映射表

| 旧配置变量名 | 新配置键名 | 说明 |
|-------------|-----------|------|
| `SEED_CFG` | `seed` | 随机种子 |
| `TIMEFRAME` | `timeframe` | 时间框架 |
| `H_TRIALS` | `h_trials` | 优化试验数 |
| `K_TEST_GROUPS` | `k_test_groups` | 测试组数 |
| `NUM_PATHS` | `num_paths` | 路径数 |
| `no_candles_for_train` | `no_candles_for_train` | 训练蜡烛数 |
| `no_candles_for_val` | `no_candles_for_val` | 验证蜡烛数 |
| `TICKER_LIST` | `ticker_list` | 交易对列表 |
| `ALPACA_LIMITS` | `alpaca_limits` | 最小购买限制 |
| `TECHNICAL_INDICATORS_LIST` | `technical_indicators` | 技术指标列表 |
| `TRAIN_START_DATE` | `train_start_date` | 训练开始日期（计算得出） |
| `TRAIN_END_DATE` | `train_end_date` | 训练结束日期（计算得出） |
| `VAL_START_DATE` | `val_start_date` | 验证开始日期（计算得出） |
| `VAL_END_DATE` | `val_end_date` | 验证结束日期（计算得出） |
| `N_GROUPS` | `n_groups` | 总组数（计算得出） |
| `NUMBER_OF_SPLITS` | `number_of_splits` | 分割数（计算得出） |

## 逐步迁移指南

### 步骤1：安装依赖

确保安装了必要的依赖包：

```bash
pip install pyyaml
```

### 步骤2：更新导入语句

将原有的导入语句：

```python
# 旧导入
from config_main import *
# 或
from config_main import TIMEFRAME, TICKER_LIST, no_candles_for_train
```

替换为：

```python
# 新导入
from utils.config_manager import get_config, get_config_manager
```

### 步骤3：更新配置访问方式

将直接使用变量名的方式：

```python
# 旧方式
print(f"时间框架: {TIMEFRAME}")
if no_candles_for_train > 1000:
    # 处理逻辑
```

替换为：

```python
# 新方式
print(f"时间框架: {get_config('timeframe')}")
if get_config('no_candles_for_train') > 1000:
    # 处理逻辑
```

### 步骤4：处理配置修改

如果需要修改配置，使用新的设置方法：

```python
# 旧方式（不推荐，直接修改全局变量）
# TIMEFRAME = '1h'  # 这种方式不安全

# 新方式
from utils.config_manager import set_config
set_config('timeframe', '1h')
```

### 步骤5：使用配置文件（可选）

创建配置文件来管理不同环境的配置：

```yaml
# config/production.yaml
seed: 2390408
timeframe: '5m'
h_trials: 100
no_candles_for_train: 30000
no_candles_for_val: 7500

ticker_list:
  - 'BTCUSDT'
  - 'ETHUSDT'
  - 'ADAUSDT'
```

然后在代码中加载：

```python
from utils.config_manager import init_config_manager

# 初始化配置管理器
config_manager = init_config_manager('config/production.yaml')
```

## 具体文件迁移示例

### 示例1：优化脚本迁移

**迁移前 (`1_optimize_kcv.py`)：**

```python
from config_main import *

def print_config():
    print('TIMEFRAME                  ', TIMEFRAME)
    print('TRAIN SAMPLES              ', no_candles_for_train)
    print('TRIALS NO.                 ', H_TRIALS)
    print('TICKER LIST                ', TICKER_LIST)

def objective(trial):
    # 使用全局变量
    env_params = {
        'lookback': trial.suggest_int('lookback', 1, 30),
        'no_candles_for_train': no_candles_for_train
    }
```

**迁移后：**

```python
from utils.config_manager import get_config, get_config_manager

def print_config():
    print('TIMEFRAME                  ', get_config('timeframe'))
    print('TRAIN SAMPLES              ', get_config('no_candles_for_train'))
    print('TRIALS NO.                 ', get_config('h_trials'))
    print('TICKER LIST                ', get_config('ticker_list'))

def objective(trial):
    # 使用配置管理器
    env_params = {
        'lookback': trial.suggest_int('lookback', 1, 30),
        'no_candles_for_train': get_config('no_candles_for_train')
    }
```

### 示例2：环境类迁移

**迁移前 (`environment_Alpaca.py`)：**

```python
from config_main import ALPACA_LIMITS

class CryptoEnvAlpaca:
    def __init__(self, config, env_params, initial_capital=1000000):
        self.alpaca_limits = ALPACA_LIMITS
```

**迁移后：**

```python
from utils.config_manager import get_config
import numpy as np

class CryptoEnvAlpaca:
    def __init__(self, config, env_params, initial_capital=1000000):
        self.alpaca_limits = np.array(get_config('alpaca_limits'))
```

## 新功能使用指南

### 1. 配置验证

```python
from utils.config_manager import get_config_manager

config_manager = get_config_manager()
if not config_manager.validate_config():
    print("配置验证失败，请检查配置")
```

### 2. 配置打印

```python
config_manager = get_config_manager()
config_manager.print_config("当前配置信息")
```

### 3. 保存配置到文件

```python
config_manager = get_config_manager()
config_manager.save_to_file('config/current_config.yaml')
```

### 4. 获取所有配置

```python
all_config = config_manager.get_all_config()
print(f"所有配置项: {list(all_config.keys())}")
```

## 迁移检查清单

- [ ] 更新所有文件的导入语句
- [ ] 替换所有配置变量访问方式
- [ ] 测试所有功能是否正常工作
- [ ] 验证计算配置（日期、组数等）是否正确
- [ ] 考虑使用配置文件管理不同环境
- [ ] 添加配置验证逻辑
- [ ] 更新文档和注释

## 常见问题

### Q1: 为什么要迁移到配置管理器？

**A:** 新的配置管理器提供了以下优势：
- 统一的配置管理接口
- 支持从文件加载配置
- 配置验证功能
- 动态配置计算
- 更好的可维护性和可扩展性

### Q2: 迁移后性能会受影响吗？

**A:** 不会。配置管理器使用单例模式，配置值在初始化时计算并缓存，访问性能与直接使用变量相当。

### Q3: 如何处理配置的默认值？

**A:** 使用 `get_config('key', default_value)` 方式提供默认值：

```python
# 如果配置不存在，使用默认值
initial_capital = get_config('initial_capital', 1000000)
```

### Q4: 可以同时使用新旧两种方式吗？

**A:** 技术上可以，但不推荐。建议完全迁移到新的配置管理器以保持代码一致性。

### Q5: 如何在不同环境使用不同配置？

**A:** 创建不同的配置文件：

```python
# 开发环境
config_manager = init_config_manager('config/development.yaml')

# 生产环境
config_manager = init_config_manager('config/production.yaml')
```

## 总结

配置管理器的引入显著提升了项目的配置管理能力。通过遵循本迁移指南，您可以平滑地从旧的全局变量方式迁移到新的配置管理器系统，享受更好的代码组织和维护体验。

如果在迁移过程中遇到问题，请参考 `examples/config_manager_example.py` 中的示例代码，或查看测试文件 `improvetest/test_stage1_3_config_manager.py` 了解详细用法。