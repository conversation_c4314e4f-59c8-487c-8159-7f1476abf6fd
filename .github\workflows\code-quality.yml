name: Code Quality

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  code-quality:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install flake8 black isort mypy bandit safety pylint radon vulture
        pip install pre-commit
    
    - name: Run pre-commit hooks
      run: |
        pre-commit run --all-files
      continue-on-error: true
    
    - name: Code formatting check (Black)
      run: |
        black --check --diff --color .
    
    - name: Import sorting check (isort)
      run: |
        isort --check-only --diff --color .
    
    - name: Linting (flake8)
      run: |
        flake8 . --format=html --htmldir=flake8-report
        flake8 . --format=json --output-file=flake8-report.json
    
    - name: Advanced linting (pylint)
      run: |
        pylint --output-format=json --reports=y . > pylint-report.json || true
        pylint --output-format=html . > pylint-report.html || true
    
    - name: Type checking (mypy)
      run: |
        mypy . --html-report mypy-report --xml-report mypy-xml --ignore-missing-imports
      continue-on-error: true
    
    - name: Security analysis (bandit)
      run: |
        bandit -r . -f json -o bandit-report.json
        bandit -r . -f html -o bandit-report.html
      continue-on-error: true
    
    - name: Dependency security check (safety)
      run: |
        safety check --json --output safety-report.json
        safety check --output safety-report.txt
      continue-on-error: true
    
    - name: Code complexity analysis (radon)
      run: |
        radon cc . --json > radon-cc.json
        radon mi . --json > radon-mi.json
        radon raw . --json > radon-raw.json
        radon hal . --json > radon-hal.json
    
    - name: Dead code detection (vulture)
      run: |
        vulture . --min-confidence 80 > vulture-report.txt || true
    
    - name: Upload code quality reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: code-quality-reports
        path: |
          flake8-report/
          flake8-report.json
          pylint-report.json
          pylint-report.html
          mypy-report/
          mypy-xml/
          bandit-report.json
          bandit-report.html
          safety-report.json
          safety-report.txt
          radon-*.json
          vulture-report.txt
    
    - name: Comment PR with quality report
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          
          let comment = '## 🔍 Code Quality Report\n\n';
          
          // Flake8 report
          try {
            const flake8 = JSON.parse(fs.readFileSync('flake8-report.json', 'utf8'));
            comment += `### Flake8 Issues: ${flake8.length}\n`;
          } catch (e) {
            comment += '### Flake8: ✅ No issues found\n';
          }
          
          // Bandit report
          try {
            const bandit = JSON.parse(fs.readFileSync('bandit-report.json', 'utf8'));
            comment += `### Security Issues: ${bandit.results.length}\n`;
          } catch (e) {
            comment += '### Security: ✅ No issues found\n';
          }
          
          // Safety report
          try {
            const safety = JSON.parse(fs.readFileSync('safety-report.json', 'utf8'));
            comment += `### Dependency Vulnerabilities: ${safety.length}\n`;
          } catch (e) {
            comment += '### Dependencies: ✅ No vulnerabilities found\n';
          }
          
          comment += '\n📊 Detailed reports are available in the artifacts.';
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  sonarcloud:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.pull_request.head.repo.full_name == github.repository)
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Run tests with coverage
      run: |
        pytest --cov=. --cov-report=xml
    
    - name: SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}