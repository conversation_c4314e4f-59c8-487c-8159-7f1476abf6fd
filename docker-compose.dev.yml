version: '3.8'

# FinRL Crypto 开发环境 Docker Compose配置
# 用于本地开发，包含热重载和调试功能

services:
  # 开发应用服务
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    image: finrl/crypto:dev
    container_name: finrl-crypto-app-dev
    restart: unless-stopped
    ports:
      - "8000:8000"
      - "5678:5678"  # debugpy端口
    environment:
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
      - DEBUG=true
      - WORKERS=1
      - HOST=0.0.0.0
      - PORT=8000
      - DATABASE_URL=*********************************************/finrl_dev
      - REDIS_URL=redis://:redis123@redis-dev:6379/0
      - SECRET_KEY=dev-secret-key-not-for-production
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - RELOAD=true
      - HOT_RELOAD=true
    volumes:
      - .:/app
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - ./checkpoints:/app/checkpoints
      - ./results:/app/results
      - dev_cache:/app/.cache
      - dev_pip_cache:/root/.cache/pip
    depends_on:
      - redis-dev
      - postgres-dev
    networks:
      - finrl-dev-network
    command: ["dev"]
    stdin_open: true
    tty: true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Jupyter Notebook开发环境
  jupyter-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    image: finrl/crypto:dev
    container_name: finrl-crypto-jupyter-dev
    restart: unless-stopped
    ports:
      - "8888:8888"
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=dev-token
      - DATABASE_URL=*********************************************/finrl_dev
      - REDIS_URL=redis://:redis123@redis-dev:6379/0
    volumes:
      - .:/app
      - ./notebooks:/app/notebooks
      - jupyter_dev_data:/home/<USER>/.jupyter
      - jupyter_dev_config:/home/<USER>/.jupyter/lab
    depends_on:
      - postgres-dev
      - redis-dev
    networks:
      - finrl-dev-network
    command: ["jupyter"]

  # 测试运行器
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile
      target: testing
    image: finrl/crypto:test
    container_name: finrl-crypto-test-runner
    environment:
      - ENVIRONMENT=test
      - DEBUG=true
      - DATABASE_URL=**********************************************/finrl_test
      - REDIS_URL=redis://:redis123@redis-test:6379/1
      - PYTHONPATH=/app
    volumes:
      - .:/app
      - ./test-results:/app/test-results
      - ./htmlcov:/app/htmlcov
    depends_on:
      - postgres-test
      - redis-test
    networks:
      - finrl-dev-network
    command: ["test"]
    profiles:
      - test

  # 开发数据库
  postgres-dev:
    image: postgres:15-alpine
    container_name: finrl-crypto-postgres-dev
    restart: unless-stopped
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=finrl_dev
      - POSTGRES_USER=finrl
      - POSTGRES_PASSWORD=finrl123
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-dev-db.sql:/docker-entrypoint-initdb.d/init-dev-db.sql:ro
    networks:
      - finrl-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U finrl -d finrl_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 测试数据库
  postgres-test:
    image: postgres:15-alpine
    container_name: finrl-crypto-postgres-test
    restart: unless-stopped
    ports:
      - "5434:5432"
    environment:
      - POSTGRES_DB=finrl_test
      - POSTGRES_USER=finrl
      - POSTGRES_PASSWORD=finrl123
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    networks:
      - finrl-dev-network
    profiles:
      - test
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U finrl -d finrl_test"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 开发Redis
  redis-dev:
    image: redis:7-alpine
    container_name: finrl-crypto-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_dev_data:/data
    networks:
      - finrl-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # 测试Redis
  redis-test:
    image: redis:7-alpine
    container_name: finrl-crypto-redis-test
    restart: unless-stopped
    ports:
      - "6381:6379"
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_test_data:/data
    networks:
      - finrl-dev-network
    profiles:
      - test
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # MailHog (邮件测试)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: finrl-crypto-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - finrl-dev-network

  # MinIO (S3兼容存储)
  minio:
    image: minio/minio:latest
    container_name: finrl-crypto-minio
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    volumes:
      - minio_data:/data
    networks:
      - finrl-dev-network
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Adminer (数据库管理)
  adminer:
    image: adminer:latest
    container_name: finrl-crypto-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=postgres-dev
    depends_on:
      - postgres-dev
    networks:
      - finrl-dev-network

  # Redis Commander (Redis管理)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: finrl-crypto-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis-dev:6379:1:redis123
    depends_on:
      - redis-dev
    networks:
      - finrl-dev-network

  # 文档服务器
  docs:
    build:
      context: .
      dockerfile: Dockerfile.docs
    image: finrl/crypto:docs
    container_name: finrl-crypto-docs
    restart: unless-stopped
    ports:
      - "8082:8000"
    volumes:
      - ./docs:/app/docs
      - ./mkdocs.yml:/app/mkdocs.yml
    networks:
      - finrl-dev-network
    command: ["mkdocs", "serve", "--dev-addr=0.0.0.0:8000"]
    profiles:
      - docs

  # 代码质量检查
  code-quality:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    image: finrl/crypto:dev
    container_name: finrl-crypto-code-quality
    volumes:
      - .:/app
      - ./reports:/app/reports
    networks:
      - finrl-dev-network
    command: >
      bash -c "
        echo 'Running code quality checks...' &&
        black --check . &&
        isort --check-only . &&
        flake8 . &&
        mypy . &&
        bandit -r . -f json -o reports/bandit-report.json &&
        safety check --json --output reports/safety-report.json &&
        echo 'Code quality checks completed!'
      "
    profiles:
      - quality

# 网络配置
networks:
  finrl-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  postgres_dev_data:
    driver: local
  postgres_test_data:
    driver: local
  redis_dev_data:
    driver: local
  redis_test_data:
    driver: local
  jupyter_dev_data:
    driver: local
  jupyter_dev_config:
    driver: local
  minio_data:
    driver: local
  dev_cache:
    driver: local
  dev_pip_cache:
    driver: local