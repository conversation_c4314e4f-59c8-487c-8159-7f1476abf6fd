"""系统监控模块

监控系统资源使用情况，包括CPU、内存、GPU等。
"""

import logging
import psutil
import threading
import time
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from datetime import datetime

try:
    import GPUtil
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False


@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_total_gb: float
    disk_usage_percent: float
    gpu_metrics: Optional[List[Dict[str, Any]]] = None
    network_io: Optional[Dict[str, int]] = None
    process_count: Optional[int] = None


class SystemMonitor:
    """系统监控器
    
    监控系统资源使用情况并提供实时数据。
    """
    
    def __init__(self, monitoring_interval: float = 1.0):
        """
        Args:
            monitoring_interval: 监控间隔（秒）
        """
        self.monitoring_interval = monitoring_interval
        self._is_monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._metrics_history: List[SystemMetrics] = []
        self._max_history_size = 1000
        self._callbacks: List[Callable[[SystemMetrics], None]] = []
        self._lock = threading.RLock()
        self._logger = logging.getLogger(__name__)
        
        # 初始化网络IO基线
        self._network_io_baseline = psutil.net_io_counters()
    
    def add_callback(self, callback: Callable[[SystemMetrics], None]) -> None:
        """添加监控回调函数
        
        Args:
            callback: 回调函数，接收SystemMetrics参数
        """
        with self._lock:
            self._callbacks.append(callback)
    
    def remove_callback(self, callback: Callable[[SystemMetrics], None]) -> None:
        """移除监控回调函数
        
        Args:
            callback: 要移除的回调函数
        """
        with self._lock:
            if callback in self._callbacks:
                self._callbacks.remove(callback)
    
    def start_monitoring(self) -> bool:
        """开始监控
        
        Returns:
            bool: 是否成功开始监控
        """
        try:
            with self._lock:
                if self._is_monitoring:
                    self._logger.warning("系统监控已在运行")
                    return True
                
                self._is_monitoring = True
                self._monitor_thread = threading.Thread(
                    target=self._monitoring_loop,
                    daemon=True,
                    name="SystemMonitor"
                )
                self._monitor_thread.start()
                
                self._logger.info("系统监控已启动")
                return True
                
        except Exception as e:
            self._logger.error(f"启动系统监控失败: {e}")
            self._is_monitoring = False
            return False
    
    def stop_monitoring(self) -> bool:
        """停止监控
        
        Returns:
            bool: 是否成功停止监控
        """
        try:
            with self._lock:
                if not self._is_monitoring:
                    self._logger.warning("系统监控未在运行")
                    return True
                
                self._is_monitoring = False
                
                if self._monitor_thread and self._monitor_thread.is_alive():
                    self._monitor_thread.join(timeout=5.0)
                
                self._logger.info("系统监控已停止")
                return True
                
        except Exception as e:
            self._logger.error(f"停止系统监控失败: {e}")
            return False
    
    def get_current_metrics(self) -> SystemMetrics:
        """获取当前系统指标
        
        Returns:
            SystemMetrics: 当前系统指标
        """
        return self._collect_metrics()
    
    def get_metrics_history(self, limit: Optional[int] = None) -> List[SystemMetrics]:
        """获取历史指标数据
        
        Args:
            limit: 返回的最大记录数，None表示返回所有
            
        Returns:
            List[SystemMetrics]: 历史指标数据
        """
        with self._lock:
            if limit is None:
                return self._metrics_history.copy()
            else:
                return self._metrics_history[-limit:].copy()
    
    def get_average_metrics(self, duration_minutes: int = 5) -> Optional[Dict[str, float]]:
        """获取指定时间段内的平均指标
        
        Args:
            duration_minutes: 时间段（分钟）
            
        Returns:
            Optional[Dict[str, float]]: 平均指标，如果数据不足则返回None
        """
        with self._lock:
            if not self._metrics_history:
                return None
            
            cutoff_time = datetime.now().timestamp() - (duration_minutes * 60)
            recent_metrics = [
                m for m in self._metrics_history 
                if m.timestamp.timestamp() > cutoff_time
            ]
            
            if not recent_metrics:
                return None
            
            return {
                'cpu_percent': sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics),
                'memory_percent': sum(m.memory_percent for m in recent_metrics) / len(recent_metrics),
                'memory_used_gb': sum(m.memory_used_gb for m in recent_metrics) / len(recent_metrics),
                'disk_usage_percent': sum(m.disk_usage_percent for m in recent_metrics) / len(recent_metrics)
            }
    
    def get_cpu_usage(self) -> float:
        """获取当前CPU使用率
        
        Returns:
            float: CPU使用率百分比
        """
        return psutil.cpu_percent(interval=0.1)
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况
        
        Returns:
            Dict[str, float]: 内存使用信息
        """
        try:
            import psutil
            memory = psutil.virtual_memory()
            return {
                'total': memory.total / (1024**3),
                'used': memory.used / (1024**3),
                'percent': memory.percent,
                'used_gb': memory.used / (1024**3),
                'available_gb': memory.available / (1024**3)
            }
        except ImportError:
            # 如果psutil不可用，返回模拟数据
            return {
                'total': 8.0,
                'used': 4.0,
                'percent': 50.0,
                'used_gb': 4.0,
                'available_gb': 4.0
            }
    
    def get_gpu_usage(self) -> List[Dict[str, Any]]:
        """获取GPU使用情况
        
        Returns:
            List[Dict[str, Any]]: GPU使用情况列表
        """
        if not GPU_AVAILABLE:
            return []
        
        try:
            gpus = GPUtil.getGPUs()
            gpu_info = []
            for gpu in gpus:
                gpu_info.append({
                    'id': gpu.id,
                    'name': gpu.name,
                    'load': gpu.load * 100,  # 转换为百分比
                    'memory_used': gpu.memoryUsed,
                    'memory_total': gpu.memoryTotal,
                    'memory_percent': (gpu.memoryUsed / gpu.memoryTotal) * 100,
                    'temperature': gpu.temperature
                })
            return gpu_info
        except Exception as e:
            self._logger.error(f"获取GPU信息失败: {e}")
            return []
    
    def _monitoring_loop(self) -> None:
        """监控循环"""
        while self._is_monitoring:
            try:
                metrics = self._collect_metrics()
                
                # 存储历史数据
                with self._lock:
                    self._metrics_history.append(metrics)
                    
                    # 限制历史数据大小
                    if len(self._metrics_history) > self._max_history_size:
                        self._metrics_history = self._metrics_history[-self._max_history_size:]
                    
                    # 调用回调函数
                    for callback in self._callbacks:
                        try:
                            callback(metrics)
                        except Exception as e:
                            self._logger.error(f"监控回调函数执行失败: {e}")
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                self._logger.error(f"监控循环错误: {e}")
                time.sleep(self.monitoring_interval)
    
    def _collect_metrics(self) -> SystemMetrics:
        """收集系统指标
        
        Returns:
            SystemMetrics: 系统指标数据
        """
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_gb = memory.used / (1024**3)
        memory_total_gb = memory.total / (1024**3)
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        disk_usage_percent = disk.percent
        
        # 网络IO
        current_net_io = psutil.net_io_counters()
        network_io = {
            'bytes_sent': current_net_io.bytes_sent - self._network_io_baseline.bytes_sent,
            'bytes_recv': current_net_io.bytes_recv - self._network_io_baseline.bytes_recv,
            'packets_sent': current_net_io.packets_sent - self._network_io_baseline.packets_sent,
            'packets_recv': current_net_io.packets_recv - self._network_io_baseline.packets_recv
        }
        
        # 进程数量
        process_count = len(psutil.pids())
        
        # GPU指标（如果可用）
        gpu_metrics = None
        if GPU_AVAILABLE:
            try:
                gpus = GPUtil.getGPUs()
                gpu_metrics = []
                for gpu in gpus:
                    gpu_metrics.append({
                        'id': gpu.id,
                        'name': gpu.name,
                        'load': gpu.load * 100,  # 转换为百分比
                        'memory_used': gpu.memoryUsed,
                        'memory_total': gpu.memoryTotal,
                        'memory_percent': (gpu.memoryUsed / gpu.memoryTotal) * 100,
                        'temperature': gpu.temperature
                    })
            except Exception as e:
                self._logger.warning(f"获取GPU指标失败: {e}")
        
        return SystemMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_gb=memory_used_gb,
            memory_total_gb=memory_total_gb,
            disk_usage_percent=disk_usage_percent,
            gpu_metrics=gpu_metrics,
            network_io=network_io,
            process_count=process_count
        )
    
    def is_monitoring(self) -> bool:
        """检查是否正在监控
        
        Returns:
            bool: 是否正在监控
        """
        return self._is_monitoring
    
    def clear_history(self) -> None:
        """清空历史数据"""
        with self._lock:
            self._metrics_history.clear()
            self._logger.info("系统监控历史数据已清空")
    
    def set_max_history_size(self, size: int) -> None:
        """设置最大历史数据大小
        
        Args:
            size: 最大历史数据条数
        """
        with self._lock:
            self._max_history_size = max(1, size)
            if len(self._metrics_history) > self._max_history_size:
                self._metrics_history = self._metrics_history[-self._max_history_size:]
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统基本信息
        
        Returns:
            Dict[str, Any]: 系统信息
        """
        try:
            cpu_info = {
                'physical_cores': psutil.cpu_count(logical=False),
                'logical_cores': psutil.cpu_count(logical=True),
                'max_frequency': psutil.cpu_freq().max if psutil.cpu_freq() else None,
                'current_frequency': psutil.cpu_freq().current if psutil.cpu_freq() else None
            }
            
            memory_info = {
                'total_gb': psutil.virtual_memory().total / (1024**3),
                'available_gb': psutil.virtual_memory().available / (1024**3)
            }
            
            disk_info = {
                'total_gb': psutil.disk_usage('/').total / (1024**3),
                'free_gb': psutil.disk_usage('/').free / (1024**3)
            }
            
            gpu_info = []
            if GPU_AVAILABLE:
                try:
                    gpus = GPUtil.getGPUs()
                    for gpu in gpus:
                        gpu_info.append({
                            'id': gpu.id,
                            'name': gpu.name,
                            'memory_total': gpu.memoryTotal
                        })
                except Exception:
                    pass
            
            return {
                'cpu': cpu_info,
                'memory': memory_info,
                'disk': disk_info,
                'gpu': gpu_info,
                'platform': psutil.WINDOWS if hasattr(psutil, 'WINDOWS') else 'unknown'
            }
            
        except Exception as e:
            self._logger.error(f"获取系统信息失败: {e}")
            return {}