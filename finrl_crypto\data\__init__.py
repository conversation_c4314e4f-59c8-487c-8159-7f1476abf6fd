"""FinRL Crypto数据处理模块

提供统一的数据获取、处理和管理接口。

主要组件:
- DataProcessor: 数据处理基类
- BinanceProcessor: Binance数据处理器
- YahooProcessor: Yahoo Finance数据处理器
- DataManager: 数据管理器
- TechnicalIndicators: 技术指标计算器
- DataProcessorFactory: 数据处理器工厂

基本用法:
    >>> from finrl_crypto.data import DataManager
    >>> 
    >>> # 创建数据管理器
    >>> manager = DataManager(
    ...     data_source='binance',
    ...     start_date='2023-01-01',
    ...     end_date='2023-12-31'
    ... )
    >>> 
    >>> # 获取数据
    >>> data = manager.get_data(['BTCUSDT', 'ETHUSDT'])
    >>> 
    >>> # 添加技术指标
    >>> data = manager.add_technical_indicators(['rsi', 'macd', 'bb'])
"""

from .base import DataProcessor
from .processors import (
    BinanceProcessor,
    YahooProcessor,
    DataProcessorFactory
)
from .manager import DataManager
from .indicators import TechnicalIndicators

__all__ = [
    'DataProcessor',
    'BinanceProcessor', 
    'YahooProcessor',
    'DataProcessorFactory',
    'DataManager',
    'TechnicalIndicators'
]

# 版本信息
__version__ = '0.1.0'

# 支持的数据源
SUPPORTED_DATA_SOURCES = DataProcessorFactory.get_supported_sources()

# 快速创建函数
def create_data_manager(data_source: str, 
                       start_date: str, 
                       end_date: str, 
                       **kwargs) -> DataManager:
    """快速创建数据管理器
    
    Args:
        data_source: 数据源名称
        start_date: 开始日期
        end_date: 结束日期
        **kwargs: 其他参数
        
    Returns:
        DataManager实例
    """
    return DataManager(
        data_source=data_source,
        start_date=start_date,
        end_date=end_date,
        **kwargs
    )

def get_supported_indicators() -> list:
    """获取支持的技术指标列表
    
    Returns:
        支持的指标列表
    """
    indicators = TechnicalIndicators()
    return indicators.get_supported_indicators()