#!/usr/bin/env python3
"""
FinRL Crypto 测试运行器
提供统一的测试执行、覆盖率检查和质量报告功能
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path
from typing import List, Dict, Optional
import json
import xml.etree.ElementTree as ET


class TestRunner:
    """测试运行器类"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root or os.getcwd())
        self.reports_dir = self.project_root / "reports"
        self.logs_dir = self.project_root / "tests" / "logs"
        self.ensure_directories()
        
    def ensure_directories(self):
        """确保必要的目录存在"""
        self.reports_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        
    def run_unit_tests(self, verbose: bool = True, coverage: bool = True) -> bool:
        """运行单元测试"""
        print("🧪 运行单元测试...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/unit/",
            "-m", "unit",
            "--tb=short",
            "--durations=10"
        ]
        
        if verbose:
            cmd.append("-v")
            
        if coverage:
            cmd.extend([
                "--cov=finrl_crypto",
                "--cov-report=html:htmlcov",
                "--cov-report=xml:coverage.xml",
                "--cov-report=term-missing",
                "--cov-fail-under=80"
            ])
            
        return self._run_command(cmd, "单元测试")
        
    def run_integration_tests(self, verbose: bool = True) -> bool:
        """运行集成测试"""
        print("🔗 运行集成测试...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/integration/",
            "-m", "integration",
            "--tb=short",
            "--durations=10"
        ]
        
        if verbose:
            cmd.append("-v")
            
        return self._run_command(cmd, "集成测试")
        
    def run_e2e_tests(self, verbose: bool = True) -> bool:
        """运行端到端测试"""
        print("🎯 运行端到端测试...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/e2e/",
            "-m", "e2e",
            "--tb=short",
            "--durations=10"
        ]
        
        if verbose:
            cmd.append("-v")
            
        return self._run_command(cmd, "端到端测试")
        
    def run_performance_tests(self, verbose: bool = True) -> bool:
        """运行性能测试"""
        print("⚡ 运行性能测试...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/",
            "-m", "performance",
            "--tb=short",
            "--durations=0",
            "--benchmark-only",
            "--benchmark-json=reports/benchmark.json"
        ]
        
        if verbose:
            cmd.append("-v")
            
        return self._run_command(cmd, "性能测试")
        
    def run_security_tests(self, verbose: bool = True) -> bool:
        """运行安全测试"""
        print("🔒 运行安全测试...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/",
            "-m", "security",
            "--tb=short"
        ]
        
        if verbose:
            cmd.append("-v")
            
        return self._run_command(cmd, "安全测试")
        
    def run_all_tests(self, verbose: bool = True, coverage: bool = True) -> bool:
        """运行所有测试"""
        print("🚀 运行完整测试套件...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/",
            "--tb=short",
            "--durations=10",
            "--junitxml=reports/junit.xml",
            "--html=reports/report.html",
            "--self-contained-html"
        ]
        
        if verbose:
            cmd.append("-v")
            
        if coverage:
            cmd.extend([
                "--cov=finrl_crypto",
                "--cov-report=html:htmlcov",
                "--cov-report=xml:coverage.xml",
                "--cov-report=term-missing",
                "--cov-fail-under=80"
            ])
            
        return self._run_command(cmd, "完整测试套件")
        
    def run_quick_tests(self, verbose: bool = False) -> bool:
        """运行快速测试（排除慢速测试）"""
        print("⚡ 运行快速测试...")
        
        cmd = [
            "python", "-m", "pytest",
            "tests/",
            "-m", "not slow",
            "--tb=short",
            "--maxfail=5"
        ]
        
        if verbose:
            cmd.append("-v")
            
        return self._run_command(cmd, "快速测试")
        
    def run_code_quality_checks(self) -> Dict[str, bool]:
        """运行代码质量检查"""
        print("📊 运行代码质量检查...")
        
        results = {}
        
        # Black 代码格式化检查
        print("  检查代码格式 (Black)...")
        results['black'] = self._run_command([
            "python", "-m", "black", "--check", "--diff", "."
        ], "Black格式检查", capture_output=True)
        
        # isort 导入排序检查
        print("  检查导入排序 (isort)...")
        results['isort'] = self._run_command([
            "python", "-m", "isort", "--check-only", "--diff", "."
        ], "isort导入检查", capture_output=True)
        
        # flake8 代码风格检查
        print("  检查代码风格 (flake8)...")
        results['flake8'] = self._run_command([
            "python", "-m", "flake8", ".", "--output-file=reports/flake8.txt"
        ], "flake8风格检查", capture_output=True)
        
        # mypy 类型检查
        print("  检查类型注解 (mypy)...")
        results['mypy'] = self._run_command([
            "python", "-m", "mypy", ".", "--html-report", "reports/mypy"
        ], "mypy类型检查", capture_output=True)
        
        # pylint 代码质量检查
        print("  检查代码质量 (pylint)...")
        results['pylint'] = self._run_command([
            "python", "-m", "pylint", "finrl_crypto", "--output-format=json", "--reports=y"
        ], "pylint质量检查", capture_output=True)
        
        # bandit 安全检查
        print("  检查安全问题 (bandit)...")
        results['bandit'] = self._run_command([
            "python", "-m", "bandit", "-r", ".", "-f", "json", "-o", "reports/bandit.json"
        ], "bandit安全检查", capture_output=True)
        
        # safety 依赖安全检查
        print("  检查依赖安全 (safety)...")
        results['safety'] = self._run_command([
            "python", "-m", "safety", "check", "--json", "--output", "reports/safety.json"
        ], "safety依赖检查", capture_output=True)
        
        return results
        
    def fix_code_style(self) -> bool:
        """自动修复代码风格问题"""
        print("🔧 自动修复代码风格...")
        
        # Black 格式化
        print("  格式化代码 (Black)...")
        black_result = self._run_command([
            "python", "-m", "black", "."
        ], "Black格式化")
        
        # isort 排序导入
        print("  排序导入 (isort)...")
        isort_result = self._run_command([
            "python", "-m", "isort", "."
        ], "isort导入排序")
        
        return black_result and isort_result
        
    def generate_test_report(self) -> Dict:
        """生成测试报告摘要"""
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "coverage": self._parse_coverage_report(),
            "test_results": self._parse_junit_report(),
            "quality_metrics": self._parse_quality_reports()
        }
        
        # 保存报告
        report_file = self.reports_dir / "test_summary.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        return report
        
    def _run_command(self, cmd: List[str], description: str, 
                    capture_output: bool = False) -> bool:
        """运行命令并返回是否成功"""
        try:
            if capture_output:
                result = subprocess.run(
                    cmd, 
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=300
                )
            else:
                result = subprocess.run(
                    cmd, 
                    cwd=self.project_root,
                    timeout=300
                )
                
            if result.returncode == 0:
                print(f"  ✅ {description} 成功")
                return True
            else:
                print(f"  ❌ {description} 失败 (退出码: {result.returncode})")
                if capture_output and result.stderr:
                    print(f"     错误: {result.stderr.strip()}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ {description} 超时")
            return False
        except Exception as e:
            print(f"  💥 {description} 异常: {e}")
            return False
            
    def _parse_coverage_report(self) -> Dict:
        """解析覆盖率报告"""
        coverage_file = self.project_root / "coverage.xml"
        if not coverage_file.exists():
            return {}
            
        try:
            tree = ET.parse(coverage_file)
            root = tree.getroot()
            
            coverage_data = {
                "line_rate": float(root.get("line-rate", 0)) * 100,
                "branch_rate": float(root.get("branch-rate", 0)) * 100,
                "lines_covered": int(root.get("lines-covered", 0)),
                "lines_valid": int(root.get("lines-valid", 0)),
                "branches_covered": int(root.get("branches-covered", 0)),
                "branches_valid": int(root.get("branches-valid", 0))
            }
            
            return coverage_data
        except Exception as e:
            print(f"解析覆盖率报告失败: {e}")
            return {}
            
    def _parse_junit_report(self) -> Dict:
        """解析JUnit测试报告"""
        junit_file = self.reports_dir / "junit.xml"
        if not junit_file.exists():
            return {}
            
        try:
            tree = ET.parse(junit_file)
            root = tree.getroot()
            
            test_data = {
                "tests": int(root.get("tests", 0)),
                "failures": int(root.get("failures", 0)),
                "errors": int(root.get("errors", 0)),
                "skipped": int(root.get("skipped", 0)),
                "time": float(root.get("time", 0))
            }
            
            test_data["success_rate"] = (
                (test_data["tests"] - test_data["failures"] - test_data["errors"]) 
                / max(test_data["tests"], 1) * 100
            )
            
            return test_data
        except Exception as e:
            print(f"解析JUnit报告失败: {e}")
            return {}
            
    def _parse_quality_reports(self) -> Dict:
        """解析代码质量报告"""
        quality_data = {}
        
        # 解析bandit安全报告
        bandit_file = self.reports_dir / "bandit.json"
        if bandit_file.exists():
            try:
                with open(bandit_file, 'r') as f:
                    bandit_data = json.load(f)
                    quality_data["security_issues"] = len(bandit_data.get("results", []))
            except Exception:
                pass
                
        # 解析safety依赖报告
        safety_file = self.reports_dir / "safety.json"
        if safety_file.exists():
            try:
                with open(safety_file, 'r') as f:
                    safety_data = json.load(f)
                    quality_data["vulnerability_count"] = len(safety_data)
            except Exception:
                pass
                
        return quality_data
        
    def print_summary(self, report: Dict):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("📊 测试报告摘要")
        print("="*60)
        
        # 覆盖率信息
        if "coverage" in report and report["coverage"]:
            cov = report["coverage"]
            print(f"📈 代码覆盖率: {cov.get('line_rate', 0):.1f}%")
            print(f"🌿 分支覆盖率: {cov.get('branch_rate', 0):.1f}%")
            
        # 测试结果
        if "test_results" in report and report["test_results"]:
            test = report["test_results"]
            print(f"🧪 测试总数: {test.get('tests', 0)}")
            print(f"✅ 成功率: {test.get('success_rate', 0):.1f}%")
            print(f"❌ 失败数: {test.get('failures', 0)}")
            print(f"💥 错误数: {test.get('errors', 0)}")
            print(f"⏭️ 跳过数: {test.get('skipped', 0)}")
            print(f"⏱️ 执行时间: {test.get('time', 0):.2f}秒")
            
        # 质量指标
        if "quality_metrics" in report and report["quality_metrics"]:
            quality = report["quality_metrics"]
            if "security_issues" in quality:
                print(f"🔒 安全问题: {quality['security_issues']}")
            if "vulnerability_count" in quality:
                print(f"🚨 依赖漏洞: {quality['vulnerability_count']}")
                
        print("="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="FinRL Crypto 测试运行器")
    parser.add_argument("--type", 
                       choices=["unit", "integration", "e2e", "performance", 
                               "security", "all", "quick"],
                       default="all",
                       help="测试类型")
    parser.add_argument("--no-coverage", action="store_true",
                       help="禁用覆盖率检查")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="详细输出")
    parser.add_argument("--quality", action="store_true",
                       help="运行代码质量检查")
    parser.add_argument("--fix", action="store_true",
                       help="自动修复代码风格")
    parser.add_argument("--report", action="store_true",
                       help="生成测试报告")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    # 自动修复代码风格
    if args.fix:
        runner.fix_code_style()
        return
        
    # 代码质量检查
    if args.quality:
        results = runner.run_code_quality_checks()
        passed = sum(results.values())
        total = len(results)
        print(f"\n质量检查结果: {passed}/{total} 通过")
        return
        
    # 运行测试
    coverage = not args.no_coverage
    success = False
    
    if args.type == "unit":
        success = runner.run_unit_tests(args.verbose, coverage)
    elif args.type == "integration":
        success = runner.run_integration_tests(args.verbose)
    elif args.type == "e2e":
        success = runner.run_e2e_tests(args.verbose)
    elif args.type == "performance":
        success = runner.run_performance_tests(args.verbose)
    elif args.type == "security":
        success = runner.run_security_tests(args.verbose)
    elif args.type == "quick":
        success = runner.run_quick_tests(args.verbose)
    else:  # all
        success = runner.run_all_tests(args.verbose, coverage)
        
    # 生成报告
    if args.report or args.type == "all":
        report = runner.generate_test_report()
        runner.print_summary(report)
        
    # 退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()