#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器测试
文件名：test_stage1_3_config_manager.py
目标：测试基础配置管理器的功能
"""

import pytest
import os
import tempfile
import json
import yaml
from datetime import datetime

# 添加项目根目录到路径
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config_manager import ConfigManager, get_config_manager, init_config_manager, get_config, set_config


class TestConfigManager:
    """配置管理器测试类"""
    
    def test_default_config_loading(self):
        """测试默认配置加载"""
        config_manager = ConfigManager()
        
        # 测试基础配置
        assert config_manager.get('seed') == 2390408
        assert config_manager.get('timeframe') == '5m'
        assert config_manager.get('h_trials') == 50
        assert config_manager.get('no_candles_for_train') == 20000
        assert config_manager.get('no_candles_for_val') == 5000
        
        # 测试列表配置
        ticker_list = config_manager.get('ticker_list')
        assert isinstance(ticker_list, list)
        assert len(ticker_list) == 10
        assert 'BTCUSDT' in ticker_list
        
        # 测试技术指标配置
        tech_indicators = config_manager.get('technical_indicators')
        assert isinstance(tech_indicators, list)
        assert 'close' in tech_indicators
        assert 'rsi' in tech_indicators
    
    def test_computed_config(self):
        """测试计算配置"""
        config_manager = ConfigManager()
        
        # 测试计算的配置项
        n_groups = config_manager.get('n_groups')
        assert n_groups == 5  # num_paths(4) + 1
        
        number_of_splits = config_manager.get('number_of_splits')
        assert isinstance(number_of_splits, int)
        assert number_of_splits > 0
        
        # 测试计算的日期
        train_start = config_manager.get('train_start_date')
        train_end = config_manager.get('train_end_date')
        val_start = config_manager.get('val_start_date')
        val_end = config_manager.get('val_end_date')
        
        assert isinstance(train_start, str)
        assert isinstance(train_end, str)
        assert isinstance(val_start, str)
        assert isinstance(val_end, str)
        
        # 验证日期格式
        datetime.strptime(train_start, "%Y-%m-%d %H:%M:%S")
        datetime.strptime(train_end, "%Y-%m-%d %H:%M:%S")
        datetime.strptime(val_start, "%Y-%m-%d %H:%M:%S")
        datetime.strptime(val_end, "%Y-%m-%d %H:%M:%S")
    
    def test_config_get_set(self):
        """测试配置的获取和设置"""
        config_manager = ConfigManager()
        
        # 测试获取不存在的配置
        assert config_manager.get('non_existent_key') is None
        assert config_manager.get('non_existent_key', 'default') == 'default'
        
        # 测试设置配置
        config_manager.set('test_key', 'test_value')
        assert config_manager.get('test_key') == 'test_value'
        
        # 测试修改现有配置
        original_timeframe = config_manager.get('timeframe')
        config_manager.set('timeframe', '1h')
        assert config_manager.get('timeframe') == '1h'
        
        # 验证派生配置重新计算
        new_train_start = config_manager.get('train_start_date')
        assert isinstance(new_train_start, str)
    
    def test_config_validation(self):
        """测试配置验证"""
        config_manager = ConfigManager()
        
        # 测试有效配置
        assert config_manager.validate_config() is True
        
        # 测试无效配置 - 负数蜡烛数
        config_manager.set('no_candles_for_train', -100)
        assert config_manager.validate_config() is False
        
        # 恢复有效配置
        config_manager.set('no_candles_for_train', 20000)
        assert config_manager.validate_config() is True
        
        # 测试无效时间框架
        config_manager.set('timeframe', 'invalid_timeframe')
        assert config_manager.validate_config() is False
    
    def test_json_config_file(self):
        """测试JSON配置文件加载和保存"""
        # 创建临时JSON配置文件
        test_config = {
            'seed': 12345,
            'timeframe': '1h',
            'h_trials': 100,
            'ticker_list': ['BTCUSDT', 'ETHUSDT']
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_config, f)
            temp_file = f.name
        
        try:
            # 从文件加载配置
            config_manager = ConfigManager(temp_file)
            
            # 验证配置加载
            assert config_manager.get('seed') == 12345
            assert config_manager.get('timeframe') == '1h'
            assert config_manager.get('h_trials') == 100
            
            ticker_list = config_manager.get('ticker_list')
            assert len(ticker_list) == 2
            assert 'BTCUSDT' in ticker_list
            
            # 测试保存配置
            config_manager.set('test_save', 'test_value')
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                save_file = f.name
            
            config_manager.save_to_file(save_file)
            
            # 验证保存的文件
            with open(save_file, 'r') as f:
                saved_config = json.load(f)
            
            assert saved_config['test_save'] == 'test_value'
            assert saved_config['seed'] == 12345
            
            os.unlink(save_file)
            
        finally:
            os.unlink(temp_file)
    
    def test_yaml_config_file(self):
        """测试YAML配置文件加载和保存"""
        # 创建临时YAML配置文件
        test_config = {
            'seed': 54321,
            'timeframe': '30m',
            'ticker_list': ['ADAUSDT', 'DOTUSDT', 'SOLUSDT']
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(test_config, f)
            temp_file = f.name
        
        try:
            # 从文件加载配置
            config_manager = ConfigManager(temp_file)
            
            # 验证配置加载
            assert config_manager.get('seed') == 54321
            assert config_manager.get('timeframe') == '30m'
            
            ticker_list = config_manager.get('ticker_list')
            assert len(ticker_list) == 3
            assert 'SOLUSDT' in ticker_list
            
            # 测试保存为YAML
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                save_file = f.name
            
            config_manager.save_to_file(save_file)
            
            # 验证保存的文件
            with open(save_file, 'r') as f:
                saved_config = yaml.safe_load(f)
            
            assert saved_config['seed'] == 54321
            assert saved_config['timeframe'] == '30m'
            
            os.unlink(save_file)
            
        finally:
            os.unlink(temp_file)
    
    def test_global_config_manager(self):
        """测试全局配置管理器"""
        # 测试单例模式
        manager1 = get_config_manager()
        manager2 = get_config_manager()
        assert manager1 is manager2
        
        # 测试便捷函数
        original_seed = get_config('seed')
        set_config('seed', 99999)
        assert get_config('seed') == 99999
        
        # 恢复原始值
        set_config('seed', original_seed)
    
    def test_get_all_config(self):
        """测试获取所有配置"""
        config_manager = ConfigManager()
        
        all_config = config_manager.get_all_config()
        
        # 验证包含基础配置
        assert 'seed' in all_config
        assert 'timeframe' in all_config
        assert 'ticker_list' in all_config
        
        # 验证包含计算配置
        assert 'train_start_date' in all_config
        assert 'n_groups' in all_config
        assert 'number_of_splits' in all_config
        
        # 验证配置值
        assert all_config['seed'] == 2390408
        assert isinstance(all_config['ticker_list'], list)
    
    def test_nCr_calculation(self):
        """测试组合数计算"""
        config_manager = ConfigManager()
        
        # 测试已知的组合数
        assert config_manager._nCr(5, 2) == 10
        assert config_manager._nCr(4, 1) == 4
        assert config_manager._nCr(6, 3) == 20
        assert config_manager._nCr(10, 0) == 1
        assert config_manager._nCr(5, 5) == 1
    
    def test_date_calculation(self):
        """测试日期计算"""
        config_manager = ConfigManager()
        
        # 设置已知的配置
        config_manager.set('trade_start_date', '2022-01-01 00:00:00')
        config_manager.set('timeframe', '1h')
        config_manager.set('no_candles_for_train', 100)
        config_manager.set('no_candles_for_val', 20)
        
        # 获取计算的日期
        train_start = config_manager.get('train_start_date')
        train_end = config_manager.get('train_end_date')
        val_start = config_manager.get('val_start_date')
        val_end = config_manager.get('val_end_date')
        
        # 验证日期顺序
        train_start_dt = datetime.strptime(train_start, "%Y-%m-%d %H:%M:%S")
        train_end_dt = datetime.strptime(train_end, "%Y-%m-%d %H:%M:%S")
        val_start_dt = datetime.strptime(val_start, "%Y-%m-%d %H:%M:%S")
        val_end_dt = datetime.strptime(val_end, "%Y-%m-%d %H:%M:%S")
        
        assert train_start_dt < train_end_dt
        assert train_end_dt < val_start_dt
        assert val_start_dt < val_end_dt
    
    def test_error_handling(self):
        """测试错误处理"""
        config_manager = ConfigManager()
        
        # 测试无效配置文件格式
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("invalid config")
            temp_file = f.name
        
        try:
            with pytest.raises(RuntimeError) as exc_info:
                config_manager.save_to_file(temp_file)
            assert "不支持的配置文件格式" in str(exc_info.value)
        finally:
            os.unlink(temp_file)
        
        # 测试无效JSON文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write("invalid json content")
            temp_file = f.name
        
        try:
            with pytest.raises(RuntimeError) as exc_info:
                ConfigManager(temp_file)
            assert "加载配置文件失败" in str(exc_info.value)
        finally:
            os.unlink(temp_file)


def test_config_manager_integration():
    """集成测试：模拟实际使用场景"""
    # 创建配置管理器
    config_manager = ConfigManager()
    
    # 模拟优化脚本的使用
    timeframe = config_manager.get('timeframe')
    no_candles_train = config_manager.get('no_candles_for_train')
    ticker_list = config_manager.get('ticker_list')
    
    assert timeframe in ['1m', '5m', '10m', '30m', '1h', '2h', '4h', '12h']
    assert no_candles_train > 0
    assert len(ticker_list) > 0
    
    # 模拟环境配置的使用
    train_start = config_manager.get('train_start_date')
    val_end = config_manager.get('val_end_date')
    
    assert isinstance(train_start, str)
    assert isinstance(val_end, str)
    
    # 验证配置的一致性
    assert config_manager.validate_config()


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])