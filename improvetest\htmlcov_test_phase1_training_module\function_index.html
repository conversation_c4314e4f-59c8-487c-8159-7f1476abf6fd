<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>FinRL Crypto Test Coverage Report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>FinRL Crypto Test Coverage Report:
            <span class="pc_cov">11.33%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>b</kbd>
                        <kbd>p</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-19 02:06 +0900
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="branches" aria-sort="none" data-default-sort-order="descending" data-shortcut="b">branches<span class="arrows"></span></th>
                <th id="partial" aria-sort="none" data-default-sort-order="descending" data-shortcut="p">partial<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db___init___py.html">core\__init__.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t56">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t56"><data value='load'>FileConfigLoader.load</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t87">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t87"><data value='load'>EnvironmentConfigLoader.load</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t127">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t127"><data value='init__'>ConfigValidator.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t130">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t130"><data value='validate'>ConfigValidator.validate</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t145">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t145"><data value='add_custom_rule'>ConfigValidator.add_custom_rule</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t154">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t154"><data value='apply_custom_rule'>ConfigValidator.apply_custom_rule</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t173">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t173"><data value='init__'>ConfigSchema.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t176">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t176"><data value='validate'>ConfigSchema.validate</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t192">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t192"><data value='init__'>UnifiedConfigManager.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t202">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t202"><data value='load_from_file'>UnifiedConfigManager.load_from_file</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t220">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t220"><data value='load_from_dict'>UnifiedConfigManager.load_from_dict</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t235">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t235"><data value='load_from_environment'>UnifiedConfigManager.load_from_environment</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t245">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t245"><data value='get_config'>UnifiedConfigManager.get_config</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t262">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t262"><data value='get_all_configs'>UnifiedConfigManager.get_all_configs</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t271">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t271"><data value='update_config'>UnifiedConfigManager.update_config</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t288">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t288"><data value='set_validation_schema'>UnifiedConfigManager.set_validation_schema</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t301">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t301"><data value='reload'>UnifiedConfigManager.reload</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t331">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t331"><data value='create_backup'>UnifiedConfigManager.create_backup</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t342">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t342"><data value='restore_backup'>UnifiedConfigManager.restore_backup</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t357">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t357"><data value='merge_config'>UnifiedConfigManager._merge_config</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t371">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t371"><data value='has_config'>UnifiedConfigManager.has_config</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t383">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t383"><data value='get_config_value'>UnifiedConfigManager.get_config_value</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t404">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t404"><data value='set_config_value'>UnifiedConfigManager.set_config_value</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t426">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t426"><data value='clear'>UnifiedConfigManager.clear</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t432">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t432"><data value='export_to_file'>UnifiedConfigManager.export_to_file</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t56">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t56"><data value='post_init__'>ServiceDescriptor.__post_init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t80">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t80"><data value='init__'>CircularDependencyError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t88">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t88"><data value='init__'>ServiceNotFoundError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t106">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t106"><data value='init__'>ServiceScope.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t111">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t111"><data value='get_instance'>ServiceScope.get_instance</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t116">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t116"><data value='set_instance'>ServiceScope.set_instance</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t121">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t121"><data value='dispose'>ServiceScope.dispose</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t136">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t136"><data value='init__'>DIContainer.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t146">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t146"><data value='register_singleton'>DIContainer.register_singleton</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t156">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t156"><data value='register_transient'>DIContainer.register_transient</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t166">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t166"><data value='register_scoped'>DIContainer.register_scoped</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t176">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t176"><data value='register_factory'>DIContainer.register_factory</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t185">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t185"><data value='register_instance'>DIContainer.register_instance</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t194">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t194"><data value='register_service'>DIContainer._register_service</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t201">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t201"><data value='get_service'>DIContainer.get_service</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t206">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t206"><data value='resolve_service'>DIContainer._resolve_service</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t227">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t227"><data value='resolve_singleton'>DIContainer._resolve_singleton</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t237">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t237"><data value='resolve_scoped'>DIContainer._resolve_scoped</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t251">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t251"><data value='resolve_transient'>DIContainer._resolve_transient</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t255">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t255"><data value='create_instance'>DIContainer._create_instance</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t269">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t269"><data value='create_instance_from_type'>DIContainer._create_instance_from_type</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t332">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t332"><data value='is_registered'>DIContainer.is_registered</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t337">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t337"><data value='get_registered_services'>DIContainer.get_registered_services</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t343">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t343"><data value='create_scope'>DIContainer.create_scope</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t365">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t365"><data value='load_from_config'>DIContainer.load_from_config</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t399">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t399"><data value='validate_config'>DIContainer.validate_config</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t440">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t440"><data value='resolve_type_from_string'>DIContainer._resolve_type_from_string</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t449">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t449"><data value='resolve_factory_from_string'>DIContainer._resolve_factory_from_string</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t461">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t461"><data value='dispose'>DIContainer.dispose</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t492">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t492"><data value='get_default_container'>get_default_container</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t504">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t504"><data value='set_default_container'>set_default_container</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t513">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t513"><data value='injectable'>injectable</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t520">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t520"><data value='inject'>inject</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t522">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t522"><data value='wrapper'>inject.wrapper</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t543">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t543"><data value='Singleton'>Singleton</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t549">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t549"><data value='Transient'>Transient</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t555">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t555"><data value='Scoped'>Scoped</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t562">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t562"><data value='register_singleton'>register_singleton</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t567">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t567"><data value='register_transient'>register_transient</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t572">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t572"><data value='register_scoped'>register_scoped</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t577">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t577"><data value='get_service'>get_service</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>77</td>
                <td>77</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t61">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t61"><data value='init__'>DRLAgentConfigValidator.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t69">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t69"><data value='validate'>DRLAgentConfigValidator.validate</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t106">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t106"><data value='init__'>AgentFactory.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t116">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t116"><data value='get_supported_models'>AgentFactory.get_supported_models</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t120">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t120"><data value='is_model_supported'>AgentFactory.is_model_supported</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t124">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t124"><data value='create_agent'>AgentFactory.create_agent</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t133">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t133"><data value='get_agent_config'>AgentFactory.get_agent_config</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t145">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t145"><data value='init__'>ModelBuilder.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t148">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t148"><data value='build_model_args'>ModelBuilder.build_model_args</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t189">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t189"><data value='get_model_config'>ModelBuilder.get_model_config</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t201">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t201"><data value='init__'>TrainingService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t204">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t204"><data value='get_training_config'>TrainingService.get_training_config</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t211">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t211"><data value='configure_training'>TrainingService.configure_training</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t231">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t231"><data value='train_model'>TrainingService.train_model</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t238">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t238"><data value='get_training_metrics'>TrainingService.get_training_metrics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t250">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t250"><data value='init__'>PredictionService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t253">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t253"><data value='get_prediction_config'>PredictionService.get_prediction_config</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t260">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t260"><data value='load_model'>PredictionService.load_model</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t281">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t281"><data value='predict'>PredictionService.predict</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t320">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t320"><data value='get_prediction_metrics'>PredictionService.get_prediction_metrics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t332">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t332"><data value='init__'>RefactoredDRLAgent.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t348">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t348"><data value='validate_configuration'>RefactoredDRLAgent._validate_configuration</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t354">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t354"><data value='create_model'>RefactoredDRLAgent.create_model</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t364">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t364"><data value='train'>RefactoredDRLAgent.train</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t375">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t375"><data value='predict'>RefactoredDRLAgent.predict</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t392">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t392"><data value='get_metrics'>RefactoredDRLAgent.get_metrics</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t405">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t405"><data value='get_model'>RefactoredDRLAgent.get_model</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t425">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t425"><data value='init__'>RefactoredDRLAgent.get_model.DummyEnv.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t435">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t435"><data value='register_drl_agent_dependencies'>register_drl_agent_dependencies</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t445">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t445"><data value='create_drl_agent_with_config'>create_drl_agent_with_config</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t460">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t460"><data value='create_drl_agent_from_file'>create_drl_agent_from_file</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ddb02fcb4060c71d___init___py.html">finrl_crypto\__init__.py</a></td>
                <td class="name left"><a href="z_ddb02fcb4060c71d___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 17">76.47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be___init___py.html#t60">finrl_crypto\agent\__init__.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be___init___py.html#t60"><data value='create_trading_agent'>create_trading_agent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be___init___py.html">finrl_crypto\agent\__init__.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t20">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t20"><data value='init__'>A2CActor.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t62">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t62"><data value='forward'>A2CActor.forward</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t82">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t82"><data value='get_action_and_log_prob'>A2CActor.get_action_and_log_prob</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t106">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t106"><data value='get_log_prob_and_entropy'>A2CActor.get_log_prob_and_entropy</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t133">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t133"><data value='init__'>A2CCritic.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t156">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t156"><data value='forward'>A2CCritic.forward</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t174">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t174"><data value='init__'>A2CAgent.__init__</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t244">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t244"><data value='build_networks'>A2CAgent._build_networks</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t263">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t263"><data value='build_optimizers'>A2CAgent._build_optimizers</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t270">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t270"><data value='act'>A2CAgent.act</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t306">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t306"><data value='store_reward_and_done'>A2CAgent.store_reward_and_done</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t316">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t316"><data value='learn'>A2CAgent.learn</data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t412">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t412"><data value='reset_episode'>A2CAgent.reset_episode</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t422">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t422"><data value='get_save_dict'>A2CAgent._get_save_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t435">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t435"><data value='load_from_dict'>A2CAgent._load_from_dict</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t441">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t441"><data value='set_training_mode'>A2CAgent.set_training_mode</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t450">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t450"><data value='get_action_distribution'>A2CAgent.get_action_distribution</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t472">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t472"><data value='get_value'>A2CAgent.get_value</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t23">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t23"><data value='init__'>BaseAgent.__init__</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t114">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t114"><data value='set_seed'>BaseAgent.set_seed</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t130">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t130"><data value='save'>BaseAgent.save</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t156">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t156"><data value='load'>BaseAgent.load</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t197">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t197"><data value='update_episode_stats'>BaseAgent.update_episode_stats</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t208">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t208"><data value='get_stats'>BaseAgent.get_stats</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t242">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t242"><data value='reset_stats'>BaseAgent.reset_stats</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t250">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t250"><data value='set_training_mode'>BaseAgent.set_training_mode</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t259">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t259"><data value='get_action_distribution'>BaseAgent.get_action_distribution</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t275">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t275"><data value='get_model_info'>BaseAgent.get_model_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t306">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t306"><data value='init__'>ReplayBuffer.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t325">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t325"><data value='add'>ReplayBuffer.add</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t349">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t349"><data value='sample'>ReplayBuffer.sample</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t372">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t372"><data value='is_ready'>ReplayBuffer.is_ready</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t384">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t384"><data value='create_mlp'>create_mlp</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t19">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t19"><data value='init__'>DQNNetwork.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t48">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t48"><data value='forward'>DQNNetwork.forward</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t66">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t66"><data value='init__'>DQNAgent.__init__</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t137">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t137"><data value='build_networks'>DQNAgent._build_networks</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t176">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t176"><data value='build_optimizers'>DQNAgent._build_optimizers</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t183">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t183"><data value='act'>DQNAgent.act</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t206">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t206"><data value='learn'>DQNAgent.learn</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t293">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t293"><data value='get_save_dict'>DQNAgent._get_save_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t310">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t310"><data value='load_from_dict'>DQNAgent._load_from_dict</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t318">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t318"><data value='set_training_mode'>DQNAgent.set_training_mode</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t325">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t325"><data value='get_action_distribution'>DQNAgent.get_action_distribution</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t348">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t348"><data value='init__'>DuelingDQNNetwork.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t383">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t383"><data value='forward'>DuelingDQNNetwork.forward</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t82">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t82"><data value='create_agent'>AgentFactory.create_agent</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t124">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t124"><data value='register_agent'>AgentFactory.register_agent</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t137">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t137"><data value='get_available_agents'>AgentFactory.get_available_agents</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t146">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t146"><data value='get_agent_info'>AgentFactory.get_agent_info</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t171">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t171"><data value='create_dqn_agent'>AgentFactory.create_dqn_agent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t188">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t188"><data value='create_ppo_agent'>AgentFactory.create_ppo_agent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t205">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t205"><data value='create_a2c_agent'>AgentFactory.create_a2c_agent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t222">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t222"><data value='create_sac_agent'>AgentFactory.create_sac_agent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t239">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t239"><data value='validate_config'>AgentFactory.validate_config</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t276">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t276"><data value='get_recommended_config'>AgentFactory.get_recommended_config</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t333">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t333"><data value='create_agent'>create_agent</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t351">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t351"><data value='get_available_agents'>get_available_agents</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t360">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t360"><data value='get_agent_info'>get_agent_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t20">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t20"><data value='init__'>PPOActor.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t62">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t62"><data value='forward'>PPOActor.forward</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t82">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t82"><data value='get_action_and_log_prob'>PPOActor.get_action_and_log_prob</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t104">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t104"><data value='get_log_prob'>PPOActor.get_log_prob</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t127">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t127"><data value='init__'>PPOCritic.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t150">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t150"><data value='forward'>PPOCritic.forward</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t165">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t165"><data value='init__'>PPOBuffer.__init__</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t190">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t190"><data value='add'>PPOBuffer.add</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t212">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t212"><data value='compute_gae'>PPOBuffer.compute_gae</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t240">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t240"><data value='get_batch'>PPOBuffer.get_batch</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t250">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t250"><data value='clear'>PPOBuffer.clear</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t262">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t262"><data value='init__'>PPOAgent.__init__</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t344">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t344"><data value='build_networks'>PPOAgent._build_networks</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t363">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t363"><data value='build_optimizers'>PPOAgent._build_optimizers</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t370">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t370"><data value='act'>PPOAgent.act</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t398">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t398"><data value='get_value'>PPOAgent.get_value</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t412">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t412"><data value='store_transition'>PPOAgent.store_transition</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t435">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t435"><data value='learn'>PPOAgent.learn</data></a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t559">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t559"><data value='get_save_dict'>PPOAgent._get_save_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t573">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t573"><data value='load_from_dict'>PPOAgent._load_from_dict</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t579">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t579"><data value='set_training_mode'>PPOAgent.set_training_mode</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t588">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t588"><data value='get_action_distribution'>PPOAgent.get_action_distribution</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t20">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t20"><data value='init__'>SACGaussianActor.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t59">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t59"><data value='forward'>SACGaussianActor.forward</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t74">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t74"><data value='sample'>SACGaussianActor.sample</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t109">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t109"><data value='get_log_prob'>SACGaussianActor.get_log_prob</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t138">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t138"><data value='init__'>SACCritic.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t163">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t163"><data value='forward'>SACCritic.forward</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t183">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t183"><data value='init__'>SACAgent.__init__</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t263">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t263"><data value='build_networks'>SACAgent._build_networks</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t319">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t319"><data value='build_optimizers'>SACAgent._build_optimizers</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t342">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t342"><data value='act'>SACAgent.act</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t363">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t363"><data value='learn'>SACAgent.learn</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t431">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t431"><data value='update_critics'>SACAgent._update_critics</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t463">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t463"><data value='update_actor'>SACAgent._update_actor</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t483">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t483"><data value='update_alpha'>SACAgent._update_alpha</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t501">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t501"><data value='soft_update_targets'>SACAgent._soft_update_targets</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t509">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t509"><data value='get_save_dict'>SACAgent._get_save_dict</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t536">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t536"><data value='load_from_dict'>SACAgent._load_from_dict</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t555">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t555"><data value='set_training_mode'>SACAgent.set_training_mode</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t566">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t566"><data value='get_action_distribution'>SACAgent.get_action_distribution</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05___init___py.html#t51">finrl_crypto\environment\__init__.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05___init___py.html#t51"><data value='create_environment'>create_environment</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05___init___py.html">finrl_crypto\environment\__init__.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t44">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t44"><data value='init__'>BaseEnvironment.__init__</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t125">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t125"><data value='preprocess_data'>BaseEnvironment._preprocess_data</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t150">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t150"><data value='validate_data'>BaseEnvironment._validate_data</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t213">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t213"><data value='reset'>BaseEnvironment.reset</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t247">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t247"><data value='step'>BaseEnvironment.step</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t292">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t292"><data value='get_date'>BaseEnvironment._get_date</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t310">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t310"><data value='get_current_data'>BaseEnvironment._get_current_data</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t328">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t328"><data value='update_turbulence'>BaseEnvironment._update_turbulence</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t335">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t335"><data value='print_progress'>BaseEnvironment._print_progress</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t348">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t348"><data value='get_portfolio_history'>BaseEnvironment.get_portfolio_history</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t368">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t368"><data value='get_performance_metrics'>BaseEnvironment.get_performance_metrics</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t390">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t390"><data value='calculate_max_drawdown'>BaseEnvironment._calculate_max_drawdown</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t401">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t401"><data value='render'>BaseEnvironment.render</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t418">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t418"><data value='close'>BaseEnvironment.close</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t422">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t422"><data value='seed'>BaseEnvironment.seed</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>41</td>
                <td>2</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 41">95.12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t45">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t45"><data value='init__'>CryptoTradingEnvironment.__init__</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t137">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t137"><data value='setup_spaces'>CryptoTradingEnvironment._setup_spaces</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t189">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t189"><data value='calculate_normalization_params'>CryptoTradingEnvironment._calculate_normalization_params</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t202">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t202"><data value='get_state'>CryptoTradingEnvironment._get_state</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t232">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t232"><data value='get_data_for_day'>CryptoTradingEnvironment._get_data_for_day</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t246">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t246"><data value='extract_features_from_data'>CryptoTradingEnvironment._extract_features_from_data</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t285">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t285"><data value='get_portfolio_state'>CryptoTradingEnvironment._get_portfolio_state</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t306">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t306"><data value='get_expected_feature_length'>CryptoTradingEnvironment._get_expected_feature_length</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t310">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t310"><data value='get_single_day_feature_length'>CryptoTradingEnvironment._get_single_day_feature_length</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t314">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t314"><data value='get_single_asset_feature_length'>CryptoTradingEnvironment._get_single_asset_feature_length</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t321">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t321"><data value='calculate_reward'>CryptoTradingEnvironment._calculate_reward</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t349">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t349"><data value='execute_action'>CryptoTradingEnvironment._execute_action</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t409">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t409"><data value='get_current_prices'>CryptoTradingEnvironment._get_current_prices</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t442">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t442"><data value='get_total_portfolio_value'>CryptoTradingEnvironment._get_total_portfolio_value</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t448">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t448"><data value='calculate_transaction_cost'>CryptoTradingEnvironment._calculate_transaction_cost</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t465">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t465"><data value='calculate_transaction_cost_from_trades'>CryptoTradingEnvironment._calculate_transaction_cost_from_trades</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t470">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t470"><data value='reset_specific_state'>CryptoTradingEnvironment._reset_specific_state</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t478">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t478"><data value='get_portfolio_allocation'>CryptoTradingEnvironment.get_portfolio_allocation</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t494">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t494"><data value='get_detailed_info'>CryptoTradingEnvironment.get_detailed_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>44</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="42 44">95.45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t28">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t28"><data value='create_environment'>EnvironmentFactory.create_environment</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t63">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t63"><data value='register_environment'>EnvironmentFactory.register_environment</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t79">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t79"><data value='get_available_environments'>EnvironmentFactory.get_available_environments</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t88">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t88"><data value='create_crypto_trading_environment'>EnvironmentFactory.create_crypto_trading_environment</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t113">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t113"><data value='create_portfolio_environment'>EnvironmentFactory.create_portfolio_environment</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t138">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t138"><data value='get_environment_info'>EnvironmentFactory.get_environment_info</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t160">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t160"><data value='validate_environment_config'>EnvironmentFactory.validate_environment_config</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t201">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t201"><data value='validate_crypto_trading_config'>EnvironmentFactory._validate_crypto_trading_config</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t234">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t234"><data value='validate_portfolio_config'>EnvironmentFactory._validate_portfolio_config</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t275">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t275"><data value='create_trading_environment'>create_trading_environment</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t288">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t288"><data value='create_portfolio_environment'>create_portfolio_environment</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t301">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t301"><data value='get_supported_environments'>get_supported_environments</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t45">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t45"><data value='init__'>PortfolioEnvironment.__init__</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t157">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t157"><data value='setup_spaces'>PortfolioEnvironment._setup_spaces</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t200">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t200"><data value='get_state'>PortfolioEnvironment._get_state</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t234">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t234"><data value='calculate_reward'>PortfolioEnvironment._calculate_reward</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t270">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t270"><data value='execute_action'>PortfolioEnvironment._execute_action</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t335">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t335"><data value='calculate_portfolio_return'>PortfolioEnvironment._calculate_portfolio_return</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t345">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t345"><data value='calculate_portfolio_volatility'>PortfolioEnvironment._calculate_portfolio_volatility</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t356">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t356"><data value='calculate_sharpe_ratio'>PortfolioEnvironment._calculate_sharpe_ratio</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t371">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t371"><data value='calculate_portfolio_value'>PortfolioEnvironment._calculate_portfolio_value</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t404">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t404"><data value='get_current_prices'>PortfolioEnvironment._get_current_prices</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t422">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t422"><data value='update_weights_by_price_change'>PortfolioEnvironment._update_weights_by_price_change</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t453">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t453"><data value='calculate_transaction_cost'>PortfolioEnvironment._calculate_transaction_cost</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t466">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t466"><data value='reset_specific_state'>PortfolioEnvironment._reset_specific_state</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t482">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t482"><data value='get_portfolio_weights'>PortfolioEnvironment.get_portfolio_weights</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t500">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t500"><data value='get_portfolio_performance'>PortfolioEnvironment.get_portfolio_performance</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t544">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t544"><data value='get_detailed_info'>PortfolioEnvironment.get_detailed_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>41</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 41">95.12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a___init___py.html#t79">finrl_crypto\training\__init__.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a___init___py.html#t79"><data value='create_single_asset_trainer'>create_single_asset_trainer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a___init___py.html#t83">finrl_crypto\training\__init__.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a___init___py.html#t83"><data value='create_multi_asset_trainer'>create_multi_asset_trainer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a___init___py.html#t87">finrl_crypto\training\__init__.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a___init___py.html#t87"><data value='create_portfolio_trainer'>create_portfolio_trainer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a___init___py.html#t91">finrl_crypto\training\__init__.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a___init___py.html#t91"><data value='get_supported_trainers'>get_supported_trainers</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a___init___py.html">finrl_crypto\training\__init__.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t79">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t79"><data value='to_dict'>EvaluationMetrics.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t103">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t103"><data value='init__'>BaseTrainer.__init__</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t159">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t159"><data value='set_seed'>BaseTrainer._set_seed</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t169">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t169"><data value='setup_logging'>BaseTrainer._setup_logging</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t204">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t204"><data value='save_model'>BaseTrainer.save_model</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t216">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t216"><data value='load_model'>BaseTrainer.load_model</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t225">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t225"><data value='save_training_history'>BaseTrainer.save_training_history</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t240">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t240"><data value='plot_training_curves'>BaseTrainer.plot_training_curves</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t298">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t298"><data value='should_evaluate'>BaseTrainer._should_evaluate</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t303">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t303"><data value='should_save'>BaseTrainer._should_save</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t308">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t308"><data value='should_log'>BaseTrainer._should_log</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t313">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t313"><data value='check_early_stopping'>BaseTrainer._check_early_stopping</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t330">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t330"><data value='log_training_info'>BaseTrainer._log_training_info</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t345">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t345"><data value='get_training_info'>BaseTrainer.get_training_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t361">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t361"><data value='reset_training_state'>BaseTrainer.reset_training_state</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>69</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="69 69">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t23">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t23"><data value='init__'>TrainerFactory.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t80">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t80"><data value='create_trainer'>TrainerFactory.create_trainer</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t137">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t137"><data value='register_trainer'>TrainerFactory.register_trainer</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t150">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t150"><data value='get_available_trainers'>TrainerFactory.get_available_trainers</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t158">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t158"><data value='get_trainer_info'>TrainerFactory.get_trainer_info</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t180">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t180"><data value='get_default_config'>TrainerFactory.get_default_config</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t210">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t210"><data value='create_single_asset_trainer'>TrainerFactory.create_single_asset_trainer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t237">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t237"><data value='create_multi_asset_trainer'>TrainerFactory.create_multi_asset_trainer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t264">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t264"><data value='create_portfolio_trainer'>TrainerFactory.create_portfolio_trainer</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t298">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t298"><data value='validate_config'>TrainerFactory._validate_config</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t338">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t338"><data value='get_recommended_config'>TrainerFactory.get_recommended_config</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t400">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t400"><data value='create_trainer'>create_trainer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t432">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t432"><data value='create_single_asset_trainer'>create_single_asset_trainer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t447">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t447"><data value='create_multi_asset_trainer'>create_multi_asset_trainer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t462">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t462"><data value='create_portfolio_trainer'>create_portfolio_trainer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t479">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t479"><data value='get_available_trainers'>get_available_trainers</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t484">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t484"><data value='get_trainer_info'>get_trainer_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t489">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t489"><data value='get_recommended_config'>get_recommended_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t23">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t23"><data value='init__'>MultiAssetTrainer.__init__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t80">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t80"><data value='train'>MultiAssetTrainer.train</data></a></td>
                <td>68</td>
                <td>68</td>
                <td>0</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 102">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t208">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t208"><data value='evaluate'>MultiAssetTrainer.evaluate</data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 71">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t315">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t315"><data value='update_multi_asset_stats'>MultiAssetTrainer._update_multi_asset_stats</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t353">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t353"><data value='log_episode_info'>MultiAssetTrainer._log_episode_info</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t385">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t385"><data value='calculate_sharpe_ratio'>MultiAssetTrainer._calculate_sharpe_ratio</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t401">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t401"><data value='calculate_max_drawdown'>MultiAssetTrainer._calculate_max_drawdown</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t418">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t418"><data value='calculate_diversification_ratio'>MultiAssetTrainer._calculate_diversification_ratio</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t438">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t438"><data value='calculate_asset_correlations'>MultiAssetTrainer._calculate_asset_correlations</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t459">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t459"><data value='generate_training_report'>MultiAssetTrainer._generate_training_report</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t532">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t532"><data value='get_asset_statistics'>MultiAssetTrainer.get_asset_statistics</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t572">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t572"><data value='get_portfolio_statistics'>MultiAssetTrainer.get_portfolio_statistics</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t599">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t599"><data value='reset_statistics'>MultiAssetTrainer.reset_statistics</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t23">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t23"><data value='init__'>PortfolioTrainer.__init__</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t101">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t101"><data value='train'>PortfolioTrainer.train</data></a></td>
                <td>71</td>
                <td>71</td>
                <td>0</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 103">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t236">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t236"><data value='evaluate'>PortfolioTrainer.evaluate</data></a></td>
                <td>67</td>
                <td>67</td>
                <td>0</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 83">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t356">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t356"><data value='calculate_episode_metrics'>PortfolioTrainer._calculate_episode_metrics</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t390">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t390"><data value='update_portfolio_stats'>PortfolioTrainer._update_portfolio_stats</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t435">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t435"><data value='log_episode_info'>PortfolioTrainer._log_episode_info</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t465">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t465"><data value='calculate_sharpe_ratio'>PortfolioTrainer._calculate_sharpe_ratio</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t473">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t473"><data value='calculate_sortino_ratio'>PortfolioTrainer._calculate_sortino_ratio</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t490">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t490"><data value='calculate_calmar_ratio'>PortfolioTrainer._calculate_calmar_ratio</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t498">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t498"><data value='calculate_max_drawdown'>PortfolioTrainer._calculate_max_drawdown</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t508">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t508"><data value='calculate_information_ratio'>PortfolioTrainer._calculate_information_ratio</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t521">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t521"><data value='calculate_benchmark_returns'>PortfolioTrainer._calculate_benchmark_returns</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t534">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t534"><data value='calculate_final_risk_metrics'>PortfolioTrainer._calculate_final_risk_metrics</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t555">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t555"><data value='generate_training_report'>PortfolioTrainer._generate_training_report</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t614">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t614"><data value='get_portfolio_statistics'>PortfolioTrainer.get_portfolio_statistics</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t633">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t633"><data value='get_risk_metrics'>PortfolioTrainer.get_risk_metrics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t637">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t637"><data value='reset_statistics'>PortfolioTrainer.reset_statistics</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t23">finrl_crypto\training\single_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t23"><data value='init__'>SingleAssetTrainer.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t63">finrl_crypto\training\single_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t63"><data value='train'>SingleAssetTrainer.train</data></a></td>
                <td>59</td>
                <td>59</td>
                <td>0</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 87">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t178">finrl_crypto\training\single_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t178"><data value='evaluate'>SingleAssetTrainer.evaluate</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t255">finrl_crypto\training\single_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t255"><data value='update_trade_stats'>SingleAssetTrainer._update_trade_stats</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t272">finrl_crypto\training\single_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t272"><data value='log_episode_info'>SingleAssetTrainer._log_episode_info</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t294">finrl_crypto\training\single_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t294"><data value='calculate_sharpe_ratio'>SingleAssetTrainer._calculate_sharpe_ratio</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t310">finrl_crypto\training\single_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t310"><data value='calculate_max_drawdown'>SingleAssetTrainer._calculate_max_drawdown</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t327">finrl_crypto\training\single_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t327"><data value='generate_training_report'>SingleAssetTrainer._generate_training_report</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t383">finrl_crypto\training\single_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t383"><data value='get_trade_statistics'>SingleAssetTrainer.get_trade_statistics</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t420">finrl_crypto\training\single_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t420"><data value='reset_trade_statistics'>SingleAssetTrainer.reset_trade_statistics</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html">finrl_crypto\training\single_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t20">finrl_crypto\training\utils.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t20"><data value='save_training_results'>save_training_results</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t44">finrl_crypto\training\utils.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t44"><data value='convert_numpy'>save_training_results.convert_numpy</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t75">finrl_crypto\training\utils.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t75"><data value='load_training_results'>load_training_results</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t100">finrl_crypto\training\utils.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t100"><data value='plot_training_curves'>plot_training_curves</data></a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t212">finrl_crypto\training\utils.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t212"><data value='plot_portfolio_analysis'>plot_portfolio_analysis</data></a></td>
                <td>63</td>
                <td>63</td>
                <td>0</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 81">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t320">finrl_crypto\training\utils.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t320"><data value='calculate_performance_metrics'>calculate_performance_metrics</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t391">finrl_crypto\training\utils.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t391"><data value='compare_experiments'>compare_experiments</data></a></td>
                <td>83</td>
                <td>83</td>
                <td>0</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 121">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t519">finrl_crypto\training\utils.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html#t519"><data value='generate_training_report'>generate_training_report</data></a></td>
                <td>89</td>
                <td>89</td>
                <td>0</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 133">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html">finrl_crypto\training\utils.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100.00%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>3831</td>
                <td>3279</td>
                <td>155</td>
                <td>1040</td>
                <td>0</td>
                <td class="right" data-ratio="552 4871">11.33%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
        <p>21 empty functions skipped.</p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-19 02:06 +0900
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
