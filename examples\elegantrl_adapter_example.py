#!/usr/bin/env python3
"""
ElegantRL适配器使用示例

这个示例展示了如何使用新的ElegantRL抽象层进行训练和预测。
通过适配器模式，可以在不直接依赖ElegantRL的情况下使用其功能。

作者: FinRL-Crypto 重构项目
日期: 2024
"""

import os
import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from adapters import create_trainer, get_available_trainers
from env.env_stocktrading import StockTradingEnv


def create_sample_data():
    """
    创建示例数据用于演示
    
    Returns:
        tuple: (price_array, tech_array, env_params)
    """
    # 创建示例价格数据 (时间步数, 股票数量)
    time_steps = 100
    num_stocks = 3
    
    # 生成随机价格数据
    np.random.seed(42)
    base_prices = np.array([100.0, 50.0, 200.0])  # 初始价格
    price_changes = np.random.normal(0, 0.02, (time_steps, num_stocks))  # 价格变化
    
    price_array = np.zeros((time_steps, num_stocks))
    price_array[0] = base_prices
    
    for i in range(1, time_steps):
        price_array[i] = price_array[i-1] * (1 + price_changes[i])
    
    # 创建示例技术指标数据 (时间步数, 技术指标数量)
    tech_indicators = 5  # 假设有5个技术指标
    tech_array = np.random.normal(0, 1, (time_steps, tech_indicators))
    
    # 环境参数
    env_params = {
        'initial_amount': 100000,  # 初始资金
        'transaction_fee_percent': 0.001,  # 交易费用
        'tech_indicator_list': ['macd', 'rsi', 'cci', 'adx', 'boll'],
        'max_stock': 100  # 最大持股数量
    }
    
    return price_array, tech_array, env_params


def example_training():
    """
    训练示例
    """
    print("=== ElegantRL适配器训练示例 ===")
    
    # 1. 获取可用的训练器
    available_trainers = get_available_trainers()
    print(f"可用的训练器: {available_trainers}")
    
    # 2. 创建训练器
    trainer = create_trainer('elegantrl')
    print(f"创建的训练器类型: {type(trainer).__name__}")
    
    # 3. 获取支持的模型
    supported_models = trainer.get_supported_models()
    print(f"支持的模型: {supported_models}")
    
    # 4. 准备数据
    price_array, tech_array, env_params = create_sample_data()
    print(f"价格数据形状: {price_array.shape}")
    print(f"技术指标数据形状: {tech_array.shape}")
    
    # 5. 配置参数
    agent_config = {
        'model_name': 'sac',
        'model_kwargs': {
            'learning_rate': 0.0003,
            'batch_size': 256,
            'gamma': 0.99,
            'net_dimension': 512,
            'target_step': 1000
        }
    }
    
    env_config = {
        'env_class': StockTradingEnv,
        'price_array': price_array,
        'tech_array': tech_array,
        'env_params': env_params,
        'if_log': True
    }
    
    training_config = {
        'cwd': './models/sac_example',
        'total_timesteps': 2000,
        'gpu_id': 0
    }
    
    # 6. 验证配置
    is_valid = trainer.validate_config(agent_config)
    print(f"配置验证结果: {is_valid}")
    
    # 7. 获取模型信息
    model_info = trainer.get_model_info('sac')
    print(f"SAC模型信息: {model_info}")
    
    # 8. 开始训练
    print("\n开始训练...")
    try:
        model_path = trainer.train(agent_config, env_config, training_config)
        print(f"训练完成，模型保存在: {model_path}")
        return model_path
    except Exception as e:
        print(f"训练失败: {e}")
        return None


def example_evaluation(model_path):
    """
    评估示例
    
    Args:
        model_path: 训练好的模型路径
    """
    if not model_path or not os.path.exists(model_path):
        print("模型路径不存在，跳过评估")
        return
    
    print("\n=== ElegantRL适配器评估示例 ===")
    
    # 1. 创建训练器
    trainer = create_trainer('elegantrl')
    
    # 2. 准备评估数据（可以是不同的数据集）
    price_array, tech_array, env_params = create_sample_data()
    
    env_config = {
        'env_class': StockTradingEnv,
        'price_array': price_array,
        'tech_array': tech_array,
        'env_params': env_params,
        'if_log': False
    }
    
    evaluation_config = {
        'model_name': 'sac',
        'net_dimension': 512,
        'gpu_id': 0
    }
    
    # 3. 执行评估
    print("开始评估...")
    try:
        metrics = trainer.evaluate(model_path, env_config, evaluation_config)
        print("评估结果:")
        for metric, value in metrics.items():
            print(f"  {metric}: {value:.4f}")
    except Exception as e:
        print(f"评估失败: {e}")


def example_prediction(model_path):
    """
    预测示例
    
    Args:
        model_path: 训练好的模型路径
    """
    if not model_path or not os.path.exists(model_path):
        print("模型路径不存在，跳过预测")
        return
    
    print("\n=== ElegantRL适配器预测示例 ===")
    
    # 1. 创建训练器
    trainer = create_trainer('elegantrl')
    
    # 2. 准备预测数据
    price_array, tech_array, env_params = create_sample_data()
    
    env_config = {
        'env_class': StockTradingEnv,
        'price_array': price_array,
        'tech_array': tech_array,
        'env_params': env_params,
        'if_log': False
    }
    
    prediction_config = {
        'model_name': 'sac',
        'net_dimension': 512,
        'gpu_id': 0
    }
    
    # 3. 执行预测
    print("开始预测...")
    try:
        episode_assets = trainer.predict(model_path, env_config, prediction_config)
        print(f"预测完成，获得 {len(episode_assets)} 个时间步的资产数据")
        print(f"初始资产: {episode_assets[0]:.2f}")
        print(f"最终资产: {episode_assets[-1]:.2f}")
        print(f"总收益率: {(episode_assets[-1] - episode_assets[0]) / episode_assets[0] * 100:.2f}%")
    except Exception as e:
        print(f"预测失败: {e}")


def main():
    """
    主函数
    """
    print("ElegantRL适配器使用示例")
    print("=" * 50)
    
    # 训练示例
    model_path = example_training()
    
    # 评估示例
    example_evaluation(model_path)
    
    # 预测示例
    example_prediction(model_path)
    
    print("\n示例运行完成！")


if __name__ == "__main__":
    main()