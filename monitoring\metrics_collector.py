"""指标收集器模块

统一收集和管理各种系统和业务指标。
"""

import logging
import time
import threading
from typing import Dict, List, Optional, Callable, Any, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict, deque
import json


class MetricType(Enum):
    """指标类型枚举"""
    COUNTER = "counter"  # 计数器，只增不减
    GAUGE = "gauge"      # 仪表，可增可减
    HISTOGRAM = "histogram"  # 直方图
    TIMER = "timer"      # 计时器
    RATE = "rate"        # 速率


@dataclass
class MetricValue:
    """指标值数据类"""
    timestamp: datetime
    value: Union[float, int]
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MetricDefinition:
    """指标定义数据类"""
    name: str
    metric_type: MetricType
    description: str
    unit: str = ""
    tags: Dict[str, str] = field(default_factory=dict)
    retention_hours: int = 24  # 数据保留时间（小时）


class Counter:
    """计数器指标
    
    只能增加的指标，用于统计事件发生次数。
    """
    
    def __init__(self, name: str, description: str = "", tags: Optional[Dict[str, str]] = None):
        self.name = name
        self.description = description
        self.tags = tags or {}
        self._value = 0
        self._lock = threading.Lock()
    
    def increment(self, amount: Union[int, float] = 1) -> None:
        """增加计数器值
        
        Args:
            amount: 增加的数量
        """
        if amount < 0:
            raise ValueError("计数器只能增加正数")
        
        with self._lock:
            self._value += amount
    
    def get_value(self) -> Union[int, float]:
        """获取当前值
        
        Returns:
            Union[int, float]: 当前计数器值
        """
        with self._lock:
            return self._value
    
    def reset(self) -> None:
        """重置计数器"""
        with self._lock:
            self._value = 0


class Gauge:
    """仪表指标
    
    可以增加或减少的指标，用于表示当前状态。
    """
    
    def __init__(self, name: str, description: str = "", tags: Optional[Dict[str, str]] = None):
        self.name = name
        self.description = description
        self.tags = tags or {}
        self._value = 0
        self._lock = threading.Lock()
    
    def set_value(self, value: Union[int, float]) -> None:
        """设置仪表值
        
        Args:
            value: 新的值
        """
        with self._lock:
            self._value = value
    
    def increment(self, amount: Union[int, float] = 1) -> None:
        """增加仪表值
        
        Args:
            amount: 增加的数量
        """
        with self._lock:
            self._value += amount
    
    def decrement(self, amount: Union[int, float] = 1) -> None:
        """减少仪表值
        
        Args:
            amount: 减少的数量
        """
        with self._lock:
            self._value -= amount
    
    def get_value(self) -> Union[int, float]:
        """获取当前值
        
        Returns:
            Union[int, float]: 当前仪表值
        """
        with self._lock:
            return self._value


class Histogram:
    """直方图指标
    
    统计数值分布的指标。
    """
    
    def __init__(self, name: str, description: str = "", 
                 buckets: Optional[List[float]] = None,
                 tags: Optional[Dict[str, str]] = None):
        self.name = name
        self.description = description
        self.tags = tags or {}
        
        # 默认桶边界
        self.buckets = buckets or [0.1, 0.5, 1.0, 2.5, 5.0, 10.0, float('inf')]
        self._bucket_counts = [0] * len(self.buckets)
        self._sum = 0
        self._count = 0
        self._lock = threading.Lock()
    
    def observe(self, value: Union[int, float]) -> None:
        """观察一个值
        
        Args:
            value: 观察的值
        """
        with self._lock:
            self._sum += value
            self._count += 1
            
            # 更新桶计数
            for i, bucket in enumerate(self.buckets):
                if value <= bucket:
                    self._bucket_counts[i] += 1
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            if self._count == 0:
                return {
                    'count': 0,
                    'sum': 0,
                    'average': 0,
                    'buckets': dict(zip(self.buckets, self._bucket_counts))
                }
            
            return {
                'count': self._count,
                'sum': self._sum,
                'average': self._sum / self._count,
                'buckets': dict(zip(self.buckets, self._bucket_counts))
            }


class Timer:
    """计时器指标
    
    测量操作持续时间的指标。
    """
    
    def __init__(self, name: str, description: str = "", tags: Optional[Dict[str, str]] = None):
        self.name = name
        self.description = description
        self.tags = tags or {}
        self._durations = deque(maxlen=1000)  # 保留最近1000次测量
        self._lock = threading.Lock()
    
    def time_operation(self):
        """上下文管理器，用于计时操作
        
        Returns:
            上下文管理器
        """
        return TimerContext(self)
    
    def record_duration(self, duration_seconds: float) -> None:
        """记录持续时间
        
        Args:
            duration_seconds: 持续时间（秒）
        """
        with self._lock:
            self._durations.append(duration_seconds)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            if not self._durations:
                return {
                    'count': 0,
                    'min': 0,
                    'max': 0,
                    'average': 0,
                    'percentiles': {}
                }
            
            durations = list(self._durations)
            durations.sort()
            
            count = len(durations)
            total = sum(durations)
            
            # 计算百分位数
            percentiles = {}
            for p in [50, 75, 90, 95, 99]:
                index = int(count * p / 100)
                if index >= count:
                    index = count - 1
                percentiles[f'p{p}'] = durations[index]
            
            return {
                'count': count,
                'min': min(durations),
                'max': max(durations),
                'average': total / count,
                'percentiles': percentiles
            }


class TimerContext:
    """计时器上下文管理器"""
    
    def __init__(self, timer: Timer):
        self.timer = timer
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.perf_counter()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time is not None:
            duration = time.perf_counter() - self.start_time
            self.timer.record_duration(duration)


class Rate:
    """速率指标
    
    计算事件发生速率的指标。
    """
    
    def __init__(self, name: str, description: str = "", 
                 window_seconds: int = 60,
                 tags: Optional[Dict[str, str]] = None):
        self.name = name
        self.description = description
        self.tags = tags or {}
        self.window_seconds = window_seconds
        self._events = deque()
        self._lock = threading.Lock()
    
    def mark_event(self, count: int = 1) -> None:
        """标记事件发生
        
        Args:
            count: 事件数量
        """
        current_time = time.time()
        
        with self._lock:
            self._events.append((current_time, count))
            self._cleanup_old_events(current_time)
    
    def get_rate(self) -> float:
        """获取当前速率（事件/秒）
        
        Returns:
            float: 当前速率
        """
        current_time = time.time()
        
        with self._lock:
            self._cleanup_old_events(current_time)
            
            if not self._events:
                return 0.0
            
            total_events = sum(count for _, count in self._events)
            return total_events / self.window_seconds
    
    def _cleanup_old_events(self, current_time: float) -> None:
        """清理过期事件
        
        Args:
            current_time: 当前时间
        """
        cutoff_time = current_time - self.window_seconds
        
        while self._events and self._events[0][0] < cutoff_time:
            self._events.popleft()


class MetricsCollector:
    """指标收集器主类
    
    统一管理所有指标的收集和存储。
    """
    
    def __init__(self):
        self._metrics: Dict[str, Union[Counter, Gauge, Histogram, Timer, Rate]] = {}
        self._metric_definitions: Dict[str, MetricDefinition] = {}
        self._metric_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self._callbacks: List[Callable[[str, MetricValue], None]] = []
        self._lock = threading.RLock()
        self._logger = logging.getLogger(__name__)
        
        # 自动收集线程
        self._collection_thread = None
        self._collection_interval = 10  # 秒
        self._stop_collection = threading.Event()
    
    def register_metric(self, definition: MetricDefinition) -> None:
        """注册指标定义
        
        Args:
            definition: 指标定义
        """
        with self._lock:
            self._metric_definitions[definition.name] = definition
            
            # 创建对应的指标对象
            if definition.metric_type == MetricType.COUNTER:
                self._metrics[definition.name] = Counter(
                    definition.name, definition.description, definition.tags
                )
            elif definition.metric_type == MetricType.GAUGE:
                self._metrics[definition.name] = Gauge(
                    definition.name, definition.description, definition.tags
                )
            elif definition.metric_type == MetricType.HISTOGRAM:
                self._metrics[definition.name] = Histogram(
                    definition.name, definition.description, tags=definition.tags
                )
            elif definition.metric_type == MetricType.TIMER:
                self._metrics[definition.name] = Timer(
                    definition.name, definition.description, definition.tags
                )
            elif definition.metric_type == MetricType.RATE:
                self._metrics[definition.name] = Rate(
                    definition.name, definition.description, tags=definition.tags
                )
            
            self._logger.info(f"注册指标: {definition.name} ({definition.metric_type.value})")
    
    def get_metric(self, name: str) -> Optional[Union[Counter, Gauge, Histogram, Timer, Rate]]:
        """获取指标对象
        
        Args:
            name: 指标名称
            
        Returns:
            Optional[Union[Counter, Gauge, Histogram, Timer, Rate]]: 指标对象
        """
        with self._lock:
            return self._metrics.get(name)
    
    def increment_counter(self, name: str, amount: Union[int, float] = 1,
                         tags: Optional[Dict[str, str]] = None) -> None:
        """增加计数器
        
        Args:
            name: 指标名称
            amount: 增加数量
            tags: 标签
        """
        metric = self.get_metric(name)
        if isinstance(metric, Counter):
            metric.increment(amount)
            self._record_metric_value(name, metric.get_value(), tags)
        else:
            self._logger.warning(f"指标 {name} 不是计数器类型")
    
    def set_gauge(self, name: str, value: Union[int, float],
                 tags: Optional[Dict[str, str]] = None) -> None:
        """设置仪表值
        
        Args:
            name: 指标名称
            value: 值
            tags: 标签
        """
        metric = self.get_metric(name)
        if isinstance(metric, Gauge):
            metric.set_value(value)
            self._record_metric_value(name, value, tags)
        else:
            self._logger.warning(f"指标 {name} 不是仪表类型")
    
    def observe_histogram(self, name: str, value: Union[int, float],
                         tags: Optional[Dict[str, str]] = None) -> None:
        """观察直方图值
        
        Args:
            name: 指标名称
            value: 观察值
            tags: 标签
        """
        metric = self.get_metric(name)
        if isinstance(metric, Histogram):
            metric.observe(value)
            self._record_metric_value(name, value, tags)
        else:
            self._logger.warning(f"指标 {name} 不是直方图类型")
    
    def time_operation(self, name: str, tags: Optional[Dict[str, str]] = None):
        """计时操作
        
        Args:
            name: 指标名称
            tags: 标签
            
        Returns:
            上下文管理器
        """
        metric = self.get_metric(name)
        if isinstance(metric, Timer):
            return TimedOperationContext(self, name, metric, tags)
        else:
            self._logger.warning(f"指标 {name} 不是计时器类型")
            return DummyContext()
    
    def mark_rate_event(self, name: str, count: int = 1,
                       tags: Optional[Dict[str, str]] = None) -> None:
        """标记速率事件
        
        Args:
            name: 指标名称
            count: 事件数量
            tags: 标签
        """
        metric = self.get_metric(name)
        if isinstance(metric, Rate):
            metric.mark_event(count)
            self._record_metric_value(name, metric.get_rate(), tags)
        else:
            self._logger.warning(f"指标 {name} 不是速率类型")
    
    def _record_metric_value(self, name: str, value: Union[int, float],
                           tags: Optional[Dict[str, str]] = None) -> None:
        """记录指标值
        
        Args:
            name: 指标名称
            value: 值
            tags: 标签
        """
        metric_value = MetricValue(
            timestamp=datetime.now(),
            value=value,
            tags=tags or {}
        )
        
        with self._lock:
            self._metric_history[name].append(metric_value)
            
            # 调用回调函数
            for callback in self._callbacks:
                try:
                    callback(name, metric_value)
                except Exception as e:
                    self._logger.error(f"指标收集回调函数执行失败: {e}")
    
    def add_callback(self, callback: Callable[[str, MetricValue], None]) -> None:
        """添加指标收集回调函数
        
        Args:
            callback: 回调函数
        """
        with self._lock:
            self._callbacks.append(callback)
    
    def remove_callback(self, callback: Callable[[str, MetricValue], None]) -> None:
        """移除指标收集回调函数
        
        Args:
            callback: 回调函数
        """
        with self._lock:
            if callback in self._callbacks:
                self._callbacks.remove(callback)
    
    def get_metric_history(self, name: str, 
                          time_window_hours: Optional[int] = None) -> List[MetricValue]:
        """获取指标历史
        
        Args:
            name: 指标名称
            time_window_hours: 时间窗口（小时）
            
        Returns:
            List[MetricValue]: 指标历史
        """
        with self._lock:
            history = list(self._metric_history.get(name, []))
            
            if time_window_hours:
                cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
                history = [h for h in history if h.timestamp > cutoff_time]
            
            return history
    
    def get_all_metrics_snapshot(self) -> Dict[str, Any]:
        """获取所有指标的快照
        
        Returns:
            Dict[str, Any]: 指标快照
        """
        snapshot = {}
        
        with self._lock:
            for name, metric in self._metrics.items():
                if isinstance(metric, Counter):
                    snapshot[name] = {
                        'type': 'counter',
                        'value': metric.get_value()
                    }
                elif isinstance(metric, Gauge):
                    snapshot[name] = {
                        'type': 'gauge',
                        'value': metric.get_value()
                    }
                elif isinstance(metric, Histogram):
                    snapshot[name] = {
                        'type': 'histogram',
                        'statistics': metric.get_statistics()
                    }
                elif isinstance(metric, Timer):
                    snapshot[name] = {
                        'type': 'timer',
                        'statistics': metric.get_statistics()
                    }
                elif isinstance(metric, Rate):
                    snapshot[name] = {
                        'type': 'rate',
                        'value': metric.get_rate()
                    }
        
        return snapshot
    
    def start_auto_collection(self) -> None:
        """启动自动收集"""
        if self._collection_thread and self._collection_thread.is_alive():
            self._logger.warning("自动收集已经在运行")
            return
        
        self._stop_collection.clear()
        self._collection_thread = threading.Thread(
            target=self._auto_collection_loop,
            daemon=True
        )
        self._collection_thread.start()
        self._logger.info("自动指标收集已启动")
    
    def stop_auto_collection(self) -> None:
        """停止自动收集"""
        self._stop_collection.set()
        if self._collection_thread:
            self._collection_thread.join(timeout=5)
        self._logger.info("自动指标收集已停止")
    
    def _auto_collection_loop(self) -> None:
        """自动收集循环"""
        while not self._stop_collection.wait(self._collection_interval):
            try:
                # 收集所有指标的当前值
                snapshot = self.get_all_metrics_snapshot()
                
                # 清理过期数据
                self._cleanup_expired_data()
                
            except Exception as e:
                self._logger.error(f"自动收集过程中发生错误: {e}")
    
    def _cleanup_expired_data(self) -> None:
        """清理过期数据"""
        with self._lock:
            for name, definition in self._metric_definitions.items():
                if name in self._metric_history:
                    cutoff_time = datetime.now() - timedelta(hours=definition.retention_hours)
                    history = self._metric_history[name]
                    
                    # 移除过期数据
                    while history and history[0].timestamp < cutoff_time:
                        history.popleft()
    
    def export_metrics(self, format_type: str = 'json') -> str:
        """导出指标数据
        
        Args:
            format_type: 导出格式 ('json', 'prometheus')
            
        Returns:
            str: 导出的数据
        """
        if format_type == 'json':
            return self._export_json()
        elif format_type == 'prometheus':
            return self._export_prometheus()
        else:
            raise ValueError(f"不支持的导出格式: {format_type}")
    
    def _export_json(self) -> str:
        """导出为JSON格式
        
        Returns:
            str: JSON格式的指标数据
        """
        data = {
            'timestamp': datetime.now().isoformat(),
            'metrics': self.get_all_metrics_snapshot(),
            'definitions': {
                name: {
                    'type': definition.metric_type.value,
                    'description': definition.description,
                    'unit': definition.unit,
                    'tags': definition.tags
                }
                for name, definition in self._metric_definitions.items()
            }
        }
        return json.dumps(data, indent=2)
    
    def _export_prometheus(self) -> str:
        """导出为Prometheus格式
        
        Returns:
            str: Prometheus格式的指标数据
        """
        lines = []
        
        with self._lock:
            for name, metric in self._metrics.items():
                definition = self._metric_definitions.get(name)
                
                if definition and definition.description:
                    lines.append(f"# HELP {name} {definition.description}")
                
                if isinstance(metric, Counter):
                    lines.append(f"# TYPE {name} counter")
                    lines.append(f"{name} {metric.get_value()}")
                elif isinstance(metric, Gauge):
                    lines.append(f"# TYPE {name} gauge")
                    lines.append(f"{name} {metric.get_value()}")
                elif isinstance(metric, Histogram):
                    lines.append(f"# TYPE {name} histogram")
                    stats = metric.get_statistics()
                    lines.append(f"{name}_count {stats['count']}")
                    lines.append(f"{name}_sum {stats['sum']}")
                    for bucket, count in stats['buckets'].items():
                        lines.append(f'{name}_bucket{{le="{bucket}"}} {count}')
        
        return '\n'.join(lines)


class TimedOperationContext:
    """计时操作上下文管理器"""
    
    def __init__(self, collector: MetricsCollector, name: str, timer: Timer,
                 tags: Optional[Dict[str, str]] = None):
        self.collector = collector
        self.name = name
        self.timer = timer
        self.tags = tags
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.perf_counter()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time is not None:
            duration = time.perf_counter() - self.start_time
            self.timer.record_duration(duration)
            self.collector._record_metric_value(self.name, duration, self.tags)


class DummyContext:
    """虚拟上下文管理器"""
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass