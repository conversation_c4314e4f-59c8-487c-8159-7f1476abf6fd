#!/usr/bin/env python3
"""
ElegantRL抽象层测试

测试第一阶段第四个子任务：创建ElegantRL抽象层的功能。
包括适配器、工厂类、接口等组件的测试。

作者: FinRL-Crypto 重构项目
日期: 2024
"""

import unittest
import os
import sys
import tempfile
import shutil
import numpy as np
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    from interfaces.trainer_interface import (
        TrainerInterface, ModelInterface, TrainingError, 
        EvaluationError, PredictionError, ModelNotFoundError, ConfigurationError
    )
    from adapters.elegantrl_adapter import ElegantRLAdapter, ElegantRLModel
    from adapters.trainer_factory import TrainerFactory, create_trainer, get_available_trainers
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保项目结构正确")
    sys.exit(1)


class TestTrainerInterface(unittest.TestCase):
    """
    测试训练器接口
    """
    
    def test_trainer_interface_is_abstract(self):
        """测试训练器接口是抽象类"""
        with self.assertRaises(TypeError):
            TrainerInterface()
    
    def test_model_interface_is_abstract(self):
        """测试模型接口是抽象类"""
        with self.assertRaises(TypeError):
            ModelInterface()
    
    def test_custom_exceptions_exist(self):
        """测试自定义异常类存在"""
        exceptions = [
            TrainingError, EvaluationError, PredictionError,
            ModelNotFoundError, ConfigurationError
        ]
        
        for exc_class in exceptions:
            self.assertTrue(issubclass(exc_class, Exception))
            
            # 测试异常可以正常创建和抛出
            with self.assertRaises(exc_class):
                raise exc_class("测试异常")


class TestElegantRLAdapter(unittest.TestCase):
    """
    测试ElegantRL适配器
    """
    
    def setUp(self):
        """设置测试环境"""
        self.adapter = ElegantRLAdapter()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_adapter_implements_interface(self):
        """测试适配器实现了训练器接口"""
        self.assertIsInstance(self.adapter, TrainerInterface)
    
    def test_get_supported_models(self):
        """测试获取支持的模型列表"""
        models = self.adapter.get_supported_models()
        
        self.assertIsInstance(models, list)
        self.assertGreater(len(models), 0)
        
        expected_models = ['ddpg', 'td3', 'sac', 'ppo', 'a2c']
        for model in expected_models:
            self.assertIn(model, models)
    
    def test_validate_config_valid(self):
        """测试有效配置验证"""
        valid_config = {
            'model_name': 'sac',
            'model_kwargs': {
                'learning_rate': 0.0003,
                'batch_size': 256,
                'gamma': 0.99
            }
        }
        
        self.assertTrue(self.adapter.validate_config(valid_config))
    
    def test_validate_config_invalid(self):
        """测试无效配置验证"""
        # 缺少model_name
        invalid_config1 = {
            'model_kwargs': {'learning_rate': 0.0003}
        }
        self.assertFalse(self.adapter.validate_config(invalid_config1))
        
        # 不支持的模型
        invalid_config2 = {
            'model_name': 'unsupported_model'
        }
        self.assertFalse(self.adapter.validate_config(invalid_config2))
        
        # 错误的参数类型
        invalid_config3 = {
            'model_name': 'sac',
            'model_kwargs': {
                'learning_rate': 'invalid_type'
            }
        }
        self.assertFalse(self.adapter.validate_config(invalid_config3))
    
    def test_get_model_info(self):
        """测试获取模型信息"""
        model_info = self.adapter.get_model_info('sac')
        
        self.assertIsInstance(model_info, dict)
        
        required_keys = ['name', 'class', 'policy_type', 'description', 'supported_parameters']
        for key in required_keys:
            self.assertIn(key, model_info)
        
        self.assertEqual(model_info['name'], 'sac')
        self.assertIn(model_info['policy_type'], ['off_policy', 'on_policy'])
    
    def test_get_model_info_invalid(self):
        """测试获取不存在模型的信息"""
        with self.assertRaises(ModelNotFoundError):
            self.adapter.get_model_info('invalid_model')
    
    def test_model_classification(self):
        """测试模型分类"""
        # 测试离线策略模型
        off_policy_models = ['ddpg', 'td3', 'sac']
        for model in off_policy_models:
            self.assertIn(model, ElegantRLAdapter.OFF_POLICY_MODELS)
        
        # 测试在线策略模型
        on_policy_models = ['ppo', 'a2c']
        for model in on_policy_models:
            self.assertIn(model, ElegantRLAdapter.ON_POLICY_MODELS)
    
    @patch('adapters.elegantrl_adapter.train_and_evaluate')
    @patch('adapters.elegantrl_adapter.Arguments')
    def test_train_method_structure(self, mock_arguments, mock_train):
        """测试训练方法的结构（不执行实际训练）"""
        # 模拟Arguments对象
        mock_args = Mock()
        mock_arguments.return_value = mock_args
        
        # 模拟环境类
        mock_env_class = Mock()
        mock_env = Mock()
        mock_env.env_num = 1
        mock_env_class.return_value = mock_env
        
        # 准备配置
        agent_config = {
            'model_name': 'sac',
            'model_kwargs': {'learning_rate': 0.0003}
        }
        
        env_config = {
            'env_class': mock_env_class,
            'price_array': np.random.rand(100, 3),
            'tech_array': np.random.rand(100, 5),
            'env_params': {'initial_amount': 100000}
        }
        
        training_config = {
            'cwd': self.temp_dir,
            'total_timesteps': 1000
        }
        
        # 执行训练
        result = self.adapter.train(agent_config, env_config, training_config)
        
        # 验证结果
        self.assertEqual(result, self.temp_dir)
        mock_arguments.assert_called_once()
        mock_train.assert_called_once_with(mock_args)
    
    def test_validate_training_config(self):
        """测试训练配置验证"""
        # 有效配置
        valid_agent_config = {'model_name': 'sac'}
        valid_env_config = {
            'env_class': Mock,
            'price_array': np.array([]),
            'tech_array': np.array([]),
            'env_params': {}
        }
        valid_training_config = {'cwd': '/tmp'}
        
        # 应该不抛出异常
        try:
            self.adapter._validate_training_config(
                valid_agent_config, valid_env_config, valid_training_config
            )
        except ConfigurationError:
            self.fail("有效配置不应该抛出ConfigurationError")
        
        # 无效配置 - 缺少model_name
        invalid_agent_config = {}
        with self.assertRaises(ConfigurationError):
            self.adapter._validate_training_config(
                invalid_agent_config, valid_env_config, valid_training_config
            )
        
        # 无效配置 - 缺少环境参数
        invalid_env_config = {'env_class': Mock}
        with self.assertRaises(ConfigurationError):
            self.adapter._validate_training_config(
                valid_agent_config, invalid_env_config, valid_training_config
            )
        
        # 无效配置 - 缺少cwd
        invalid_training_config = {}
        with self.assertRaises(ConfigurationError):
            self.adapter._validate_training_config(
                valid_agent_config, valid_env_config, invalid_training_config
            )


class TestElegantRLModel(unittest.TestCase):
    """
    测试ElegantRL模型包装器
    """
    
    def setUp(self):
        """设置测试环境"""
        self.mock_agent = Mock()
        self.mock_device = 'cpu'
        self.model = ElegantRLModel(self.mock_agent, self.mock_device)
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_model_implements_interface(self):
        """测试模型实现了模型接口"""
        self.assertIsInstance(self.model, ModelInterface)
    
    def test_model_initialization(self):
        """测试模型初始化"""
        self.assertEqual(self.model.agent, self.mock_agent)
        self.assertEqual(self.model.device, self.mock_device)
        self.assertEqual(self.model.act, self.mock_agent.act)
    
    def test_save_method(self):
        """测试保存方法"""
        save_path = os.path.join(self.temp_dir, 'model')
        
        self.model.save(save_path)
        
        # 验证目录被创建
        self.assertTrue(os.path.exists(save_path))
        
        # 验证代理的保存方法被调用
        self.mock_agent.save_or_load_agent.assert_called_once_with(save_path, if_save=True)
    
    def test_load_method_valid_path(self):
        """测试加载方法 - 有效路径"""
        # 创建一个存在的路径
        load_path = self.temp_dir
        
        self.model.load(load_path)
        
        # 验证代理的加载方法被调用
        self.mock_agent.save_or_load_agent.assert_called_once_with(load_path, if_save=False)
    
    def test_load_method_invalid_path(self):
        """测试加载方法 - 无效路径"""
        invalid_path = '/nonexistent/path'
        
        with self.assertRaises(FileNotFoundError):
            self.model.load(invalid_path)
    
    @patch('torch.no_grad')
    @patch('torch.as_tensor')
    def test_predict_method(self, mock_as_tensor, mock_no_grad):
        """测试预测方法"""
        # 模拟torch操作
        mock_tensor = Mock()
        mock_as_tensor.return_value = mock_tensor
        
        mock_action_tensor = Mock()
        # 模拟返回的是一个包含数组的列表，然后取第一个元素
        mock_action_tensor.detach.return_value.cpu.return_value.numpy.return_value = [np.array([0.5, -0.3])]
        self.mock_agent.act.return_value = mock_action_tensor
        
        # 测试状态
        test_state = np.array([1.0, 2.0, 3.0])
        
        # 执行预测
        result = self.model.predict(test_state)
        
        # 验证结果
        self.assertIsInstance(result, np.ndarray)
        np.testing.assert_array_equal(result, np.array([0.5, -0.3]))
        
        # 验证调用
        mock_as_tensor.assert_called_once_with((test_state,), device=self.mock_device)
        self.mock_agent.act.assert_called_once_with(mock_tensor)


class TestTrainerFactory(unittest.TestCase):
    """
    测试训练器工厂
    """
    
    def test_create_trainer_elegantrl(self):
        """测试创建ElegantRL训练器"""
        trainer = TrainerFactory.create_trainer('elegantrl')
        
        self.assertIsInstance(trainer, ElegantRLAdapter)
        self.assertIsInstance(trainer, TrainerInterface)
    
    def test_create_trainer_invalid(self):
        """测试创建不支持的训练器"""
        with self.assertRaises(ValueError) as context:
            TrainerFactory.create_trainer('invalid_trainer')
        
        self.assertIn('不支持的训练器类型', str(context.exception))
    
    def test_get_supported_trainers(self):
        """测试获取支持的训练器列表"""
        trainers = TrainerFactory.get_supported_trainers()
        
        self.assertIsInstance(trainers, list)
        self.assertIn('elegantrl', trainers)
    
    def test_is_trainer_supported(self):
        """测试检查训练器是否支持"""
        self.assertTrue(TrainerFactory.is_trainer_supported('elegantrl'))
        self.assertFalse(TrainerFactory.is_trainer_supported('invalid_trainer'))
    
    def test_get_trainer_info(self):
        """测试获取训练器信息"""
        info = TrainerFactory.get_trainer_info('elegantrl')
        
        self.assertIsInstance(info, dict)
        
        required_keys = ['type', 'class_name', 'module', 'description']
        for key in required_keys:
            self.assertIn(key, info)
        
        self.assertEqual(info['type'], 'elegantrl')
        self.assertEqual(info['class_name'], 'ElegantRLAdapter')
    
    def test_get_trainer_info_invalid(self):
        """测试获取不支持训练器的信息"""
        with self.assertRaises(ValueError):
            TrainerFactory.get_trainer_info('invalid_trainer')
    
    def test_register_trainer(self):
        """测试注册新的训练器"""
        # 创建一个模拟的训练器类
        class MockTrainer(TrainerInterface):
            def train(self, agent_config, env_config, training_config):
                return 'mock_path'
            
            def evaluate(self, model_path, env_config, evaluation_config):
                return {'metric': 1.0}
            
            def predict(self, model_path, env_config, prediction_config):
                return [1.0, 2.0]
            
            def get_supported_models(self):
                return ['mock_model']
            
            def validate_config(self, config):
                return True
            
            def get_model_info(self, model_name):
                return {'name': model_name}
        
        # 注册新训练器
        TrainerFactory.register_trainer('mock_trainer', MockTrainer)
        
        # 验证注册成功
        self.assertTrue(TrainerFactory.is_trainer_supported('mock_trainer'))
        
        # 验证可以创建
        trainer = TrainerFactory.create_trainer('mock_trainer')
        self.assertIsInstance(trainer, MockTrainer)
        
        # 清理
        if 'mock_trainer' in TrainerFactory._trainers:
            del TrainerFactory._trainers['mock_trainer']
    
    def test_register_trainer_invalid_class(self):
        """测试注册无效的训练器类"""
        class InvalidTrainer:
            pass
        
        with self.assertRaises(TypeError):
            TrainerFactory.register_trainer('invalid', InvalidTrainer)


class TestConvenienceFunctions(unittest.TestCase):
    """
    测试便利函数
    """
    
    def test_create_trainer_function(self):
        """测试create_trainer便利函数"""
        # 默认创建elegantrl训练器
        trainer = create_trainer()
        self.assertIsInstance(trainer, ElegantRLAdapter)
        
        # 指定类型创建
        trainer2 = create_trainer('elegantrl')
        self.assertIsInstance(trainer2, ElegantRLAdapter)
    
    def test_get_available_trainers_function(self):
        """测试get_available_trainers便利函数"""
        trainers = get_available_trainers()
        
        self.assertIsInstance(trainers, list)
        self.assertIn('elegantrl', trainers)


class TestIntegration(unittest.TestCase):
    """
    集成测试
    """
    
    def test_full_workflow_structure(self):
        """测试完整工作流程的结构（不执行实际训练）"""
        # 1. 创建训练器
        trainer = create_trainer('elegantrl')
        
        # 2. 验证训练器功能
        models = trainer.get_supported_models()
        self.assertGreater(len(models), 0)
        
        # 3. 验证配置
        config = {'model_name': 'sac'}
        self.assertTrue(trainer.validate_config(config))
        
        # 4. 获取模型信息
        info = trainer.get_model_info('sac')
        self.assertIn('name', info)
        
        # 5. 验证工厂功能
        self.assertTrue(TrainerFactory.is_trainer_supported('elegantrl'))
        factory_info = TrainerFactory.get_trainer_info('elegantrl')
        self.assertIn('type', factory_info)


def run_tests():
    """
    运行所有测试
    """
    # 创建测试套件
    test_classes = [
        TestTrainerInterface,
        TestElegantRLAdapter,
        TestElegantRLModel,
        TestTrainerFactory,
        TestConvenienceFunctions,
        TestIntegration
    ]
    
    suite = unittest.TestSuite()
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 返回测试结果
    return result.wasSuccessful(), len(result.failures), len(result.errors)


if __name__ == '__main__':
    print("运行ElegantRL抽象层测试...")
    print("=" * 50)
    
    success, failures, errors = run_tests()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 所有测试通过！")
    else:
        print(f"❌ 测试失败: {failures} 个失败, {errors} 个错误")
    
    sys.exit(0 if success else 1)