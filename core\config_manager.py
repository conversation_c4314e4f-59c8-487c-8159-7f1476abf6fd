#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置管理系统

提供统一的配置加载、验证、合并和管理功能
支持多种配置格式（YAML、JSON）和环境变量覆盖
"""

import os
import json
import yaml
import copy
import threading
import time
from pathlib import Path
from typing import Dict, Any, Optional, List, Union, Callable
from abc import ABC, abstractmethod
import jsonschema
from jsonschema import validate, ValidationError


class ConfigLoadError(Exception):
    """配置加载错误"""
    pass


class ConfigValidationError(Exception):
    """配置验证错误"""
    pass


class ConfigLoader(ABC):
    """配置加载器抽象基类"""
    
    @abstractmethod
    def load(self, source: str, **kwargs) -> Dict[str, Any]:
        """加载配置
        
        Args:
            source: 配置源（文件路径、环境变量前缀等）
            **kwargs: 额外参数
            
        Returns:
            配置字典
            
        Raises:
            ConfigLoadError: 加载失败时抛出
        """
        pass


class FileConfigLoader(ConfigLoader):
    """文件配置加载器"""
    
    def load(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """从文件加载配置
        
        Args:
            file_path: 配置文件路径
            **kwargs: 额外参数
            
        Returns:
            配置字典
            
        Raises:
            ConfigLoadError: 文件不存在或格式错误时抛出
        """
        if not os.path.exists(file_path):
            raise ConfigLoadError(f"Configuration file not found: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.endswith('.yaml') or file_path.endswith('.yml'):
                    return yaml.safe_load(f) or {}
                elif file_path.endswith('.json'):
                    return json.load(f)
                else:
                    raise ValueError(f"Unsupported file format: {file_path}")
        except Exception as e:
            raise ConfigLoadError(f"Failed to load configuration file {file_path}: {e}")


class EnvironmentConfigLoader(ConfigLoader):
    """环境变量配置加载器"""
    
    def load(self, prefix: str = "", **kwargs) -> Dict[str, Any]:
        """从环境变量加载配置
        
        Args:
            prefix: 环境变量前缀
            **kwargs: 额外参数
            
        Returns:
            配置字典
        """
        config = {}
        
        for key, value in os.environ.items():
            if prefix and not key.startswith(prefix):
                continue
            
            # 移除前缀
            config_key = key[len(prefix):] if prefix else key
            
            # 将环境变量键转换为嵌套字典结构
            # 例如: DATABASE_HOST -> {"database": {"host": value}}
            parts = config_key.lower().split('_')
            current = config
            
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                elif not isinstance(current[part], dict):
                    # 如果已存在的值不是字典，创建新字典
                    current[part] = {}
                current = current[part]
            
            current[parts[-1]] = value
        
        return config


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self._custom_rules: Dict[str, Callable] = {}
    
    def validate(self, config: Dict[str, Any], schema: Dict[str, Any]) -> None:
        """使用JSON Schema验证配置
        
        Args:
            config: 要验证的配置
            schema: JSON Schema
            
        Raises:
            ConfigValidationError: 验证失败时抛出
        """
        try:
            validate(instance=config, schema=schema)
        except ValidationError as e:
            raise ConfigValidationError(f"Configuration validation failed: {e.message}")
    
    def add_custom_rule(self, name: str, rule_func: Callable) -> None:
        """添加自定义验证规则
        
        Args:
            name: 规则名称
            rule_func: 验证函数，应该在验证失败时抛出ConfigValidationError
        """
        self._custom_rules[name] = rule_func
    
    def apply_custom_rule(self, rule_name: str, value: Any) -> None:
        """应用自定义验证规则
        
        Args:
            rule_name: 规则名称
            value: 要验证的值
            
        Raises:
            ConfigValidationError: 验证失败时抛出
        """
        if rule_name not in self._custom_rules:
            raise ConfigValidationError(f"Unknown validation rule: {rule_name}")
        
        self._custom_rules[rule_name](value)


class ConfigSchema:
    """配置模式定义"""
    
    def __init__(self, schema: Dict[str, Any]):
        self.schema = schema
    
    def validate(self, config: Dict[str, Any]) -> None:
        """验证配置
        
        Args:
            config: 要验证的配置
            
        Raises:
            ConfigValidationError: 验证失败时抛出
        """
        validator = ConfigValidator()
        validator.validate(config, self.schema)


class UnifiedConfigManager:
    """统一配置管理器"""
    
    def __init__(self):
        self._configs: Dict[str, Any] = {}
        self._schema: Optional[Dict[str, Any]] = None
        self._validator = ConfigValidator()
        self._file_loader = FileConfigLoader()
        self._env_loader = EnvironmentConfigLoader()
        self._lock = threading.RLock()
        self._backups: Dict[str, Dict[str, Any]] = {}
        self._loaded_files: List[str] = []
    
    def load_from_file(self, file_path: str) -> None:
        """从文件加载配置
        
        Args:
            file_path: 配置文件路径
            
        Raises:
            ConfigLoadError: 加载失败时抛出
            ConfigValidationError: 验证失败时抛出
        """
        with self._lock:
            config = self._file_loader.load(file_path)
            self._merge_config(config)
            self._loaded_files.append(file_path)
            
            if self._schema:
                self._validator.validate(self._configs, self._schema)
    
    def load_from_dict(self, config: Dict[str, Any]) -> None:
        """从字典加载配置
        
        Args:
            config: 配置字典
            
        Raises:
            ConfigValidationError: 验证失败时抛出
        """
        with self._lock:
            self._merge_config(config)
            
            if self._schema:
                self._validator.validate(self._configs, self._schema)
    
    def load_from_environment(self, prefix: str = "") -> None:
        """从环境变量加载配置
        
        Args:
            prefix: 环境变量前缀
        """
        with self._lock:
            env_config = self._env_loader.load(prefix)
            self._merge_config(env_config)
    
    def get_config(self, section: str) -> Dict[str, Any]:
        """获取配置节
        
        Args:
            section: 配置节名称
            
        Returns:
            配置节内容
            
        Raises:
            KeyError: 配置节不存在时抛出
        """
        with self._lock:
            if section not in self._configs:
                raise KeyError(f"Configuration section '{section}' not found")
            return copy.deepcopy(self._configs[section])
    
    def get_all_configs(self) -> Dict[str, Any]:
        """获取所有配置
        
        Returns:
            所有配置的深拷贝
        """
        with self._lock:
            return copy.deepcopy(self._configs)
    
    def update_config(self, section: str, config: Dict[str, Any]) -> None:
        """更新配置节
        
        Args:
            section: 配置节名称
            config: 新的配置内容
            
        Raises:
            ConfigValidationError: 验证失败时抛出
        """
        with self._lock:
            old_config = self._configs.get(section, {})
            self._configs[section] = {**old_config, **config}
            
            if self._schema:
                self._validator.validate(self._configs, self._schema)
    
    def set_validation_schema(self, schema: Dict[str, Any]) -> None:
        """设置验证模式
        
        Args:
            schema: JSON Schema
        """
        with self._lock:
            self._schema = schema
            
            # 验证当前配置
            if self._configs and self._schema:
                self._validator.validate(self._configs, self._schema)
    
    def reload(self) -> None:
        """重新加载所有配置文件
        
        Raises:
            ConfigLoadError: 加载失败时抛出
            ConfigValidationError: 验证失败时抛出
        """
        with self._lock:
            old_configs = copy.deepcopy(self._configs)
            
            try:
                self._configs.clear()
                
                # 重新加载所有文件
                for file_path in self._loaded_files:
                    config = self._file_loader.load(file_path)
                    self._merge_config(config)
                
                # 重新加载环境变量
                env_config = self._env_loader.load()
                self._merge_config(env_config)
                
                if self._schema:
                    self._validator.validate(self._configs, self._schema)
                    
            except Exception:
                # 恢复旧配置
                self._configs = old_configs
                raise
    
    def create_backup(self) -> str:
        """创建配置备份
        
        Returns:
            备份ID
        """
        with self._lock:
            backup_id = f"backup_{int(time.time() * 1000)}"
            self._backups[backup_id] = copy.deepcopy(self._configs)
            return backup_id
    
    def restore_backup(self, backup_id: str) -> None:
        """恢复配置备份
        
        Args:
            backup_id: 备份ID
            
        Raises:
            KeyError: 备份不存在时抛出
        """
        with self._lock:
            if backup_id not in self._backups:
                raise KeyError(f"Backup '{backup_id}' not found")
            
            self._configs = copy.deepcopy(self._backups[backup_id])
    
    def _merge_config(self, new_config: Dict[str, Any]) -> None:
        """合并配置
        
        Args:
            new_config: 新配置
        """
        for key, value in new_config.items():
            if key in self._configs and isinstance(self._configs[key], dict) and isinstance(value, dict):
                # 递归合并字典
                self._configs[key] = {**self._configs[key], **value}
            else:
                # 直接覆盖
                self._configs[key] = value
    
    def has_config(self, section: str) -> bool:
        """检查配置节是否存在
        
        Args:
            section: 配置节名称
            
        Returns:
            是否存在
        """
        with self._lock:
            return section in self._configs
    
    def get_config_value(self, path: str, default: Any = None) -> Any:
        """获取配置值（支持点分隔路径）
        
        Args:
            path: 配置路径，如 "database.host"
            default: 默认值
            
        Returns:
            配置值
        """
        with self._lock:
            parts = path.split('.')
            current = self._configs
            
            try:
                for part in parts:
                    current = current[part]
                return current
            except (KeyError, TypeError):
                return default
    
    def set_config_value(self, path: str, value: Any) -> None:
        """设置配置值（支持点分隔路径）
        
        Args:
            path: 配置路径，如 "database.host"
            value: 配置值
        """
        with self._lock:
            parts = path.split('.')
            current = self._configs
            
            # 创建嵌套结构
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            
            current[parts[-1]] = value
            
            if self._schema:
                self._validator.validate(self._configs, self._schema)
    
    def clear(self) -> None:
        """清空所有配置"""
        with self._lock:
            self._configs.clear()
            self._loaded_files.clear()
    
    def export_to_file(self, file_path: str, format_type: str = 'json') -> None:
        """导出配置到文件
        
        Args:
            file_path: 输出文件路径
            format_type: 格式类型（'json' 或 'yaml'）
        """
        with self._lock:
            path = Path(file_path)
            
            with open(path, 'w', encoding='utf-8') as f:
                if format_type.lower() == 'yaml':
                    yaml.dump(self._configs, f, default_flow_style=False, allow_unicode=True)
                else:
                    json.dump(self._configs, f, indent=2, ensure_ascii=False)