"""Advantage Actor-Critic (A2C) 智能体实现

实现A2C算法用于连续和离散动作空间的强化学习。
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import Normal, Categorical
from typing import Dict, List, Tuple, Any, Optional, Union
import random

from .base import BaseAgent, create_mlp


class A2CActor(nn.Module):
    """A2C演员网络"""
    
    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 hidden_dims: List[int] = [256, 256],
                 activation: str = 'relu',
                 dropout: float = 0.0,
                 action_type: str = 'continuous',
                 log_std_init: float = 0.0):
        """初始化演员网络
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            hidden_dims: 隐藏层维度
            activation: 激活函数
            dropout: Dropout概率
            action_type: 动作类型 ('continuous' 或 'discrete')
            log_std_init: 对数标准差初始值（仅用于连续动作）
        """
        super().__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.action_type = action_type
        
        # 共享网络
        self.shared_net = create_mlp(
            input_dim=state_dim,
            output_dim=hidden_dims[-1],
            hidden_dims=hidden_dims[:-1],
            activation=activation,
            dropout=dropout
        )
        
        if action_type == 'continuous':
            # 连续动作空间
            self.mean_layer = nn.Linear(hidden_dims[-1], action_dim)
            self.log_std = nn.Parameter(torch.ones(action_dim) * log_std_init)
        else:
            # 离散动作空间
            self.action_layer = nn.Linear(hidden_dims[-1], action_dim)
    
    def forward(self, state: torch.Tensor) -> Union[Tuple[torch.Tensor, torch.Tensor], torch.Tensor]:
        """前向传播
        
        Args:
            state: 状态张量
            
        Returns:
            连续动作：(均值, 标准差)
            离散动作：动作logits
        """
        features = self.shared_net(state)
        
        if self.action_type == 'continuous':
            mean = self.mean_layer(features)
            std = torch.exp(self.log_std.expand_as(mean))
            return mean, std
        else:
            action_logits = self.action_layer(features)
            return action_logits
    
    def get_action_and_log_prob(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """获取动作、对数概率和熵
        
        Args:
            state: 状态张量
            
        Returns:
            (动作, 对数概率, 熵)
        """
        if self.action_type == 'continuous':
            mean, std = self.forward(state)
            dist = Normal(mean, std)
            action = dist.sample()
            log_prob = dist.log_prob(action).sum(dim=-1)
            entropy = dist.entropy().sum(dim=-1)
            return action, log_prob, entropy
        else:
            action_logits = self.forward(state)
            dist = Categorical(logits=action_logits)
            action = dist.sample()
            log_prob = dist.log_prob(action)
            entropy = dist.entropy()
            return action, log_prob, entropy
    
    def get_log_prob_and_entropy(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """获取给定动作的对数概率和熵
        
        Args:
            state: 状态张量
            action: 动作张量
            
        Returns:
            (对数概率, 熵)
        """
        if self.action_type == 'continuous':
            mean, std = self.forward(state)
            dist = Normal(mean, std)
            log_prob = dist.log_prob(action).sum(dim=-1)
            entropy = dist.entropy().sum(dim=-1)
            return log_prob, entropy
        else:
            action_logits = self.forward(state)
            dist = Categorical(logits=action_logits)
            log_prob = dist.log_prob(action.squeeze(-1))
            entropy = dist.entropy()
            return log_prob, entropy


class A2CCritic(nn.Module):
    """A2C评论家网络"""
    
    def __init__(self,
                 state_dim: int,
                 hidden_dims: List[int] = [256, 256],
                 activation: str = 'relu',
                 dropout: float = 0.0):
        """初始化评论家网络
        
        Args:
            state_dim: 状态维度
            hidden_dims: 隐藏层维度
            activation: 激活函数
            dropout: Dropout概率
        """
        super().__init__()
        
        self.value_net = create_mlp(
            input_dim=state_dim,
            output_dim=1,
            hidden_dims=hidden_dims,
            activation=activation,
            dropout=dropout
        )
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """前向传播
        
        Args:
            state: 状态张量
            
        Returns:
            状态价值
        """
        return self.value_net(state)


class A2CAgent(BaseAgent):
    """A2C智能体
    
    实现Advantage Actor-Critic算法。
    """
    
    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 learning_rate: float = 3e-4,
                 gamma: float = 0.99,
                 value_coef: float = 0.5,
                 entropy_coef: float = 0.01,
                 max_grad_norm: float = 0.5,
                 n_steps: int = 5,
                 hidden_dims: List[int] = [256, 256],
                 activation: str = 'relu',
                 dropout: float = 0.0,
                 action_type: str = 'continuous',
                 log_std_init: float = 0.0,
                 device: str = 'auto',
                 **kwargs):
        """初始化A2C智能体
        
        Args:
            state_dim: 状态空间维度
            action_dim: 动作空间维度
            learning_rate: 学习率
            gamma: 折扣因子
            value_coef: 价值损失系数
            entropy_coef: 熵损失系数
            max_grad_norm: 最大梯度范数
            n_steps: n步TD学习
            hidden_dims: 隐藏层维度
            activation: 激活函数
            dropout: Dropout概率
            action_type: 动作类型
            log_std_init: 对数标准差初始值
            device: 计算设备
            **kwargs: 其他参数
        """
        # A2C特定参数
        self.gamma = gamma
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef
        self.max_grad_norm = max_grad_norm
        self.n_steps = n_steps
        self.hidden_dims = hidden_dims
        self.activation = activation
        self.dropout = dropout
        self.action_type = action_type
        self.log_std_init = log_std_init
        
        # 调用父类初始化
        super().__init__(
            state_dim=state_dim,
            action_dim=action_dim,
            learning_rate=learning_rate,
            device=device,
            **kwargs
        )
        
        # 经验存储
        self.states = []
        self.actions = []
        self.rewards = []
        self.values = []
        self.log_probs = []
        self.entropies = []
        self.dones = []
        
        # 学习统计
        self.policy_losses = []
        self.value_losses = []
        self.entropy_losses = []
    
    def _build_networks(self):
        """构建神经网络"""
        self.actor = A2CActor(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dims=self.hidden_dims,
            activation=self.activation,
            dropout=self.dropout,
            action_type=self.action_type,
            log_std_init=self.log_std_init
        ).to(self.device)
        
        self.critic = A2CCritic(
            state_dim=self.state_dim,
            hidden_dims=self.hidden_dims,
            activation=self.activation,
            dropout=self.dropout
        ).to(self.device)
    
    def _build_optimizers(self):
        """构建优化器"""
        self.optimizer = torch.optim.Adam(
            list(self.actor.parameters()) + list(self.critic.parameters()),
            lr=self.learning_rate
        )
    
    def act(self, state: np.ndarray, training: bool = True) -> np.ndarray:
        """选择动作
        
        Args:
            state: 当前状态
            training: 是否为训练模式
            
        Returns:
            选择的动作
        """
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            
            if training:
                action, log_prob, entropy = self.actor.get_action_and_log_prob(state_tensor)
                value = self.critic(state_tensor)
                
                # 存储用于学习
                self.states.append(state)
                self.actions.append(action.squeeze(0).cpu().numpy())
                self.log_probs.append(log_prob.item())
                self.entropies.append(entropy.item())
                self.values.append(value.item())
            else:
                if self.action_type == 'continuous':
                    mean, _ = self.actor(state_tensor)
                    action = mean
                else:
                    action_logits = self.actor(state_tensor)
                    action = action_logits.argmax(dim=-1, keepdim=True)
            
            if self.action_type == 'continuous':
                return action.squeeze(0).cpu().numpy()
            else:
                return action.squeeze(0).cpu().numpy()
    
    def store_reward_and_done(self, reward: float, done: bool):
        """存储奖励和结束标志
        
        Args:
            reward: 奖励
            done: 是否结束
        """
        self.rewards.append(reward)
        self.dones.append(done)
    
    def learn(self, next_state: Optional[np.ndarray] = None) -> Dict[str, float]:
        """学习更新
        
        Args:
            next_state: 下一状态（用于计算最后的价值）
            
        Returns:
            学习统计信息
        """
        if len(self.states) < self.n_steps and not self.dones[-1] if self.dones else False:
            return {'policy_loss': 0.0, 'value_loss': 0.0, 'entropy_loss': 0.0}
        
        # 计算下一状态价值
        next_value = 0.0
        if next_state is not None and not self.dones[-1]:
            with torch.no_grad():
                next_state_tensor = torch.FloatTensor(next_state).unsqueeze(0).to(self.device)
                next_value = self.critic(next_state_tensor).item()
        
        # 计算回报和优势
        returns = []
        advantages = []
        
        R = next_value
        for i in reversed(range(len(self.rewards))):
            R = self.rewards[i] + self.gamma * R * (1 - self.dones[i])
            returns.insert(0, R)
            advantage = R - self.values[i]
            advantages.insert(0, advantage)
        
        # 转换为张量
        states = torch.FloatTensor(np.array(self.states)).to(self.device)
        actions = torch.FloatTensor(np.array(self.actions)).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)
        advantages = torch.FloatTensor(advantages).to(self.device)
        old_log_probs = torch.FloatTensor(self.log_probs).to(self.device)
        
        if self.action_type == 'discrete':
            actions = actions.long()
        
        # 标准化优势
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # 计算新的对数概率、熵和状态价值
        new_log_probs, entropies = self.actor.get_log_prob_and_entropy(states, actions)
        values = self.critic(states).squeeze(-1)
        
        # 计算损失
        policy_loss = -(new_log_probs * advantages).mean()
        value_loss = F.mse_loss(values, returns)
        entropy_loss = -entropies.mean()
        
        # 总损失
        total_loss = policy_loss + self.value_coef * value_loss + self.entropy_coef * entropy_loss
        
        # 反向传播
        self.optimizer.zero_grad()
        total_loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(
            list(self.actor.parameters()) + list(self.critic.parameters()),
            self.max_grad_norm
        )
        
        self.optimizer.step()
        
        # 清空存储
        self.states.clear()
        self.actions.clear()
        self.rewards.clear()
        self.values.clear()
        self.log_probs.clear()
        self.entropies.clear()
        self.dones.clear()
        
        # 更新训练步数
        self.training_step += 1
        
        # 记录损失
        policy_loss_value = policy_loss.item()
        value_loss_value = value_loss.item()
        entropy_loss_value = entropy_loss.item()
        
        self.policy_losses.append(policy_loss_value)
        self.value_losses.append(value_loss_value)
        self.entropy_losses.append(entropy_loss_value)
        self.losses.append(total_loss.item())
        
        return {
            'policy_loss': policy_loss_value,
            'value_loss': value_loss_value,
            'entropy_loss': entropy_loss_value,
            'total_loss': total_loss.item()
        }
    
    def reset_episode(self):
        """重置回合"""
        self.states.clear()
        self.actions.clear()
        self.rewards.clear()
        self.values.clear()
        self.log_probs.clear()
        self.entropies.clear()
        self.dones.clear()
    
    def _get_save_dict(self) -> Dict[str, Any]:
        """获取保存字典"""
        return {
            'actor_state_dict': self.actor.state_dict(),
            'critic_state_dict': self.critic.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'gamma': self.gamma,
            'value_coef': self.value_coef,
            'entropy_coef': self.entropy_coef,
            'n_steps': self.n_steps,
            'action_type': self.action_type,
        }
    
    def _load_from_dict(self, save_dict: Dict[str, Any]):
        """从保存字典加载"""
        self.actor.load_state_dict(save_dict['actor_state_dict'])
        self.critic.load_state_dict(save_dict['critic_state_dict'])
        self.optimizer.load_state_dict(save_dict['optimizer_state_dict'])
    
    def set_training_mode(self, training: bool = True):
        """设置训练模式"""
        if training:
            self.actor.train()
            self.critic.train()
        else:
            self.actor.eval()
            self.critic.eval()
    
    def get_action_distribution(self, state: np.ndarray) -> Dict[str, Any]:
        """获取动作分布"""
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            
            if self.action_type == 'continuous':
                mean, std = self.actor(state_tensor)
                return {
                    'mean': mean.squeeze(0).cpu().numpy(),
                    'std': std.squeeze(0).cpu().numpy(),
                    'action_type': 'continuous'
                }
            else:
                action_logits = self.actor(state_tensor)
                action_probs = F.softmax(action_logits, dim=-1)
                return {
                    'logits': action_logits.squeeze(0).cpu().numpy(),
                    'probabilities': action_probs.squeeze(0).cpu().numpy(),
                    'best_action': action_logits.argmax().item(),
                    'action_type': 'discrete'
                }
    
    def get_value(self, state: np.ndarray) -> float:
        """获取状态价值
        
        Args:
            state: 状态
            
        Returns:
            状态价值
        """
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            value = self.critic(state_tensor)
            return value.item()