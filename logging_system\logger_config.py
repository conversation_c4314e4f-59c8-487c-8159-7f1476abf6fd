import logging
import logging.handlers
import os
from pathlib import Path
from typing import Optional, Dict, Any


class LoggerConfig:
    """日志配置管理器"""
    
    _loggers: Dict[str, logging.Logger] = {}
    _handlers: Dict[str, logging.Handler] = {}
    
    @classmethod
    def setup_logger(
        cls,
        name: str,
        log_file: Optional[str] = None,
        level: str = 'INFO',
        format_string: Optional[str] = None,
        max_bytes: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5
    ) -> logging.Logger:
        """设置日志记录器
        
        Args:
            name: 日志记录器名称
            log_file: 日志文件路径
            level: 日志级别
            format_string: 日志格式字符串
            max_bytes: 单个日志文件最大字节数
            backup_count: 备份文件数量
            
        Returns:
            配置好的日志记录器
        """
        # 如果已存在，直接返回
        if name in cls._loggers:
            return cls._loggers[name]
        
        # 创建日志记录器
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, level.upper(), logging.INFO))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 设置默认格式
        if format_string is None:
            format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        formatter = logging.Formatter(format_string)
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 添加文件处理器（如果指定了文件路径）
        if log_file:
            # 确保目录存在
            log_dir = Path(log_file).parent
            log_dir.mkdir(parents=True, exist_ok=True)
            
            # 使用轮转文件处理器
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
            
            cls._handlers[f"{name}_file"] = file_handler
        
        cls._loggers[name] = logger
        return logger
    
    @classmethod
    def get_logger(cls, name: str) -> Optional[logging.Logger]:
        """获取已配置的日志记录器"""
        return cls._loggers.get(name)
    
    @classmethod
    def remove_logger(cls, name: str) -> bool:
        """移除日志记录器"""
        if name in cls._loggers:
            logger = cls._loggers[name]
            # 关闭所有处理器
            for handler in logger.handlers[:]:
                handler.close()
                logger.removeHandler(handler)
            
            del cls._loggers[name]
            
            # 移除相关的文件处理器
            file_handler_key = f"{name}_file"
            if file_handler_key in cls._handlers:
                del cls._handlers[file_handler_key]
            
            return True
        return False
    
    @classmethod
    def set_level(cls, name: str, level: str) -> bool:
        """设置日志记录器级别"""
        if name in cls._loggers:
            logger = cls._loggers[name]
            logger.setLevel(getattr(logging, level.upper(), logging.INFO))
            return True
        return False
    
    @classmethod
    def add_handler(cls, name: str, handler: logging.Handler) -> bool:
        """为指定日志记录器添加处理器"""
        if name in cls._loggers:
            logger = cls._loggers[name]
            logger.addHandler(handler)
            return True
        return False
    
    @classmethod
    def remove_handler(cls, name: str, handler: logging.Handler) -> bool:
        """从指定日志记录器移除处理器"""
        if name in cls._loggers:
            logger = cls._loggers[name]
            logger.removeHandler(handler)
            return True
        return False
    
    @classmethod
    def setup_rotating_file_logger(
        cls,
        name: str,
        log_file: str,
        level: str = 'INFO',
        max_bytes: int = 10 * 1024 * 1024,
        backup_count: int = 5
    ) -> logging.Logger:
        """设置轮转文件日志记录器"""
        return cls.setup_logger(
            name=name,
            log_file=log_file,
            level=level,
            max_bytes=max_bytes,
            backup_count=backup_count
        )
    
    @classmethod
    def setup_time_rotating_logger(
        cls,
        name: str,
        log_file: str,
        level: str = 'INFO',
        when: str = 'midnight',
        interval: int = 1,
        backup_count: int = 7
    ) -> logging.Logger:
        """设置时间轮转日志记录器"""
        # 如果已存在，直接返回
        if name in cls._loggers:
            return cls._loggers[name]
        
        # 创建日志记录器
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, level.upper(), logging.INFO))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 设置格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 确保目录存在
        log_dir = Path(log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 添加时间轮转文件处理器
        time_handler = logging.handlers.TimedRotatingFileHandler(
            log_file,
            when=when,
            interval=interval,
            backupCount=backup_count,
            encoding='utf-8'
        )
        time_handler.setFormatter(formatter)
        logger.addHandler(time_handler)
        
        cls._loggers[name] = logger
        cls._handlers[f"{name}_time"] = time_handler
        
        return logger
    
    @classmethod
    def cleanup_all(cls):
        """清理所有日志记录器和处理器"""
        for name in list(cls._loggers.keys()):
            cls.remove_logger(name)
        
        cls._loggers.clear()
        cls._handlers.clear()
    
    @classmethod
    def get_all_loggers(cls) -> Dict[str, logging.Logger]:
        """获取所有已配置的日志记录器"""
        return cls._loggers.copy()
    
    @classmethod
    def configure_root_logger(cls, level: str = 'WARNING'):
        """配置根日志记录器"""
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, level.upper(), logging.WARNING))
        
        # 如果没有处理器，添加一个基本的控制台处理器
        if not root_logger.handlers:
            console_handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)