<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="7" skipped="0" tests="36" time="24.362" timestamp="2025-06-19T02:05:06.829185+09:00" hostname="E-5CG22747W5"><testcase classname="improvetest.test_phase1_agent_module.TestBaseAgent" name="test_agent_configuration" time="1.078" /><testcase classname="improvetest.test_phase1_agent_module.TestBaseAgent" name="test_base_agent_abstract_methods" time="0.032"><failure message="AssertionError: False is not true">improvetest\test_phase1_agent_module.py:526: in test_base_agent_abstract_methods
    self.assertTrue(issubclass(BaseAgent, ABC))
E   AssertionError: False is not true</failure></testcase><testcase classname="improvetest.test_phase1_agent_module.TestBaseAgent" name="test_base_agent_initialization" time="0.003" /><testcase classname="improvetest.test_phase1_agent_module.TestPPOAgent" name="test_ppo_advantage_calculation" time="0.005"><failure message="AttributeError: 'PPOAgent' object has no attribute 'gamma'">improvetest\test_phase1_agent_module.py:684: in test_ppo_advantage_calculation
    advantage = rewards[i] + agent.gamma * next_value - values[i]
E   AttributeError: 'PPOAgent' object has no attribute 'gamma'</failure></testcase><testcase classname="improvetest.test_phase1_agent_module.TestPPOAgent" name="test_ppo_agent_initialization" time="0.008" /><testcase classname="improvetest.test_phase1_agent_module.TestPPOAgent" name="test_ppo_policy_update" time="0.006"><failure message="AttributeError: 'NoneType' object has no attribute 'parameters'">improvetest\test_phase1_agent_module.py:634: in test_ppo_policy_update
    old_params = [param.clone() for param in agent.actor.parameters()]
E   AttributeError: 'NoneType' object has no attribute 'parameters'</failure></testcase><testcase classname="improvetest.test_phase1_agent_module.TestPPOAgent" name="test_ppo_prediction" time="0.003" /><testcase classname="improvetest.test_phase1_agent_module.TestPPOAgent" name="test_ppo_training" time="0.006"><failure message="AssertionError: {'loss': 0.1, 'accuracy': 0.9} is not an instance of (&lt;class 'int'&gt;, &lt;class 'float'&gt;, &lt;class 'NoneType'&gt;)">improvetest\test_phase1_agent_module.py:623: in test_ppo_training
    self.assertIsInstance(loss, (int, float, type(None)))
E   AssertionError: {'loss': 0.1, 'accuracy': 0.9} is not an instance of (&lt;class 'int'&gt;, &lt;class 'float'&gt;, &lt;class 'NoneType'&gt;)</failure></testcase><testcase classname="improvetest.test_phase1_agent_module.TestA2CAgent" name="test_a2c_actor_critic_networks" time="0.008" /><testcase classname="improvetest.test_phase1_agent_module.TestA2CAgent" name="test_a2c_agent_initialization" time="0.005" /><testcase classname="improvetest.test_phase1_agent_module.TestA2CAgent" name="test_a2c_policy_gradient" time="0.045"><failure message="TypeError: 'NoneType' object is not callable">improvetest\test_phase1_agent_module.py:752: in test_a2c_policy_gradient
    action_logits = agent.actor(state_tensor)
E   TypeError: 'NoneType' object is not callable</failure></testcase><testcase classname="improvetest.test_phase1_agent_module.TestA2CAgent" name="test_a2c_value_estimation" time="0.007"><failure message="TypeError: 'NoneType' object is not callable">improvetest\test_phase1_agent_module.py:734: in test_a2c_value_estimation
    value = agent.critic(state_tensor)
E   TypeError: 'NoneType' object is not callable</failure></testcase><testcase classname="improvetest.test_phase1_agent_module.TestSACAgent" name="test_sac_agent_initialization" time="0.003" /><testcase classname="improvetest.test_phase1_agent_module.TestSACAgent" name="test_sac_entropy_regularization" time="0.005" /><testcase classname="improvetest.test_phase1_agent_module.TestSACAgent" name="test_sac_soft_q_learning" time="0.007"><failure message="TypeError: 'NoneType' object is not callable">improvetest\test_phase1_agent_module.py:797: in test_sac_soft_q_learning
    q_value = agent.critic1(state_tensor, action_tensor)
E   TypeError: 'NoneType' object is not callable</failure></testcase><testcase classname="improvetest.test_phase1_agent_module.TestSACAgent" name="test_sac_temperature_parameter" time="0.003" /><testcase classname="improvetest.test_phase1_agent_module.TestModelManager" name="test_load_model" time="0.113" /><testcase classname="improvetest.test_phase1_agent_module.TestModelManager" name="test_model_manager_initialization" time="0.010" /><testcase classname="improvetest.test_phase1_agent_module.TestModelManager" name="test_model_metadata" time="0.044" /><testcase classname="improvetest.test_phase1_agent_module.TestModelManager" name="test_model_versioning" time="0.061" /><testcase classname="improvetest.test_phase1_agent_module.TestModelManager" name="test_save_model" time="0.020" /><testcase classname="improvetest.test_phase1_agent_module.TestAgentFactory" name="test_agent_with_custom_config" time="0.005" /><testcase classname="improvetest.test_phase1_agent_module.TestAgentFactory" name="test_create_a2c_agent" time="0.004" /><testcase classname="improvetest.test_phase1_agent_module.TestAgentFactory" name="test_create_ppo_agent" time="0.003" /><testcase classname="improvetest.test_phase1_agent_module.TestAgentFactory" name="test_create_sac_agent" time="0.007" /><testcase classname="improvetest.test_phase1_agent_module.TestAgentFactory" name="test_invalid_agent_type" time="0.003" /><testcase classname="improvetest.test_phase1_agent_module.TestMultiAgentManager" name="test_add_agent" time="0.005" /><testcase classname="improvetest.test_phase1_agent_module.TestMultiAgentManager" name="test_agent_coordination" time="0.003" /><testcase classname="improvetest.test_phase1_agent_module.TestMultiAgentManager" name="test_agent_performance_tracking" time="0.003" /><testcase classname="improvetest.test_phase1_agent_module.TestMultiAgentManager" name="test_ensemble_prediction" time="0.003" /><testcase classname="improvetest.test_phase1_agent_module.TestMultiAgentManager" name="test_get_agent_predictions" time="0.003" /><testcase classname="improvetest.test_phase1_agent_module.TestMultiAgentManager" name="test_multi_agent_manager_initialization" time="0.006" /><testcase classname="improvetest.test_phase1_agent_module.TestMultiAgentManager" name="test_remove_agent" time="0.003" /><testcase classname="improvetest.test_phase1_agent_module.TestAgentModuleIntegration" name="test_agent_evaluation" time="0.012" /><testcase classname="improvetest.test_phase1_agent_module.TestAgentModuleIntegration" name="test_complete_training_pipeline" time="0.033" /><testcase classname="improvetest.test_phase1_agent_module.TestAgentModuleIntegration" name="test_multi_agent_training" time="0.013" /></testsuite></testsuites>