# FinRL Crypto 文档

欢迎使用 FinRL Crypto - 一个基于强化学习的加密货币交易系统！

## 🚀 项目简介

FinRL Crypto 是一个专门为加密货币市场设计的强化学习交易框架。它提供了完整的数据处理、模型训练、策略回测和实时交易功能，帮助研究人员和交易者开发和部署智能交易策略。

## ✨ 主要特性

- **🔄 完整的交易流程**: 从数据获取到策略部署的端到端解决方案
- **🧠 多种RL算法**: 支持PPO、A2C、DDPG、SAC等主流强化学习算法
- **📊 丰富的技术指标**: 内置多种技术分析指标和特征工程工具
- **⚡ 高性能回测**: 快速准确的历史数据回测系统
- **🔌 插件化架构**: 易于扩展的模块化设计
- **📈 实时监控**: 完善的性能监控和风险管理系统
- **🐳 容器化部署**: 支持Docker和Kubernetes部署
- **📚 详细文档**: 完整的API文档和使用教程

## 🏗️ 系统架构

```mermaid
graph TB
    A[数据源] --> B[数据处理模块]
    B --> C[特征工程]
    C --> D[模型训练]
    D --> E[策略优化]
    E --> F[回测验证]
    F --> G[实时交易]
    G --> H[性能监控]
    H --> I[风险管理]
    
    subgraph "核心组件"
        J[环境模拟器]
        K[智能体]
        L[奖励函数]
        M[动作空间]
    end
    
    subgraph "扩展功能"
        N[插件系统]
        O[可视化工具]
        P[报告生成]
        Q[API接口]
    end
```

## 🎯 适用场景

### 学术研究
- 强化学习算法研究
- 金融市场建模
- 交易策略开发
- 风险管理研究

### 商业应用
- 量化交易策略
- 投资组合管理
- 风险控制系统
- 算法交易平台

### 教育培训
- 金融科技教学
- 机器学习实践
- 量化分析培训
- 研究项目开发

## 📋 快速开始

### 1. 安装

```bash
# 使用pip安装
pip install finrl-crypto

# 或者从源码安装
git clone https://github.com/your-org/FinRL-Crypto.git
cd FinRL-Crypto
pip install -e .
```

### 2. 基础使用

```python
from finrl_crypto import FinRLCrypto
from finrl_crypto.config import Config

# 初始化配置
config = Config()
config.load_from_file('config_main.py')

# 创建交易系统
trader = FinRLCrypto(config)

# 数据准备
trader.prepare_data(
    symbols=['BTC-USD', 'ETH-USD'],
    start_date='2020-01-01',
    end_date='2023-12-31'
)

# 模型训练
trader.train_model(
    algorithm='PPO',
    total_timesteps=100000
)

# 策略回测
results = trader.backtest(
    start_date='2023-01-01',
    end_date='2023-12-31'
)

# 查看结果
print(f"总收益率: {results['total_return']:.2%}")
print(f"夏普比率: {results['sharpe_ratio']:.2f}")
print(f"最大回撤: {results['max_drawdown']:.2%}")
```

### 3. Docker部署

```bash
# 构建镜像
docker build -t finrl-crypto .

# 运行容器
docker run -p 8000:8000 finrl-crypto

# 或使用docker-compose
docker-compose up -d
```

## 📊 性能指标

| 指标 | 描述 | 目标值 |
|------|------|--------|
| 年化收益率 | 策略年化收益 | > 15% |
| 夏普比率 | 风险调整收益 | > 1.5 |
| 最大回撤 | 最大损失幅度 | < 20% |
| 胜率 | 盈利交易比例 | > 55% |
| 信息比率 | 超额收益稳定性 | > 0.5 |

## 🔧 配置选项

### 数据配置
```python
DATA_CONFIG = {
    'source': 'yfinance',  # 数据源
    'symbols': ['BTC-USD', 'ETH-USD'],  # 交易对
    'timeframe': '1h',  # 时间周期
    'features': ['open', 'high', 'low', 'close', 'volume'],  # 特征
    'indicators': ['sma', 'ema', 'rsi', 'macd'],  # 技术指标
}
```

### 模型配置
```python
MODEL_CONFIG = {
    'algorithm': 'PPO',  # 算法类型
    'policy': 'MlpPolicy',  # 策略网络
    'learning_rate': 3e-4,  # 学习率
    'n_steps': 2048,  # 步数
    'batch_size': 64,  # 批次大小
    'n_epochs': 10,  # 训练轮数
}
```

### 交易配置
```python
TRADING_CONFIG = {
    'initial_amount': 100000,  # 初始资金
    'transaction_cost': 0.001,  # 交易费用
    'max_position': 0.95,  # 最大仓位
    'stop_loss': 0.05,  # 止损比例
    'take_profit': 0.15,  # 止盈比例
}
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [贡献指南](development/contributing.md) 了解详细信息。

### 开发流程
1. Fork 项目仓库
2. 创建功能分支
3. 提交代码更改
4. 编写测试用例
5. 提交 Pull Request

### 代码规范
- 遵循 PEP 8 代码风格
- 编写完整的文档字符串
- 添加类型注解
- 保持测试覆盖率 > 80%

## 📞 支持与反馈

- **GitHub Issues**: [报告问题](https://github.com/your-org/FinRL-Crypto/issues)
- **讨论区**: [GitHub Discussions](https://github.com/your-org/FinRL-Crypto/discussions)
- **邮件**: <EMAIL>
- **文档**: [在线文档](https://finrl-crypto.readthedocs.io/)

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](license.md) 文件。

## 🙏 致谢

感谢以下项目和组织的支持：

- [FinRL](https://github.com/AI4Finance-Foundation/FinRL) - 金融强化学习框架
- [Stable Baselines3](https://github.com/DLR-RM/stable-baselines3) - 强化学习算法库
- [OpenAI Gym](https://github.com/openai/gym) - 强化学习环境
- [YFinance](https://github.com/ranaroussi/yfinance) - 金融数据获取

---

**开始您的强化学习交易之旅！** 🚀

[快速开始](getting-started/quickstart.md){ .md-button .md-button--primary }
[API文档](api/core.md){ .md-button }
[示例代码](examples/complete-examples.md){ .md-button }