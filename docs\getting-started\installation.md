# 安装指南

本指南将帮助您在不同环境中安装和配置 FinRL Crypto。

## 系统要求

### 最低要求
- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **Python**: 3.8 - 3.11
- **内存**: 8GB RAM
- **存储**: 10GB 可用空间
- **网络**: 稳定的互联网连接（用于数据下载）

### 推荐配置
- **操作系统**: Ubuntu 20.04+ 或 Windows 11
- **Python**: 3.9 或 3.10
- **内存**: 16GB+ RAM
- **存储**: 50GB+ SSD
- **GPU**: NVIDIA GPU（支持CUDA 11.0+）用于加速训练
- **CPU**: 8核心以上

## 安装方式

### 方式一：使用 pip 安装（推荐）

#### 1. 创建虚拟环境

```bash
# 使用 venv
python -m venv finrl-crypto-env

# 激活虚拟环境
# Windows
finrl-crypto-env\Scripts\activate
# macOS/Linux
source finrl-crypto-env/bin/activate
```

或使用 conda：

```bash
# 创建conda环境
conda create -n finrl-crypto python=3.9
conda activate finrl-crypto
```

#### 2. 安装 FinRL Crypto

```bash
# 从 PyPI 安装最新稳定版
pip install finrl-crypto

# 或安装开发版本
pip install finrl-crypto[dev]

# 安装完整版本（包含所有可选依赖）
pip install finrl-crypto[all]
```

#### 3. 验证安装

```python
import finrl_crypto
print(f"FinRL Crypto 版本: {finrl_crypto.__version__}")

# 运行快速测试
from finrl_crypto.utils import check_installation
check_installation()
```

### 方式二：从源码安装

#### 1. 克隆仓库

```bash
git clone https://github.com/your-org/FinRL-Crypto.git
cd FinRL-Crypto
```

#### 2. 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装开发依赖
pip install -r requirements-dev.txt

# 安装项目（开发模式）
pip install -e .
```

#### 3. 运行测试

```bash
# 运行单元测试
pytest improvetest/

# 运行代码质量检查
flake8 .
black --check .
mypy .
```

### 方式三：使用 Docker

#### 1. 安装 Docker

请参考 [Docker 官方文档](https://docs.docker.com/get-docker/) 安装 Docker。

#### 2. 拉取镜像

```bash
# 拉取最新镜像
docker pull finrl/crypto:latest

# 或拉取特定版本
docker pull finrl/crypto:v1.0.0
```

#### 3. 运行容器

```bash
# 运行交互式容器
docker run -it --rm finrl/crypto:latest bash

# 运行 Jupyter Notebook
docker run -p 8888:8888 finrl/crypto:latest jupyter

# 运行 Web 服务
docker run -p 8000:8000 finrl/crypto:latest web
```

#### 4. 使用 Docker Compose

```bash
# 克隆仓库
git clone https://github.com/your-org/FinRL-Crypto.git
cd FinRL-Crypto

# 启动完整服务栈
docker-compose up -d

# 启动开发环境
docker-compose -f docker-compose.dev.yml up -d
```

## 可选依赖

### GPU 支持

如果您有 NVIDIA GPU，可以安装 CUDA 支持：

```bash
# 安装 PyTorch GPU 版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 验证 GPU 可用性
python -c "import torch; print(f'CUDA 可用: {torch.cuda.is_available()}')"
```

### 数据库支持

```bash
# PostgreSQL 支持
pip install psycopg2-binary

# MySQL 支持
pip install pymysql

# SQLite（默认包含）
```

### 可视化工具

```bash
# 高级可视化
pip install plotly dash bokeh

# 交互式图表
pip install ipywidgets
```

### 性能优化

```bash
# 数值计算加速
pip install numba

# 并行处理
pip install joblib dask

# 内存优化
pip install memory_profiler
```

## 环境配置

### 1. 环境变量

创建 `.env` 文件：

```bash
# 数据配置
DATA_SOURCE=yfinance
DATA_PATH=./data
CACHE_PATH=./cache

# 模型配置
MODEL_PATH=./models
CHECKPOINT_PATH=./checkpoints

# 日志配置
LOG_LEVEL=INFO
LOG_PATH=./logs

# API 配置
API_KEY=your_api_key_here
API_SECRET=your_api_secret_here

# 数据库配置
DATABASE_URL=sqlite:///finrl_crypto.db
# DATABASE_URL=postgresql://user:password@localhost:5432/finrl_crypto

# Redis 配置（可选）
REDIS_URL=redis://localhost:6379/0
```

### 2. 配置文件

复制并修改配置文件：

```bash
# 复制默认配置
cp config_main.py.example config_main.py

# 编辑配置
vim config_main.py
```

### 3. 数据目录

创建必要的目录结构：

```bash
mkdir -p data/{raw,processed,features}
mkdir -p models/{trained,checkpoints}
mkdir -p logs/{training,trading,system}
mkdir -p results/{backtest,reports}
mkdir -p cache
```

## 常见问题

### 安装问题

#### Q: pip 安装失败，提示编译错误

**A**: 通常是缺少编译工具或依赖库：

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install build-essential python3-dev

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install python3-devel

# macOS
xcode-select --install

# Windows
# 安装 Microsoft C++ Build Tools
```

#### Q: 导入模块失败

**A**: 检查虚拟环境和 Python 路径：

```bash
# 检查当前 Python 路径
which python
python -c "import sys; print(sys.path)"

# 重新安装
pip uninstall finrl-crypto
pip install finrl-crypto
```

#### Q: GPU 不可用

**A**: 检查 CUDA 安装和版本兼容性：

```bash
# 检查 CUDA 版本
nvidia-smi
nvcc --version

# 检查 PyTorch CUDA 支持
python -c "import torch; print(torch.version.cuda)"
```

### 性能问题

#### Q: 训练速度慢

**A**: 优化建议：

1. 使用 GPU 加速
2. 调整批次大小
3. 启用多进程
4. 使用更快的数据加载器

```python
# 配置示例
config.training.use_gpu = True
config.training.batch_size = 256
config.training.num_workers = 4
config.data.use_cache = True
```

#### Q: 内存不足

**A**: 内存优化：

```python
# 减少批次大小
config.training.batch_size = 64

# 启用梯度累积
config.training.gradient_accumulation_steps = 4

# 使用混合精度训练
config.training.use_amp = True
```

### 数据问题

#### Q: 数据下载失败

**A**: 检查网络连接和 API 配置：

```python
# 测试网络连接
import requests
response = requests.get('https://api.binance.com/api/v3/ping')
print(response.status_code)

# 检查 API 密钥
from finrl_crypto.data import check_api_connection
check_api_connection()
```

## 下一步

安装完成后，您可以：

1. 查看 [快速教程](quickstart.md) 开始第一个项目
2. 阅读 [配置说明](configuration.md) 了解详细配置
3. 浏览 [API 文档](../api/core.md) 学习核心功能
4. 运行 [示例代码](../examples/complete-examples.md) 熟悉框架

## 获取帮助

如果遇到安装问题，请：

1. 查看 [故障排除](../troubleshooting/faq.md) 页面
2. 搜索 [GitHub Issues](https://github.com/your-org/FinRL-Crypto/issues)
3. 在 [讨论区](https://github.com/your-org/FinRL-Crypto/discussions) 提问
4. 发送邮件至 <EMAIL>