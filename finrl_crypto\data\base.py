"""数据处理器基类模块

提供统一的数据处理接口和基础功能实现。
"""

import logging
import numpy as np
import pandas as pd
import stockstats
from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Union, Tuple
from datetime import datetime, timedelta

# 条件导入talib，如果没有安装则设为None
try:
    import talib
except ImportError:
    talib = None
    logging.warning("TA-Lib not installed. Some technical indicators may not be available.")


class DataProcessor(ABC):
    """数据处理器抽象基类
    
    提供统一的数据处理接口，所有具体的数据处理器都应继承此类。
    
    Attributes:
        data_source (str): 数据源名称
        start_date (str): 开始日期
        end_date (str): 结束日期
        time_interval (str): 时间间隔
        time_zone (str): 时区
        dataframe (pd.DataFrame): 存储的数据
        logger (logging.Logger): 日志记录器
    """
    
    def __init__(self, 
                 data_source: str,
                 start_date: str,
                 end_date: str, 
                 time_interval: str = '1d',
                 time_zone: str = 'UTC',
                 cache_dir: Optional[str] = None,
                 **kwargs):
        """初始化数据处理器
        
        Args:
            data_source: 数据源名称
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            time_interval: 时间间隔，默认'1d'
            time_zone: 时区，默认'UTC'
            cache_dir: 缓存目录路径
            **kwargs: 其他参数
        """
        self.data_source = data_source
        self.start_date = start_date
        self.end_date = end_date
        self.time_interval = time_interval
        self.time_zone = time_zone
        self.cache_dir = cache_dir
        
        # 数据存储
        self.dataframe = pd.DataFrame()
        self.metadata = {}
        
        # 配置日志
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # 验证参数
        self._validate_parameters()
    
    def _validate_parameters(self) -> None:
        """验证初始化参数"""
        try:
            datetime.strptime(self.start_date, '%Y-%m-%d')
            datetime.strptime(self.end_date, '%Y-%m-%d')
        except ValueError as e:
            raise ValueError(f"日期格式错误，应为YYYY-MM-DD: {e}")
            
        if self.start_date >= self.end_date:
            raise ValueError("开始日期必须早于结束日期")
    
    @abstractmethod
    def download_data(self, ticker_list: List[str]) -> pd.DataFrame:
        """下载数据的抽象方法
        
        Args:
            ticker_list: 股票代码列表
            
        Returns:
            包含下载数据的DataFrame
        """
        pass
    
    @abstractmethod
    def fetch_data(self, ticker_list: List[str] = None) -> pd.DataFrame:
        """获取数据的抽象方法
        
        Args:
            ticker_list: 交易对列表，可选参数
            
        Returns:
            获取的数据DataFrame
        """
        pass
    
    def clean_data(self) -> pd.DataFrame:
        """清理和标准化数据
        
        Returns:
            清理后的DataFrame
        """
        if self.dataframe.empty:
            self.logger.warning("数据为空，无法清理")
            return self.dataframe
            
        # 标准化时间列名
        self._standardize_time_column()
        
        # 标准化其他列名
        self._standardize_columns()
        
        # 处理缺失值
        self._handle_missing_values()
        
        # 排序数据
        self._sort_data()
        
        # 验证数据完整性
        self._validate_data()
        
        self.logger.info(f"数据清理完成，共{len(self.dataframe)}行")
        return self.dataframe
    
    def _standardize_time_column(self) -> None:
        """标准化时间列名"""
        time_columns = ['date', 'datetime', 'timestamp']
        
        for col in time_columns:
            if col in self.dataframe.columns:
                self.dataframe.rename(columns={col: 'time'}, inplace=True)
                break
                
        # 特殊数据源处理
        if self.data_source == "ccxt" and 'index' in self.dataframe.columns:
            self.dataframe.rename(columns={'index': 'time'}, inplace=True)
    
    def _standardize_columns(self) -> None:
        """标准化列名"""
        # 数据源特定的列名映射
        column_mappings = {
            'ricequant': {'order_book_id': 'tic'},
            'baostock': {'code': 'tic'}
        }
        
        if self.data_source in column_mappings:
            self.dataframe.rename(columns=column_mappings[self.data_source], inplace=True)
        
        # 确保有调整后收盘价
        if 'adjusted_close' not in self.dataframe.columns:
            self.dataframe['adjusted_close'] = self.dataframe['close']
    
    def _handle_missing_values(self) -> None:
        """处理缺失值"""
        initial_rows = len(self.dataframe)
        self.dataframe.dropna(inplace=True)
        dropped_rows = initial_rows - len(self.dataframe)
        
        if dropped_rows > 0:
            self.logger.info(f"删除了{dropped_rows}行包含缺失值的数据")
    
    def _sort_data(self) -> None:
        """排序数据"""
        if 'time' in self.dataframe.columns and 'tic' in self.dataframe.columns:
            self.dataframe.sort_values(by=['time', 'tic'], inplace=True)
        elif 'time' in self.dataframe.columns:
            self.dataframe.sort_values(by=['time'], inplace=True)
    
    def _validate_data(self) -> None:
        """验证数据完整性"""
        required_columns = ['tic', 'time', 'open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in self.dataframe.columns]
        
        if missing_columns:
            self.logger.warning(f"缺少必需列: {missing_columns}")
        
        # 重新排列列顺序
        available_columns = [col for col in required_columns if col in self.dataframe.columns]
        other_columns = [col for col in self.dataframe.columns if col not in required_columns]
        self.dataframe = self.dataframe[available_columns + other_columns]
    
    def add_technical_indicators(self, 
                               tech_indicator_list: List[str],
                               method: str = 'stockstats') -> pd.DataFrame:
        """添加技术指标
        
        Args:
            tech_indicator_list: 技术指标列表
            method: 计算方法 ('stockstats' 或 'talib')
            
        Returns:
            添加技术指标后的DataFrame
        """
        if self.dataframe.empty:
            self.logger.error("数据为空，无法计算技术指标")
            return self.dataframe
            
        self.logger.info(f"开始计算技术指标: {tech_indicator_list}")
        
        if method == 'stockstats':
            self._add_indicators_stockstats(tech_indicator_list)
        elif method == 'talib':
            self._add_indicators_talib(tech_indicator_list)
        else:
            raise ValueError(f"不支持的计算方法: {method}")
            
        # 清理包含NaN的时间点
        self._clean_indicator_data()
        
        self.logger.info("技术指标计算完成")
        return self.dataframe
    
    def _add_indicators_stockstats(self, tech_indicator_list: List[str]) -> None:
        """使用stockstats计算技术指标"""
        self.dataframe.reset_index(drop=True, inplace=True)
        
        # 清理索引列
        for col in ["level_0", "level_1"]:
            if col in self.dataframe.columns:
                if col == "level_0" and "tic" not in self.dataframe.columns:
                    self.dataframe.rename(columns={"level_0": "tic"}, inplace=True)
                else:
                    self.dataframe.drop(columns=[col], inplace=True)
        
        stock = stockstats.StockDataFrame.retype(self.dataframe)
        unique_tickers = stock.tic.unique()
        
        for indicator in tech_indicator_list:
            self.logger.debug(f"计算指标: {indicator}")
            indicator_df = pd.DataFrame()
            
            for ticker in unique_tickers:
                try:
                    ticker_data = stock[stock.tic == ticker]
                    temp_indicator = ticker_data[indicator]
                    
                    temp_df = pd.DataFrame({
                        indicator: temp_indicator,
                        'tic': ticker,
                        'time': ticker_data['time'].tolist()
                    })
                    
                    indicator_df = pd.concat([indicator_df, temp_df], ignore_index=True)
                    
                except Exception as e:
                    self.logger.warning(f"计算{ticker}的{indicator}指标失败: {e}")
            
            if not indicator_df.empty:
                self.dataframe = self.dataframe.merge(
                    indicator_df[['tic', 'time', indicator]], 
                    on=['tic', 'time'], 
                    how='left'
                )
    
    def _add_indicators_talib(self, tech_indicator_list: List[str]) -> None:
        """使用talib计算技术指标"""
        final_df = pd.DataFrame()
        
        for ticker in self.dataframe.tic.unique():
            ticker_df = self.dataframe[self.dataframe.tic == ticker].copy()
            
            # 计算常用技术指标
            if 'macd' in tech_indicator_list:
                ticker_df['macd'], ticker_df['macd_signal'], ticker_df['macd_hist'] = \
                    talib.MACD(ticker_df['close'], fastperiod=12, slowperiod=26, signalperiod=9)
            
            if 'rsi' in tech_indicator_list:
                ticker_df['rsi'] = talib.RSI(ticker_df['close'], timeperiod=14)
            
            if 'cci' in tech_indicator_list:
                ticker_df['cci'] = talib.CCI(ticker_df['high'], ticker_df['low'], 
                                           ticker_df['close'], timeperiod=14)
            
            if 'dx' in tech_indicator_list:
                ticker_df['dx'] = talib.DX(ticker_df['high'], ticker_df['low'], 
                                         ticker_df['close'], timeperiod=14)
            
            final_df = pd.concat([final_df, ticker_df], ignore_index=True)
        
        self.dataframe = final_df
    
    def _clean_indicator_data(self) -> None:
        """清理技术指标数据中的NaN值"""
        self.dataframe.sort_values(by=["time", "tic"], inplace=True)
        
        # 找出包含NaN的时间点
        nan_times = self.dataframe[self.dataframe.isna().any(axis=1)].time.unique()
        
        if len(nan_times) > 0:
            self.dataframe = self.dataframe[~self.dataframe.time.isin(nan_times)]
            self.logger.info(f"删除了{len(nan_times)}个包含NaN的时间点")
    
    def get_trading_days(self, start: str, end: str) -> Optional[List[str]]:
        """获取交易日列表
        
        Args:
            start: 开始日期
            end: 结束日期
            
        Returns:
            交易日列表，如果不支持则返回None
        """
        unsupported_sources = ["binance", "ccxt", "quantconnect", "ricequant", "tushare"]
        
        if self.data_source in unsupported_sources:
            self.logger.warning(f"数据源{self.data_source}暂不支持获取交易日")
            return None
        
        # 子类应该实现具体的交易日获取逻辑
        return None
    
    def to_array(self, 
                tech_indicator_list: List[str], 
                include_risk: bool = False) -> Tuple[np.ndarray, np.ndarray, Optional[np.ndarray]]:
        """将DataFrame转换为numpy数组
        
        Args:
            tech_indicator_list: 技术指标列表
            include_risk: 是否包含风险指标
            
        Returns:
            (价格数组, 技术指标数组, 风险指标数组)
        """
        if self.dataframe.empty:
            raise ValueError("数据为空，无法转换为数组")
        
        unique_tickers = self.dataframe.tic.unique()
        
        # 价格数组
        price_array = np.column_stack([
            self.dataframe[self.dataframe.tic == tic].close.values 
            for tic in unique_tickers
        ])
        
        # 技术指标数组
        available_indicators = [
            indicator for indicator in tech_indicator_list 
            if indicator in self.dataframe.columns
        ]
        
        tech_array = np.hstack([
            self.dataframe.loc[
                self.dataframe.tic == tic, available_indicators
            ].values 
            for tic in unique_tickers
        ])
        
        # 风险指标数组
        risk_array = None
        if include_risk:
            if 'vix' in self.dataframe.columns:
                risk_array = np.column_stack([
                    self.dataframe[self.dataframe.tic == tic].vix.values 
                    for tic in unique_tickers
                ])
            elif 'turbulence' in self.dataframe.columns:
                risk_array = np.column_stack([
                    self.dataframe[self.dataframe.tic == tic].turbulence.values 
                    for tic in unique_tickers
                ])
        
        self.logger.info("成功转换为数组格式")
        return price_array, tech_array, risk_array
    
    def save_data(self, filepath: str, format: str = 'csv') -> None:
        """保存数据到文件
        
        Args:
            filepath: 文件路径
            format: 文件格式 ('csv', 'parquet', 'pickle')
        """
        if self.dataframe.empty:
            self.logger.warning("数据为空，无法保存")
            return
        
        if format == 'csv':
            self.dataframe.to_csv(filepath, index=False)
        elif format == 'parquet':
            self.dataframe.to_parquet(filepath, index=False)
        elif format == 'pickle':
            self.dataframe.to_pickle(filepath)
        else:
            raise ValueError(f"不支持的文件格式: {format}")
        
        self.logger.info(f"数据已保存到: {filepath}")
    
    def load_data(self, filepath: str, format: str = 'csv') -> pd.DataFrame:
        """从文件加载数据
        
        Args:
            filepath: 文件路径
            format: 文件格式 ('csv', 'parquet', 'pickle')
            
        Returns:
            加载的DataFrame
        """
        if format == 'csv':
            self.dataframe = pd.read_csv(filepath)
        elif format == 'parquet':
            self.dataframe = pd.read_parquet(filepath)
        elif format == 'pickle':
            self.dataframe = pd.read_pickle(filepath)
        else:
            raise ValueError(f"不支持的文件格式: {format}")
        
        self.logger.info(f"数据已从{filepath}加载，共{len(self.dataframe)}行")
        return self.dataframe
    
    def get_data_info(self) -> Dict:
        """获取数据信息
        
        Returns:
            包含数据信息的字典
        """
        if self.dataframe.empty:
            return {"status": "empty"}
        
        return {
            "data_source": self.data_source,
            "start_date": self.start_date,
            "end_date": self.end_date,
            "time_interval": self.time_interval,
            "rows": len(self.dataframe),
            "columns": list(self.dataframe.columns),
            "tickers": self.dataframe.tic.unique().tolist() if 'tic' in self.dataframe.columns else [],
            "date_range": {
                "start": self.dataframe.time.min() if 'time' in self.dataframe.columns else None,
                "end": self.dataframe.time.max() if 'time' in self.dataframe.columns else None
            }
        }