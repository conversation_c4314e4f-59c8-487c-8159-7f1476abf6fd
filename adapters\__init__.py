#!/usr/bin/env python3
"""
适配器包

这个包包含了各种RL框架的适配器实现，提供统一的训练接口。

模块:
    elegantrl_adapter: ElegantRL框架适配器
    trainer_factory: 训练器工厂类

作者: FinRL-Crypto 重构项目
日期: 2024
"""

from .elegantrl_adapter import ElegantRLAdapter, ElegantRLModel
from .trainer_factory import TrainerFactory, create_trainer, get_available_trainers

__all__ = [
    'ElegantRLAdapter',
    'ElegantRLModel', 
    'TrainerFactory',
    'create_trainer',
    'get_available_trainers'
]

__version__ = '1.0.0'
__author__ = 'FinRL-Crypto 重构项目'