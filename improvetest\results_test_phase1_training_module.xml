<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="0" skipped="1" tests="1" time="46.623" timestamp="2025-06-19T02:05:44.300455+09:00" hostname="E-5CG22747W5"><testcase classname="" name="improvetest.test_phase1_training_module" time="0.000"><skipped message="collection skipped">('C:\\Users\\<USER>\\OneDrive - <PERSON><PERSON>\\Desktop\\tdccp\\AI4Fin\\FinRL_Crypto\\improvetest\\test_phase1_training_module.py', 28, "Skipped: Required training modules not available: cannot import name 'TrainingUtils' from 'finrl_crypto.training.utils' (C:\\Users\\<USER>\\OneDrive - <PERSON><PERSON>\\Desktop\\tdccp\\AI4Fin\\FinRL_Crypto\\finrl_crypto\\training\\utils.py)")</skipped></testcase></testsuite></testsuites>