#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一阶段测试运行器
执行所有第一阶段的单元测试和集成测试，生成详细的测试报告
"""

import os
import sys
import subprocess
import json
import time
from datetime import datetime
from pathlib import Path
import argparse
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class Phase1TestRunner:
    """第一阶段测试运行器"""
    
    def __init__(self, test_dir: str = None):
        self.test_dir = Path(test_dir) if test_dir else Path(__file__).parent
        self.project_root = self.test_dir.parent
        self.results = {}
        self.start_time = None
        self.end_time = None
        
        # 第一阶段测试文件列表
        self.phase1_test_files = [
            "test_config_manager_unit.py",
            "test_dependency_injection_unit.py", 
            "test_phase1_data_module.py",
            "test_phase1_environment_module.py",
            "test_phase1_agent_module.py",
            "test_phase1_training_module.py",
            "test_phase1_integration.py"
        ]
    
    def setup_environment(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        
        # 确保所有必要的目录存在
        os.makedirs(self.test_dir, exist_ok=True)
        
        # 设置环境变量
        os.environ['PYTHONPATH'] = str(self.project_root)
        os.environ['PYTEST_CURRENT_TEST'] = 'phase1_tests'
        
        # 检查pytest是否可用
        try:
            # 首先尝试直接运行pytest
            result = subprocess.run(['pytest', '--version'], 
                                  capture_output=True, text=True, check=True)
            print(f"✅ Pytest版本: {result.stdout.strip()}")
            self.pytest_cmd = 'pytest'
        except (subprocess.CalledProcessError, FileNotFoundError):
            try:
                # 如果直接运行失败，尝试通过python -m pytest
                result = subprocess.run(['python', '-m', 'pytest', '--version'], 
                                      capture_output=True, text=True, check=True)
                print(f"✅ Pytest版本: {result.stdout.strip()}")
                self.pytest_cmd = 'python -m pytest'
            except (subprocess.CalledProcessError, FileNotFoundError):
                print("❌ 错误: pytest未安装或不可用")
                print("请运行: pip install pytest pytest-cov pytest-html")
                sys.exit(1)
    
    def check_test_files(self) -> List[str]:
        """检查测试文件是否存在"""
        print("📋 检查测试文件...")
        
        existing_files = []
        missing_files = []
        
        for test_file in self.phase1_test_files:
            test_path = self.test_dir / test_file
            if test_path.exists():
                existing_files.append(test_file)
                print(f"✅ {test_file}")
            else:
                missing_files.append(test_file)
                print(f"❌ {test_file} (缺失)")
        
        if missing_files:
            print(f"\n⚠️  警告: {len(missing_files)} 个测试文件缺失")
            print("缺失的文件:", missing_files)
        
        return existing_files
    
    def run_single_test(self, test_file: str, verbose: bool = False) -> Dict[str, Any]:
        """运行单个测试文件"""
        test_path = self.test_dir / test_file
        
        print(f"\n🧪 运行测试: {test_file}")
        print("=" * 60)
        
        # 构建pytest命令
        if self.pytest_cmd == 'python -m pytest':
            cmd = [
                'python', '-m', 'pytest',
                str(test_path),
                '-v' if verbose else '-q',
                '--tb=short',
                '--durations=10',
                f'--junitxml={self.test_dir}/results_{test_file.replace(".py", ".xml")}',
                '--cov=finrl_crypto',
                '--cov=core',
                f'--cov-report=html:{self.test_dir}/htmlcov_{test_file.replace(".py", "")}',
                '--cov-report=term-missing'
            ]
        else:
            cmd = [
                'pytest',
                str(test_path),
                '-v' if verbose else '-q',
                '--tb=short',
                '--durations=10',
                f'--junitxml={self.test_dir}/results_{test_file.replace(".py", ".xml")}',
                '--cov=finrl_crypto',
                '--cov=core',
                f'--cov-report=html:{self.test_dir}/htmlcov_{test_file.replace(".py", "")}',
                '--cov-report=term-missing'
            ]
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 解析输出
            output_lines = result.stdout.split('\n')
            error_lines = result.stderr.split('\n')
            
            # 提取测试统计信息
            stats = self._parse_pytest_output(result.stdout)
            
            test_result = {
                'file': test_file,
                'status': 'PASSED' if result.returncode == 0 else 'FAILED',
                'return_code': result.returncode,
                'duration': duration,
                'stats': stats,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'timestamp': datetime.now().isoformat()
            }
            
            # 打印结果摘要
            if result.returncode == 0:
                print(f"✅ {test_file} - 通过 ({duration:.2f}s)")
                if stats:
                    print(f"   📊 {stats.get('passed', 0)} 通过, {stats.get('failed', 0)} 失败, {stats.get('skipped', 0)} 跳过")
            else:
                print(f"❌ {test_file} - 失败 ({duration:.2f}s)")
                if verbose:
                    print("错误输出:")
                    print(result.stderr)
            
            return test_result
            
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_file} - 超时 (>300s)")
            return {
                'file': test_file,
                'status': 'TIMEOUT',
                'return_code': -1,
                'duration': 300,
                'stats': {},
                'stdout': '',
                'stderr': 'Test timed out after 300 seconds',
                'timestamp': datetime.now().isoformat()
            }
        
        except Exception as e:
            print(f"💥 {test_file} - 异常: {str(e)}")
            return {
                'file': test_file,
                'status': 'ERROR',
                'return_code': -2,
                'duration': 0,
                'stats': {},
                'stdout': '',
                'stderr': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _parse_pytest_output(self, output: str) -> Dict[str, int]:
        """解析pytest输出，提取统计信息"""
        stats = {'passed': 0, 'failed': 0, 'skipped': 0, 'errors': 0}
        
        # 查找结果行，通常格式为: "=== 5 passed, 2 failed, 1 skipped in 10.23s ==="
        for line in output.split('\n'):
            line = line.strip()
            if 'passed' in line or 'failed' in line or 'skipped' in line:
                if '===' in line and 'in' in line and 's ===' in line:
                    # 解析统计信息
                    parts = line.replace('===', '').replace('in', '').split()
                    i = 0
                    while i < len(parts) - 1:
                        try:
                            count = int(parts[i])
                            status = parts[i + 1].rstrip(',')
                            if status in stats:
                                stats[status] = count
                            i += 2
                        except (ValueError, IndexError):
                            i += 1
                    break
        
        return stats
    
    def run_all_tests(self, verbose: bool = False, fail_fast: bool = False) -> Dict[str, Any]:
        """运行所有第一阶段测试"""
        self.start_time = datetime.now()
        print(f"🚀 开始第一阶段测试 - {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 检查测试文件
        existing_files = self.check_test_files()
        
        if not existing_files:
            print("❌ 没有找到任何测试文件")
            return {'status': 'NO_TESTS', 'results': []}
        
        # 运行测试
        test_results = []
        passed_count = 0
        failed_count = 0
        
        for test_file in existing_files:
            result = self.run_single_test(test_file, verbose)
            test_results.append(result)
            
            if result['status'] == 'PASSED':
                passed_count += 1
            else:
                failed_count += 1
                if fail_fast:
                    print(f"\n🛑 快速失败模式: 在 {test_file} 失败后停止")
                    break
        
        self.end_time = datetime.now()
        total_duration = (self.end_time - self.start_time).total_seconds()
        
        # 汇总结果
        summary = {
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'total_duration': total_duration,
            'total_files': len(existing_files),
            'passed_files': passed_count,
            'failed_files': failed_count,
            'success_rate': passed_count / len(existing_files) * 100 if existing_files else 0,
            'results': test_results
        }
        
        self.results = summary
        return summary
    
    def generate_report(self, output_file: str = None) -> str:
        """生成测试报告"""
        if not self.results:
            print("❌ 没有测试结果可生成报告")
            return ""
        
        report_file = output_file or str(self.test_dir / f"phase1_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")
        
        # 生成Markdown报告
        report_content = self._generate_markdown_report()
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📄 测试报告已生成: {report_file}")
        return report_file
    
    def _generate_markdown_report(self) -> str:
        """生成Markdown格式的测试报告"""
        results = self.results
        
        report = f"""# FinRL Crypto 第一阶段测试报告

## 测试概览

- **测试时间**: {results['start_time']} - {results['end_time']}
- **总耗时**: {results['total_duration']:.2f} 秒
- **测试文件数**: {results['total_files']}
- **通过文件数**: {results['passed_files']}
- **失败文件数**: {results['failed_files']}
- **成功率**: {results['success_rate']:.1f}%

## 测试状态

{'🟢 所有测试通过' if results['failed_files'] == 0 else '🔴 存在测试失败'}

## 详细结果

| 测试文件 | 状态 | 耗时(s) | 通过 | 失败 | 跳过 |
|---------|------|---------|------|------|------|
"""
        
        for result in results['results']:
            status_icon = {
                'PASSED': '✅',
                'FAILED': '❌', 
                'TIMEOUT': '⏰',
                'ERROR': '💥'
            }.get(result['status'], '❓')
            
            stats = result.get('stats', {})
            
            report += f"| {result['file']} | {status_icon} {result['status']} | {result['duration']:.2f} | {stats.get('passed', 0)} | {stats.get('failed', 0)} | {stats.get('skipped', 0)} |\n"
        
        # 添加失败详情
        failed_tests = [r for r in results['results'] if r['status'] != 'PASSED']
        if failed_tests:
            report += "\n## 失败测试详情\n\n"
            for result in failed_tests:
                report += f"### {result['file']}\n\n"
                report += f"**状态**: {result['status']}\n\n"
                report += f"**错误信息**:\n```\n{result['stderr']}\n```\n\n"
        
        # 添加覆盖率信息
        report += "\n## 代码覆盖率\n\n"
        report += "详细的代码覆盖率报告请查看生成的HTML报告文件。\n\n"
        
        # 添加建议
        report += "\n## 建议\n\n"
        if results['failed_files'] == 0:
            report += "- ✅ 所有测试通过，代码质量良好\n"
            report += "- 🔄 建议定期运行测试以确保代码稳定性\n"
        else:
            report += "- 🔧 请修复失败的测试用例\n"
            report += "- 📊 检查代码覆盖率，补充缺失的测试\n"
            report += "- 🐛 分析失败原因，可能需要修复代码逻辑\n"
        
        report += "\n## 下一步\n\n"
        report += "1. 修复所有失败的测试\n"
        report += "2. 提高代码覆盖率到80%以上\n"
        report += "3. 添加更多边界情况测试\n"
        report += "4. 进行性能测试和压力测试\n"
        
        return report
    
    def save_json_report(self, output_file: str = None) -> str:
        """保存JSON格式的测试结果"""
        if not self.results:
            return ""
        
        json_file = output_file or str(self.test_dir / f"phase1_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"📊 JSON结果已保存: {json_file}")
        return json_file
    
    def print_summary(self):
        """打印测试摘要"""
        if not self.results:
            return
        
        results = self.results
        
        print("\n" + "=" * 80)
        print("📋 第一阶段测试摘要")
        print("=" * 80)
        print(f"⏱️  总耗时: {results['total_duration']:.2f} 秒")
        print(f"📁 测试文件: {results['total_files']} 个")
        print(f"✅ 通过: {results['passed_files']} 个")
        print(f"❌ 失败: {results['failed_files']} 个")
        print(f"📊 成功率: {results['success_rate']:.1f}%")
        
        if results['failed_files'] == 0:
            print("\n🎉 恭喜！所有第一阶段测试都通过了！")
        else:
            print(f"\n⚠️  需要修复 {results['failed_files']} 个失败的测试")
        
        print("=" * 80)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='运行FinRL Crypto第一阶段测试')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--fail-fast', '-x', action='store_true', help='遇到失败立即停止')
    parser.add_argument('--test-dir', '-d', help='测试目录路径')
    parser.add_argument('--output', '-o', help='报告输出文件路径')
    parser.add_argument('--json', action='store_true', help='同时生成JSON报告')
    
    args = parser.parse_args()
    
    # 创建测试运行器
    runner = Phase1TestRunner(args.test_dir)
    
    try:
        # 设置环境
        runner.setup_environment()
        
        # 运行测试
        results = runner.run_all_tests(verbose=args.verbose, fail_fast=args.fail_fast)
        
        # 打印摘要
        runner.print_summary()
        
        # 生成报告
        report_file = runner.generate_report(args.output)
        
        if args.json:
            runner.save_json_report()
        
        # 返回适当的退出码
        if results.get('failed_files', 0) > 0:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 测试运行器发生错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()