"""计算优化器模块

提供计算资源优化和张量操作优化功能。
"""

import logging
import threading
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass
from datetime import datetime

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False


@dataclass
class ComputeStats:
    """计算统计信息"""
    device_type: str
    device_count: int
    memory_total: Optional[int] = None
    memory_used: Optional[int] = None
    memory_free: Optional[int] = None
    utilization: Optional[float] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class ComputeOptimizer:
    """计算优化器
    
    优化计算资源使用和张量操作性能。
    """
    
    def __init__(self, device: Optional[str] = None, num_workers: Optional[int] = None):
        """初始化计算优化器
        
        Args:
            device: 指定设备 ('cpu', 'cuda', 'auto')
            num_workers: 工作线程数
        """
        self._logger = logging.getLogger(__name__)
        
        # 设备选择
        if device == 'auto' or device is None:
            try:
                import torch
                if torch.cuda.is_available():
                    self.device = torch.device('cuda')
                else:
                    self.device = torch.device('cpu')
            except ImportError:
                # 如果torch不可用，使用字符串表示
                self.device = type('Device', (), {'type': 'cpu'})()
        else:
            try:
                import torch
                self.device = torch.device(device)
            except ImportError:
                # 如果torch不可用，创建一个简单的设备对象
                self.device = type('Device', (), {'type': device})()
        
        # 工作线程数
        if num_workers is None:
            self.num_workers = self._auto_select_workers()
        else:
            self.num_workers = num_workers
        
        self._stats_history: List[ComputeStats] = []
        self._lock = threading.RLock()
        
        self._logger.info(f"计算优化器初始化完成 - 设备: {self.device}, 工作线程: {self.num_workers}")
    
    def _auto_select_device(self):
        """自动选择最佳设备"""
        if TORCH_AVAILABLE:
            if torch.cuda.is_available():
                device = torch.device('cuda')
                self._logger.info(f"选择CUDA设备: {torch.cuda.get_device_name()}")
                return device
            else:
                device = torch.device('cpu')
                self._logger.info("选择CPU设备")
                return device
        else:
            # 模拟设备对象
            class MockDevice:
                def __init__(self, device_type):
                    self.type = device_type
                
                def __str__(self):
                    return self.type
            
            return MockDevice('cpu')
    
    def _parse_device(self, device_str: str):
        """解析设备字符串"""
        if TORCH_AVAILABLE:
            return torch.device(device_str)
        else:
            class MockDevice:
                def __init__(self, device_type):
                    self.type = device_type
                
                def __str__(self):
                    return self.type
            
            return MockDevice(device_str)
    
    def _auto_select_workers(self) -> int:
        """自动选择工作线程数"""
        import os
        cpu_count = os.cpu_count() or 4
        # 通常使用CPU核心数的一半到全部
        return min(cpu_count, 8)
    
    def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息"""
        info = {
            'device_type': self.device.type,
            'device_str': str(self.device)
        }
        
        if TORCH_AVAILABLE and self.device.type == 'cuda':
            info.update({
                'device_count': torch.cuda.device_count(),
                'current_device': torch.cuda.current_device(),
                'device_name': torch.cuda.get_device_name(),
                'memory_allocated': torch.cuda.memory_allocated(),
                'memory_reserved': torch.cuda.memory_reserved(),
                'memory_total': torch.cuda.get_device_properties(0).total_memory
            })
        
        return info
    
    def optimize_tensor_operations(self, enable_optimization: bool = True) -> Dict[str, Any]:
        """优化张量操作
        
        Args:
            enable_optimization: 是否启用优化
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        result = {
            'optimization_enabled': enable_optimization,
            'optimizations_applied': []
        }
        
        if not TORCH_AVAILABLE:
            result['warning'] = 'PyTorch不可用，无法进行张量优化'
            return result
        
        if enable_optimization:
            # 启用自动混合精度
            if hasattr(torch.backends, 'cudnn'):
                torch.backends.cudnn.benchmark = True
                result['optimizations_applied'].append('cudnn_benchmark')
            
            # 设置线程数
            if hasattr(torch, 'set_num_threads'):
                torch.set_num_threads(self.num_workers)
                result['optimizations_applied'].append(f'num_threads_{self.num_workers}')
            
            self._logger.info(f"张量操作优化已启用: {result['optimizations_applied']}")
        else:
            if hasattr(torch.backends, 'cudnn'):
                torch.backends.cudnn.benchmark = False
            
            self._logger.info("张量操作优化已禁用")
        
        return result
    
    def get_compute_stats(self) -> ComputeStats:
        """获取计算统计信息"""
        device_info = self.get_device_info()
        
        stats = ComputeStats(
            device_type=device_info['device_type'],
            device_count=device_info.get('device_count', 1)
        )
        
        if 'memory_total' in device_info:
            stats.memory_total = device_info['memory_total']
            stats.memory_used = device_info.get('memory_allocated', 0)
            stats.memory_free = stats.memory_total - stats.memory_used
            stats.utilization = (stats.memory_used / stats.memory_total) * 100
        
        with self._lock:
            self._stats_history.append(stats)
            # 限制历史记录数量
            if len(self._stats_history) > 1000:
                self._stats_history = self._stats_history[-1000:]
        
        return stats
    
    def get_stats_history(self, limit: Optional[int] = None) -> List[ComputeStats]:
        """获取统计历史
        
        Args:
            limit: 返回的最大记录数
            
        Returns:
            List[ComputeStats]: 统计历史
        """
        with self._lock:
            if limit is None:
                return self._stats_history.copy()
            else:
                return self._stats_history[-limit:].copy()
    
    def clear_cache(self) -> Dict[str, Any]:
        """清理缓存
        
        Returns:
            Dict[str, Any]: 清理结果
        """
        result = {'cache_cleared': False}
        
        if TORCH_AVAILABLE and self.device.type == 'cuda':
            try:
                memory_before = torch.cuda.memory_allocated()
                torch.cuda.empty_cache()
                memory_after = torch.cuda.memory_allocated()
                
                result.update({
                    'cache_cleared': True,
                    'memory_freed': memory_before - memory_after,
                    'memory_before': memory_before,
                    'memory_after': memory_after
                })
                
                self._logger.info(f"CUDA缓存已清理，释放内存: {result['memory_freed']} bytes")
            except Exception as e:
                result['error'] = str(e)
                self._logger.error(f"清理CUDA缓存失败: {e}")
        
        return result
    
    def benchmark_operations(self, operations: List[Callable], 
                           iterations: int = 100) -> Dict[str, Any]:
        """基准测试操作
        
        Args:
            operations: 要测试的操作列表
            iterations: 迭代次数
            
        Returns:
            Dict[str, Any]: 基准测试结果
        """
        import time
        
        results = {
            'device': str(self.device),
            'iterations': iterations,
            'operations': {}
        }
        
        for i, operation in enumerate(operations):
            op_name = getattr(operation, '__name__', f'operation_{i}')
            
            try:
                # 预热
                for _ in range(min(10, iterations // 10)):
                    operation()
                
                # 同步（如果是CUDA）
                if TORCH_AVAILABLE and self.device.type == 'cuda':
                    torch.cuda.synchronize()
                
                # 计时
                start_time = time.perf_counter()
                for _ in range(iterations):
                    operation()
                
                if TORCH_AVAILABLE and self.device.type == 'cuda':
                    torch.cuda.synchronize()
                
                end_time = time.perf_counter()
                
                total_time = end_time - start_time
                avg_time = total_time / iterations
                
                results['operations'][op_name] = {
                    'total_time': total_time,
                    'avg_time': avg_time,
                    'ops_per_second': 1.0 / avg_time if avg_time > 0 else float('inf')
                }
                
            except Exception as e:
                results['operations'][op_name] = {
                    'error': str(e)
                }
                self._logger.error(f"基准测试操作 {op_name} 失败: {e}")
        
        return results
    
    def get_optimization_recommendations(self) -> List[str]:
        """获取优化建议
        
        Returns:
            List[str]: 优化建议列表
        """
        recommendations = []
        
        device_info = self.get_device_info()
        
        # 设备相关建议
        if device_info['device_type'] == 'cpu':
            if TORCH_AVAILABLE and torch.cuda.is_available():
                recommendations.append("考虑使用CUDA设备以提高性能")
        
        # 内存相关建议
        if 'memory_total' in device_info and 'memory_allocated' in device_info:
            memory_usage = device_info['memory_allocated'] / device_info['memory_total']
            if memory_usage > 0.9:
                recommendations.append("GPU内存使用率过高，考虑减少批次大小或清理缓存")
            elif memory_usage < 0.3:
                recommendations.append("GPU内存使用率较低，可以考虑增加批次大小")
        
        # 线程数建议
        import os
        cpu_count = os.cpu_count() or 4
        if self.num_workers < cpu_count // 2:
            recommendations.append(f"考虑增加工作线程数到 {cpu_count // 2} 或更多")
        elif self.num_workers > cpu_count * 2:
            recommendations.append(f"工作线程数可能过多，考虑减少到 {cpu_count} 左右")
        
        return recommendations