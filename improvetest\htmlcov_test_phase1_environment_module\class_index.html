<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>FinRL Crypto Test Coverage Report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>FinRL Crypto Test Coverage Report:
            <span class="pc_cov">13.53%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>b</kbd>
                        <kbd>p</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-19 02:04 +0900
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="branches" aria-sort="none" data-default-sort-order="descending" data-shortcut="b">branches<span class="arrows"></span></th>
                <th id="partial" aria-sort="none" data-default-sort-order="descending" data-shortcut="p">partial<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db___init___py.html">core\__init__.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t53">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t53"><data value='FileConfigLoader'>FileConfigLoader</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t84">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t84"><data value='EnvironmentConfigLoader'>EnvironmentConfigLoader</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t124">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t124"><data value='ConfigValidator'>ConfigValidator</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t170">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t170"><data value='ConfigSchema'>ConfigSchema</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t189">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html#t189"><data value='UnifiedConfigManager'>UnifiedConfigManager</data></a></td>
                <td>92</td>
                <td>92</td>
                <td>0</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 122">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html">core\config_manager.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_config_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t48">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t48"><data value='ServiceDescriptor'>ServiceDescriptor</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t78">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t78"><data value='CircularDependencyError'>CircularDependencyError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t86">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t86"><data value='ServiceNotFoundError'>ServiceNotFoundError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t103">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t103"><data value='ServiceScope'>ServiceScope</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t133">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html#t133"><data value='DIContainer'>DIContainer</data></a></td>
                <td>176</td>
                <td>176</td>
                <td>2</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 252">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html">core\dependency_injection.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_dependency_injection_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>107</td>
                <td>107</td>
                <td>2</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 115">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t58">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t58"><data value='DRLAgentConfigValidator'>DRLAgentConfigValidator</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t103">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t103"><data value='AgentFactory'>AgentFactory</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t142">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t142"><data value='ModelBuilder'>ModelBuilder</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t198">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t198"><data value='TrainingService'>TrainingService</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t247">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t247"><data value='PredictionService'>PredictionService</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t329">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t329"><data value='RefactoredDRLAgent'>RefactoredDRLAgent</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t424">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html#t424"><data value='DummyEnv'>RefactoredDRLAgent.get_model.DummyEnv</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html">core\drl_agent.py</a></td>
                <td class="name left"><a href="z_57760688d1f824db_drl_agent_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>96</td>
                <td>96</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 96">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ddb02fcb4060c71d___init___py.html">finrl_crypto\__init__.py</a></td>
                <td class="name left"><a href="z_ddb02fcb4060c71d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 17">76.47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be___init___py.html">finrl_crypto\agent\__init__.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">90.91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t17">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t17"><data value='A2CActor'>A2CActor</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t130">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t130"><data value='A2CCritic'>A2CCritic</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t168">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html#t168"><data value='A2CAgent'>A2CAgent</data></a></td>
                <td>121</td>
                <td>121</td>
                <td>0</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html">finrl_crypto\agent\a2c.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_a2c_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t17">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t17"><data value='BaseAgent'>BaseAgent</data></a></td>
                <td>58</td>
                <td>58</td>
                <td>43</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t300">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html#t300"><data value='ReplayBuffer'>ReplayBuffer</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html">finrl_crypto\agent\base.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>16</td>
                <td>20</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="26 54">48.15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t16">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t16"><data value='DQNNetwork'>DQNNetwork</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t60">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t60"><data value='DQNAgent'>DQNAgent</data></a></td>
                <td>74</td>
                <td>74</td>
                <td>0</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 88">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t342">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html#t342"><data value='DuelingDQNNetwork'>DuelingDQNNetwork</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html">finrl_crypto\agent\dqn.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_dqn_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t16">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html#t16"><data value='AgentFactory'>AgentFactory</data></a></td>
                <td>55</td>
                <td>55</td>
                <td>0</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 93">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html">finrl_crypto\agent\factory.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_factory_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 36">91.67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t17">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t17"><data value='PPOActor'>PPOActor</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t124">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t124"><data value='PPOCritic'>PPOCritic</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t162">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t162"><data value='PPOBuffer'>PPOBuffer</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t256">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html#t256"><data value='PPOAgent'>PPOAgent</data></a></td>
                <td>122</td>
                <td>122</td>
                <td>0</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 146">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html">finrl_crypto\agent\ppo.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_ppo_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t17">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t17"><data value='SACGaussianActor'>SACGaussianActor</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t135">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t135"><data value='SACCritic'>SACCritic</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t177">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html#t177"><data value='SACAgent'>SACAgent</data></a></td>
                <td>133</td>
                <td>133</td>
                <td>0</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 157">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html">finrl_crypto\agent\sac.py</a></td>
                <td class="name left"><a href="z_4ef26b22018bd3be_sac_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05___init___py.html">finrl_crypto\environment\__init__.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">88.89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t38">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html#t38"><data value='BaseEnvironment'>BaseEnvironment</data></a></td>
                <td>114</td>
                <td>40</td>
                <td>33</td>
                <td>42</td>
                <td>16</td>
                <td class="right" data-ratio="90 156">57.69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html">finrl_crypto\environment\base.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>1</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 41">97.56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t38">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html#t38"><data value='CryptoTradingEnvironment'>CryptoTradingEnvironment</data></a></td>
                <td>175</td>
                <td>35</td>
                <td>0</td>
                <td>64</td>
                <td>23</td>
                <td class="right" data-ratio="175 239">73.22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html">finrl_crypto\environment\crypto_trading.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_crypto_trading_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="43 44">97.73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t15">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html#t15"><data value='EnvironmentFactory'>EnvironmentFactory</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 127">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html">finrl_crypto\environment\factory.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_factory_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 32">90.62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t38">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html#t38"><data value='PortfolioEnvironment'>PortfolioEnvironment</data></a></td>
                <td>199</td>
                <td>168</td>
                <td>0</td>
                <td>76</td>
                <td>5</td>
                <td class="right" data-ratio="36 275">13.09%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html">finrl_crypto\environment\portfolio.py</a></td>
                <td class="name left"><a href="z_58676b528f8eed05_portfolio_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 41">97.56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a___init___py.html">finrl_crypto\training\__init__.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t58">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t58"><data value='EvaluationMetrics'>EvaluationMetrics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t97">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html#t97"><data value='BaseTrainer'>BaseTrainer</data></a></td>
                <td>103</td>
                <td>103</td>
                <td>22</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 141">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html">finrl_crypto\training\base.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>69</td>
                <td>69</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 69">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t17">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html#t17"><data value='TrainerFactory'>TrainerFactory</data></a></td>
                <td>63</td>
                <td>63</td>
                <td>0</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 95">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html">finrl_crypto\training\factory.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_factory_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t17">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html#t17"><data value='MultiAssetTrainer'>MultiAssetTrainer</data></a></td>
                <td>230</td>
                <td>230</td>
                <td>0</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="0 320">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html">finrl_crypto\training\multi_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_multi_asset_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t17">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html#t17"><data value='PortfolioTrainer'>PortfolioTrainer</data></a></td>
                <td>268</td>
                <td>268</td>
                <td>0</td>
                <td>98</td>
                <td>0</td>
                <td class="right" data-ratio="0 366">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html">finrl_crypto\training\portfolio.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_portfolio_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t17">finrl_crypto\training\single_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html#t17"><data value='SingleAssetTrainer'>SingleAssetTrainer</data></a></td>
                <td>141</td>
                <td>141</td>
                <td>0</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 189">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html">finrl_crypto\training\single_asset.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_single_asset_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0.00%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html">finrl_crypto\training\utils.py</a></td>
                <td class="name left"><a href="z_65d867bacf62c25a_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>395</td>
                <td>395</td>
                <td>0</td>
                <td>160</td>
                <td>0</td>
                <td class="right" data-ratio="0 555">0.00%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>3831</td>
                <td>3228</td>
                <td>155</td>
                <td>1040</td>
                <td>44</td>
                <td class="right" data-ratio="659 4871">13.53%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
        <p>8 empty classes skipped.</p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-19 02:04 +0900
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
