"""插件管理器模块

负责插件的注册、加载、卸载和生命周期管理。
"""

import logging
import threading
from typing import Dict, List, Optional, Type, Any
from pathlib import Path

from .plugin_interface import IPlugin, PluginMetadata, PluginType
from .plugin_loader import PluginLoader


class PluginManager:
    """插件管理器
    
    负责插件的注册、加载、卸载和生命周期管理。
    """
    
    def __init__(self):
        self._registered_plugins: Dict[str, PluginMetadata] = {}
        self._loaded_plugins: Dict[str, IPlugin] = {}
        self._plugins: Dict[str, IPlugin] = {}
        self._plugin_metadata: Dict[str, PluginMetadata] = {}
        self._plugin_types: Dict[PluginType, List[str]] = {}
        self._plugin_configs: Dict[str, Dict[str, Any]] = {}
        self._logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        self._loader = PluginLoader()
        
        # 为了兼容测试，提供plugins属性
        self.plugins = self._loaded_plugins
    
    def register_plugin(self, plugin_class: Type[IPlugin], config: Optional[Dict[str, Any]] = None) -> bool:
        """注册插件类
        
        Args:
            plugin_class: 插件类
            config: 插件配置
            
        Returns:
            bool: 注册是否成功
        """
        try:
            with self._lock:
                # 创建插件实例
                plugin_instance = plugin_class(config)
                metadata = plugin_instance.metadata
                
                # 检查插件是否已存在
                if metadata.name in self._plugins:
                    self._logger.warning(f"插件 {metadata.name} 已存在，将被替换")
                
                # 验证配置
                if config and hasattr(plugin_instance, 'validate_config') and not plugin_instance.validate_config(config):
                    self._logger.error(f"插件 {metadata.name} 配置验证失败")
                    return False
                
                # 注册插件
                self._plugins[metadata.name] = plugin_instance
                self._loaded_plugins[metadata.name] = plugin_instance  # 同步到_loaded_plugins
                self._plugin_metadata[metadata.name] = metadata
                
                # 按类型分类
                if metadata.plugin_type not in self._plugin_types:
                    self._plugin_types[metadata.plugin_type] = []
                if metadata.name not in self._plugin_types[metadata.plugin_type]:
                    self._plugin_types[metadata.plugin_type].append(metadata.name)
                
                # 激活插件
                plugin_instance.activate()
                
                self._logger.info(f"插件 {metadata.name} 注册成功")
                return True
                
        except Exception as e:
            self._logger.error(f"注册插件失败: {e}")
            return False
    
    def load_plugin(self, plugin_path: str, config: Optional[Dict[str, Any]] = None) -> bool:
        """加载插件（兼容测试接口）
        
        Args:
            plugin_path: 插件文件路径
            config: 插件配置
            
        Returns:
            bool: 加载是否成功
        """
        return self.load_plugin_from_file(Path(plugin_path), config)
    
    def load_plugin_from_file(self, plugin_path: Path, config: Optional[Dict[str, Any]] = None) -> bool:
        """从文件加载插件
        
        Args:
            plugin_path: 插件文件路径
            config: 插件配置
            
        Returns:
            bool: 加载是否成功
        """
        try:
            plugin_class = self._loader.load_from_file(plugin_path)
            return self.register_plugin(plugin_class, config)
            
        except Exception as e:
            self._logger.error(f"从文件加载插件失败: {e}")
            return False
    
    def load_plugins_from_directory(self, directory: Path, config: Optional[Dict[str, Dict[str, Any]]] = None) -> int:
        """从目录加载所有插件
        
        Args:
            directory: 插件目录路径
            config: 插件配置字典，键为插件名
            
        Returns:
            int: 成功加载的插件数量
        """
        loaded_count = 0
        config = config or {}
        
        try:
            plugin_classes = self._loader.load_from_directory(directory)
            for plugin_class in plugin_classes:
                # 获取插件名用于配置
                temp_instance = plugin_class()
                plugin_name = temp_instance.metadata.name
                plugin_config = config.get(plugin_name, {})
                
                if self.register_plugin(plugin_class, plugin_config):
                    loaded_count += 1
                    
        except Exception as e:
            self._logger.error(f"从目录加载插件失败: {e}")
        
        return loaded_count
    
    def unload_plugin(self, plugin_name: str) -> bool:
        """卸载插件
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            bool: 卸载是否成功
        """
        try:
            with self._lock:
                if plugin_name not in self._plugins:
                    self._logger.warning(f"插件 {plugin_name} 不存在")
                    return False
                
                plugin = self._plugins[plugin_name]
                metadata = self._plugin_metadata[plugin_name]
                
                # 停用并清理插件
                if plugin.is_active:
                    plugin.deactivate()
                
                plugin.cleanup()
                
                # 从管理器中移除
                del self._plugins[plugin_name]
                del self._plugin_metadata[plugin_name]
                
                # 从类型分类中移除
                if plugin_name in self._plugin_types[metadata.plugin_type]:
                    self._plugin_types[metadata.plugin_type].remove(plugin_name)
                
                self._logger.info(f"插件 {plugin_name} 卸载成功")
                return True
                
        except Exception as e:
            self._logger.error(f"卸载插件失败: {e}")
            return False
    
    def get_plugin(self, plugin_name: str) -> Optional[IPlugin]:
        """获取插件实例
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            Optional[IPlugin]: 插件实例，如果不存在则返回None
        """
        return self._plugins.get(plugin_name)
    
    def get_plugins_by_type(self, plugin_type: PluginType) -> List[IPlugin]:
        """按类型获取插件列表
        
        Args:
            plugin_type: 插件类型
            
        Returns:
            List[IPlugin]: 插件实例列表
        """
        plugin_names = self._plugin_types.get(plugin_type, [])
        return [self._plugins[name] for name in plugin_names if name in self._plugins]
    
    def list_plugins(self) -> List[PluginMetadata]:
        """列出所有插件的元数据
        
        Returns:
            List[PluginMetadata]: 插件元数据列表
        """
        return list(self._plugin_metadata.values())
    
    def activate_plugin(self, plugin_name: str) -> bool:
        """激活插件
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            bool: 激活是否成功
        """
        plugin = self.get_plugin(plugin_name)
        if plugin:
            return plugin.activate()
        return False
    
    def deactivate_plugin(self, plugin_name: str) -> bool:
        """停用插件
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            bool: 停用是否成功
        """
        plugin = self.get_plugin(plugin_name)
        if plugin:
            return plugin.deactivate()
        return False
    
    def execute_plugin(self, plugin_name: str, context: Dict[str, Any]) -> Any:
        """执行插件
        
        Args:
            plugin_name: 插件名称
            context: 执行上下文
            
        Returns:
            Any: 插件执行结果
        """
        with self._lock:
            if plugin_name not in self._loaded_plugins:
                raise ValueError(f"插件 {plugin_name} 不存在")
            
            plugin = self._loaded_plugins[plugin_name]
            
            try:
                result = plugin.execute(context)
                self._logger.info(f"插件 {plugin_name} 执行成功")
                return result
                
            except Exception as e:
                self._logger.error(f"插件 {plugin_name} 执行失败: {e}")
                raise
    
    def unload_plugin(self, plugin_name: str) -> bool:
        """卸载插件
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            bool: 卸载是否成功
        """
        with self._lock:
            if plugin_name not in self._loaded_plugins:
                self._logger.warning(f"插件未加载，无法卸载: {plugin_name}")
                return False
            
            try:
                plugin = self._loaded_plugins[plugin_name]
                plugin.cleanup()
                del self._loaded_plugins[plugin_name]
                self._logger.info(f"插件已卸载: {plugin_name}")
                return True
            except Exception as e:
                self._logger.error(f"卸载插件失败: {plugin_name}, 错误: {e}")
                return False
    
    def get_plugin_status(self, plugin_name: str) -> Optional[Dict[str, Any]]:
        """获取插件状态
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            Optional[Dict[str, Any]]: 插件状态信息
        """
        plugin = self.get_plugin(plugin_name)
        if plugin:
            metadata = self._plugin_metadata[plugin_name]
            return {
                'name': metadata.name,
                'version': metadata.version,
                'type': metadata.plugin_type.value,
                'is_initialized': plugin.is_initialized,
                'is_active': plugin.is_active,
                'description': metadata.description,
                'author': metadata.author
            }
        return None
    
    def get_plugin_stats(self) -> Dict[str, Any]:
        """获取插件统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            stats = {
                'total_registered': len(self._registered_plugins),
                'total_loaded': len(self._loaded_plugins),
                'plugins_by_type': {},
                'plugin_status': {}
            }
            
            # 按类型统计
            for metadata in self._registered_plugins.values():
                plugin_type = metadata.plugin_type.value
                if plugin_type not in stats['plugins_by_type']:
                    stats['plugins_by_type'][plugin_type] = 0
                stats['plugins_by_type'][plugin_type] += 1
            
            # 插件状态
            for name, plugin in self._loaded_plugins.items():
                stats['plugin_status'][name] = {
                    'loaded': True,
                    'metadata': self._registered_plugins.get(name, None)
                }
            
            return stats
    
    def shutdown(self) -> bool:
        """关闭插件管理器，清理所有插件
        
        Returns:
            bool: 关闭是否成功
        """
        try:
            with self._lock:
                plugin_names = list(self._plugins.keys())
                for plugin_name in plugin_names:
                    self.unload_plugin(plugin_name)
                
                self._logger.info("插件管理器关闭成功")
                return True
                
        except Exception as e:
            self._logger.error(f"关闭插件管理器失败: {e}")
            return False