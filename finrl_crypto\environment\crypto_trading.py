"""加密货币交易环境模块

实现专门用于加密货币交易的强化学习环境。
"""

try:
    import gymnasium as gym
except ImportError:
    try:
        import gym
    except ImportError:
        # 如果都没有安装，创建一个简单的替代
        class MockSpace:
            def __init__(self, *args, **kwargs):
                pass
                
        class MockSpaces:
            Box = MockSpace
            Discrete = MockSpace
            
        class MockEnv:
            def __init__(self):
                pass
                
        class MockGym:
            Env = MockEnv
            spaces = MockSpaces()
            
        gym = MockGym()
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
import logging

from .base import BaseEnvironment


class CryptoTradingEnvironment(BaseEnvironment):
    """加密货币交易环境
    
    专门为加密货币交易设计的强化学习环境。
    支持多资产交易、动态仓位调整和风险管理。
    """
    
    def __init__(self,
                 data: pd.DataFrame,
                 initial_amount: float = 10000,
                 transaction_cost_pct: float = 0.001,
                 reward_scaling: float = 1e-4,
                 state_space: Optional[int] = None,
                 action_space: Optional[int] = None,
                 tech_indicator_list: Optional[List[str]] = None,
                 turbulence_threshold: Optional[float] = None,
                 risk_indicator_col: str = 'turbulence',
                 make_plots: bool = False,
                 print_verbosity: int = 10,
                 day: int = 0,
                 initial: bool = True,
                 previous_state: Optional[List] = None,
                 model_name: str = '',
                 mode: str = '',
                 iteration: str = '',
                 lookback: int = 1,
                 normalize_features: bool = True,
                 enable_short: bool = False,
                 max_position: float = 1.0,
                 min_position: float = 0.0,
                 **kwargs):
        """初始化加密货币交易环境
        
        Args:
            data: 交易数据
            initial_amount: 初始资金
            transaction_cost_pct: 交易成本百分比
            reward_scaling: 奖励缩放因子
            state_space: 状态空间维度
            action_space: 动作空间维度
            tech_indicator_list: 技术指标列表
            turbulence_threshold: 波动阈值
            risk_indicator_col: 风险指标列名
            make_plots: 是否生成图表
            print_verbosity: 打印详细程度
            day: 当前交易日
            initial: 是否为初始状态
            previous_state: 前一状态
            model_name: 模型名称
            mode: 模式
            iteration: 迭代次数
            lookback: 历史回看窗口
            normalize_features: 是否标准化特征
            enable_short: 是否允许做空
            max_position: 最大仓位
            min_position: 最小仓位
            **kwargs: 其他参数
        """
        # 加密货币特定参数
        self.lookback = lookback
        self.normalize_features = normalize_features
        self.enable_short = enable_short
        self.max_position = max_position
        self.min_position = min_position if not enable_short else -max_position
        
        # 调用父类初始化
        super().__init__(
            data=data,
            initial_amount=initial_amount,
            transaction_cost_pct=transaction_cost_pct,
            reward_scaling=reward_scaling,
            state_space=state_space,
            action_space=action_space,
            tech_indicator_list=tech_indicator_list,
            turbulence_threshold=turbulence_threshold,
            risk_indicator_col=risk_indicator_col,
            make_plots=make_plots,
            print_verbosity=print_verbosity,
            day=day,
            initial=initial,
            previous_state=previous_state,
            model_name=model_name,
            mode=mode,
            iteration=iteration,
            **kwargs
        )
        
        # 初始化持仓和现金
        self.cash = self.initial_amount
        self.holdings = np.zeros(self.stock_dim)  # 每个资产的持有数量
        self.positions = np.zeros(self.stock_dim)  # 每个资产的仓位权重
        
        # 价格历史（用于计算收益率等）
        self.price_history = []
        
        # 特征标准化参数
        if self.normalize_features:
            self._calculate_normalization_params()
    
    def _setup_spaces(self, state_space: Optional[int], action_space: Optional[int]):
        """设置动作和观察空间"""
        # 计算状态空间维度
        if state_space is None:
            # 基础特征：价格、成交量等
            basic_features = ['open', 'high', 'low', 'close', 'volume']
            basic_dim = len([col for col in basic_features if col in self.data.columns])
            
            # 技术指标
            tech_dim = len(self.tech_indicator_list)
            
            # 投资组合状态：现金比例 + 各资产持仓比例
            portfolio_dim = 1 + self.stock_dim
            
            # 历史回看
            feature_dim = (basic_dim + tech_dim) * self.stock_dim
            state_dim = feature_dim * self.lookback + portfolio_dim
        else:
            state_dim = state_space
        
        # 设置观察空间
        self.observation_space = gym.spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(state_dim,),
            dtype=np.float32
        )
        
        # 设置动作空间
        if action_space is None:
            # 动作为每个资产的目标仓位权重
            action_dim = self.stock_dim
        else:
            action_dim = action_space
        
        if self.enable_short:
            # 允许做空：动作范围为[-max_position, max_position]
            self.action_space = gym.spaces.Box(
                low=self.min_position,
                high=self.max_position,
                shape=(action_dim,),
                dtype=np.float32
            )
        else:
            # 只允许做多：动作范围为[0, max_position]
            self.action_space = gym.spaces.Box(
                low=0,
                high=self.max_position,
                shape=(action_dim,),
                dtype=np.float32
            )
    
    def _calculate_normalization_params(self):
        """计算特征标准化参数"""
        # 选择数值列进行标准化
        numeric_cols = self.data.select_dtypes(include=[np.number]).columns
        exclude_cols = ['time', 'tic']
        feature_cols = [col for col in numeric_cols if col not in exclude_cols]
        
        self.feature_means = self.data[feature_cols].mean()
        self.feature_stds = self.data[feature_cols].std()
        
        # 避免除零
        self.feature_stds = self.feature_stds.replace(0, 1)
    
    def _get_state(self) -> np.ndarray:
        """获取当前状态"""
        # 获取历史数据
        start_day = max(0, self.day - self.lookback + 1)
        end_day = self.day + 1
        
        state_features = []
        
        # 获取历史特征
        for day_offset in range(start_day, end_day):
            if day_offset >= len(self.unique_trade_date):
                # 如果超出数据范围，使用最后一天的数据
                day_offset = len(self.unique_trade_date) - 1
            
            day_data = self._get_data_for_day(day_offset)
            day_features = self._extract_features_from_data(day_data)
            state_features.extend(day_features)
        
        # 如果历史数据不足，用第一天的数据填充
        while len(state_features) < self._get_expected_feature_length():
            first_day_data = self._get_data_for_day(0)
            first_day_features = self._extract_features_from_data(first_day_data)
            state_features = first_day_features + state_features
        
        # 添加投资组合状态
        portfolio_state = self._get_portfolio_state()
        state_features.extend(portfolio_state)
        
        return np.array(state_features, dtype=np.float32)
    
    def _get_data_for_day(self, day: int) -> pd.DataFrame:
        """获取指定日期的数据"""
        if day >= len(self.unique_trade_date):
            return pd.DataFrame()
        
        target_date = self.unique_trade_date[day]
        
        if 'time' in self.data.columns:
            day_data = self.data[self.data['time'] == target_date]
        else:
            day_data = self.data.iloc[day:day+1]
        
        return day_data.reset_index(drop=True)
    
    def _extract_features_from_data(self, data: pd.DataFrame) -> List[float]:
        """从数据中提取特征"""
        if data.empty:
            return [0.0] * self._get_single_day_feature_length()
        
        features = []
        
        # 按股票顺序提取特征
        for tic in self.tic_list:
            tic_data = data[data['tic'] == tic] if 'tic' in data.columns else data
            
            if tic_data.empty:
                # 如果没有该股票的数据，用零填充
                features.extend([0.0] * self._get_single_asset_feature_length())
                continue
            
            # 基础价格特征
            basic_features = ['open', 'high', 'low', 'close', 'volume']
            for feature in basic_features:
                if feature in tic_data.columns:
                    value = tic_data[feature].iloc[0]
                    if self.normalize_features and feature in self.feature_means:
                        value = (value - self.feature_means[feature]) / self.feature_stds[feature]
                    features.append(float(value))
                else:
                    features.append(0.0)
            
            # 技术指标
            for indicator in self.tech_indicator_list:
                if indicator in tic_data.columns:
                    value = tic_data[indicator].iloc[0]
                    if self.normalize_features and indicator in self.feature_means:
                        value = (value - self.feature_means[indicator]) / self.feature_stds[indicator]
                    features.append(float(value))
                else:
                    features.append(0.0)
        
        return features
    
    def _get_portfolio_state(self) -> List[float]:
        """获取投资组合状态"""
        total_value = self._get_total_portfolio_value()
        
        # 现金比例
        cash_ratio = self.cash / total_value if total_value > 0 else 1.0
        
        # 各资产持仓比例
        asset_ratios = []
        current_prices = self._get_current_prices()
        
        for i in range(self.stock_dim):
            if total_value > 0 and current_prices[i] > 0:
                asset_value = self.holdings[i] * current_prices[i]
                asset_ratio = asset_value / total_value
            else:
                asset_ratio = 0.0
            asset_ratios.append(asset_ratio)
        
        return [cash_ratio] + asset_ratios
    
    def _get_expected_feature_length(self) -> int:
        """获取期望的特征长度"""
        return self._get_single_day_feature_length() * self.lookback
    
    def _get_single_day_feature_length(self) -> int:
        """获取单日特征长度"""
        return self._get_single_asset_feature_length() * self.stock_dim
    
    def _get_single_asset_feature_length(self) -> int:
        """获取单个资产的特征长度"""
        basic_features = ['open', 'high', 'low', 'close', 'volume']
        basic_count = len([col for col in basic_features if col in self.data.columns])
        tech_count = len(self.tech_indicator_list)
        return basic_count + tech_count
    
    def _calculate_reward(self, action: np.ndarray) -> float:
        """计算奖励"""
        # 计算投资组合价值变化
        current_total_value = self._get_total_portfolio_value()
        
        if len(self.asset_memory) > 1:
            previous_total_value = self.asset_memory[-1]
            portfolio_return = (current_total_value - previous_total_value) / previous_total_value
        else:
            portfolio_return = 0.0
        
        # 基础奖励：投资组合收益率
        reward = portfolio_return
        
        # 风险调整
        if self.turbulence_threshold is not None and self.turbulence > self.turbulence_threshold:
            # 高波动期间降低奖励
            reward *= 0.5
        
        # 交易成本惩罚
        transaction_cost = self._calculate_transaction_cost(action)
        reward -= transaction_cost
        
        # 应用奖励缩放
        reward *= self.reward_scaling
        
        return reward
    
    def _execute_action(self, action: np.ndarray) -> Dict[str, Any]:
        """执行动作"""
        # 标准化动作（确保权重和为1）
        action = np.array(action)
        if not self.enable_short:
            action = np.clip(action, 0, self.max_position)
        else:
            action = np.clip(action, self.min_position, self.max_position)
        
        # 如果不允许做空，确保权重和为1
        if not self.enable_short and np.sum(action) > 0:
            action = action / np.sum(action)
        
        # 获取当前价格
        current_prices = self._get_current_prices()
        current_total_value = self._get_total_portfolio_value()
        
        # 计算目标持仓
        target_values = action * current_total_value
        target_holdings = np.array([
            target_values[i] / current_prices[i] if current_prices[i] > 0 else 0
            for i in range(self.stock_dim)
        ])
        
        # 计算交易量和成本
        trades = target_holdings - self.holdings
        transaction_cost = self._calculate_transaction_cost_from_trades(trades, current_prices)
        
        # 执行交易
        self.holdings = target_holdings.copy()
        self.positions = action.copy()
        self.cash = current_total_value - np.sum(target_values) - transaction_cost
        
        # 更新记录
        new_total_value = self._get_total_portfolio_value()
        self.asset_memory.append(new_total_value)
        
        # 计算收益率
        if len(self.asset_memory) > 1:
            portfolio_return = (new_total_value - self.asset_memory[-2]) / self.asset_memory[-2]
        else:
            portfolio_return = 0.0
        
        self.portfolio_return_memory.append(portfolio_return)
        self.date_memory.append(self._get_date())
        
        # 更新波动指标
        self._update_turbulence()
        
        return {
            'trades': trades,
            'transaction_cost': transaction_cost,
            'new_total_value': new_total_value,
            'portfolio_return': portfolio_return,
            'positions': self.positions.copy(),
            'holdings': self.holdings.copy(),
            'cash': self.cash,
            'turbulence': self.turbulence
        }
    
    def _get_current_prices(self) -> np.ndarray:
        """获取当前价格"""
        current_data = self._get_current_data()
        prices = np.zeros(self.stock_dim)
        
        if current_data.empty:
            # 如果没有当前数据，使用最后已知价格
            if self.price_history:
                last_prices = self.price_history[-1]
                # 确保返回的是正确维度的数组
                if isinstance(last_prices, (int, float)):
                    return np.full(self.stock_dim, last_prices)
                else:
                    return np.array(last_prices)
            else:
                return np.ones(self.stock_dim)  # 默认价格为1
        
        for i, tic in enumerate(self.tic_list):
            if 'tic' in current_data.columns:
                tic_data = current_data[current_data['tic'] == tic]
            else:
                tic_data = current_data
            
            if not tic_data.empty and 'close' in tic_data.columns:
                prices[i] = tic_data['close'].iloc[0]
            else:
                prices[i] = 1.0  # 默认价格
        
        # 记录价格历史
        self.price_history.append(prices.copy())
        
        return prices
    
    def _get_total_portfolio_value(self) -> float:
        """获取投资组合总价值"""
        current_prices = self._get_current_prices()
        asset_values = self.holdings * current_prices
        return self.cash + np.sum(asset_values)
    
    def _calculate_transaction_cost(self, action: np.ndarray) -> float:
        """计算交易成本"""
        current_prices = self._get_current_prices()
        current_total_value = self._get_total_portfolio_value()
        
        # 计算目标持仓
        target_values = action * current_total_value
        target_holdings = np.array([
            target_values[i] / current_prices[i] if current_prices[i] > 0 else 0
            for i in range(self.stock_dim)
        ])
        
        # 计算交易量
        trades = np.abs(target_holdings - self.holdings)
        
        return self._calculate_transaction_cost_from_trades(trades, current_prices)
    
    def _calculate_transaction_cost_from_trades(self, trades: np.ndarray, prices: np.ndarray) -> float:
        """根据交易量计算交易成本"""
        trade_values = trades * prices
        return np.sum(trade_values) * self.transaction_cost_pct
    
    def _reset_specific_state(self):
        """重置特定于子类的状态"""
        self.cash = self.initial_amount
        self.holdings = np.zeros(self.stock_dim)
        self.positions = np.zeros(self.stock_dim)
        self.price_history = []
        self.turbulence = 0
    
    def get_portfolio_allocation(self) -> Dict[str, float]:
        """获取当前投资组合配置
        
        Returns:
            投资组合配置字典
        """
        total_value = self._get_total_portfolio_value()
        allocation = {'cash': self.cash / total_value if total_value > 0 else 1.0}
        
        current_prices = self._get_current_prices()
        for i, tic in enumerate(self.tic_list):
            asset_value = self.holdings[i] * current_prices[i]
            allocation[tic] = asset_value / total_value if total_value > 0 else 0.0
        
        return allocation
    
    def get_detailed_info(self) -> Dict[str, Any]:
        """获取详细信息
        
        Returns:
            详细信息字典
        """
        return {
            'day': self.day,
            'date': self._get_date(),
            'total_value': self._get_total_portfolio_value(),
            'cash': self.cash,
            'holdings': dict(zip(self.tic_list, self.holdings)),
            'positions': dict(zip(self.tic_list, self.positions)),
            'allocation': self.get_portfolio_allocation(),
            'turbulence': self.turbulence,
            'performance': self.get_performance_metrics()
        }