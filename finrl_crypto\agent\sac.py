"""Soft Actor-Critic (SAC) 智能体实现

实现SAC算法用于连续动作空间的强化学习。
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import Normal
from typing import Dict, List, Tuple, Any, Optional
import random

from .base import BaseAgent, ReplayBuffer, create_mlp


class SACGaussianActor(nn.Module):
    """SAC高斯演员网络"""
    
    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 hidden_dims: List[int] = [256, 256],
                 activation: str = 'relu',
                 dropout: float = 0.0,
                 log_std_min: float = -20,
                 log_std_max: float = 2):
        """初始化演员网络
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            hidden_dims: 隐藏层维度
            activation: 激活函数
            dropout: Dropout概率
            log_std_min: 对数标准差最小值
            log_std_max: 对数标准差最大值
        """
        super().__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.log_std_min = log_std_min
        self.log_std_max = log_std_max
        
        # 共享网络
        self.shared_net = create_mlp(
            input_dim=state_dim,
            output_dim=hidden_dims[-1],
            hidden_dims=hidden_dims[:-1],
            activation=activation,
            dropout=dropout
        )
        
        # 均值和对数标准差层
        self.mean_layer = nn.Linear(hidden_dims[-1], action_dim)
        self.log_std_layer = nn.Linear(hidden_dims[-1], action_dim)
    
    def forward(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """前向传播
        
        Args:
            state: 状态张量
            
        Returns:
            (均值, 对数标准差)
        """
        features = self.shared_net(state)
        mean = self.mean_layer(features)
        log_std = self.log_std_layer(features)
        log_std = torch.clamp(log_std, self.log_std_min, self.log_std_max)
        return mean, log_std
    
    def sample(self, state: torch.Tensor, reparameterize: bool = True) -> Tuple[torch.Tensor, torch.Tensor]:
        """采样动作
        
        Args:
            state: 状态张量
            reparameterize: 是否使用重参数化技巧
            
        Returns:
            (动作, 对数概率)
        """
        mean, log_std = self.forward(state)
        std = log_std.exp()
        
        if reparameterize:
            # 重参数化采样
            normal = Normal(0, 1)
            z = normal.sample(mean.shape).to(mean.device)
            action = mean + std * z
        else:
            # 直接采样
            normal = Normal(mean, std)
            action = normal.sample()
        
        # 应用tanh激活并计算对数概率
        action_tanh = torch.tanh(action)
        
        # 计算对数概率
        normal = Normal(mean, std)
        log_prob = normal.log_prob(action).sum(dim=-1)
        
        # 修正tanh变换的雅可比行列式
        log_prob -= torch.log(1 - action_tanh.pow(2) + 1e-6).sum(dim=-1)
        
        return action_tanh, log_prob
    
    def get_log_prob(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """获取给定动作的对数概率
        
        Args:
            state: 状态张量
            action: 动作张量
            
        Returns:
            对数概率
        """
        mean, log_std = self.forward(state)
        std = log_std.exp()
        
        # 反向tanh变换
        action_pre_tanh = torch.atanh(torch.clamp(action, -0.999999, 0.999999))
        
        # 计算对数概率
        normal = Normal(mean, std)
        log_prob = normal.log_prob(action_pre_tanh).sum(dim=-1)
        
        # 修正tanh变换的雅可比行列式
        log_prob -= torch.log(1 - action.pow(2) + 1e-6).sum(dim=-1)
        
        return log_prob


class SACCritic(nn.Module):
    """SAC评论家网络（Q网络）"""
    
    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 hidden_dims: List[int] = [256, 256],
                 activation: str = 'relu',
                 dropout: float = 0.0):
        """初始化评论家网络
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            hidden_dims: 隐藏层维度
            activation: 激活函数
            dropout: Dropout概率
        """
        super().__init__()
        
        self.q_net = create_mlp(
            input_dim=state_dim + action_dim,
            output_dim=1,
            hidden_dims=hidden_dims,
            activation=activation,
            dropout=dropout
        )
    
    def forward(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """前向传播
        
        Args:
            state: 状态张量
            action: 动作张量
            
        Returns:
            Q值
        """
        x = torch.cat([state, action], dim=-1)
        return self.q_net(x)


class SACAgent(BaseAgent):
    """SAC智能体
    
    实现Soft Actor-Critic算法，适用于连续动作空间。
    """
    
    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 learning_rate: float = 3e-4,
                 gamma: float = 0.99,
                 tau: float = 0.005,
                 alpha: float = 0.2,
                 automatic_entropy_tuning: bool = True,
                 target_entropy: Optional[float] = None,
                 buffer_size: int = 1000000,
                 batch_size: int = 256,
                 hidden_dims: List[int] = [256, 256],
                 activation: str = 'relu',
                 dropout: float = 0.0,
                 log_std_min: float = -20,
                 log_std_max: float = 2,
                 device: str = 'auto',
                 **kwargs):
        """初始化SAC智能体
        
        Args:
            state_dim: 状态空间维度
            action_dim: 动作空间维度
            learning_rate: 学习率
            gamma: 折扣因子
            tau: 软更新参数
            alpha: 熵正则化系数
            automatic_entropy_tuning: 是否自动调节熵系数
            target_entropy: 目标熵值
            buffer_size: 经验回放缓冲区大小
            batch_size: 批次大小
            hidden_dims: 隐藏层维度
            activation: 激活函数
            dropout: Dropout概率
            log_std_min: 对数标准差最小值
            log_std_max: 对数标准差最大值
            device: 计算设备
            **kwargs: 其他参数
        """
        # SAC特定参数
        self.gamma = gamma
        self.tau = tau
        self.alpha = alpha
        self.automatic_entropy_tuning = automatic_entropy_tuning
        self.batch_size = batch_size
        self.hidden_dims = hidden_dims
        self.activation = activation
        self.dropout = dropout
        self.log_std_min = log_std_min
        self.log_std_max = log_std_max
        
        # 目标熵
        if target_entropy is None:
            self.target_entropy = -action_dim
        else:
            self.target_entropy = target_entropy
        
        # 调用父类初始化
        super().__init__(
            state_dim=state_dim,
            action_dim=action_dim,
            learning_rate=learning_rate,
            device=device,
            **kwargs
        )
        
        # 经验回放缓冲区
        self.replay_buffer = ReplayBuffer(
            capacity=buffer_size,
            state_dim=state_dim,
            action_dim=action_dim
        )
        
        # 学习统计
        self.actor_losses = []
        self.critic1_losses = []
        self.critic2_losses = []
        self.alpha_losses = []
        self.alpha_values = []
    
    def _build_networks(self):
        """构建神经网络"""
        # 演员网络
        self.actor = SACGaussianActor(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dims=self.hidden_dims,
            activation=self.activation,
            dropout=self.dropout,
            log_std_min=self.log_std_min,
            log_std_max=self.log_std_max
        ).to(self.device)
        
        # 双Q网络
        self.critic1 = SACCritic(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dims=self.hidden_dims,
            activation=self.activation,
            dropout=self.dropout
        ).to(self.device)
        
        self.critic2 = SACCritic(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dims=self.hidden_dims,
            activation=self.activation,
            dropout=self.dropout
        ).to(self.device)
        
        # 目标Q网络
        self.target_critic1 = SACCritic(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dims=self.hidden_dims,
            activation=self.activation,
            dropout=self.dropout
        ).to(self.device)
        
        self.target_critic2 = SACCritic(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dims=self.hidden_dims,
            activation=self.activation,
            dropout=self.dropout
        ).to(self.device)
        
        # 初始化目标网络
        self.target_critic1.load_state_dict(self.critic1.state_dict())
        self.target_critic2.load_state_dict(self.critic2.state_dict())
        
        # 熵系数
        if self.automatic_entropy_tuning:
            self.log_alpha = torch.zeros(1, requires_grad=True, device=self.device)
            self.alpha = self.log_alpha.exp()
    
    def _build_optimizers(self):
        """构建优化器"""
        self.actor_optimizer = torch.optim.Adam(
            self.actor.parameters(),
            lr=self.learning_rate
        )
        
        self.critic1_optimizer = torch.optim.Adam(
            self.critic1.parameters(),
            lr=self.learning_rate
        )
        
        self.critic2_optimizer = torch.optim.Adam(
            self.critic2.parameters(),
            lr=self.learning_rate
        )
        
        if self.automatic_entropy_tuning:
            self.alpha_optimizer = torch.optim.Adam(
                [self.log_alpha],
                lr=self.learning_rate
            )
    
    def act(self, state: np.ndarray, training: bool = True) -> np.ndarray:
        """选择动作
        
        Args:
            state: 当前状态
            training: 是否为训练模式
            
        Returns:
            选择的动作
        """
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            
            if training:
                action, _ = self.actor.sample(state_tensor)
            else:
                mean, _ = self.actor(state_tensor)
                action = torch.tanh(mean)
            
            return action.squeeze(0).cpu().numpy()
    
    def learn(self,
              state: np.ndarray,
              action: np.ndarray,
              reward: float,
              next_state: np.ndarray,
              done: bool) -> Dict[str, float]:
        """学习更新
        
        Args:
            state: 当前状态
            action: 执行的动作
            reward: 获得的奖励
            next_state: 下一状态
            done: 是否结束
            
        Returns:
            学习统计信息
        """
        # 存储经验
        self.replay_buffer.add(state, action, reward, next_state, done)
        
        # 检查是否可以开始学习
        if not self.replay_buffer.is_ready(self.batch_size):
            return {'actor_loss': 0.0, 'critic1_loss': 0.0, 'critic2_loss': 0.0, 'alpha_loss': 0.0}
        
        # 从经验回放缓冲区采样
        batch_states, batch_actions, batch_rewards, batch_next_states, batch_dones = \
            self.replay_buffer.sample(self.batch_size)
        
        # 转换为张量
        states = torch.FloatTensor(batch_states).to(self.device)
        actions = torch.FloatTensor(batch_actions).to(self.device)
        rewards = torch.FloatTensor(batch_rewards).to(self.device)
        next_states = torch.FloatTensor(batch_next_states).to(self.device)
        dones = torch.BoolTensor(batch_dones).to(self.device)
        
        # 更新Q网络
        critic1_loss, critic2_loss = self._update_critics(states, actions, rewards, next_states, dones)
        
        # 更新演员网络
        actor_loss = self._update_actor(states)
        
        # 更新熵系数
        alpha_loss = 0.0
        if self.automatic_entropy_tuning:
            alpha_loss = self._update_alpha(states)
        
        # 软更新目标网络
        self._soft_update_targets()
        
        # 更新训练步数
        self.training_step += 1
        
        # 记录损失
        self.actor_losses.append(actor_loss)
        self.critic1_losses.append(critic1_loss)
        self.critic2_losses.append(critic2_loss)
        self.alpha_losses.append(alpha_loss)
        self.alpha_values.append(self.alpha.item())
        
        return {
            'actor_loss': actor_loss,
            'critic1_loss': critic1_loss,
            'critic2_loss': critic2_loss,
            'alpha_loss': alpha_loss,
            'alpha': self.alpha.item()
        }
    
    def _update_critics(self, states: torch.Tensor, actions: torch.Tensor,
                       rewards: torch.Tensor, next_states: torch.Tensor,
                       dones: torch.Tensor) -> Tuple[float, float]:
        """更新评论家网络"""
        with torch.no_grad():
            # 计算目标Q值
            next_actions, next_log_probs = self.actor.sample(next_states)
            target_q1 = self.target_critic1(next_states, next_actions)
            target_q2 = self.target_critic2(next_states, next_actions)
            target_q = torch.min(target_q1, target_q2) - self.alpha * next_log_probs.unsqueeze(-1)
            target_q = rewards.unsqueeze(-1) + (self.gamma * target_q * (~dones).unsqueeze(-1))
        
        # 计算当前Q值
        current_q1 = self.critic1(states, actions)
        current_q2 = self.critic2(states, actions)
        
        # 计算损失
        critic1_loss = F.mse_loss(current_q1, target_q)
        critic2_loss = F.mse_loss(current_q2, target_q)
        
        # 更新Q1网络
        self.critic1_optimizer.zero_grad()
        critic1_loss.backward()
        self.critic1_optimizer.step()
        
        # 更新Q2网络
        self.critic2_optimizer.zero_grad()
        critic2_loss.backward()
        self.critic2_optimizer.step()
        
        return critic1_loss.item(), critic2_loss.item()
    
    def _update_actor(self, states: torch.Tensor) -> float:
        """更新演员网络"""
        # 采样动作
        actions, log_probs = self.actor.sample(states)
        
        # 计算Q值
        q1 = self.critic1(states, actions)
        q2 = self.critic2(states, actions)
        q = torch.min(q1, q2)
        
        # 计算演员损失
        actor_loss = (self.alpha * log_probs.unsqueeze(-1) - q).mean()
        
        # 更新演员网络
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        self.actor_optimizer.step()
        
        return actor_loss.item()
    
    def _update_alpha(self, states: torch.Tensor) -> float:
        """更新熵系数"""
        with torch.no_grad():
            _, log_probs = self.actor.sample(states)
        
        # 计算熵损失
        alpha_loss = -(self.log_alpha * (log_probs + self.target_entropy)).mean()
        
        # 更新熵系数
        self.alpha_optimizer.zero_grad()
        alpha_loss.backward()
        self.alpha_optimizer.step()
        
        # 更新alpha值
        self.alpha = self.log_alpha.exp()
        
        return alpha_loss.item()
    
    def _soft_update_targets(self):
        """软更新目标网络"""
        for target_param, param in zip(self.target_critic1.parameters(), self.critic1.parameters()):
            target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)
        
        for target_param, param in zip(self.target_critic2.parameters(), self.critic2.parameters()):
            target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)
    
    def _get_save_dict(self) -> Dict[str, Any]:
        """获取保存字典"""
        save_dict = {
            'actor_state_dict': self.actor.state_dict(),
            'critic1_state_dict': self.critic1.state_dict(),
            'critic2_state_dict': self.critic2.state_dict(),
            'target_critic1_state_dict': self.target_critic1.state_dict(),
            'target_critic2_state_dict': self.target_critic2.state_dict(),
            'actor_optimizer_state_dict': self.actor_optimizer.state_dict(),
            'critic1_optimizer_state_dict': self.critic1_optimizer.state_dict(),
            'critic2_optimizer_state_dict': self.critic2_optimizer.state_dict(),
            'gamma': self.gamma,
            'tau': self.tau,
            'automatic_entropy_tuning': self.automatic_entropy_tuning,
            'target_entropy': self.target_entropy,
        }
        
        if self.automatic_entropy_tuning:
            save_dict.update({
                'log_alpha': self.log_alpha,
                'alpha_optimizer_state_dict': self.alpha_optimizer.state_dict(),
            })
        else:
            save_dict['alpha'] = self.alpha
        
        return save_dict
    
    def _load_from_dict(self, save_dict: Dict[str, Any]):
        """从保存字典加载"""
        self.actor.load_state_dict(save_dict['actor_state_dict'])
        self.critic1.load_state_dict(save_dict['critic1_state_dict'])
        self.critic2.load_state_dict(save_dict['critic2_state_dict'])
        self.target_critic1.load_state_dict(save_dict['target_critic1_state_dict'])
        self.target_critic2.load_state_dict(save_dict['target_critic2_state_dict'])
        
        self.actor_optimizer.load_state_dict(save_dict['actor_optimizer_state_dict'])
        self.critic1_optimizer.load_state_dict(save_dict['critic1_optimizer_state_dict'])
        self.critic2_optimizer.load_state_dict(save_dict['critic2_optimizer_state_dict'])
        
        if self.automatic_entropy_tuning and 'log_alpha' in save_dict:
            self.log_alpha = save_dict['log_alpha']
            self.alpha = self.log_alpha.exp()
            self.alpha_optimizer.load_state_dict(save_dict['alpha_optimizer_state_dict'])
        elif 'alpha' in save_dict:
            self.alpha = save_dict['alpha']
    
    def set_training_mode(self, training: bool = True):
        """设置训练模式"""
        if training:
            self.actor.train()
            self.critic1.train()
            self.critic2.train()
        else:
            self.actor.eval()
            self.critic1.eval()
            self.critic2.eval()
    
    def get_action_distribution(self, state: np.ndarray) -> Dict[str, Any]:
        """获取动作分布"""
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            mean, log_std = self.actor(state_tensor)
            std = log_std.exp()
            
            return {
                'mean': mean.squeeze(0).cpu().numpy(),
                'std': std.squeeze(0).cpu().numpy(),
                'log_std': log_std.squeeze(0).cpu().numpy(),
                'action_type': 'continuous'
            }