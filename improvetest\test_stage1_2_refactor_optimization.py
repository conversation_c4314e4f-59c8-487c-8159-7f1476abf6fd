#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件：第一阶段第二个子项 - 重构优化脚本
文件名：test_stage1_2_refactor_optimization.py
目标：测试重构后的优化脚本正确使用共享工具模块
"""

import unittest
import tempfile
import os
import sys
import pickle
import shutil
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestRefactoredOptimization(unittest.TestCase):
    """测试重构后的优化脚本功能"""
    
    def setUp(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        
        # 创建测试数据目录结构
        self.test_data_dir = os.path.join(self.temp_dir, 'data', '1h_1500')
        os.makedirs(self.test_data_dir, exist_ok=True)
        
        # 创建测试训练结果目录
        self.test_results_dir = os.path.join(self.temp_dir, 'train_results')
        os.makedirs(self.test_results_dir, exist_ok=True)
        
        # 创建模拟数据文件
        self._create_mock_data_files()
        
    def tearDown(self):
        """测试后的清理"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def _create_mock_data_files(self):
        """创建模拟数据文件"""
        mock_data = {
            'data_from_processor': pd.DataFrame({
                'timestamp': pd.date_range('2023-01-01', periods=100, freq='H'),
                'close': [100 + i for i in range(100)],
                'volume': [1000 + i*10 for i in range(100)]
            }),
            'price_array': [[100 + i, 101 + i, 99 + i, 100.5 + i] for i in range(100)],
            'tech_array': [[0.5 + i*0.01, 0.3 + i*0.005] for i in range(100)],
            'time_array': [f'2023-01-{i+1:02d}' for i in range(100)]
        }
        
        for filename, data in mock_data.items():
            filepath = os.path.join(self.test_data_dir, filename)
            with open(filepath, 'wb') as f:
                pickle.dump(data, f)
    
    def test_refactored_cpcv_imports_utils(self):
        """测试重构后的CPCV脚本正确导入工具模块"""
        # 这个测试将在重构完成后验证导入
        # 现在先创建测试框架
        self.assertTrue(True, "测试框架已创建，等待重构实现")
    
    def test_refactored_cpcv_uses_shared_hyperparams(self):
        """测试重构后的CPCV脚本使用共享的超参数采样"""
        from utils.optimization_utils import OptimizationUtils
        
        # 模拟trial和config
        mock_trial = Mock()
        mock_trial.suggest_categorical.return_value = 0.001
        
        config = {
            'no_candles_for_train': 1000,
            'learning_rates': [3e-2, 2.3e-2],
            'batch_sizes': [512, 1280],
            'gammas': [0.85, 0.99]
        }
        
        # 测试共享工具能正确工作
        erl_params, env_params = OptimizationUtils.sample_hyperparams(mock_trial, config)
        
        self.assertIsInstance(erl_params, dict)
        self.assertIsInstance(env_params, dict)
        self.assertIn('learning_rate', erl_params)
        self.assertIn('batch_size', erl_params)
    
    def test_refactored_cpcv_uses_shared_data_loading(self):
        """测试重构后的CPCV脚本使用共享的数据加载"""
        from utils.optimization_utils import OptimizationUtils
        
        with patch('utils.optimization_utils.OptimizationUtils._get_data_folder_path') as mock_path:
            mock_path.return_value = self.test_data_dir
            
            # 测试数据加载功能
            loaded_data = OptimizationUtils.load_saved_data('1h', 1000, 500)
            
            self.assertEqual(len(loaded_data), 4)
            data_from_processor, price_array, tech_array, time_array = loaded_data
            
            # 验证数据类型
            self.assertIsInstance(data_from_processor, pd.DataFrame)
            self.assertIsInstance(price_array, list)
            self.assertIsInstance(tech_array, list)
            self.assertIsInstance(time_array, list)
    
    def test_refactored_cpcv_uses_shared_logging(self):
        """测试重构后的CPCV脚本使用共享的日志功能"""
        from utils.optimization_utils import OptimizationUtils
        
        mock_trial = Mock()
        mock_trial.number = 1
        
        config = {
            'model_name': 'PPO',
            'cwd': '/test/cwd',
            'erl_params': {'learning_rate': 0.001},
            'env_params': {'lookback': 1}
        }
        
        with patch('utils.optimization_utils.OptimizationUtils._get_train_results_path') as mock_path:
            mock_path.return_value = self.test_results_dir
            
            log_path = OptimizationUtils.write_logs('test_folder', config, mock_trial)
            
            # 验证日志文件被创建
            expected_log_path = os.path.join(self.test_results_dir, 'test_folder', 'logs.txt')
            self.assertTrue(os.path.exists(expected_log_path))
    
    def test_refactored_kcv_imports_utils(self):
        """测试重构后的KCV脚本正确导入工具模块"""
        # 这个测试将在重构完成后验证导入
        self.assertTrue(True, "测试框架已创建，等待重构实现")
    
    def test_refactored_kcv_uses_shared_functions(self):
        """测试重构后的KCV脚本使用共享函数"""
        from utils.optimization_utils import OptimizationUtils
        
        # 测试共享函数的可用性
        self.assertTrue(hasattr(OptimizationUtils, 'sample_hyperparams'))
        self.assertTrue(hasattr(OptimizationUtils, 'set_trial_attributes'))
        self.assertTrue(hasattr(OptimizationUtils, 'load_saved_data'))
        self.assertTrue(hasattr(OptimizationUtils, 'write_logs'))
        self.assertTrue(hasattr(OptimizationUtils, 'save_best_agent'))
    
    def test_code_duplication_elimination(self):
        """测试代码重复消除效果"""
        # 这个测试将验证重构后代码重复的减少
        # 通过检查函数定义的唯一性
        
        # 模拟检查：确保关键函数只在utils模块中定义
        from utils.optimization_utils import OptimizationUtils
        
        # 验证OptimizationUtils包含所有必要方法
        required_methods = [
            'sample_hyperparams',
            'set_trial_attributes', 
            'load_saved_data',
            'write_logs',
            'save_best_agent'
        ]
        
        for method in required_methods:
            self.assertTrue(
                hasattr(OptimizationUtils, method),
                f"OptimizationUtils缺少方法: {method}"
            )
    
    def test_refactored_scripts_maintain_functionality(self):
        """测试重构后的脚本保持原有功能"""
        # 这个测试确保重构不会破坏现有功能
        # 通过模拟关键流程来验证
        
        from utils.optimization_utils import OptimizationUtils
        
        # 模拟完整的优化流程
        mock_trial = Mock()
        mock_trial.suggest_categorical.return_value = 0.001
        mock_trial.number = 1
        mock_trial.user_attrs = {}
        
        mock_study = Mock()
        
        config = {
            'model_name': 'PPO',
            'timeframe': '1h',
            'no_candles_for_train': 1000,
            'train_start_date': '2023-01-01',
            'train_end_date': '2023-06-01',
            'val_start_date': '2023-06-01', 
            'val_end_date': '2023-12-01',
            'ticker_list': ['BTCUSDT'],
            'technical_indicators': ['RSI'],
            'learning_rates': [0.001],
            'batch_sizes': [512],
            'gammas': [0.99]
        }
        
        # 测试完整流程不会出错
        try:
            # 1. 采样超参数
            erl_params, env_params = OptimizationUtils.sample_hyperparams(mock_trial, config)
            
            # 2. 设置trial属性
            with patch('joblib.dump'):
                OptimizationUtils.set_trial_attributes(
                    mock_trial, config, 'test_folder', 'test_name', mock_study
                )
            
            # 3. 写入日志
            with patch('utils.optimization_utils.OptimizationUtils._get_train_results_path') as mock_path:
                mock_path.return_value = self.test_results_dir
                OptimizationUtils.write_logs('test_folder', config, mock_trial)
            
            # 如果没有异常，测试通过
            self.assertTrue(True)
            
        except Exception as e:
            self.fail(f"重构后的工具流程出现错误: {e}")
    
    def test_performance_impact(self):
        """测试重构对性能的影响"""
        import time
        from utils.optimization_utils import OptimizationUtils
        
        # 简单的性能测试
        mock_trial = Mock()
        mock_trial.suggest_categorical.return_value = 0.001
        
        config = {
            'no_candles_for_train': 1000,
            'learning_rates': [0.001, 0.002, 0.003],
            'batch_sizes': [512, 1024, 2048],
            'gammas': [0.85, 0.99, 0.999]
        }
        
        # 测量执行时间
        start_time = time.time()
        for _ in range(100):  # 重复100次
            OptimizationUtils.sample_hyperparams(mock_trial, config)
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        # 确保性能合理（100次调用应该在1秒内完成）
        self.assertLess(execution_time, 1.0, "性能测试失败：执行时间过长")

if __name__ == '__main__':
    unittest.main()