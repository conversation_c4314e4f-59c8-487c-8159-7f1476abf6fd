"""训练工具模块

提供训练相关的实用函数和工具。
"""

import os
import json
import pickle
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import logging

from .base import EvaluationMetrics


def save_training_results(results: Dict[str, Any], 
                         save_path: str, 
                         experiment_name: str,
                         format: str = 'json') -> str:
    """保存训练结果
    
    Args:
        results: 训练结果字典
        save_path: 保存路径
        experiment_name: 实验名称
        format: 保存格式 ('json', 'pickle')
        
    Returns:
        保存的文件路径
    """
    os.makedirs(save_path, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"{experiment_name}_results_{timestamp}"
    
    if format == 'json':
        filepath = os.path.join(save_path, f"{filename}.json")
        
        # 转换numpy数组为列表
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, dict):
                return {k: convert_numpy(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            else:
                return obj
        
        serializable_results = convert_numpy(results)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
    
    elif format == 'pickle':
        filepath = os.path.join(save_path, f"{filename}.pkl")
        with open(filepath, 'wb') as f:
            pickle.dump(results, f)
    
    else:
        raise ValueError(f"不支持的格式: {format}")
    
    logging.info(f"训练结果已保存到: {filepath}")
    return filepath


def load_training_results(filepath: str) -> Dict[str, Any]:
    """加载训练结果
    
    Args:
        filepath: 文件路径
        
    Returns:
        训练结果字典
    """
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"文件不存在: {filepath}")
    
    if filepath.endswith('.json'):
        with open(filepath, 'r', encoding='utf-8') as f:
            results = json.load(f)
    elif filepath.endswith('.pkl'):
        with open(filepath, 'rb') as f:
            results = pickle.load(f)
    else:
        raise ValueError(f"不支持的文件格式: {filepath}")
    
    logging.info(f"训练结果已从 {filepath} 加载")
    return results


def plot_training_curves(training_history: Dict[str, List],
                        save_path: Optional[str] = None,
                        experiment_name: str = "experiment",
                        figsize: Tuple[int, int] = (15, 10)) -> plt.Figure:
    """绘制训练曲线
    
    Args:
        training_history: 训练历史数据
        save_path: 保存路径
        experiment_name: 实验名称
        figsize: 图像大小
        
    Returns:
        matplotlib图像对象
    """
    # 设置样式
    plt.style.use('seaborn-v0_8')
    fig, axes = plt.subplots(2, 3, figsize=figsize)
    fig.suptitle(f'Training Curves - {experiment_name}', fontsize=16, fontweight='bold')
    
    # 奖励曲线
    if 'rewards' in training_history and training_history['rewards']:
        axes[0, 0].plot(training_history['rewards'], alpha=0.7, color='blue')
        if len(training_history['rewards']) > 100:
            # 添加移动平均线
            window = min(100, len(training_history['rewards']) // 10)
            moving_avg = pd.Series(training_history['rewards']).rolling(window=window).mean()
            axes[0, 0].plot(moving_avg, color='red', linewidth=2, label=f'MA({window})')
            axes[0, 0].legend()
        axes[0, 0].set_title('Episode Rewards')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Reward')
        axes[0, 0].grid(True, alpha=0.3)
    
    # 回报率曲线
    if 'returns' in training_history and training_history['returns']:
        returns_pct = [r * 100 for r in training_history['returns']]
        axes[0, 1].plot(returns_pct, alpha=0.7, color='green')
        if len(returns_pct) > 100:
            window = min(100, len(returns_pct) // 10)
            moving_avg = pd.Series(returns_pct).rolling(window=window).mean()
            axes[0, 1].plot(moving_avg, color='red', linewidth=2, label=f'MA({window})')
            axes[0, 1].legend()
        axes[0, 1].set_title('Episode Returns (%)')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Return (%)')
        axes[0, 1].grid(True, alpha=0.3)
    
    # 损失曲线
    if 'losses' in training_history and training_history['losses']:
        axes[0, 2].plot(training_history['losses'], alpha=0.7, color='orange')
        if len(training_history['losses']) > 100:
            window = min(100, len(training_history['losses']) // 10)
            moving_avg = pd.Series(training_history['losses']).rolling(window=window).mean()
            axes[0, 2].plot(moving_avg, color='red', linewidth=2, label=f'MA({window})')
            axes[0, 2].legend()
        axes[0, 2].set_title('Training Loss')
        axes[0, 2].set_xlabel('Training Step')
        axes[0, 2].set_ylabel('Loss')
        axes[0, 2].grid(True, alpha=0.3)
    
    # 夏普比率曲线
    if 'sharpe_ratios' in training_history and training_history['sharpe_ratios']:
        axes[1, 0].plot(training_history['sharpe_ratios'], alpha=0.7, color='purple')
        if len(training_history['sharpe_ratios']) > 100:
            window = min(100, len(training_history['sharpe_ratios']) // 10)
            moving_avg = pd.Series(training_history['sharpe_ratios']).rolling(window=window).mean()
            axes[1, 0].plot(moving_avg, color='red', linewidth=2, label=f'MA({window})')
            axes[1, 0].legend()
        axes[1, 0].set_title('Sharpe Ratio')
        axes[1, 0].set_xlabel('Episode')
        axes[1, 0].set_ylabel('Sharpe Ratio')
        axes[1, 0].grid(True, alpha=0.3)
    
    # 最大回撤曲线
    if 'max_drawdowns' in training_history and training_history['max_drawdowns']:
        drawdowns_pct = [d * 100 for d in training_history['max_drawdowns']]
        axes[1, 1].plot(drawdowns_pct, alpha=0.7, color='red')
        if len(drawdowns_pct) > 100:
            window = min(100, len(drawdowns_pct) // 10)
            moving_avg = pd.Series(drawdowns_pct).rolling(window=window).mean()
            axes[1, 1].plot(moving_avg, color='darkred', linewidth=2, label=f'MA({window})')
            axes[1, 1].legend()
        axes[1, 1].set_title('Max Drawdown (%)')
        axes[1, 1].set_xlabel('Episode')
        axes[1, 1].set_ylabel('Drawdown (%)')
        axes[1, 1].grid(True, alpha=0.3)
    
    # 评估奖励曲线
    if 'eval_rewards' in training_history and training_history['eval_rewards']:
        eval_timesteps = training_history.get('eval_timesteps', range(len(training_history['eval_rewards'])))
        axes[1, 2].plot(eval_timesteps, training_history['eval_rewards'], 
                       marker='o', alpha=0.7, color='darkgreen', linewidth=2)
        axes[1, 2].set_title('Evaluation Rewards')
        axes[1, 2].set_xlabel('Timestep')
        axes[1, 2].set_ylabel('Eval Reward')
        axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图像
    if save_path:
        os.makedirs(save_path, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{experiment_name}_training_curves_{timestamp}.png"
        filepath = os.path.join(save_path, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        logging.info(f"训练曲线已保存到: {filepath}")
    
    return fig


def plot_portfolio_analysis(training_history: Dict[str, List],
                           assets: List[str],
                           save_path: Optional[str] = None,
                           experiment_name: str = "portfolio_experiment",
                           figsize: Tuple[int, int] = (16, 12)) -> plt.Figure:
    """绘制投资组合分析图
    
    Args:
        training_history: 训练历史数据
        assets: 资产列表
        save_path: 保存路径
        experiment_name: 实验名称
        figsize: 图像大小
        
    Returns:
        matplotlib图像对象
    """
    plt.style.use('seaborn-v0_8')
    fig, axes = plt.subplots(2, 3, figsize=figsize)
    fig.suptitle(f'Portfolio Analysis - {experiment_name}', fontsize=16, fontweight='bold')
    
    # 投资组合价值曲线
    if 'portfolio_values' in training_history and training_history['portfolio_values']:
        axes[0, 0].plot(training_history['portfolio_values'], color='blue', linewidth=2)
        axes[0, 0].set_title('Portfolio Value')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Portfolio Value')
        axes[0, 0].grid(True, alpha=0.3)
    
    # 权重分布热力图
    if 'portfolio_weights' in training_history and training_history['portfolio_weights']:
        weights_data = np.array(training_history['portfolio_weights'])
        if weights_data.ndim == 2 and weights_data.shape[1] == len(assets):
            # 取最后100个回合的权重
            recent_weights = weights_data[-100:] if len(weights_data) > 100 else weights_data
            
            im = axes[0, 1].imshow(recent_weights.T, aspect='auto', cmap='viridis')
            axes[0, 1].set_title('Portfolio Weights Heatmap')
            axes[0, 1].set_xlabel('Episode (Recent)')
            axes[0, 1].set_ylabel('Assets')
            axes[0, 1].set_yticks(range(len(assets)))
            axes[0, 1].set_yticklabels(assets)
            plt.colorbar(im, ax=axes[0, 1])
    
    # 平均权重饼图
    if 'portfolio_weights' in training_history and training_history['portfolio_weights']:
        weights_data = np.array(training_history['portfolio_weights'])
        if weights_data.ndim == 2 and weights_data.shape[1] == len(assets):
            avg_weights = np.mean(weights_data, axis=0)
            colors = plt.cm.Set3(np.linspace(0, 1, len(assets)))
            
            wedges, texts, autotexts = axes[0, 2].pie(avg_weights, labels=assets, autopct='%1.1f%%',
                                                     colors=colors, startangle=90)
            axes[0, 2].set_title('Average Portfolio Weights')
    
    # 收益分布直方图
    if 'returns' in training_history and training_history['returns']:
        returns_pct = [r * 100 for r in training_history['returns']]
        axes[1, 0].hist(returns_pct, bins=50, alpha=0.7, color='green', edgecolor='black')
        axes[1, 0].axvline(np.mean(returns_pct), color='red', linestyle='--', 
                          label=f'Mean: {np.mean(returns_pct):.2f}%')
        axes[1, 0].set_title('Returns Distribution')
        axes[1, 0].set_xlabel('Return (%)')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
    
    # 风险-收益散点图
    if ('returns' in training_history and training_history['returns'] and
        'portfolio_volatility' in training_history and training_history['portfolio_volatility']):
        
        returns_pct = [r * 100 for r in training_history['returns']]
        volatility_pct = [v * 100 for v in training_history['portfolio_volatility']]
        
        scatter = axes[1, 1].scatter(volatility_pct, returns_pct, alpha=0.6, c=range(len(returns_pct)), 
                                   cmap='viridis', s=30)
        axes[1, 1].set_title('Risk-Return Scatter')
        axes[1, 1].set_xlabel('Volatility (%)')
        axes[1, 1].set_ylabel('Return (%)')
        axes[1, 1].grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=axes[1, 1], label='Episode')
    
    # 累积收益曲线
    if 'returns' in training_history and training_history['returns']:
        returns = np.array(training_history['returns'])
        cumulative_returns = np.cumprod(1 + returns) - 1
        cumulative_returns_pct = cumulative_returns * 100
        
        axes[1, 2].plot(cumulative_returns_pct, color='darkgreen', linewidth=2)
        axes[1, 2].set_title('Cumulative Returns')
        axes[1, 2].set_xlabel('Episode')
        axes[1, 2].set_ylabel('Cumulative Return (%)')
        axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图像
    if save_path:
        os.makedirs(save_path, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{experiment_name}_portfolio_analysis_{timestamp}.png"
        filepath = os.path.join(save_path, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        logging.info(f"投资组合分析图已保存到: {filepath}")
    
    return fig


def calculate_performance_metrics(returns: List[float], 
                                benchmark_returns: Optional[List[float]] = None,
                                risk_free_rate: float = 0.0) -> Dict[str, float]:
    """计算性能指标
    
    Args:
        returns: 收益率列表
        benchmark_returns: 基准收益率列表
        risk_free_rate: 无风险利率
        
    Returns:
        性能指标字典
    """
    if not returns:
        return {}
    
    returns_array = np.array(returns)
    
    metrics = {
        'total_return': np.prod(1 + returns_array) - 1,
        'annualized_return': np.mean(returns_array) * 252,  # 假设252个交易日
        'volatility': np.std(returns_array) * np.sqrt(252),
        'sharpe_ratio': 0.0,
        'max_drawdown': 0.0,
        'calmar_ratio': 0.0,
        'sortino_ratio': 0.0,
        'win_rate': np.sum(returns_array > 0) / len(returns_array),
        'avg_win': np.mean(returns_array[returns_array > 0]) if np.any(returns_array > 0) else 0.0,
        'avg_loss': np.mean(returns_array[returns_array < 0]) if np.any(returns_array < 0) else 0.0,
    }
    
    # 夏普比率
    if metrics['volatility'] > 0:
        metrics['sharpe_ratio'] = (metrics['annualized_return'] - risk_free_rate) / metrics['volatility']
    
    # 最大回撤
    cumulative_returns = np.cumprod(1 + returns_array)
    peak = np.maximum.accumulate(cumulative_returns)
    drawdown = (peak - cumulative_returns) / peak
    metrics['max_drawdown'] = np.max(drawdown)
    
    # 卡尔马比率
    if metrics['max_drawdown'] > 0:
        metrics['calmar_ratio'] = metrics['annualized_return'] / metrics['max_drawdown']
    
    # 索提诺比率
    downside_returns = returns_array[returns_array < risk_free_rate]
    if len(downside_returns) > 0:
        downside_deviation = np.std(downside_returns) * np.sqrt(252)
        if downside_deviation > 0:
            metrics['sortino_ratio'] = (metrics['annualized_return'] - risk_free_rate) / downside_deviation
    
    # 基准相关指标
    if benchmark_returns and len(benchmark_returns) == len(returns):
        benchmark_array = np.array(benchmark_returns)
        excess_returns = returns_array - benchmark_array
        
        metrics['alpha'] = np.mean(excess_returns) * 252
        
        if np.std(benchmark_array) > 0:
            metrics['beta'] = np.cov(returns_array, benchmark_array)[0, 1] / np.var(benchmark_array)
        
        tracking_error = np.std(excess_returns) * np.sqrt(252)
        metrics['tracking_error'] = tracking_error
        
        if tracking_error > 0:
            metrics['information_ratio'] = metrics['alpha'] / tracking_error
    
    return metrics


def compare_experiments(experiment_results: Dict[str, Dict[str, Any]],
                       save_path: Optional[str] = None,
                       figsize: Tuple[int, int] = (15, 10)) -> plt.Figure:
    """比较多个实验结果
    
    Args:
        experiment_results: 实验结果字典，键为实验名称
        save_path: 保存路径
        figsize: 图像大小
        
    Returns:
        matplotlib图像对象
    """
    plt.style.use('seaborn-v0_8')
    fig, axes = plt.subplots(2, 2, figsize=figsize)
    fig.suptitle('Experiment Comparison', fontsize=16, fontweight='bold')
    
    # 准备数据
    experiment_names = list(experiment_results.keys())
    colors = plt.cm.Set1(np.linspace(0, 1, len(experiment_names)))
    
    # 奖励比较
    for i, (name, results) in enumerate(experiment_results.items()):
        if 'training_history' in results and 'rewards' in results['training_history']:
            rewards = results['training_history']['rewards']
            if rewards:
                # 计算移动平均
                window = min(100, len(rewards) // 10) if len(rewards) > 100 else 1
                if window > 1:
                    moving_avg = pd.Series(rewards).rolling(window=window).mean()
                    axes[0, 0].plot(moving_avg, label=name, color=colors[i], linewidth=2)
                else:
                    axes[0, 0].plot(rewards, label=name, color=colors[i], linewidth=2)
    
    axes[0, 0].set_title('Training Rewards Comparison')
    axes[0, 0].set_xlabel('Episode')
    axes[0, 0].set_ylabel('Reward')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 收益率比较
    for i, (name, results) in enumerate(experiment_results.items()):
        if 'training_history' in results and 'returns' in results['training_history']:
            returns = results['training_history']['returns']
            if returns:
                returns_pct = [r * 100 for r in returns]
                window = min(100, len(returns_pct) // 10) if len(returns_pct) > 100 else 1
                if window > 1:
                    moving_avg = pd.Series(returns_pct).rolling(window=window).mean()
                    axes[0, 1].plot(moving_avg, label=name, color=colors[i], linewidth=2)
                else:
                    axes[0, 1].plot(returns_pct, label=name, color=colors[i], linewidth=2)
    
    axes[0, 1].set_title('Returns Comparison (%)')
    axes[0, 1].set_xlabel('Episode')
    axes[0, 1].set_ylabel('Return (%)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 性能指标比较（条形图）
    metrics_to_compare = ['sharpe_ratio', 'max_drawdown', 'total_return']
    metric_data = {metric: [] for metric in metrics_to_compare}
    
    for name, results in experiment_results.items():
        if 'final_evaluation' in results:
            eval_results = results['final_evaluation']
            for metric in metrics_to_compare:
                if metric in eval_results:
                    value = eval_results[metric]
                    if metric == 'max_drawdown':
                        value *= 100  # 转换为百分比
                    elif metric == 'total_return':
                        value *= 100  # 转换为百分比
                    metric_data[metric].append(value)
                else:
                    metric_data[metric].append(0)
    
    x = np.arange(len(experiment_names))
    width = 0.25
    
    for i, metric in enumerate(metrics_to_compare):
        offset = (i - 1) * width
        axes[1, 0].bar(x + offset, metric_data[metric], width, 
                      label=metric.replace('_', ' ').title(), alpha=0.8)
    
    axes[1, 0].set_title('Performance Metrics Comparison')
    axes[1, 0].set_xlabel('Experiment')
    axes[1, 0].set_ylabel('Value')
    axes[1, 0].set_xticks(x)
    axes[1, 0].set_xticklabels(experiment_names, rotation=45)
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 训练时间比较
    training_times = []
    for name, results in experiment_results.items():
        if 'training_info' in results and 'training_time' in results['training_info']:
            training_times.append(results['training_info']['training_time'])
        else:
            training_times.append(0)
    
    bars = axes[1, 1].bar(experiment_names, training_times, color=colors, alpha=0.8)
    axes[1, 1].set_title('Training Time Comparison')
    axes[1, 1].set_xlabel('Experiment')
    axes[1, 1].set_ylabel('Training Time (seconds)')
    axes[1, 1].tick_params(axis='x', rotation=45)
    axes[1, 1].grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, time_val in zip(bars, training_times):
        height = bar.get_height()
        axes[1, 1].text(bar.get_x() + bar.get_width()/2., height,
                        f'{time_val:.1f}s', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 保存图像
    if save_path:
        os.makedirs(save_path, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"experiment_comparison_{timestamp}.png"
        filepath = os.path.join(save_path, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        logging.info(f"实验比较图已保存到: {filepath}")
    
    return fig


def generate_training_report(results: Dict[str, Any], 
                           save_path: Optional[str] = None,
                           experiment_name: str = "experiment") -> str:
    """生成训练报告
    
    Args:
        results: 训练结果
        save_path: 保存路径
        experiment_name: 实验名称
        
    Returns:
        报告内容字符串
    """
    report_lines = []
    report_lines.append(f"# 训练报告 - {experiment_name}")
    report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("\n" + "="*50 + "\n")
    
    # 训练信息
    if 'training_info' in results:
        info = results['training_info']
        report_lines.append("## 训练信息")
        report_lines.append(f"- 总时间步数: {info.get('total_timesteps', 'N/A'):,}")
        report_lines.append(f"- 总回合数: {info.get('total_episodes', 'N/A'):,}")
        report_lines.append(f"- 训练时间: {info.get('training_time', 0):.2f} 秒")
        report_lines.append(f"- 每秒时间步数: {info.get('timesteps_per_second', 0):.2f}")
        if 'assets' in info:
            report_lines.append(f"- 资产: {', '.join(info['assets'])}")
        report_lines.append("")
    
    # 训练性能
    if 'training_performance' in results:
        perf = results['training_performance']
        report_lines.append("## 训练性能")
        report_lines.append(f"- 平均回合奖励: {perf.get('avg_episode_reward', 0):.4f}")
        report_lines.append(f"- 平均回合收益: {perf.get('avg_episode_return', 0)*100:.2f}%")
        if 'avg_sharpe_ratio' in perf:
            report_lines.append(f"- 平均夏普比率: {perf['avg_sharpe_ratio']:.4f}")
        if 'avg_max_drawdown' in perf:
            report_lines.append(f"- 平均最大回撤: {perf['avg_max_drawdown']*100:.2f}%")
        report_lines.append("")
    
    # 最终评估
    if 'final_evaluation' in results:
        eval_results = results['final_evaluation']
        report_lines.append("## 最终评估")
        report_lines.append(f"- 平均奖励: {eval_results.get('mean_reward', 0):.4f}")
        report_lines.append(f"- 总收益: {eval_results.get('total_return', 0)*100:.2f}%")
        if 'sharpe_ratio' in eval_results:
            report_lines.append(f"- 夏普比率: {eval_results['sharpe_ratio']:.4f}")
        if 'max_drawdown' in eval_results:
            report_lines.append(f"- 最大回撤: {eval_results['max_drawdown']*100:.2f}%")
        if 'volatility' in eval_results:
            report_lines.append(f"- 波动率: {eval_results['volatility']*100:.2f}%")
        if 'win_rate' in eval_results:
            report_lines.append(f"- 胜率: {eval_results['win_rate']*100:.2f}%")
        report_lines.append("")
    
    # 投资组合统计（如果有）
    if 'portfolio_stats' in results:
        stats = results['portfolio_stats']
        report_lines.append("## 投资组合统计")
        report_lines.append(f"- 总调仓次数: {stats.get('total_rebalances', 0):,}")
        report_lines.append(f"- 总交易成本: {stats.get('total_transaction_costs', 0):.4f}")
        report_lines.append(f"- 平均换手率: {stats.get('avg_turnover_rate', 0)*100:.2f}%")
        report_lines.append(f"- 最大权重集中度: {stats.get('max_weight_concentration', 0)*100:.2f}%")
        report_lines.append(f"- 平均持仓数量: {stats.get('avg_num_positions', 0):.1f}")
        
        if 'avg_weights' in stats:
            report_lines.append("\n### 平均权重分配")
            for asset, weight in stats['avg_weights'].items():
                report_lines.append(f"- {asset}: {weight*100:.2f}%")
        report_lines.append("")
    
    # 风险指标（如果有）
    if 'risk_metrics' in results:
        risk = results['risk_metrics']
        report_lines.append("## 风险指标")
        if risk.get('var_95', 0) != 0:
            report_lines.append(f"- 95% VaR: {risk['var_95']*100:.2f}%")
        if risk.get('cvar_95', 0) != 0:
            report_lines.append(f"- 95% CVaR: {risk['cvar_95']*100:.2f}%")
        if risk.get('downside_deviation', 0) != 0:
            report_lines.append(f"- 下行偏差: {risk['downside_deviation']*100:.2f}%")
        if risk.get('beta', 0) != 0:
            report_lines.append(f"- Beta: {risk['beta']:.4f}")
        if risk.get('alpha', 0) != 0:
            report_lines.append(f"- Alpha: {risk['alpha']*100:.2f}%")
        report_lines.append("")
    
    # 智能体和环境信息
    if 'agent_info' in results:
        agent_info = results['agent_info']
        report_lines.append("## 智能体信息")
        report_lines.append(f"- 类型: {agent_info.get('agent_type', 'N/A')}")
        report_lines.append(f"- 状态维度: {agent_info.get('state_dim', 'N/A')}")
        report_lines.append(f"- 动作维度: {agent_info.get('action_dim', 'N/A')}")
        report_lines.append("")
    
    if 'environment_info' in results:
        env_info = results['environment_info']
        report_lines.append("## 环境信息")
        report_lines.append(f"- 类型: {env_info.get('env_type', 'N/A')}")
        report_lines.append(f"- 观察空间: {env_info.get('observation_space', 'N/A')}")
        report_lines.append(f"- 动作空间: {env_info.get('action_space', 'N/A')}")
        report_lines.append("")
    
    report_content = "\n".join(report_lines)
    
    # 保存报告
    if save_path:
        os.makedirs(save_path, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{experiment_name}_report_{timestamp}.md"
        filepath = os.path.join(save_path, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logging.info(f"训练报告已保存到: {filepath}")
    
    return report_content


class TrainingUtils:
    """训练工具类，提供训练相关的实用方法"""
    
    @staticmethod
    def save_results(results: Dict[str, Any], save_path: str, experiment_name: str, format: str = 'json') -> str:
        """保存训练结果"""
        return save_training_results(results, save_path, experiment_name, format)
    
    @staticmethod
    def load_results(filepath: str) -> Dict[str, Any]:
        """加载训练结果"""
        return load_training_results(filepath)
    
    @staticmethod
    def plot_results(results: Dict[str, Any], save_path: Optional[str] = None) -> None:
        """绘制训练结果"""
        plot_training_results(results, save_path)
    
    @staticmethod
    def generate_report(results: Dict[str, Any], experiment_name: str, save_path: Optional[str] = None) -> str:
        """生成训练报告"""
        return generate_training_report(results, experiment_name, save_path)
    
    @staticmethod
    def calculate_sharpe_ratio(returns: np.ndarray, risk_free_rate: float = 0.02) -> float:
        """计算夏普比率"""
        if len(returns) == 0:
            return 0.0
        
        excess_returns = returns - risk_free_rate / 252  # 假设252个交易日
        if np.std(excess_returns) == 0:
            return 0.0
        
        return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
    
    @staticmethod
    def calculate_max_drawdown(portfolio_values: np.ndarray) -> float:
        """计算最大回撤"""
        if len(portfolio_values) == 0:
            return 0.0
        
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak
        return np.min(drawdown)
    
    @staticmethod
    def calculate_volatility(returns: np.ndarray, annualize: bool = True) -> float:
        """计算波动率"""
        if len(returns) == 0:
            return 0.0
        
        vol = np.std(returns)
        if annualize:
            vol *= np.sqrt(252)  # 年化
        
        return vol
    
    @staticmethod
    def prepare_training_data(data: pd.DataFrame, 
                            feature_columns: List[str],
                            target_column: str,
                            lookback_window: int = 5,
                            prediction_horizon: int = 1) -> Tuple[np.ndarray, np.ndarray]:
        """准备训练数据"""
        # 提取特征和目标数据
        feature_data = data[feature_columns].values
        target_data = data[target_column].values
        
        # 创建时间序列特征
        features = []
        targets = []
        
        for i in range(lookback_window, len(data) - prediction_horizon + 1):
            # 特征：过去lookback_window天的数据
            feature_window = feature_data[i-lookback_window:i].flatten()
            features.append(feature_window)
            
            # 目标：未来prediction_horizon天后的目标值
            target_value = target_data[i + prediction_horizon - 1]
            targets.append(target_value)
        
        return np.array(features), np.array(targets)
    
    @staticmethod
    def split_data(data: np.ndarray,
                  train_ratio: float = 0.7,
                  val_ratio: float = 0.2,
                  test_ratio: float = 0.1) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """分割数据"""
        assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-6, "比例之和必须等于1"
        
        n_samples = len(data)
        train_size = int(n_samples * train_ratio)
        val_size = int(n_samples * val_ratio)
        test_size = n_samples - train_size - val_size  # 确保总数正确
        
        train_data = data[:train_size]
        val_data = data[train_size:train_size + val_size]
        test_data = data[train_size + val_size:]
        
        return train_data, val_data, test_data
    
    @staticmethod
    def normalize_features(features: np.ndarray) -> Tuple[np.ndarray, Dict[str, np.ndarray]]:
        """标准化特征"""
        if len(features) == 0:
            return features, {}
        
        mean = np.mean(features, axis=0)
        std = np.std(features, axis=0)
        std[std == 0] = 1  # 避免除零
        
        normalized_features = (features - mean) / std
        scaler = {'mean': mean, 'std': std}
        
        return normalized_features, scaler
    
    @staticmethod
    def save_training_state(state: Dict[str, Any], filepath: str) -> None:
        """保存训练状态"""
        with open(filepath, 'wb') as f:
            pickle.dump(state, f)
    
    @staticmethod
    def load_training_state(filepath: str) -> Dict[str, Any]:
        """加载训练状态"""
        with open(filepath, 'rb') as f:
            return pickle.load(f)