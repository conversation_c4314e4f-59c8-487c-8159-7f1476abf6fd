"""多资产交易训练器模块

专门用于多个加密货币资产的强化学习交易训练。
"""

import time
from typing import Dict, Any, Optional, List
import numpy as np
import pandas as pd
from collections import deque, defaultdict

from .base import BaseTrainer, TrainingConfig, EvaluationMetrics
from ..agent.base import BaseAgent
from ..environment.base import BaseEnvironment


class MultiAssetTrainer(BaseTrainer):
    """多资产交易训练器
    
    专门用于多个加密货币资产的同时交易训练，提供资产间相关性分析和组合优化。
    """
    
    def __init__(self,
                 agent: BaseAgent,
                 env: BaseEnvironment,
                 config: Optional[TrainingConfig] = None,
                 save_path: str = "./models",
                 experiment_name: str = "multi_asset_experiment"):
        """初始化多资产训练器
        
        Args:
            agent: 智能体
            env: 交易环境
            config: 训练配置
            save_path: 模型保存路径
            experiment_name: 实验名称
        """
        super().__init__(agent, env, config, save_path, experiment_name)
        
        # 多资产特定属性
        self.symbols = None  # 交易的加密货币符号列表
        
        # 获取资产信息
        self.assets = getattr(env, 'assets', ['BTC', 'ETH'])  # 默认资产
        try:
            self.n_assets = len(self.assets)
        except TypeError:
            # 处理Mock对象或其他没有len()的对象
            self.n_assets = 2  # 默认值
        
        # 多资产特定的训练历史
        self.training_history.update({
            'portfolio_values': [],
            'asset_weights': [],
            'asset_returns': [],
            'correlations': [],
            'diversification_ratios': [],
            'rebalance_costs': [],
        })
        
        # 每个资产的交易统计
        self.asset_trade_stats = {asset: {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_profit': 0.0,
            'total_loss': 0.0,
            'avg_holding_period': 0.0,
        } for asset in self.assets}
        
        # 组合统计
        self.portfolio_stats = {
            'total_rebalances': 0,
            'rebalance_costs': 0.0,
            'max_concentration': 0.0,
            'avg_diversification': 0.0,
        }
        
        # 回合缓冲区
        self.episode_rewards = deque(maxlen=100)
        self.episode_returns = deque(maxlen=100)
        self.episode_sharpe_ratios = deque(maxlen=100)
        
        # 资产权重历史
        self.weight_history = deque(maxlen=1000)
        
    def train(self) -> Dict[str, Any]:
        """训练智能体
        
        Returns:
            训练结果字典
        """
        self.logger.info(f"开始多资产训练 - 资产: {self.assets}, 总时间步数: {self.config.total_timesteps}")
        start_time = time.time()
        
        # 重置环境
        obs = self.env.reset()
        episode_reward = 0.0
        episode_length = 0
        episode_start_value = self.env.get_portfolio_value()
        
        while self.current_timestep < self.config.total_timesteps:
            # 选择动作
            if self.current_timestep < self.config.learning_starts:
                # 随机探索阶段
                action = self.env.action_space.sample()
            else:
                action = self.agent.select_action(obs, training=True)
            
            # 执行动作
            next_obs, reward, done, info = self.env.step(action)
            
            # 存储经验
            if hasattr(self.agent, 'store_transition'):
                self.agent.store_transition(obs, action, reward, next_obs, done)
            
            # 更新统计
            episode_reward += reward
            episode_length += 1
            self.current_timestep += 1
            
            # 记录权重历史
            if hasattr(self.env, 'get_portfolio_weights'):
                weights = self.env.get_portfolio_weights()
                self.weight_history.append(weights)
            
            # 学习更新
            if (self.current_timestep >= self.config.learning_starts and 
                self.current_timestep % self.config.train_freq == 0):
                
                for _ in range(self.config.gradient_steps):
                    loss_info = self.agent.learn()
                    if loss_info and 'loss' in loss_info:
                        self.training_history['losses'].append(loss_info['loss'])
            
            # 目标网络更新（如果适用）
            if (hasattr(self.agent, 'update_target_network') and 
                self.current_timestep % self.config.target_update_interval == 0):
                self.agent.update_target_network()
            
            # 回合结束处理
            if done:
                # 计算回合统计
                episode_end_value = self.env.get_portfolio_value()
                episode_return = (episode_end_value - episode_start_value) / episode_start_value
                
                # 计算夏普比率
                if hasattr(self.env, 'get_portfolio_history'):
                    portfolio_history = self.env.get_portfolio_history()
                    if len(portfolio_history) > 1:
                        returns = np.diff(portfolio_history) / portfolio_history[:-1]
                        sharpe_ratio = self._calculate_sharpe_ratio(returns)
                        self.episode_sharpe_ratios.append(sharpe_ratio)
                
                # 更新历史记录
                self.episode_rewards.append(episode_reward)
                self.episode_returns.append(episode_return)
                self.training_history['rewards'].append(episode_reward)
                self.training_history['returns'].append(episode_return)
                self.training_history['timesteps'].append(self.current_timestep)
                
                # 更新多资产统计
                self._update_multi_asset_stats(info)
                
                # 记录回合信息
                if self._should_log():
                    self._log_episode_info(episode_reward, episode_return, episode_length)
                
                # 重置环境
                obs = self.env.reset()
                episode_reward = 0.0
                episode_length = 0
                episode_start_value = self.env.get_portfolio_value()
                self.current_episode += 1
            else:
                obs = next_obs
            
            # 评估
            if self._should_evaluate():
                eval_metrics = self.evaluate()
                self.training_history['eval_rewards'].append(eval_metrics.mean_reward)
                self.training_history['eval_timesteps'].append(self.current_timestep)
                
                # 早停检查
                if self.config.early_stopping_patience > 0:
                    if self._check_early_stopping(eval_metrics.mean_reward):
                        self.logger.info(f"早停触发 - 在时间步 {self.current_timestep}")
                        break
                
                # 保存最佳模型
                if self.config.save_best_model and eval_metrics.mean_reward > self.best_mean_reward:
                    self.save_model()
            
            # 定期保存
            if self._should_save():
                save_path = f"{self.save_path}/{self.experiment_name}_step_{self.current_timestep}.pth"
                self.save_model(save_path)
        
        # 训练结束
        training_time = time.time() - start_time
        
        # 最终评估
        final_eval = self.evaluate()
        
        # 保存训练历史
        self.save_training_history()
        
        # 生成训练报告
        training_results = self._generate_training_report(training_time, final_eval)
        
        self.logger.info(f"多资产训练完成 - 总时间: {training_time:.2f}秒")
        
        return training_results
    
    def evaluate(self, n_episodes: Optional[int] = None) -> EvaluationMetrics:
        """评估智能体
        
        Args:
            n_episodes: 评估回合数
            
        Returns:
            评估指标
        """
        if n_episodes is None:
            n_episodes = self.config.n_eval_episodes
        
        self.logger.info(f"开始多资产评估 - {n_episodes} 回合")
        start_time = time.time()
        
        # 设置为评估模式
        self.agent.set_training_mode(False)
        
        episode_rewards = []
        episode_lengths = []
        episode_returns = []
        portfolio_values = []
        sharpe_ratios = []
        max_drawdowns = []
        diversification_ratios = []
        
        for episode in range(n_episodes):
            obs = self.eval_env.reset()
            episode_reward = 0.0
            episode_length = 0
            start_value = self.eval_env.get_portfolio_value()
            
            episode_portfolio_values = [start_value]
            episode_weights = []
            
            done = False
            while not done:
                action = self.agent.select_action(obs, training=False)
                obs, reward, done, info = self.eval_env.step(action)
                
                episode_reward += reward
                episode_length += 1
                
                # 记录组合价值和权重
                current_value = self.eval_env.get_portfolio_value()
                episode_portfolio_values.append(current_value)
                
                if hasattr(self.eval_env, 'get_portfolio_weights'):
                    weights = self.eval_env.get_portfolio_weights()
                    episode_weights.append(weights)
            
            end_value = self.eval_env.get_portfolio_value()
            episode_return = (end_value - start_value) / start_value
            
            # 计算回合指标
            if len(episode_portfolio_values) > 1:
                returns = np.diff(episode_portfolio_values) / episode_portfolio_values[:-1]
                sharpe_ratio = self._calculate_sharpe_ratio(returns)
                max_drawdown = self._calculate_max_drawdown(episode_portfolio_values)
                
                sharpe_ratios.append(sharpe_ratio)
                max_drawdowns.append(max_drawdown)
            
            # 计算多样化比率
            if episode_weights:
                avg_weights = np.mean(episode_weights, axis=0)
                diversification_ratio = self._calculate_diversification_ratio(avg_weights)
                diversification_ratios.append(diversification_ratio)
            
            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)
            episode_returns.append(episode_return)
            portfolio_values.append(end_value)
        
        # 恢复训练模式
        self.agent.set_training_mode(True)
        
        # 计算评估指标
        eval_time = time.time() - start_time
        
        metrics = EvaluationMetrics(
            episode_rewards=episode_rewards,
            episode_lengths=episode_lengths,
            mean_reward=np.mean(episode_rewards),
            std_reward=np.std(episode_rewards),
            min_reward=np.min(episode_rewards),
            max_reward=np.max(episode_rewards),
            total_return=np.mean(episode_returns),
            evaluation_time=eval_time,
            timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
        )
        
        # 计算多资产相关指标
        if episode_returns:
            returns_array = np.array(episode_returns)
            metrics.sharpe_ratio = np.mean(sharpe_ratios) if sharpe_ratios else 0.0
            metrics.max_drawdown = np.mean(max_drawdowns) if max_drawdowns else 0.0
            metrics.volatility = np.std(returns_array)
            metrics.win_rate = np.sum(returns_array > 0) / len(returns_array)
        
        self.logger.info(f"多资产评估完成 - 平均奖励: {metrics.mean_reward:.4f}, "
                        f"平均回报: {metrics.total_return:.4f}, "
                        f"夏普比率: {metrics.sharpe_ratio:.4f}, "
                        f"平均多样化比率: {np.mean(diversification_ratios) if diversification_ratios else 0:.4f}")
        
        return metrics
    
    def _update_multi_asset_stats(self, info: Dict[str, Any]):
        """更新多资产统计
        
        Args:
            info: 环境返回的信息
        """
        # 更新每个资产的交易统计
        if 'asset_trades' in info:
            for asset, trades in info['asset_trades'].items():
                if asset in self.asset_trade_stats:
                    for trade in trades:
                        self.asset_trade_stats[asset]['total_trades'] += 1
                        if trade['profit'] > 0:
                            self.asset_trade_stats[asset]['winning_trades'] += 1
                            self.asset_trade_stats[asset]['total_profit'] += trade['profit']
                        else:
                            self.asset_trade_stats[asset]['losing_trades'] += 1
                            self.asset_trade_stats[asset]['total_loss'] += abs(trade['profit'])
        
        # 更新组合统计
        if 'rebalance_cost' in info:
            self.portfolio_stats['total_rebalances'] += 1
            self.portfolio_stats['rebalance_costs'] += info['rebalance_cost']
        
        if 'portfolio_weights' in info:
            weights = info['portfolio_weights']
            concentration = np.max(weights)
            self.portfolio_stats['max_concentration'] = max(
                self.portfolio_stats['max_concentration'], concentration
            )
            
            # 计算多样化指标
            diversification = self._calculate_diversification_ratio(weights)
            self.portfolio_stats['avg_diversification'] = (
                (self.portfolio_stats['avg_diversification'] * (self.current_episode - 1) + diversification) / 
                self.current_episode
            )
    
    def _log_episode_info(self, reward: float, return_rate: float, length: int):
        """记录回合信息
        
        Args:
            reward: 回合奖励
            return_rate: 回合回报率
            length: 回合长度
        """
        avg_reward = np.mean(self.episode_rewards) if self.episode_rewards else 0
        avg_return = np.mean(self.episode_returns) if self.episode_returns else 0
        avg_sharpe = np.mean(self.episode_sharpe_ratios) if self.episode_sharpe_ratios else 0
        
        # 计算当前权重分布
        current_weights = "N/A"
        if self.weight_history:
            latest_weights = self.weight_history[-1]
            weight_str = ", ".join([f"{asset}: {w:.3f}" for asset, w in zip(self.assets, latest_weights)])
            current_weights = f"[{weight_str}]"
        
        info = {
            'episode': self.current_episode,
            'reward': reward,
            'return': return_rate * 100,
            'length': length,
            'avg_reward_100': avg_reward,
            'avg_return_100': avg_return * 100,
            'avg_sharpe_100': avg_sharpe,
            'weights': current_weights,
        }
        
        self._log_training_info(info)
    
    def _calculate_sharpe_ratio(self, returns: np.ndarray, risk_free_rate: float = 0.0) -> float:
        """计算夏普比率
        
        Args:
            returns: 回报率数组
            risk_free_rate: 无风险利率
            
        Returns:
            夏普比率
        """
        if len(returns) == 0 or np.std(returns) == 0:
            return 0.0
        
        excess_returns = returns - risk_free_rate
        return np.mean(excess_returns) / np.std(returns)
    
    def _calculate_max_drawdown(self, portfolio_values: List[float]) -> float:
        """计算最大回撤
        
        Args:
            portfolio_values: 投资组合价值列表
            
        Returns:
            最大回撤
        """
        if not portfolio_values:
            return 0.0
        
        values = np.array(portfolio_values)
        peak = np.maximum.accumulate(values)
        drawdown = (peak - values) / peak
        return np.max(drawdown)
    
    def _calculate_diversification_ratio(self, weights: np.ndarray) -> float:
        """计算多样化比率
        
        Args:
            weights: 权重数组
            
        Returns:
            多样化比率 (0-1, 1表示完全多样化)
        """
        if len(weights) == 0:
            return 0.0
        
        # 使用赫芬达尔指数的倒数作为多样化指标
        herfindahl_index = np.sum(weights ** 2)
        max_diversification = 1.0 / len(weights)  # 等权重时的最大多样化
        current_diversification = 1.0 / herfindahl_index
        
        # 标准化到0-1范围
        return (current_diversification - 1.0) / (1.0 / max_diversification - 1.0)
    
    def _calculate_asset_correlations(self) -> Dict[str, float]:
        """计算资产间相关性
        
        Returns:
            资产相关性字典
        """
        if len(self.weight_history) < 10:
            return {}
        
        # 获取最近的权重历史
        recent_weights = np.array(list(self.weight_history)[-100:])
        
        correlations = {}
        for i in range(len(self.assets)):
            for j in range(i + 1, len(self.assets)):
                corr = np.corrcoef(recent_weights[:, i], recent_weights[:, j])[0, 1]
                if not np.isnan(corr):
                    correlations[f"{self.assets[i]}-{self.assets[j]}"] = corr
        
        return correlations
    
    def _generate_training_report(self, training_time: float, final_eval: EvaluationMetrics) -> Dict[str, Any]:
        """生成训练报告
        
        Args:
            training_time: 训练时间
            final_eval: 最终评估结果
            
        Returns:
            训练报告字典
        """
        # 基础统计
        total_episodes = len(self.training_history['rewards'])
        avg_episode_reward = np.mean(self.training_history['rewards']) if self.training_history['rewards'] else 0
        avg_episode_return = np.mean(self.training_history['returns']) if self.training_history['returns'] else 0
        
        # 多资产统计
        asset_stats = {}
        for asset in self.assets:
            stats = self.asset_trade_stats[asset]
            total_trades = stats['total_trades']
            win_rate = stats['winning_trades'] / max(total_trades, 1)
            profit_factor = stats['total_profit'] / max(stats['total_loss'], 1e-8)
            
            asset_stats[asset] = {
                'total_trades': total_trades,
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'total_profit': stats['total_profit'],
                'total_loss': stats['total_loss'],
            }
        
        # 相关性分析
        correlations = self._calculate_asset_correlations()
        
        report = {
            'training_info': {
                'total_timesteps': self.current_timestep,
                'total_episodes': total_episodes,
                'training_time': training_time,
                'timesteps_per_second': self.current_timestep / training_time,
                'assets': self.assets,
                'n_assets': self.n_assets,
            },
            'training_performance': {
                'avg_episode_reward': avg_episode_reward,
                'avg_episode_return': avg_episode_return,
                'best_mean_reward': self.best_mean_reward,
                'avg_sharpe_ratio': np.mean(self.episode_sharpe_ratios) if self.episode_sharpe_ratios else 0,
            },
            'final_evaluation': final_eval.to_dict(),
            'asset_trading_stats': asset_stats,
            'portfolio_stats': {
                'total_rebalances': self.portfolio_stats['total_rebalances'],
                'avg_rebalance_cost': (self.portfolio_stats['rebalance_costs'] / 
                                     max(self.portfolio_stats['total_rebalances'], 1)),
                'max_concentration': self.portfolio_stats['max_concentration'],
                'avg_diversification': self.portfolio_stats['avg_diversification'],
            },
            'asset_correlations': correlations,
            'agent_info': {
                'agent_type': self.agent.__class__.__name__,
                'state_dim': getattr(self.agent, 'state_dim', 'unknown'),
                'action_dim': getattr(self.agent, 'action_dim', 'unknown'),
            },
            'environment_info': {
                'env_type': self.env.__class__.__name__,
                'observation_space': str(self.env.observation_space),
                'action_space': str(self.env.action_space),
            }
        }
        
        return report
    
    def get_asset_statistics(self) -> Dict[str, Dict[str, Any]]:
        """获取每个资产的统计信息
        
        Returns:
            资产统计字典
        """
        asset_stats = {}
        
        for asset in self.assets:
            stats = self.asset_trade_stats[asset]
            total_trades = stats['total_trades']
            
            if total_trades == 0:
                asset_stats[asset] = {
                    'total_trades': 0,
                    'win_rate': 0.0,
                    'profit_factor': 0.0,
                    'avg_profit_per_trade': 0.0,
                    'avg_loss_per_trade': 0.0,
                }
            else:
                win_rate = stats['winning_trades'] / total_trades
                profit_factor = stats['total_profit'] / max(stats['total_loss'], 1e-8)
                avg_profit = stats['total_profit'] / max(stats['winning_trades'], 1)
                avg_loss = stats['total_loss'] / max(stats['losing_trades'], 1)
                
                asset_stats[asset] = {
                    'total_trades': total_trades,
                    'winning_trades': stats['winning_trades'],
                    'losing_trades': stats['losing_trades'],
                    'win_rate': win_rate,
                    'profit_factor': profit_factor,
                    'total_profit': stats['total_profit'],
                    'total_loss': stats['total_loss'],
                    'avg_profit_per_trade': avg_profit,
                    'avg_loss_per_trade': avg_loss,
                }
        
        return asset_stats
    
    def get_portfolio_statistics(self) -> Dict[str, Any]:
        """获取投资组合统计信息
        
        Returns:
            投资组合统计字典
        """
        correlations = self._calculate_asset_correlations()
        
        # 计算平均权重
        avg_weights = {}
        if self.weight_history:
            weights_array = np.array(list(self.weight_history))
            for i, asset in enumerate(self.assets):
                avg_weights[asset] = np.mean(weights_array[:, i])
        
        return {
            'total_rebalances': self.portfolio_stats['total_rebalances'],
            'total_rebalance_costs': self.portfolio_stats['rebalance_costs'],
            'avg_rebalance_cost': (self.portfolio_stats['rebalance_costs'] / 
                                 max(self.portfolio_stats['total_rebalances'], 1)),
            'max_concentration': self.portfolio_stats['max_concentration'],
            'avg_diversification': self.portfolio_stats['avg_diversification'],
            'asset_correlations': correlations,
            'avg_weights': avg_weights,
            'assets': self.assets,
        }
    
    def reset_statistics(self):
        """重置所有统计信息"""
        # 重置资产交易统计
        for asset in self.assets:
            self.asset_trade_stats[asset] = {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'total_profit': 0.0,
                'total_loss': 0.0,
                'avg_holding_period': 0.0,
            }
        
        # 重置组合统计
        self.portfolio_stats = {
            'total_rebalances': 0,
            'rebalance_costs': 0.0,
            'max_concentration': 0.0,
            'avg_diversification': 0.0,
        }
        
        # 清空缓冲区
        self.episode_rewards.clear()
        self.episode_returns.clear()
        self.episode_sharpe_ratios.clear()
        self.weight_history.clear()
        
        self.logger.info("多资产统计已重置")