"""Proximal Policy Optimization (PPO) 智能体实现

实现PPO算法用于连续和离散动作空间的强化学习。
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import Normal, Categorical
from typing import Dict, List, Tuple, Any, Optional, Union
import random

from .base import BaseAgent, create_mlp


class PPOActor(nn.Module):
    """PPO演员网络"""
    
    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 hidden_dims: List[int] = [256, 256],
                 activation: str = 'relu',
                 dropout: float = 0.0,
                 action_type: str = 'continuous',
                 log_std_init: float = 0.0):
        """初始化演员网络
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            hidden_dims: 隐藏层维度
            activation: 激活函数
            dropout: Dropout概率
            action_type: 动作类型 ('continuous' 或 'discrete')
            log_std_init: 对数标准差初始值（仅用于连续动作）
        """
        super().__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.action_type = action_type
        
        # 共享网络
        self.shared_net = create_mlp(
            input_dim=state_dim,
            output_dim=hidden_dims[-1],
            hidden_dims=hidden_dims[:-1],
            activation=activation,
            dropout=dropout
        )
        
        if action_type == 'continuous':
            # 连续动作空间
            self.mean_layer = nn.Linear(hidden_dims[-1], action_dim)
            self.log_std = nn.Parameter(torch.ones(action_dim) * log_std_init)
        else:
            # 离散动作空间
            self.action_layer = nn.Linear(hidden_dims[-1], action_dim)
    
    def forward(self, state: torch.Tensor) -> Union[Tuple[torch.Tensor, torch.Tensor], torch.Tensor]:
        """前向传播
        
        Args:
            state: 状态张量
            
        Returns:
            连续动作：(均值, 标准差)
            离散动作：动作概率
        """
        features = self.shared_net(state)
        
        if self.action_type == 'continuous':
            mean = self.mean_layer(features)
            std = torch.exp(self.log_std.expand_as(mean))
            return mean, std
        else:
            action_logits = self.action_layer(features)
            return F.softmax(action_logits, dim=-1)
    
    def get_action_and_log_prob(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """获取动作和对数概率
        
        Args:
            state: 状态张量
            
        Returns:
            (动作, 对数概率)
        """
        if self.action_type == 'continuous':
            mean, std = self.forward(state)
            dist = Normal(mean, std)
            action = dist.sample()
            log_prob = dist.log_prob(action).sum(dim=-1)
            return action, log_prob
        else:
            action_probs = self.forward(state)
            dist = Categorical(action_probs)
            action = dist.sample()
            log_prob = dist.log_prob(action)
            return action, log_prob
    
    def get_log_prob(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """获取给定动作的对数概率
        
        Args:
            state: 状态张量
            action: 动作张量
            
        Returns:
            对数概率
        """
        if self.action_type == 'continuous':
            mean, std = self.forward(state)
            dist = Normal(mean, std)
            return dist.log_prob(action).sum(dim=-1)
        else:
            action_probs = self.forward(state)
            dist = Categorical(action_probs)
            return dist.log_prob(action.squeeze(-1))


class PPOCritic(nn.Module):
    """PPO评论家网络"""
    
    def __init__(self,
                 state_dim: int,
                 hidden_dims: List[int] = [256, 256],
                 activation: str = 'relu',
                 dropout: float = 0.0):
        """初始化评论家网络
        
        Args:
            state_dim: 状态维度
            hidden_dims: 隐藏层维度
            activation: 激活函数
            dropout: Dropout概率
        """
        super().__init__()
        
        self.value_net = create_mlp(
            input_dim=state_dim,
            output_dim=1,
            hidden_dims=hidden_dims,
            activation=activation,
            dropout=dropout
        )
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """前向传播
        
        Args:
            state: 状态张量
            
        Returns:
            状态价值
        """
        return self.value_net(state)


class PPOBuffer:
    """PPO经验缓冲区"""
    
    def __init__(self, capacity: int, state_dim: int, action_dim: int, gamma: float = 0.99, lam: float = 0.95):
        """初始化缓冲区
        
        Args:
            capacity: 缓冲区容量
            state_dim: 状态维度
            action_dim: 动作维度
            gamma: 折扣因子
            lam: GAE参数
        """
        self.capacity = capacity
        self.gamma = gamma
        self.lam = lam
        
        # 缓冲区
        self.states = np.zeros((capacity, state_dim), dtype=np.float32)
        self.actions = np.zeros((capacity, action_dim), dtype=np.float32)
        self.rewards = np.zeros(capacity, dtype=np.float32)
        self.values = np.zeros(capacity, dtype=np.float32)
        self.log_probs = np.zeros(capacity, dtype=np.float32)
        self.dones = np.zeros(capacity, dtype=bool)
        
        self.ptr = 0
        self.size = 0
    
    def add(self, state: np.ndarray, action: np.ndarray, reward: float, 
            value: float, log_prob: float, done: bool):
        """添加经验
        
        Args:
            state: 状态
            action: 动作
            reward: 奖励
            value: 状态价值
            log_prob: 对数概率
            done: 是否结束
        """
        self.states[self.ptr] = state
        self.actions[self.ptr] = action
        self.rewards[self.ptr] = reward
        self.values[self.ptr] = value
        self.log_probs[self.ptr] = log_prob
        self.dones[self.ptr] = done
        
        self.ptr = (self.ptr + 1) % self.capacity
        self.size = min(self.size + 1, self.capacity)
    
    def compute_gae(self, next_value: float = 0.0) -> Tuple[np.ndarray, np.ndarray]:
        """计算GAE优势和回报
        
        Args:
            next_value: 下一状态价值
            
        Returns:
            (优势, 回报)
        """
        advantages = np.zeros(self.size, dtype=np.float32)
        returns = np.zeros(self.size, dtype=np.float32)
        
        gae = 0
        for step in reversed(range(self.size)):
            if step == self.size - 1:
                next_non_terminal = 1.0 - self.dones[step]
                next_val = next_value
            else:
                next_non_terminal = 1.0 - self.dones[step]
                next_val = self.values[step + 1]
            
            delta = self.rewards[step] + self.gamma * next_val * next_non_terminal - self.values[step]
            gae = delta + self.gamma * self.lam * next_non_terminal * gae
            advantages[step] = gae
            returns[step] = gae + self.values[step]
        
        return advantages, returns
    
    def get_batch(self) -> Tuple[np.ndarray, ...]:
        """获取批次数据
        
        Returns:
            批次数据
        """
        return (self.states[:self.size], self.actions[:self.size], 
                self.log_probs[:self.size], self.values[:self.size],
                self.rewards[:self.size], self.dones[:self.size])
    
    def clear(self):
        """清空缓冲区"""
        self.ptr = 0
        self.size = 0


class PPOAgent(BaseAgent):
    """PPO智能体
    
    实现Proximal Policy Optimization算法。
    """
    
    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 learning_rate: float = 3e-4,
                 gamma: float = 0.99,
                 lam: float = 0.95,
                 clip_ratio: float = 0.2,
                 value_coef: float = 0.5,
                 entropy_coef: float = 0.01,
                 max_grad_norm: float = 0.5,
                 ppo_epochs: int = 10,
                 batch_size: int = 64,
                 buffer_size: int = 2048,
                 hidden_dims: List[int] = [256, 256],
                 activation: str = 'relu',
                 dropout: float = 0.0,
                 action_type: str = 'continuous',
                 log_std_init: float = 0.0,
                 device: str = 'auto',
                 **kwargs):
        """初始化PPO智能体
        
        Args:
            state_dim: 状态空间维度
            action_dim: 动作空间维度
            learning_rate: 学习率
            gamma: 折扣因子
            lam: GAE参数
            clip_ratio: PPO裁剪比率
            value_coef: 价值损失系数
            entropy_coef: 熵损失系数
            max_grad_norm: 最大梯度范数
            ppo_epochs: PPO更新轮数
            batch_size: 批次大小
            buffer_size: 缓冲区大小
            hidden_dims: 隐藏层维度
            activation: 激活函数
            dropout: Dropout概率
            action_type: 动作类型
            log_std_init: 对数标准差初始值
            device: 计算设备
            **kwargs: 其他参数
        """
        # PPO特定参数
        self.gamma = gamma
        self.lam = lam
        self.clip_ratio = clip_ratio
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef
        self.max_grad_norm = max_grad_norm
        self.ppo_epochs = ppo_epochs
        self.batch_size = batch_size
        self.buffer_size = buffer_size
        self.hidden_dims = hidden_dims
        self.activation = activation
        self.dropout = dropout
        self.action_type = action_type
        self.log_std_init = log_std_init
        
        # 调用父类初始化
        super().__init__(
            state_dim=state_dim,
            action_dim=action_dim,
            learning_rate=learning_rate,
            device=device,
            **kwargs
        )
        
        # 经验缓冲区
        self.buffer = PPOBuffer(
            capacity=buffer_size,
            state_dim=state_dim,
            action_dim=action_dim if action_type == 'continuous' else 1,
            gamma=gamma,
            lam=lam
        )
        
        # 学习统计
        self.policy_losses = []
        self.value_losses = []
        self.entropy_losses = []
    
    def _build_networks(self):
        """构建神经网络"""
        self.actor = PPOActor(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dims=self.hidden_dims,
            activation=self.activation,
            dropout=self.dropout,
            action_type=self.action_type,
            log_std_init=self.log_std_init
        ).to(self.device)
        
        self.critic = PPOCritic(
            state_dim=self.state_dim,
            hidden_dims=self.hidden_dims,
            activation=self.activation,
            dropout=self.dropout
        ).to(self.device)
    
    def _build_optimizers(self):
        """构建优化器"""
        self.optimizer = torch.optim.Adam(
            list(self.actor.parameters()) + list(self.critic.parameters()),
            lr=self.learning_rate
        )
    
    def act(self, state: np.ndarray, training: bool = True) -> np.ndarray:
        """选择动作
        
        Args:
            state: 当前状态
            training: 是否为训练模式
            
        Returns:
            选择的动作
        """
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            
            if training:
                action, _ = self.actor.get_action_and_log_prob(state_tensor)
            else:
                if self.action_type == 'continuous':
                    mean, _ = self.actor(state_tensor)
                    action = mean
                else:
                    action_probs = self.actor(state_tensor)
                    action = action_probs.argmax(dim=-1, keepdim=True)
            
            if self.action_type == 'continuous':
                return action.squeeze(0).cpu().numpy()
            else:
                return action.squeeze(0).cpu().numpy()
    
    def get_value(self, state: np.ndarray) -> float:
        """获取状态价值
        
        Args:
            state: 状态
            
        Returns:
            状态价值
        """
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            value = self.critic(state_tensor)
            return value.item()
    
    def store_transition(self, state: np.ndarray, action: np.ndarray, 
                        reward: float, done: bool):
        """存储转换
        
        Args:
            state: 状态
            action: 动作
            reward: 奖励
            done: 是否结束
        """
        # 获取状态价值和动作对数概率
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            value = self.critic(state_tensor).item()
            
            action_tensor = torch.FloatTensor(action).unsqueeze(0).to(self.device)
            if self.action_type == 'discrete':
                action_tensor = action_tensor.long()
            log_prob = self.actor.get_log_prob(state_tensor, action_tensor).item()
        
        # 存储到缓冲区
        self.buffer.add(state, action, reward, value, log_prob, done)
    
    def learn(self, next_state: Optional[np.ndarray] = None) -> Dict[str, float]:
        """学习更新
        
        Args:
            next_state: 下一状态（用于计算最后的价值）
            
        Returns:
            学习统计信息
        """
        if self.buffer.size < self.buffer_size:
            return {'policy_loss': 0.0, 'value_loss': 0.0, 'entropy_loss': 0.0}
        
        # 计算下一状态价值
        next_value = 0.0
        if next_state is not None:
            next_value = self.get_value(next_state)
        
        # 计算GAE优势和回报
        advantages, returns = self.buffer.compute_gae(next_value)
        
        # 标准化优势
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # 获取批次数据
        states, actions, old_log_probs, old_values, rewards, dones = self.buffer.get_batch()
        
        # 转换为张量
        states = torch.FloatTensor(states).to(self.device)
        actions = torch.FloatTensor(actions).to(self.device)
        old_log_probs = torch.FloatTensor(old_log_probs).to(self.device)
        advantages = torch.FloatTensor(advantages).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)
        
        if self.action_type == 'discrete':
            actions = actions.long()
        
        # PPO更新
        policy_losses = []
        value_losses = []
        entropy_losses = []
        
        for _ in range(self.ppo_epochs):
            # 随机打乱数据
            indices = torch.randperm(len(states))
            
            for start in range(0, len(states), self.batch_size):
                end = start + self.batch_size
                batch_indices = indices[start:end]
                
                batch_states = states[batch_indices]
                batch_actions = actions[batch_indices]
                batch_old_log_probs = old_log_probs[batch_indices]
                batch_advantages = advantages[batch_indices]
                batch_returns = returns[batch_indices]
                
                # 计算新的对数概率和状态价值
                new_log_probs = self.actor.get_log_prob(batch_states, batch_actions)
                values = self.critic(batch_states).squeeze(-1)
                
                # 计算比率
                ratio = torch.exp(new_log_probs - batch_old_log_probs)
                
                # 计算策略损失
                surr1 = ratio * batch_advantages
                surr2 = torch.clamp(ratio, 1 - self.clip_ratio, 1 + self.clip_ratio) * batch_advantages
                policy_loss = -torch.min(surr1, surr2).mean()
                
                # 计算价值损失
                value_loss = F.mse_loss(values, batch_returns)
                
                # 计算熵损失
                if self.action_type == 'continuous':
                    mean, std = self.actor(batch_states)
                    dist = Normal(mean, std)
                    entropy = dist.entropy().sum(dim=-1).mean()
                else:
                    action_probs = self.actor(batch_states)
                    dist = Categorical(action_probs)
                    entropy = dist.entropy().mean()
                
                entropy_loss = -entropy
                
                # 总损失
                total_loss = policy_loss + self.value_coef * value_loss + self.entropy_coef * entropy_loss
                
                # 反向传播
                self.optimizer.zero_grad()
                total_loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(
                    list(self.actor.parameters()) + list(self.critic.parameters()),
                    self.max_grad_norm
                )
                
                self.optimizer.step()
                
                # 记录损失
                policy_losses.append(policy_loss.item())
                value_losses.append(value_loss.item())
                entropy_losses.append(entropy_loss.item())
        
        # 清空缓冲区
        self.buffer.clear()
        
        # 更新训练步数
        self.training_step += 1
        
        # 记录平均损失
        avg_policy_loss = np.mean(policy_losses)
        avg_value_loss = np.mean(value_losses)
        avg_entropy_loss = np.mean(entropy_losses)
        
        self.policy_losses.append(avg_policy_loss)
        self.value_losses.append(avg_value_loss)
        self.entropy_losses.append(avg_entropy_loss)
        
        return {
            'policy_loss': avg_policy_loss,
            'value_loss': avg_value_loss,
            'entropy_loss': avg_entropy_loss,
            'total_loss': avg_policy_loss + self.value_coef * avg_value_loss + self.entropy_coef * avg_entropy_loss
        }
    
    def _get_save_dict(self) -> Dict[str, Any]:
        """获取保存字典"""
        return {
            'actor_state_dict': self.actor.state_dict(),
            'critic_state_dict': self.critic.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'gamma': self.gamma,
            'lam': self.lam,
            'clip_ratio': self.clip_ratio,
            'value_coef': self.value_coef,
            'entropy_coef': self.entropy_coef,
            'action_type': self.action_type,
        }
    
    def _load_from_dict(self, save_dict: Dict[str, Any]):
        """从保存字典加载"""
        self.actor.load_state_dict(save_dict['actor_state_dict'])
        self.critic.load_state_dict(save_dict['critic_state_dict'])
        self.optimizer.load_state_dict(save_dict['optimizer_state_dict'])
    
    def set_training_mode(self, training: bool = True):
        """设置训练模式"""
        if training:
            self.actor.train()
            self.critic.train()
        else:
            self.actor.eval()
            self.critic.eval()
    
    def get_action_distribution(self, state: np.ndarray) -> Dict[str, Any]:
        """获取动作分布"""
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            
            if self.action_type == 'continuous':
                mean, std = self.actor(state_tensor)
                return {
                    'mean': mean.squeeze(0).cpu().numpy(),
                    'std': std.squeeze(0).cpu().numpy(),
                    'action_type': 'continuous'
                }
            else:
                action_probs = self.actor(state_tensor)
                return {
                    'probabilities': action_probs.squeeze(0).cpu().numpy(),
                    'best_action': action_probs.argmax().item(),
                    'action_type': 'discrete'
                }