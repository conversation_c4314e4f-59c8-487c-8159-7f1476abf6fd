import sys
sys.path.append('.')
from finrl_crypto.environment.crypto_trading import CryptoTradingEnvironment
import pandas as pd
import numpy as np

# 创建测试数据
data = pd.DataFrame({
    'time': ['2023-01-01', '2023-01-02'],
    'tic': ['BTC', 'BTC'],
    'close': [40000, 41000],
    'volume': [100, 110]
})

print("Creating environment...")
env = CryptoTradingEnvironment(data=data, initial_amount=10000)
print("Environment created successfully")

print("Testing step function...")
action = np.array([0.5])
print(f"Action: {action}")
print(f"Action shape: {action.shape}")
print(f"Stock dim: {env.stock_dim}")

try:
    obs, reward, done, info = env.step(action)
    print("Step completed successfully")
except Exception as e:
    print(f"Error during step: {e}")
    import traceback
    traceback.print_exc()