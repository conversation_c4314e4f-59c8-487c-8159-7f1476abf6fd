<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="0" skipped="1" tests="1" time="23.203" timestamp="2025-06-19T02:06:46.190517+09:00" hostname="E-5CG22747W5"><testcase classname="" name="improvetest.test_phase1_integration" time="0.000"><skipped message="collection skipped">('C:\\Users\\<USER>\\OneDrive - <PERSON><PERSON>\\Desktop\\tdccp\\AI4Fin\\FinRL_Crypto\\improvetest\\test_phase1_integration.py', 32, "Skipped: Required modules not available: cannot import name 'Config<PERSON>anager' from 'core.config_manager' (C:\\Users\\<USER>\\OneDrive - <PERSON><PERSON>\\Desktop\\tdccp\\AI4Fin\\FinRL_Crypto\\core\\config_manager.py)")</skipped></testcase></testsuite></testsuites>