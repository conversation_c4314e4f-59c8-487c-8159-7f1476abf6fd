#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入脚本
检查finrl_crypto模块的导入是否正常
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

print("Python路径:")
for i, path in enumerate(sys.path[:5]):
    print(f"  {i}: {path}")

print("\n开始测试导入...")

try:
    print("1. 测试基础模块导入...")
    from finrl_crypto.data.base import DataProcessor
    print("   ✅ DataProcessor 导入成功")
    
    print("2. 测试处理器模块导入...")
    from finrl_crypto.data.processors import BinanceProcessor
    print("   ✅ BinanceProcessor 导入成功")
    
    print("3. 测试指标模块导入...")
    from finrl_crypto.data.indicators import TechnicalIndicators
    print("   ✅ TechnicalIndicators 导入成功")
    
    print("4. 测试管理器模块导入...")
    from finrl_crypto.data.manager import DataManager
    print("   ✅ DataManager 导入成功")
    
    print("\n🎉 所有数据模块导入成功!")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"❌ 其他错误: {e}")
    import traceback
    traceback.print_exc()

print("\n测试完成.")