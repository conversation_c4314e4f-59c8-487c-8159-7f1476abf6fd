"""环境基类模块

定义所有交易环境的通用接口和基础功能。
"""

try:
    import gymnasium as gym
except ImportError:
    try:
        import gym
    except ImportError:
        # 如果都没有安装，创建一个简单的替代
        class MockSpace:
            def __init__(self, *args, **kwargs):
                pass
                
        class MockSpaces:
            Box = MockSpace
            Discrete = MockSpace
            
        class MockEnv:
            def __init__(self):
                pass
                
        class MockGym:
            Env = MockEnv
            spaces = MockSpaces()
            
        gym = MockGym()
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Any, Optional, Union
import logging
from datetime import datetime


class BaseEnvironment(gym.Env, ABC):
    """交易环境基类
    
    定义所有交易环境必须实现的接口。
    """
    
    def __init__(self,
                 data: pd.DataFrame,
                 initial_amount: float = 10000,
                 transaction_cost_pct: float = 0.001,
                 reward_scaling: float = 1e-4,
                 state_space: Optional[int] = None,
                 action_space: Optional[int] = None,
                 tech_indicator_list: Optional[List[str]] = None,
                 turbulence_threshold: Optional[float] = None,
                 risk_indicator_col: str = 'turbulence',
                 make_plots: bool = False,
                 print_verbosity: int = 10,
                 day: int = 0,
                 initial: bool = True,
                 previous_state: Optional[List] = None,
                 model_name: str = '',
                 mode: str = '',
                 iteration: str = '',
                 **kwargs):
        """初始化环境基类
        
        Args:
            data: 交易数据
            initial_amount: 初始资金
            transaction_cost_pct: 交易成本百分比
            reward_scaling: 奖励缩放因子
            state_space: 状态空间维度
            action_space: 动作空间维度
            tech_indicator_list: 技术指标列表
            turbulence_threshold: 波动阈值
            risk_indicator_col: 风险指标列名
            make_plots: 是否生成图表
            print_verbosity: 打印详细程度
            day: 当前交易日
            initial: 是否为初始状态
            previous_state: 前一状态
            model_name: 模型名称
            mode: 模式
            iteration: 迭代次数
            **kwargs: 其他参数
        """
        super().__init__()
        
        # 基础参数
        self.data = data.copy()
        self.initial_amount = initial_amount
        self.transaction_cost_pct = transaction_cost_pct
        self.reward_scaling = reward_scaling
        self.tech_indicator_list = tech_indicator_list or []
        self.turbulence_threshold = turbulence_threshold
        self.risk_indicator_col = risk_indicator_col
        self.make_plots = make_plots
        self.print_verbosity = print_verbosity
        self.model_name = model_name
        self.mode = mode
        self.iteration = iteration
        
        # 状态管理
        self.day = day
        self.initial = initial
        self.previous_state = previous_state
        
        # 日志
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 数据预处理
        self._preprocess_data()
        
        # 环境状态
        self.terminal = False
        self.turbulence = 0
        
        # 交易记录
        self.asset_memory = [self.initial_amount]
        self.portfolio_return_memory = [0]
        self.actions_memory = []
        self.date_memory = [self._get_date()]
        
        # 设置动作和观察空间
        self._setup_spaces(state_space, action_space)
        
    def _preprocess_data(self):
        """预处理数据"""
        # 确保数据按时间和股票排序
        if 'time' in self.data.columns and 'tic' in self.data.columns:
            self.data = self.data.sort_values(['time', 'tic']).reset_index(drop=True)
        
        # 获取股票列表
        if 'tic' in self.data.columns:
            self.stock_dim = len(self.data.tic.unique())
            self.tic_list = list(self.data.tic.unique())
        else:
            self.stock_dim = 1
            self.tic_list = ['ASSET']
        
        # 获取交易日列表
        if 'time' in self.data.columns:
            self.unique_trade_date = self.data['time'].unique()
        else:
            self.unique_trade_date = self.data.index.unique()
        
        self.max_step = len(self.unique_trade_date) - 1
        
        # 验证数据完整性
        self._validate_data()
        
    def _validate_data(self):
        """验证数据完整性"""
        required_columns = ['close']
        missing_columns = [col for col in required_columns if col not in self.data.columns]
        
        if missing_columns:
            raise ValueError(f"数据缺少必需列: {missing_columns}")
        
        if self.data.empty:
            raise ValueError("数据为空")
        
        # 检查技术指标
        missing_indicators = [ind for ind in self.tech_indicator_list 
                            if ind not in self.data.columns]
        if missing_indicators:
            self.logger.warning(f"数据中缺少技术指标: {missing_indicators}")
            # 从列表中移除缺失的指标
            self.tech_indicator_list = [ind for ind in self.tech_indicator_list 
                                      if ind in self.data.columns]
    
    @abstractmethod
    def _setup_spaces(self, state_space: Optional[int], action_space: Optional[int]):
        """设置动作和观察空间
        
        Args:
            state_space: 状态空间维度
            action_space: 动作空间维度
        """
        pass
    
    @abstractmethod
    def _get_state(self) -> np.ndarray:
        """获取当前状态
        
        Returns:
            当前状态数组
        """
        pass
    
    @abstractmethod
    def _calculate_reward(self, action: np.ndarray) -> float:
        """计算奖励
        
        Args:
            action: 执行的动作
            
        Returns:
            奖励值
        """
        pass
    
    @abstractmethod
    def _execute_action(self, action: np.ndarray) -> Dict[str, Any]:
        """执行动作
        
        Args:
            action: 要执行的动作
            
        Returns:
            执行结果信息
        """
        pass
    
    def reset(self, seed: Optional[int] = None, **kwargs) -> np.ndarray:
        """重置环境
        
        Args:
            seed: 随机种子
            **kwargs: 其他参数
            
        Returns:
            初始状态
        """
        if seed is not None:
            np.random.seed(seed)
        
        # 重置状态
        self.day = 0
        self.terminal = False
        self.turbulence = 0
        
        # 重置记录
        self.asset_memory = [self.initial_amount]
        self.portfolio_return_memory = [0]
        self.actions_memory = []
        self.date_memory = [self._get_date()]
        
        # 重置特定于子类的状态
        self._reset_specific_state()
        
        return self._get_state()
    
    @abstractmethod
    def _reset_specific_state(self):
        """重置特定于子类的状态"""
        pass
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """执行一步
        
        Args:
            action: 要执行的动作
            
        Returns:
            (下一状态, 奖励, 是否结束, 信息字典)
        """
        # 检查是否已结束
        if self.terminal:
            raise RuntimeError("环境已结束，请先调用reset()")
        
        # 执行动作
        execution_info = self._execute_action(action)
        
        # 计算奖励
        reward = self._calculate_reward(action)
        
        # 更新状态
        self.day += 1
        self.terminal = self.day >= self.max_step
        
        # 记录动作
        self.actions_memory.append(action)
        
        # 获取下一状态
        next_state = self._get_state()
        
        # 构建信息字典
        info = {
            'day': self.day,
            'terminal': self.terminal,
            'total_asset': self.asset_memory[-1],
            'portfolio_return': self.portfolio_return_memory[-1],
            'date': self._get_date(),
            **execution_info
        }
        
        # 打印信息
        if self.print_verbosity > 0 and self.day % self.print_verbosity == 0:
            self._print_progress(info)
        
        return next_state, reward, self.terminal, info
    
    def _get_date(self) -> str:
        """获取当前日期
        
        Returns:
            当前日期字符串
        """
        if self.day < len(self.unique_trade_date):
            date_item = self.unique_trade_date[self.day]
            if isinstance(date_item, str):
                return date_item
            elif hasattr(date_item, 'strftime'):
                return date_item.strftime('%Y-%m-%d')
            else:
                # 如果是其他类型（如int），转换为字符串
                return str(date_item)
        else:
            return 'END'
    
    def _get_current_data(self) -> pd.DataFrame:
        """获取当前时间步的数据
        
        Returns:
            当前时间步的数据
        """
        if self.day >= len(self.unique_trade_date):
            return pd.DataFrame()
        
        current_date = self.unique_trade_date[self.day]
        
        if 'time' in self.data.columns:
            current_data = self.data[self.data['time'] == current_date]
        else:
            current_data = self.data.iloc[self.day:self.day+1]
        
        return current_data.reset_index(drop=True)
    
    def _update_turbulence(self):
        """更新波动指标"""
        if self.risk_indicator_col in self.data.columns:
            current_data = self._get_current_data()
            if not current_data.empty and self.risk_indicator_col in current_data.columns:
                self.turbulence = current_data[self.risk_indicator_col].iloc[0]
        
    def _print_progress(self, info: Dict[str, Any]):
        """打印进度信息
        
        Args:
            info: 信息字典
        """
        self.logger.info(
            f"Day: {info['day']}, "
            f"Date: {info['date']}, "
            f"Total Asset: ${info['total_asset']:.2f}, "
            f"Return: {info['portfolio_return']:.4f}"
        )
    
    def get_portfolio_history(self) -> pd.DataFrame:
        """获取投资组合历史
        
        Returns:
            投资组合历史DataFrame
        """
        history_df = pd.DataFrame({
            'date': self.date_memory,
            'total_asset': self.asset_memory,
            'portfolio_return': self.portfolio_return_memory
        })
        
        # 添加动作历史（如果有）
        if self.actions_memory:
            actions_df = pd.DataFrame(self.actions_memory)
            actions_df.columns = [f'action_{i}' for i in range(actions_df.shape[1])]
            history_df = pd.concat([history_df, actions_df], axis=1)
        
        return history_df
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标
        
        Returns:
            性能指标字典
        """
        if len(self.asset_memory) < 2:
            return {}
        
        returns = np.array(self.portfolio_return_memory[1:])
        
        metrics = {
            'total_return': (self.asset_memory[-1] - self.initial_amount) / self.initial_amount,
            'annualized_return': np.mean(returns) * 252,  # 假设252个交易日
            'volatility': np.std(returns) * np.sqrt(252),
            'sharpe_ratio': np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0,
            'max_drawdown': self._calculate_max_drawdown(),
            'final_asset': self.asset_memory[-1]
        }
        
        return metrics
    
    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤
        
        Returns:
            最大回撤值
        """
        asset_values = np.array(self.asset_memory)
        peak = np.maximum.accumulate(asset_values)
        drawdown = (asset_values - peak) / peak
        return np.min(drawdown)
    
    def render(self, mode: str = 'human') -> Optional[np.ndarray]:
        """渲染环境
        
        Args:
            mode: 渲染模式
            
        Returns:
            渲染结果（如果适用）
        """
        if mode == 'human':
            print(f"Day: {self.day}, Total Asset: ${self.asset_memory[-1]:.2f}")
        elif mode == 'rgb_array':
            # 可以在这里实现图形渲染
            pass
        
        return None
    
    def close(self):
        """关闭环境"""
        pass
    
    def seed(self, seed: Optional[int] = None) -> List[int]:
        """设置随机种子
        
        Args:
            seed: 随机种子
            
        Returns:
            种子列表
        """
        if seed is not None:
            np.random.seed(seed)
        return [seed]