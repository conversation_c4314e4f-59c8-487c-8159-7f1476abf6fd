# FinRL-Crypto 默认配置文件
# 这个文件展示了如何使用YAML格式管理配置

# 基础设置
seed: 2390408
timeframe: '5m'

# 优化设置
h_trials: 50
kcv_groups: 5
k_test_groups: 2
num_paths: 4

# 数据设置
trade_start_date: '2022-04-30 00:00:00'
trade_end_date: '2022-06-27 00:00:00'
no_candles_for_train: 20000
no_candles_for_val: 5000

# 交易对设置
ticker_list:
  - 'AAVEUSDT'
  - 'AVAXUSDT'
  - 'BTCUSDT'
  - 'NEARUSDT'
  - 'LINKUSDT'
  - 'ETHUSDT'
  - 'LTCUSDT'
  - 'MATICUSDT'
  - 'UNIUSDT'
  - 'SOLUSDT'

# 最小购买限制
alpaca_limits:
  - 0.01
  - 0.10
  - 0.0001
  - 0.1
  - 0.1
  - 0.001
  - 0.01
  - 10
  - 0.1
  - 0.01

# 技术指标
technical_indicators:
  - 'open'
  - 'high'
  - 'low'
  - 'close'
  - 'volume'
  - 'macd'
  - 'macd_signal'
  - 'macd_hist'
  - 'rsi'
  - 'cci'
  - 'dx'

# 时间框架映射（分钟）
timeframe_minutes:
  '1m': 1
  '5m': 5
  '10m': 10
  '30m': 30
  '1h': 60
  '2h': 120
  '4h': 240
  '12h': 720

# 环境设置
environment:
  initial_capital: 1000000
  buy_cost_pct: 0.003
  sell_cost_pct: 0.003
  gamma: 0.99
  if_log: false

# ElegantRL 默认参数
elegantrl:
  learning_rate_range: [0.0001, 0.001]
  batch_size_range: [128, 512]
  target_step_range: [1000, 5000]
  repeat_times_range: [1, 4]
  net_dimension_range: [128, 512]
  break_step: 5e5
  if_allow_break: true
  if_remove: true
  random_seed: 0

# 日志设置
logging:
  level: 'INFO'
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  file_path: 'logs/finrl_crypto.log'
  max_file_size: 10485760  # 10MB
  backup_count: 5