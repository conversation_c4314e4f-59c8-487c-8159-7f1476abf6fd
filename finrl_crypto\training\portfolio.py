"""投资组合训练器模块

专门用于投资组合管理的强化学习训练。
"""

import time
from typing import Dict, Any, Optional, List, Tuple
import numpy as np
import pandas as pd
from collections import deque

from .base import BaseTrainer, TrainingConfig, EvaluationMetrics
from ..agent.base import BaseAgent
from ..environment.base import BaseEnvironment


class PortfolioTrainer(BaseTrainer):
    """投资组合训练器
    
    专门用于投资组合管理的强化学习训练，关注风险调整后的收益优化。
    """
    
    def __init__(self,
                 agent: BaseAgent,
                 env: BaseEnvironment,
                 config: Optional[TrainingConfig] = None,
                 save_path: str = "./models",
                 experiment_name: str = "portfolio_experiment",
                 benchmark_weights: Optional[np.ndarray] = None):
        """初始化投资组合训练器
        
        Args:
            agent: 智能体
            env: 投资组合环境
            config: 训练配置
            save_path: 模型保存路径
            experiment_name: 实验名称
            benchmark_weights: 基准权重（等权重或市值权重）
        """
        super().__init__(agent, env, config, save_path, experiment_name)
        
        # 投资组合特定属性
        self.initial_balance = None  # 初始资金
        self.max_position_size = None  # 最大仓位大小
        
        # 获取资产信息
        self.assets = getattr(env, 'assets', ['BTC', 'ETH', 'ADA', 'DOT'])
        try:
            self.n_assets = len(self.assets)
        except TypeError:
            # 处理Mock对象或其他没有len()的对象
            self.n_assets = 4  # 默认值
        
        # 基准权重
        if benchmark_weights is None:
            self.benchmark_weights = np.ones(self.n_assets) / self.n_assets  # 等权重
        else:
            self.benchmark_weights = benchmark_weights
        
        # 投资组合特定的训练历史
        self.training_history.update({
            'portfolio_values': [],
            'portfolio_weights': [],
            'portfolio_returns': [],
            'portfolio_volatility': [],
            'sharpe_ratios': [],
            'sortino_ratios': [],
            'max_drawdowns': [],
            'calmar_ratios': [],
            'information_ratios': [],
            'tracking_errors': [],
            'turnover_rates': [],
            'transaction_costs': [],
        })
        
        # 投资组合统计
        self.portfolio_stats = {
            'total_rebalances': 0,
            'total_transaction_costs': 0.0,
            'avg_turnover_rate': 0.0,
            'max_weight_concentration': 0.0,
            'min_weight_concentration': 1.0,
            'avg_num_positions': 0.0,
            'best_sharpe_ratio': -np.inf,
            'worst_drawdown': 0.0,
        }
        
        # 风险指标
        self.risk_metrics = {
            'var_95': 0.0,  # 95% VaR
            'cvar_95': 0.0,  # 95% CVaR
            'downside_deviation': 0.0,
            'upside_capture': 0.0,
            'downside_capture': 0.0,
            'beta': 0.0,
            'alpha': 0.0,
        }
        
        # 回合缓冲区
        self.episode_rewards = deque(maxlen=100)
        self.episode_returns = deque(maxlen=100)
        self.episode_sharpe_ratios = deque(maxlen=100)
        self.episode_max_drawdowns = deque(maxlen=100)
        
        # 权重和回报历史
        self.weight_history = deque(maxlen=2000)
        self.return_history = deque(maxlen=2000)
        
    def train(self) -> Dict[str, Any]:
        """训练智能体
        
        Returns:
            训练结果字典
        """
        self.logger.info(f"开始投资组合训练 - 资产: {self.assets}, 总时间步数: {self.config.total_timesteps}")
        start_time = time.time()
        
        # 重置环境
        obs = self.env.reset()
        episode_reward = 0.0
        episode_length = 0
        episode_start_value = self.env.get_portfolio_value()
        
        while self.current_timestep < self.config.total_timesteps:
            # 选择动作
            if self.current_timestep < self.config.learning_starts:
                # 随机探索阶段
                action = self.env.action_space.sample()
            else:
                action = self.agent.select_action(obs, training=True)
            
            # 执行动作
            next_obs, reward, done, info = self.env.step(action)
            
            # 存储经验
            if hasattr(self.agent, 'store_transition'):
                self.agent.store_transition(obs, action, reward, next_obs, done)
            
            # 更新统计
            episode_reward += reward
            episode_length += 1
            self.current_timestep += 1
            
            # 记录权重和回报历史
            if hasattr(self.env, 'get_portfolio_weights'):
                weights = self.env.get_portfolio_weights()
                self.weight_history.append(weights)
            
            if 'portfolio_return' in info:
                self.return_history.append(info['portfolio_return'])
            
            # 学习更新
            if (self.current_timestep >= self.config.learning_starts and 
                self.current_timestep % self.config.train_freq == 0):
                
                for _ in range(self.config.gradient_steps):
                    loss_info = self.agent.learn()
                    if loss_info and 'loss' in loss_info:
                        self.training_history['losses'].append(loss_info['loss'])
            
            # 目标网络更新（如果适用）
            if (hasattr(self.agent, 'update_target_network') and 
                self.current_timestep % self.config.target_update_interval == 0):
                self.agent.update_target_network()
            
            # 回合结束处理
            if done:
                # 计算回合统计
                episode_end_value = self.env.get_portfolio_value()
                episode_return = (episode_end_value - episode_start_value) / episode_start_value
                
                # 计算风险调整指标
                portfolio_metrics = self._calculate_episode_metrics(info)
                
                # 更新历史记录
                self.episode_rewards.append(episode_reward)
                self.episode_returns.append(episode_return)
                self.episode_sharpe_ratios.append(portfolio_metrics['sharpe_ratio'])
                self.episode_max_drawdowns.append(portfolio_metrics['max_drawdown'])
                
                self.training_history['rewards'].append(episode_reward)
                self.training_history['returns'].append(episode_return)
                self.training_history['sharpe_ratios'].append(portfolio_metrics['sharpe_ratio'])
                self.training_history['max_drawdowns'].append(portfolio_metrics['max_drawdown'])
                self.training_history['timesteps'].append(self.current_timestep)
                
                # 更新投资组合统计
                self._update_portfolio_stats(info, portfolio_metrics)
                
                # 记录回合信息
                if self._should_log():
                    self._log_episode_info(episode_reward, episode_return, episode_length, portfolio_metrics)
                
                # 重置环境
                obs = self.env.reset()
                episode_reward = 0.0
                episode_length = 0
                episode_start_value = self.env.get_portfolio_value()
                self.current_episode += 1
            else:
                obs = next_obs
            
            # 评估
            if self._should_evaluate():
                eval_metrics = self.evaluate()
                self.training_history['eval_rewards'].append(eval_metrics.mean_reward)
                self.training_history['eval_timesteps'].append(self.current_timestep)
                
                # 早停检查（基于夏普比率）
                if self.config.early_stopping_patience > 0:
                    if self._check_early_stopping(eval_metrics.sharpe_ratio):
                        self.logger.info(f"早停触发 - 在时间步 {self.current_timestep}")
                        break
                
                # 保存最佳模型（基于夏普比率）
                if self.config.save_best_model and eval_metrics.sharpe_ratio > self.portfolio_stats['best_sharpe_ratio']:
                    self.portfolio_stats['best_sharpe_ratio'] = eval_metrics.sharpe_ratio
                    self.save_model()
            
            # 定期保存
            if self._should_save():
                save_path = f"{self.save_path}/{self.experiment_name}_step_{self.current_timestep}.pth"
                self.save_model(save_path)
        
        # 训练结束
        training_time = time.time() - start_time
        
        # 最终评估
        final_eval = self.evaluate()
        
        # 计算最终风险指标
        self._calculate_final_risk_metrics()
        
        # 保存训练历史
        self.save_training_history()
        
        # 生成训练报告
        training_results = self._generate_training_report(training_time, final_eval)
        
        self.logger.info(f"投资组合训练完成 - 总时间: {training_time:.2f}秒")
        
        return training_results
    
    def evaluate(self, n_episodes: Optional[int] = None) -> EvaluationMetrics:
        """评估智能体
        
        Args:
            n_episodes: 评估回合数
            
        Returns:
            评估指标
        """
        if n_episodes is None:
            n_episodes = self.config.n_eval_episodes
        
        self.logger.info(f"开始投资组合评估 - {n_episodes} 回合")
        start_time = time.time()
        
        # 设置为评估模式
        self.agent.set_training_mode(False)
        
        episode_rewards = []
        episode_lengths = []
        episode_returns = []
        portfolio_values = []
        sharpe_ratios = []
        max_drawdowns = []
        sortino_ratios = []
        calmar_ratios = []
        information_ratios = []
        
        for episode in range(n_episodes):
            obs = self.eval_env.reset()
            episode_reward = 0.0
            episode_length = 0
            start_value = self.eval_env.get_portfolio_value()
            
            episode_portfolio_values = [start_value]
            episode_weights = []
            episode_returns_list = []
            
            done = False
            while not done:
                action = self.agent.select_action(obs, training=False)
                obs, reward, done, info = self.eval_env.step(action)
                
                episode_reward += reward
                episode_length += 1
                
                # 记录组合价值、权重和回报
                current_value = self.eval_env.get_portfolio_value()
                episode_portfolio_values.append(current_value)
                
                if hasattr(self.eval_env, 'get_portfolio_weights'):
                    weights = self.eval_env.get_portfolio_weights()
                    episode_weights.append(weights)
                
                if 'portfolio_return' in info:
                    episode_returns_list.append(info['portfolio_return'])
            
            end_value = self.eval_env.get_portfolio_value()
            episode_return = (end_value - start_value) / start_value
            
            # 计算回合指标
            if len(episode_portfolio_values) > 1:
                returns = np.diff(episode_portfolio_values) / episode_portfolio_values[:-1]
                
                sharpe_ratio = self._calculate_sharpe_ratio(returns)
                max_drawdown = self._calculate_max_drawdown(episode_portfolio_values)
                sortino_ratio = self._calculate_sortino_ratio(returns)
                calmar_ratio = self._calculate_calmar_ratio(returns, max_drawdown)
                
                # 计算相对于基准的信息比率
                if len(episode_returns_list) > 0:
                    benchmark_returns = self._calculate_benchmark_returns(len(episode_returns_list))
                    information_ratio = self._calculate_information_ratio(episode_returns_list, benchmark_returns)
                else:
                    information_ratio = 0.0
                
                sharpe_ratios.append(sharpe_ratio)
                max_drawdowns.append(max_drawdown)
                sortino_ratios.append(sortino_ratio)
                calmar_ratios.append(calmar_ratio)
                information_ratios.append(information_ratio)
            
            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)
            episode_returns.append(episode_return)
            portfolio_values.append(end_value)
        
        # 恢复训练模式
        self.agent.set_training_mode(True)
        
        # 计算评估指标
        eval_time = time.time() - start_time
        
        metrics = EvaluationMetrics(
            episode_rewards=episode_rewards,
            episode_lengths=episode_lengths,
            mean_reward=np.mean(episode_rewards),
            std_reward=np.std(episode_rewards),
            min_reward=np.min(episode_rewards),
            max_reward=np.max(episode_rewards),
            total_return=np.mean(episode_returns),
            evaluation_time=eval_time,
            timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
        )
        
        # 计算投资组合相关指标
        if episode_returns:
            returns_array = np.array(episode_returns)
            metrics.sharpe_ratio = np.mean(sharpe_ratios) if sharpe_ratios else 0.0
            metrics.max_drawdown = np.mean(max_drawdowns) if max_drawdowns else 0.0
            metrics.volatility = np.std(returns_array)
            metrics.win_rate = np.sum(returns_array > 0) / len(returns_array)
        
        self.logger.info(f"投资组合评估完成 - 平均奖励: {metrics.mean_reward:.4f}, "
                        f"平均回报: {metrics.total_return:.4f}, "
                        f"夏普比率: {metrics.sharpe_ratio:.4f}, "
                        f"最大回撤: {metrics.max_drawdown:.4f}")
        
        return metrics
    
    def _calculate_episode_metrics(self, info: Dict[str, Any]) -> Dict[str, float]:
        """计算回合指标
        
        Args:
            info: 环境返回的信息
            
        Returns:
            指标字典
        """
        metrics = {
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'sortino_ratio': 0.0,
            'calmar_ratio': 0.0,
            'volatility': 0.0,
            'turnover_rate': 0.0,
        }
        
        if 'portfolio_history' in info:
            portfolio_values = info['portfolio_history']
            if len(portfolio_values) > 1:
                returns = np.diff(portfolio_values) / portfolio_values[:-1]
                
                metrics['sharpe_ratio'] = self._calculate_sharpe_ratio(returns)
                metrics['max_drawdown'] = self._calculate_max_drawdown(portfolio_values)
                metrics['sortino_ratio'] = self._calculate_sortino_ratio(returns)
                metrics['calmar_ratio'] = self._calculate_calmar_ratio(returns, metrics['max_drawdown'])
                metrics['volatility'] = np.std(returns)
        
        if 'turnover_rate' in info:
            metrics['turnover_rate'] = info['turnover_rate']
        
        return metrics
    
    def _update_portfolio_stats(self, info: Dict[str, Any], metrics: Dict[str, float]):
        """更新投资组合统计
        
        Args:
            info: 环境返回的信息
            metrics: 计算的指标
        """
        # 更新基本统计
        if 'rebalance_count' in info:
            self.portfolio_stats['total_rebalances'] += info['rebalance_count']
        
        if 'transaction_cost' in info:
            self.portfolio_stats['total_transaction_costs'] += info['transaction_cost']
        
        if 'turnover_rate' in info:
            current_avg = self.portfolio_stats['avg_turnover_rate']
            new_avg = (current_avg * (self.current_episode - 1) + info['turnover_rate']) / self.current_episode
            self.portfolio_stats['avg_turnover_rate'] = new_avg
        
        # 更新权重集中度
        if hasattr(self.env, 'get_portfolio_weights'):
            weights = self.env.get_portfolio_weights()
            max_weight = np.max(weights)
            min_weight = np.min(weights[weights > 0])  # 排除零权重
            
            self.portfolio_stats['max_weight_concentration'] = max(
                self.portfolio_stats['max_weight_concentration'], max_weight
            )
            
            if min_weight < self.portfolio_stats['min_weight_concentration']:
                self.portfolio_stats['min_weight_concentration'] = min_weight
            
            # 平均持仓数量
            num_positions = np.sum(weights > 0.01)  # 权重大于1%的资产
            current_avg = self.portfolio_stats['avg_num_positions']
            new_avg = (current_avg * (self.current_episode - 1) + num_positions) / self.current_episode
            self.portfolio_stats['avg_num_positions'] = new_avg
        
        # 更新最佳夏普比率和最差回撤
        if metrics['sharpe_ratio'] > self.portfolio_stats['best_sharpe_ratio']:
            self.portfolio_stats['best_sharpe_ratio'] = metrics['sharpe_ratio']
        
        if metrics['max_drawdown'] > self.portfolio_stats['worst_drawdown']:
            self.portfolio_stats['worst_drawdown'] = metrics['max_drawdown']
    
    def _log_episode_info(self, reward: float, return_rate: float, length: int, metrics: Dict[str, float]):
        """记录回合信息
        
        Args:
            reward: 回合奖励
            return_rate: 回合回报率
            length: 回合长度
            metrics: 计算的指标
        """
        avg_reward = np.mean(self.episode_rewards) if self.episode_rewards else 0
        avg_return = np.mean(self.episode_returns) if self.episode_returns else 0
        avg_sharpe = np.mean(self.episode_sharpe_ratios) if self.episode_sharpe_ratios else 0
        avg_drawdown = np.mean(self.episode_max_drawdowns) if self.episode_max_drawdowns else 0
        
        info = {
            'episode': self.current_episode,
            'reward': reward,
            'return': return_rate * 100,
            'length': length,
            'sharpe': metrics['sharpe_ratio'],
            'drawdown': metrics['max_drawdown'] * 100,
            'volatility': metrics['volatility'] * 100,
            'avg_reward_100': avg_reward,
            'avg_return_100': avg_return * 100,
            'avg_sharpe_100': avg_sharpe,
            'avg_drawdown_100': avg_drawdown * 100,
        }
        
        self._log_training_info(info)
    
    def _calculate_sharpe_ratio(self, returns: np.ndarray, risk_free_rate: float = 0.0) -> float:
        """计算夏普比率"""
        if len(returns) == 0 or np.std(returns) == 0:
            return 0.0
        
        excess_returns = returns - risk_free_rate
        return np.mean(excess_returns) / np.std(returns)
    
    def _calculate_sortino_ratio(self, returns: np.ndarray, risk_free_rate: float = 0.0) -> float:
        """计算索提诺比率"""
        if len(returns) == 0:
            return 0.0
        
        excess_returns = returns - risk_free_rate
        downside_returns = excess_returns[excess_returns < 0]
        
        if len(downside_returns) == 0:
            return np.inf if np.mean(excess_returns) > 0 else 0.0
        
        downside_deviation = np.std(downside_returns)
        if downside_deviation == 0:
            return 0.0
        
        return np.mean(excess_returns) / downside_deviation
    
    def _calculate_calmar_ratio(self, returns: np.ndarray, max_drawdown: float) -> float:
        """计算卡尔马比率"""
        if len(returns) == 0 or max_drawdown == 0:
            return 0.0
        
        annual_return = np.mean(returns) * 252  # 假设252个交易日
        return annual_return / max_drawdown
    
    def _calculate_max_drawdown(self, portfolio_values: List[float]) -> float:
        """计算最大回撤"""
        if not portfolio_values:
            return 0.0
        
        values = np.array(portfolio_values)
        peak = np.maximum.accumulate(values)
        drawdown = (peak - values) / peak
        return np.max(drawdown)
    
    def _calculate_information_ratio(self, portfolio_returns: List[float], benchmark_returns: List[float]) -> float:
        """计算信息比率"""
        if len(portfolio_returns) != len(benchmark_returns) or len(portfolio_returns) == 0:
            return 0.0
        
        excess_returns = np.array(portfolio_returns) - np.array(benchmark_returns)
        tracking_error = np.std(excess_returns)
        
        if tracking_error == 0:
            return 0.0
        
        return np.mean(excess_returns) / tracking_error
    
    def _calculate_benchmark_returns(self, n_periods: int) -> List[float]:
        """计算基准回报
        
        Args:
            n_periods: 期数
            
        Returns:
            基准回报列表
        """
        # 简化实现：假设基准是等权重组合的固定回报
        # 实际应用中应该使用真实的基准数据
        return [0.0001] * n_periods  # 假设基准日回报率为0.01%
    
    def _calculate_final_risk_metrics(self):
        """计算最终风险指标"""
        if len(self.return_history) < 10:
            return
        
        returns = np.array(list(self.return_history))
        
        # VaR和CVaR
        self.risk_metrics['var_95'] = np.percentile(returns, 5)
        self.risk_metrics['cvar_95'] = np.mean(returns[returns <= self.risk_metrics['var_95']])
        
        # 下行偏差
        downside_returns = returns[returns < 0]
        self.risk_metrics['downside_deviation'] = np.std(downside_returns) if len(downside_returns) > 0 else 0.0
        
        # 简化的Beta和Alpha计算（需要基准数据）
        benchmark_returns = np.array([0.0001] * len(returns))  # 简化基准
        if np.std(benchmark_returns) > 0:
            self.risk_metrics['beta'] = np.cov(returns, benchmark_returns)[0, 1] / np.var(benchmark_returns)
            self.risk_metrics['alpha'] = np.mean(returns) - self.risk_metrics['beta'] * np.mean(benchmark_returns)
    
    def _generate_training_report(self, training_time: float, final_eval: EvaluationMetrics) -> Dict[str, Any]:
        """生成训练报告"""
        # 基础统计
        total_episodes = len(self.training_history['rewards'])
        avg_episode_reward = np.mean(self.training_history['rewards']) if self.training_history['rewards'] else 0
        avg_episode_return = np.mean(self.training_history['returns']) if self.training_history['returns'] else 0
        avg_sharpe_ratio = np.mean(self.training_history['sharpe_ratios']) if self.training_history['sharpe_ratios'] else 0
        avg_max_drawdown = np.mean(self.training_history['max_drawdowns']) if self.training_history['max_drawdowns'] else 0
        
        # 计算平均权重
        avg_weights = {}
        if self.weight_history:
            weights_array = np.array(list(self.weight_history))
            for i, asset in enumerate(self.assets):
                avg_weights[asset] = np.mean(weights_array[:, i])
        
        report = {
            'training_info': {
                'total_timesteps': self.current_timestep,
                'total_episodes': total_episodes,
                'training_time': training_time,
                'timesteps_per_second': self.current_timestep / training_time,
                'assets': self.assets,
                'n_assets': self.n_assets,
                'benchmark_weights': self.benchmark_weights.tolist(),
            },
            'training_performance': {
                'avg_episode_reward': avg_episode_reward,
                'avg_episode_return': avg_episode_return,
                'avg_sharpe_ratio': avg_sharpe_ratio,
                'avg_max_drawdown': avg_max_drawdown,
                'best_sharpe_ratio': self.portfolio_stats['best_sharpe_ratio'],
                'worst_drawdown': self.portfolio_stats['worst_drawdown'],
            },
            'final_evaluation': final_eval.to_dict(),
            'portfolio_stats': {
                'total_rebalances': self.portfolio_stats['total_rebalances'],
                'total_transaction_costs': self.portfolio_stats['total_transaction_costs'],
                'avg_turnover_rate': self.portfolio_stats['avg_turnover_rate'],
                'max_weight_concentration': self.portfolio_stats['max_weight_concentration'],
                'min_weight_concentration': self.portfolio_stats['min_weight_concentration'],
                'avg_num_positions': self.portfolio_stats['avg_num_positions'],
                'avg_weights': avg_weights,
            },
            'risk_metrics': self.risk_metrics,
            'agent_info': {
                'agent_type': self.agent.__class__.__name__,
                'state_dim': getattr(self.agent, 'state_dim', 'unknown'),
                'action_dim': getattr(self.agent, 'action_dim', 'unknown'),
            },
            'environment_info': {
                'env_type': self.env.__class__.__name__,
                'observation_space': str(self.env.observation_space),
                'action_space': str(self.env.action_space),
            }
        }
        
        return report
    
    def get_portfolio_statistics(self) -> Dict[str, Any]:
        """获取投资组合统计信息"""
        # 计算平均权重
        avg_weights = {}
        weight_volatility = {}
        if self.weight_history:
            weights_array = np.array(list(self.weight_history))
            for i, asset in enumerate(self.assets):
                avg_weights[asset] = np.mean(weights_array[:, i])
                weight_volatility[asset] = np.std(weights_array[:, i])
        
        return {
            **self.portfolio_stats,
            'avg_weights': avg_weights,
            'weight_volatility': weight_volatility,
            'benchmark_weights': {asset: weight for asset, weight in zip(self.assets, self.benchmark_weights)},
            'risk_metrics': self.risk_metrics,
        }
    
    def get_risk_metrics(self) -> Dict[str, float]:
        """获取风险指标"""
        return self.risk_metrics.copy()
    
    def reset_statistics(self):
        """重置所有统计信息"""
        # 重置投资组合统计
        self.portfolio_stats = {
            'total_rebalances': 0,
            'total_transaction_costs': 0.0,
            'avg_turnover_rate': 0.0,
            'max_weight_concentration': 0.0,
            'min_weight_concentration': 1.0,
            'avg_num_positions': 0.0,
            'best_sharpe_ratio': -np.inf,
            'worst_drawdown': 0.0,
        }
        
        # 重置风险指标
        self.risk_metrics = {
            'var_95': 0.0,
            'cvar_95': 0.0,
            'downside_deviation': 0.0,
            'upside_capture': 0.0,
            'downside_capture': 0.0,
            'beta': 0.0,
            'alpha': 0.0,
        }
        
        # 清空缓冲区
        self.episode_rewards.clear()
        self.episode_returns.clear()
        self.episode_sharpe_ratios.clear()
        self.episode_max_drawdowns.clear()
        self.weight_history.clear()
        self.return_history.clear()
        
        self.logger.info("投资组合统计已重置")