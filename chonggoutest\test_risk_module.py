#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
风险管理模块测试 - TDD测试驱动开发

测试finrl_crypto.risk模块的所有功能：
- RiskManager风险管理器
- PositionSizer仓位管理
- RiskMetrics风险指标
- PortfolioRisk组合风险
- StressTest压力测试
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Mock外部依赖
sys.modules['gym'] = Mock()
sys.modules['stable_baselines3'] = Mock()
sys.modules['torch'] = Mock()
sys.modules['tensorflow'] = Mock()
sys.modules['matplotlib'] = Mock()
sys.modules['seaborn'] = Mock()
sys.modules['plotly'] = Mock()
sys.modules['scipy'] = Mock()
sys.modules['sklearn'] = Mock()

class TestRiskManager(unittest.TestCase):
    """测试RiskManager风险管理器"""
    
    def setUp(self):
        """测试前准备"""
        self.risk_config = {
            'max_position_size': 0.1,  # 单个资产最大仓位10%
            'max_portfolio_risk': 0.02,  # 组合最大日风险2%
            'stop_loss': 0.05,  # 5%止损
            'take_profit': 0.15,  # 15%止盈
            'max_drawdown': 0.2,  # 最大回撤20%
            'var_confidence': 0.95,  # VaR置信度95%
            'correlation_threshold': 0.7,  # 相关性阈值
            'volatility_lookback': 30,  # 波动率回看期30天
            'risk_free_rate': 0.02  # 无风险利率2%
        }
        
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        np.random.seed(42)
        
        self.price_data = pd.DataFrame({
            'BTC': 100 + np.cumsum(np.random.normal(0, 2, 252)),
            'ETH': 50 + np.cumsum(np.random.normal(0, 1.5, 252)),
            'ADA': 1 + np.cumsum(np.random.normal(0, 0.1, 252))
        }, index=dates)
        
        self.returns_data = self.price_data.pct_change().dropna()
        
        self.portfolio_weights = pd.Series({
            'BTC': 0.5,
            'ETH': 0.3,
            'ADA': 0.2
        })
    
    def test_risk_manager_creation(self):
        """测试风险管理器创建"""
        try:
            from finrl_crypto.risk.manager import RiskManager
            
            risk_manager = RiskManager(self.risk_config)
            
            self.assertEqual(risk_manager.max_position_size, 0.1)
            self.assertEqual(risk_manager.max_portfolio_risk, 0.02)
            self.assertEqual(risk_manager.stop_loss, 0.05)
            
        except ImportError:
            self.fail("RiskManager类未实现")
    
    def test_position_risk_check(self):
        """测试单个仓位风险检查"""
        try:
            from finrl_crypto.risk.manager import RiskManager
            
            risk_manager = RiskManager(self.risk_config)
            
            # 测试仓位大小检查
            is_valid = risk_manager.check_position_size(
                asset='BTC',
                position_size=0.08,  # 8%的仓位
                portfolio_value=100000
            )
            self.assertTrue(is_valid)  # 应该通过检查
            
            # 测试超限仓位
            is_valid = risk_manager.check_position_size(
                asset='BTC',
                position_size=0.15,  # 15%的仓位，超过10%限制
                portfolio_value=100000
            )
            self.assertFalse(is_valid)  # 应该不通过检查
            
        except ImportError:
            self.fail("RiskManager类未实现")
    
    def test_portfolio_risk_check(self):
        """测试组合风险检查"""
        try:
            from finrl_crypto.risk.manager import RiskManager
            
            risk_manager = RiskManager(self.risk_config)
            
            # 测试组合风险计算
            portfolio_risk = risk_manager.calculate_portfolio_risk(
                weights=self.portfolio_weights,
                returns=self.returns_data
            )
            
            self.assertIsInstance(portfolio_risk, float)
            self.assertGreater(portfolio_risk, 0)
            
            # 测试风险限制检查
            is_within_limit = risk_manager.check_portfolio_risk_limit(
                portfolio_risk=portfolio_risk
            )
            self.assertIsInstance(is_within_limit, bool)
            
        except ImportError:
            self.fail("RiskManager类未实现")
    
    def test_stop_loss_take_profit(self):
        """测试止损止盈检查"""
        try:
            from finrl_crypto.risk.manager import RiskManager
            
            risk_manager = RiskManager(self.risk_config)
            
            # 测试止损触发
            should_stop_loss = risk_manager.check_stop_loss(
                entry_price=100,
                current_price=94,  # 6%损失
                position_side='long'
            )
            self.assertTrue(should_stop_loss)  # 应该触发5%止损
            
            # 测试止盈触发
            should_take_profit = risk_manager.check_take_profit(
                entry_price=100,
                current_price=116,  # 16%收益
                position_side='long'
            )
            self.assertTrue(should_take_profit)  # 应该触发15%止盈
            
        except ImportError:
            self.fail("RiskManager类未实现")
    
    def test_correlation_check(self):
        """测试相关性检查"""
        try:
            from finrl_crypto.risk.manager import RiskManager
            
            risk_manager = RiskManager(self.risk_config)
            
            # 测试相关性计算
            correlation_matrix = risk_manager.calculate_correlation_matrix(
                returns=self.returns_data
            )
            
            self.assertIsInstance(correlation_matrix, pd.DataFrame)
            self.assertEqual(correlation_matrix.shape, (3, 3))
            
            # 测试高相关性检查
            high_corr_pairs = risk_manager.find_high_correlation_pairs(
                correlation_matrix=correlation_matrix,
                threshold=self.risk_config['correlation_threshold']
            )
            
            self.assertIsInstance(high_corr_pairs, list)
            
        except ImportError:
            self.fail("RiskManager类未实现")

class TestPositionSizer(unittest.TestCase):
    """测试PositionSizer仓位管理"""
    
    def setUp(self):
        """测试前准备"""
        self.sizing_config = {
            'method': 'kelly',  # Kelly公式
            'max_position': 0.25,  # 最大仓位25%
            'min_position': 0.01,  # 最小仓位1%
            'volatility_target': 0.15,  # 目标波动率15%
            'risk_per_trade': 0.02,  # 每笔交易风险2%
            'lookback_period': 60  # 回看期60天
        }
        
        # 创建测试收益率数据
        np.random.seed(42)
        self.returns = pd.Series(np.random.normal(0.001, 0.02, 252))
        self.win_rate = 0.55
        self.avg_win = 0.025
        self.avg_loss = -0.015
    
    def test_position_sizer_creation(self):
        """测试仓位管理器创建"""
        try:
            from finrl_crypto.risk.position_sizer import PositionSizer
            
            sizer = PositionSizer(self.sizing_config)
            
            self.assertEqual(sizer.method, 'kelly')
            self.assertEqual(sizer.max_position, 0.25)
            self.assertEqual(sizer.min_position, 0.01)
            
        except ImportError:
            self.fail("PositionSizer类未实现")
    
    def test_kelly_criterion(self):
        """测试Kelly公式仓位计算"""
        try:
            from finrl_crypto.risk.position_sizer import PositionSizer
            
            sizer = PositionSizer(self.sizing_config)
            
            # 测试Kelly公式计算
            kelly_fraction = sizer.calculate_kelly_fraction(
                win_rate=self.win_rate,
                avg_win=self.avg_win,
                avg_loss=abs(self.avg_loss)
            )
            
            self.assertIsInstance(kelly_fraction, float)
            self.assertGreaterEqual(kelly_fraction, 0)
            self.assertLessEqual(kelly_fraction, 1)
            
        except ImportError:
            self.fail("PositionSizer类未实现")
    
    def test_volatility_targeting(self):
        """测试波动率目标仓位"""
        try:
            from finrl_crypto.risk.position_sizer import PositionSizer
            
            sizer = PositionSizer(self.sizing_config)
            
            # 测试波动率目标仓位计算
            position_size = sizer.calculate_volatility_target_position(
                returns=self.returns,
                target_volatility=self.sizing_config['volatility_target']
            )
            
            self.assertIsInstance(position_size, float)
            self.assertGreaterEqual(position_size, 0)
            self.assertLessEqual(position_size, 1)
            
        except ImportError:
            self.fail("PositionSizer类未实现")
    
    def test_risk_parity_sizing(self):
        """测试风险平价仓位"""
        try:
            from finrl_crypto.risk.position_sizer import PositionSizer
            
            sizer = PositionSizer(self.sizing_config)
            
            # 创建多资产收益率数据
            multi_returns = pd.DataFrame({
                'BTC': np.random.normal(0.001, 0.03, 100),
                'ETH': np.random.normal(0.0008, 0.025, 100),
                'ADA': np.random.normal(0.0005, 0.04, 100)
            })
            
            # 测试风险平价权重计算
            risk_parity_weights = sizer.calculate_risk_parity_weights(
                returns=multi_returns
            )
            
            self.assertIsInstance(risk_parity_weights, pd.Series)
            self.assertAlmostEqual(risk_parity_weights.sum(), 1.0, places=2)
            
        except ImportError:
            self.fail("PositionSizer类未实现")
    
    def test_position_size_constraints(self):
        """测试仓位大小约束"""
        try:
            from finrl_crypto.risk.position_sizer import PositionSizer
            
            sizer = PositionSizer(self.sizing_config)
            
            # 测试仓位约束应用
            raw_position = 0.35  # 原始计算的35%仓位
            constrained_position = sizer.apply_position_constraints(raw_position)
            
            # 应该被限制在最大仓位25%
            self.assertEqual(constrained_position, 0.25)
            
            # 测试最小仓位约束
            small_position = 0.005  # 0.5%仓位
            constrained_small = sizer.apply_position_constraints(small_position)
            
            # 应该被提升到最小仓位1%
            self.assertEqual(constrained_small, 0.01)
            
        except ImportError:
            self.fail("PositionSizer类未实现")

class TestRiskMetrics(unittest.TestCase):
    """测试RiskMetrics风险指标"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试数据
        np.random.seed(42)
        self.returns = pd.Series(np.random.normal(0.001, 0.02, 252))
        self.prices = pd.Series([100])
        for ret in self.returns:
            self.prices = pd.concat([self.prices, pd.Series([self.prices.iloc[-1] * (1 + ret)])])
        self.prices = self.prices.iloc[1:]
        
        self.confidence_levels = [0.90, 0.95, 0.99]
    
    def test_var_calculation(self):
        """测试VaR计算"""
        try:
            from finrl_crypto.risk.metrics import RiskMetrics
            
            metrics = RiskMetrics()
            
            # 测试历史模拟VaR
            var_historical = metrics.calculate_var_historical(
                returns=self.returns,
                confidence_level=0.95
            )
            
            self.assertIsInstance(var_historical, float)
            self.assertLess(var_historical, 0)  # VaR应该是负数
            
            # 测试参数VaR
            var_parametric = metrics.calculate_var_parametric(
                returns=self.returns,
                confidence_level=0.95
            )
            
            self.assertIsInstance(var_parametric, float)
            self.assertLess(var_parametric, 0)
            
        except ImportError:
            self.fail("RiskMetrics类未实现")
    
    def test_cvar_calculation(self):
        """测试CVaR计算"""
        try:
            from finrl_crypto.risk.metrics import RiskMetrics
            
            metrics = RiskMetrics()
            
            # 测试CVaR计算
            cvar = metrics.calculate_cvar(
                returns=self.returns,
                confidence_level=0.95
            )
            
            self.assertIsInstance(cvar, float)
            self.assertLess(cvar, 0)  # CVaR应该是负数
            
            # CVaR应该比VaR更负（更保守）
            var = metrics.calculate_var_historical(self.returns, 0.95)
            self.assertLess(cvar, var)
            
        except ImportError:
            self.fail("RiskMetrics类未实现")
    
    def test_maximum_drawdown(self):
        """测试最大回撤计算"""
        try:
            from finrl_crypto.risk.metrics import RiskMetrics
            
            metrics = RiskMetrics()
            
            # 测试最大回撤
            max_dd = metrics.calculate_maximum_drawdown(self.prices)
            
            self.assertIsInstance(max_dd, float)
            self.assertLessEqual(max_dd, 0)  # 最大回撤应该是负数或零
            
            # 测试回撤序列
            drawdown_series = metrics.calculate_drawdown_series(self.prices)
            
            self.assertIsInstance(drawdown_series, pd.Series)
            self.assertEqual(len(drawdown_series), len(self.prices))
            
        except ImportError:
            self.fail("RiskMetrics类未实现")
    
    def test_downside_risk_metrics(self):
        """测试下行风险指标"""
        try:
            from finrl_crypto.risk.metrics import RiskMetrics
            
            metrics = RiskMetrics()
            
            # 测试下行偏差
            downside_deviation = metrics.calculate_downside_deviation(
                returns=self.returns,
                target_return=0
            )
            
            self.assertIsInstance(downside_deviation, float)
            self.assertGreater(downside_deviation, 0)
            
            # 测试Sortino比率
            sortino_ratio = metrics.calculate_sortino_ratio(
                returns=self.returns,
                target_return=0,
                risk_free_rate=0.02
            )
            
            self.assertIsInstance(sortino_ratio, float)
            
        except ImportError:
            self.fail("RiskMetrics类未实现")
    
    def test_tail_risk_metrics(self):
        """测试尾部风险指标"""
        try:
            from finrl_crypto.risk.metrics import RiskMetrics
            
            metrics = RiskMetrics()
            
            # 测试偏度
            skewness = metrics.calculate_skewness(self.returns)
            self.assertIsInstance(skewness, float)
            
            # 测试峰度
            kurtosis = metrics.calculate_kurtosis(self.returns)
            self.assertIsInstance(kurtosis, float)
            
            # 测试尾部比率
            tail_ratio = metrics.calculate_tail_ratio(
                returns=self.returns,
                tail_percentile=0.05
            )
            self.assertIsInstance(tail_ratio, float)
            
        except ImportError:
            self.fail("RiskMetrics类未实现")

class TestPortfolioRisk(unittest.TestCase):
    """测试PortfolioRisk组合风险"""
    
    def setUp(self):
        """测试前准备"""
        # 创建多资产收益率数据
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        np.random.seed(42)
        
        self.returns_data = pd.DataFrame({
            'BTC': np.random.normal(0.001, 0.03, 252),
            'ETH': np.random.normal(0.0008, 0.025, 252),
            'ADA': np.random.normal(0.0005, 0.04, 252),
            'DOT': np.random.normal(0.0006, 0.035, 252)
        }, index=dates)
        
        self.weights = pd.Series({
            'BTC': 0.4,
            'ETH': 0.3,
            'ADA': 0.2,
            'DOT': 0.1
        })
    
    def test_portfolio_risk_creation(self):
        """测试组合风险分析器创建"""
        try:
            from finrl_crypto.risk.portfolio import PortfolioRisk
            
            portfolio_risk = PortfolioRisk(
                returns=self.returns_data,
                weights=self.weights
            )
            
            self.assertIsNotNone(portfolio_risk.returns)
            self.assertIsNotNone(portfolio_risk.weights)
            
        except ImportError:
            self.fail("PortfolioRisk类未实现")
    
    def test_portfolio_volatility(self):
        """测试组合波动率计算"""
        try:
            from finrl_crypto.risk.portfolio import PortfolioRisk
            
            portfolio_risk = PortfolioRisk(
                returns=self.returns_data,
                weights=self.weights
            )
            
            # 测试组合波动率
            portfolio_vol = portfolio_risk.calculate_portfolio_volatility()
            
            self.assertIsInstance(portfolio_vol, float)
            self.assertGreater(portfolio_vol, 0)
            
        except ImportError:
            self.fail("PortfolioRisk类未实现")
    
    def test_component_risk_contribution(self):
        """测试成分风险贡献"""
        try:
            from finrl_crypto.risk.portfolio import PortfolioRisk
            
            portfolio_risk = PortfolioRisk(
                returns=self.returns_data,
                weights=self.weights
            )
            
            # 测试风险贡献计算
            risk_contributions = portfolio_risk.calculate_risk_contributions()
            
            self.assertIsInstance(risk_contributions, pd.Series)
            self.assertEqual(len(risk_contributions), len(self.weights))
            
            # 风险贡献之和应该等于总风险
            total_risk = portfolio_risk.calculate_portfolio_volatility()
            self.assertAlmostEqual(
                risk_contributions.sum(), 
                total_risk, 
                places=6
            )
            
        except ImportError:
            self.fail("PortfolioRisk类未实现")
    
    def test_marginal_risk_contribution(self):
        """测试边际风险贡献"""
        try:
            from finrl_crypto.risk.portfolio import PortfolioRisk
            
            portfolio_risk = PortfolioRisk(
                returns=self.returns_data,
                weights=self.weights
            )
            
            # 测试边际风险贡献
            marginal_contributions = portfolio_risk.calculate_marginal_risk_contributions()
            
            self.assertIsInstance(marginal_contributions, pd.Series)
            self.assertEqual(len(marginal_contributions), len(self.weights))
            
        except ImportError:
            self.fail("PortfolioRisk类未实现")
    
    def test_portfolio_var(self):
        """测试组合VaR"""
        try:
            from finrl_crypto.risk.portfolio import PortfolioRisk
            
            portfolio_risk = PortfolioRisk(
                returns=self.returns_data,
                weights=self.weights
            )
            
            # 测试组合VaR
            portfolio_var = portfolio_risk.calculate_portfolio_var(
                confidence_level=0.95
            )
            
            self.assertIsInstance(portfolio_var, float)
            self.assertLess(portfolio_var, 0)
            
        except ImportError:
            self.fail("PortfolioRisk类未实现")
    
    def test_diversification_ratio(self):
        """测试分散化比率"""
        try:
            from finrl_crypto.risk.portfolio import PortfolioRisk
            
            portfolio_risk = PortfolioRisk(
                returns=self.returns_data,
                weights=self.weights
            )
            
            # 测试分散化比率
            diversification_ratio = portfolio_risk.calculate_diversification_ratio()
            
            self.assertIsInstance(diversification_ratio, float)
            self.assertGreaterEqual(diversification_ratio, 1.0)  # 应该>=1
            
        except ImportError:
            self.fail("PortfolioRisk类未实现")

class TestStressTest(unittest.TestCase):
    """测试StressTest压力测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试组合数据
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        np.random.seed(42)
        
        self.portfolio_returns = pd.Series(
            np.random.normal(0.001, 0.02, 252),
            index=dates
        )
        
        self.stress_scenarios = {
            'market_crash': {
                'description': '市场崩盘情景',
                'shock_magnitude': -0.3,  # 30%下跌
                'duration': 30  # 持续30天
            },
            'volatility_spike': {
                'description': '波动率飙升',
                'volatility_multiplier': 3,  # 波动率增加3倍
                'duration': 60
            },
            'correlation_breakdown': {
                'description': '相关性失效',
                'correlation_shock': 0.9,  # 相关性突然上升到0.9
                'duration': 45
            }
        }
    
    def test_stress_test_creation(self):
        """测试压力测试器创建"""
        try:
            from finrl_crypto.risk.stress_test import StressTest
            
            stress_test = StressTest(
                portfolio_returns=self.portfolio_returns,
                scenarios=self.stress_scenarios
            )
            
            self.assertIsNotNone(stress_test.portfolio_returns)
            self.assertIsNotNone(stress_test.scenarios)
            
        except ImportError:
            self.fail("StressTest类未实现")
    
    def test_historical_stress_test(self):
        """测试历史压力测试"""
        try:
            from finrl_crypto.risk.stress_test import StressTest
            
            stress_test = StressTest(
                portfolio_returns=self.portfolio_returns,
                scenarios=self.stress_scenarios
            )
            
            # 测试历史压力测试
            historical_results = stress_test.run_historical_stress_test(
                stress_period_start='2020-03-01',
                stress_period_end='2020-04-01'
            )
            
            self.assertIsInstance(historical_results, dict)
            
        except ImportError:
            self.fail("StressTest类未实现")
    
    def test_scenario_stress_test(self):
        """测试情景压力测试"""
        try:
            from finrl_crypto.risk.stress_test import StressTest
            
            stress_test = StressTest(
                portfolio_returns=self.portfolio_returns,
                scenarios=self.stress_scenarios
            )
            
            # 测试情景压力测试
            scenario_results = stress_test.run_scenario_stress_test()
            
            self.assertIsInstance(scenario_results, dict)
            
            # 检查每个情景的结果
            for scenario_name in self.stress_scenarios.keys():
                self.assertIn(scenario_name, scenario_results)
                
        except ImportError:
            self.fail("StressTest类未实现")
    
    def test_monte_carlo_stress_test(self):
        """测试蒙特卡洛压力测试"""
        try:
            from finrl_crypto.risk.stress_test import StressTest
            
            stress_test = StressTest(
                portfolio_returns=self.portfolio_returns,
                scenarios=self.stress_scenarios
            )
            
            # 测试蒙特卡洛压力测试
            mc_results = stress_test.run_monte_carlo_stress_test(
                num_simulations=1000,
                time_horizon=30
            )
            
            self.assertIsInstance(mc_results, dict)
            self.assertIn('simulated_returns', mc_results)
            self.assertIn('percentiles', mc_results)
            
        except ImportError:
            self.fail("StressTest类未实现")
    
    def test_stress_test_reporting(self):
        """测试压力测试报告"""
        try:
            from finrl_crypto.risk.stress_test import StressTest
            
            stress_test = StressTest(
                portfolio_returns=self.portfolio_returns,
                scenarios=self.stress_scenarios
            )
            
            # 测试报告生成
            self.assertTrue(hasattr(stress_test, 'generate_stress_report'))
            self.assertTrue(hasattr(stress_test, 'visualize_stress_results'))
            
        except ImportError:
            self.fail("StressTest类未实现")

class TestRiskReporting(unittest.TestCase):
    """测试风险报告"""
    
    def test_risk_dashboard(self):
        """测试风险仪表板"""
        try:
            from finrl_crypto.risk.reporting import RiskDashboard
            
            dashboard = RiskDashboard()
            
            # 测试仪表板功能
            self.assertTrue(hasattr(dashboard, 'create_risk_summary'))
            self.assertTrue(hasattr(dashboard, 'generate_risk_report'))
            self.assertTrue(hasattr(dashboard, 'plot_risk_metrics'))
            
        except ImportError:
            self.fail("RiskDashboard类未实现")
    
    def test_risk_alerts(self):
        """测试风险预警"""
        try:
            from finrl_crypto.risk.alerts import RiskAlerts
            
            alerts = RiskAlerts()
            
            # 测试预警功能
            self.assertTrue(hasattr(alerts, 'check_risk_limits'))
            self.assertTrue(hasattr(alerts, 'send_risk_alert'))
            self.assertTrue(hasattr(alerts, 'get_alert_history'))
            
        except ImportError:
            self.fail("RiskAlerts类未实现")

if __name__ == '__main__':
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加所有测试类
    test_classes = [
        TestRiskManager,
        TestPositionSizer,
        TestRiskMetrics,
        TestPortfolioRisk,
        TestStressTest,
        TestRiskReporting
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果统计
    print(f"\n测试结果统计:")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")