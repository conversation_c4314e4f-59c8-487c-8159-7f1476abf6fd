[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --color=yes
    --durations=10
    --cov=finrl_crypto
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
    --junitxml=reports/junit.xml
    --html=reports/report.html
    --self-contained-html

# 标记定义
markers =
    slow: 标记测试为慢速测试（运行时间 > 5秒）
    integration: 集成测试
    unit: 单元测试
    e2e: 端到端测试
    gpu: 需要GPU的测试
    network: 需要网络连接的测试
    live: 实盘交易测试（谨慎使用）
    data: 数据相关测试
    model: 模型相关测试
    strategy: 策略相关测试
    backtest: 回测相关测试
    performance: 性能测试
    security: 安全测试
    api: API测试
    database: 数据库测试
    redis: Redis缓存测试
    docker: Docker相关测试
    windows: Windows特定测试
    linux: Linux特定测试
    macos: macOS特定测试
    python38: Python 3.8特定测试
    python39: Python 3.9特定测试
    python310: Python 3.10特定测试
    python311: Python 3.11特定测试
    experimental: 实验性功能测试
    deprecated: 已弃用功能测试
    smoke: 冒烟测试
    regression: 回归测试
    stress: 压力测试
    memory: 内存测试
    timeout: 超时测试
    parallel: 并行测试
    serial: 串行测试
    mock: 模拟测试
    real: 真实环境测试

# 过滤警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::FutureWarning
    ignore:.*:pytest.PytestUnraisableExceptionWarning
    error::RuntimeWarning
    error::ResourceWarning

# 最小版本要求
minversion = 6.0

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 文件日志
log_file = tests/logs/pytest.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(filename)s:%(lineno)d %(funcName)s(): %(message)s
log_file_date_format = %Y-%m-%d %H:%M:%S

# 自动使用标记
log_auto_indent = true

# 超时设置（需要pytest-timeout插件）
timeout = 300
timeout_method = thread

# 环境变量（需要pytest-env插件）
env = 
    TESTING = 1
    LOG_LEVEL = DEBUG
    PYTHONPATH = .
    COVERAGE_CORE = sysmon

# 异步测试配置（需要pytest-asyncio插件）
asyncio_mode = auto

# 缓存配置
cache_dir = .pytest_cache

# 收集配置
collect_ignore = 
    setup.py
    build
    dist
    .git
    .tox
    __pycache__
    *.egg-info

# 文档测试
doctest_optionflags = NORMALIZE_WHITESPACE IGNORE_EXCEPTION_DETAIL

# JUnit XML配置
junit_suite_name = FinRL_Crypto_Tests
junit_logging = system-out
junit_log_passing_tests = false
junit_duration_report = total
junit_family = xunit2