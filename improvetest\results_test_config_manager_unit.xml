<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="0" skipped="0" tests="12" time="7.580" timestamp="2025-06-19T02:02:28.609623+09:00" hostname="E-5CG22747W5"><testcase classname="improvetest.test_config_manager_unit.TestConfigLoadError" name="test_config_load_error_creation" time="0.971" /><testcase classname="improvetest.test_config_manager_unit.TestConfigValidationError" name="test_config_validation_error_creation" time="0.002" /><testcase classname="improvetest.test_config_manager_unit.TestConfigLoader" name="test_abstract_base_class" time="0.005" /><testcase classname="improvetest.test_config_manager_unit.TestConfigLoader" name="test_load_empty_config" time="0.002" /><testcase classname="improvetest.test_config_manager_unit.TestConfigLoader" name="test_load_invalid_config" time="0.003" /><testcase classname="improvetest.test_config_manager_unit.TestConfigLoader" name="test_load_valid_config" time="0.002" /><testcase classname="improvetest.test_config_manager_unit.TestConfigManagerIntegration" name="test_environment_variable_override" time="0.018" /><testcase classname="improvetest.test_config_manager_unit.TestConfigManagerIntegration" name="test_json_config_loading" time="0.020" /><testcase classname="improvetest.test_config_manager_unit.TestConfigManagerIntegration" name="test_yaml_config_loading" time="0.017" /><testcase classname="improvetest.test_config_manager_unit.TestConfigValidation" name="test_schema_validation_failure" time="0.009" /><testcase classname="improvetest.test_config_manager_unit.TestConfigValidation" name="test_schema_validation_success" time="0.006" /><testcase classname="improvetest.test_config_manager_unit.TestConfigMerging" name="test_deep_merge_configs" time="0.004" /></testsuite></testsuites>