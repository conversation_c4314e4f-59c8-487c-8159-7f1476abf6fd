#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖注入容器实现

这个模块实现了一个完整的依赖注入容器，支持：
- 服务注册和解析
- 多种生命周期管理（单例、瞬态、作用域）
- 自动依赖注入
- 循环依赖检测
- 线程安全
- 配置驱动的服务注册

作者：AI Assistant
日期：2024
"""

import threading
import inspect
import importlib
from typing import (
    Dict, Any, Optional, Type, TypeVar, Callable, List, Set, 
    Union, Protocol, get_type_hints, get_origin, get_args
)
from dataclasses import dataclass
from enum import Enum
from abc import ABC, abstractmethod
import logging
import weakref
from contextlib import contextmanager

# 设置日志
logger = logging.getLogger(__name__)

# 类型变量
T = TypeVar('T')
ServiceType = TypeVar('ServiceType')


class ServiceLifetime(Enum):
    """服务生命周期枚举"""
    SINGLETON = "singleton"  # 单例：整个应用程序生命周期内只有一个实例
    TRANSIENT = "transient"  # 瞬态：每次请求都创建新实例
    SCOPED = "scoped"       # 作用域：在特定作用域内是单例


@dataclass
class ServiceDescriptor:
    """服务描述符，描述如何创建和管理服务"""
    service_type: Type  # 服务类型（通常是接口或抽象类）
    implementation_type: Optional[Type] = None  # 实现类型
    factory: Optional[Callable[[], Any]] = None  # 工厂函数
    lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT  # 生命周期
    instance: Optional[Any] = None  # 缓存的实例（用于单例）
    
    def __post_init__(self):
        """验证服务描述符的有效性"""
        # 如果已有实例，则不需要implementation_type或factory
        if self.instance is not None:
            return
            
        if self.implementation_type is None and self.factory is None:
            raise InvalidServiceError(
                f"Service {self.service_type} must have either implementation_type or factory"
            )
        
        if self.implementation_type is not None and self.factory is not None:
            raise InvalidServiceError(
                f"Service {self.service_type} cannot have both implementation_type and factory"
            )


class DIError(Exception):
    """依赖注入相关的基础异常"""
    pass


class CircularDependencyError(DIError):
    """循环依赖异常"""
    def __init__(self, dependency_chain: List[Type]):
        self.dependency_chain = dependency_chain
        chain_str = " -> ".join([getattr(cls, '__name__', str(cls)) for cls in dependency_chain])
        super().__init__(f"Circular dependency detected: {chain_str}")


class ServiceNotFoundError(DIError):
    """服务未找到异常"""
    def __init__(self, service_type: Type):
        self.service_type = service_type
        super().__init__(f"Service {service_type.__name__} is not registered")


class InvalidServiceError(DIError):
    """无效服务异常"""
    pass


class ScopeNotFoundError(DIError):
    """作用域未找到异常"""
    pass


class ServiceScope:
    """服务作用域，管理作用域内的服务实例"""
    
    def __init__(self, scope_id: str):
        self.scope_id = scope_id
        self._instances: Dict[Type, Any] = {}
        self._lock = threading.RLock()
    
    def get_instance(self, service_type: Type) -> Optional[Any]:
        """获取作用域内的服务实例"""
        with self._lock:
            return self._instances.get(service_type)
    
    def set_instance(self, service_type: Type, instance: Any) -> None:
        """设置作用域内的服务实例"""
        with self._lock:
            self._instances[service_type] = instance
    
    def dispose(self) -> None:
        """释放作用域内的所有实例"""
        with self._lock:
            for instance in self._instances.values():
                if hasattr(instance, 'dispose'):
                    try:
                        instance.dispose()
                    except Exception as e:
                        logger.warning(f"Error disposing instance: {e}")
            self._instances.clear()


class DIContainer:
    """依赖注入容器"""
    
    def __init__(self):
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._lock = threading.RLock()
        self._resolution_stack: List[Type] = []  # 用于检测循环依赖
        self._scopes: Dict[str, ServiceScope] = {}
        self._current_scope: Optional[ServiceScope] = None
        
        # 注册容器自身
        self.register_instance(DIContainer, self)
    
    def register_singleton(self, service_type: Type, implementation_type: Optional[Type] = None) -> 'DIContainer':
        """注册单例服务"""
        impl_type = implementation_type or service_type
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=impl_type,
            lifetime=ServiceLifetime.SINGLETON
        )
        return self._register_service(service_type, descriptor)
    
    def register_transient(self, service_type: Type, implementation_type: Optional[Type] = None) -> 'DIContainer':
        """注册瞬态服务"""
        impl_type = implementation_type or service_type
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=impl_type,
            lifetime=ServiceLifetime.TRANSIENT
        )
        return self._register_service(service_type, descriptor)
    
    def register_scoped(self, service_type: Type, implementation_type: Optional[Type] = None) -> 'DIContainer':
        """注册作用域服务"""
        impl_type = implementation_type or service_type
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=impl_type,
            lifetime=ServiceLifetime.SCOPED
        )
        return self._register_service(service_type, descriptor)
    
    def register_factory(self, service_type: Type, factory: Callable[[], T], lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT) -> 'DIContainer':
        """注册工厂函数"""
        descriptor = ServiceDescriptor(
            service_type=service_type,
            factory=factory,
            lifetime=lifetime
        )
        return self._register_service(service_type, descriptor)
    
    def register_instance(self, service_type: Type, instance: T) -> 'DIContainer':
        """注册实例（自动为单例）"""
        descriptor = ServiceDescriptor(
            service_type=service_type,
            lifetime=ServiceLifetime.SINGLETON,
            instance=instance
        )
        return self._register_service(service_type, descriptor)
    
    def _register_service(self, service_type: Type, descriptor: ServiceDescriptor) -> 'DIContainer':
        """内部服务注册方法"""
        with self._lock:
            self._services[service_type] = descriptor
            logger.debug(f"Registered service {service_type.__name__} with lifetime {descriptor.lifetime.value}")
        return self
    
    def get_service(self, service_type: Type[T]) -> T:
        """获取服务实例"""
        with self._lock:
            return self._resolve_service(service_type)
    
    def _resolve_service(self, service_type: Type[T]) -> T:
        """解析服务实例"""
        # 检查是否已注册
        if service_type not in self._services:
            raise ServiceNotFoundError(service_type)
        
        descriptor = self._services[service_type]
        
        # 检查循环依赖
        if service_type in self._resolution_stack:
            chain = self._resolution_stack + [service_type]
            raise CircularDependencyError(chain)
        
        # 根据生命周期处理
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            return self._resolve_singleton(service_type, descriptor)
        elif descriptor.lifetime == ServiceLifetime.SCOPED:
            return self._resolve_scoped(service_type, descriptor)
        else:  # TRANSIENT
            return self._resolve_transient(service_type, descriptor)
    
    def _resolve_singleton(self, service_type: Type[T], descriptor: ServiceDescriptor) -> T:
        """解析单例服务"""
        if descriptor.instance is not None:
            return descriptor.instance
        
        # 创建实例
        instance = self._create_instance(service_type, descriptor)
        descriptor.instance = instance
        return instance
    
    def _resolve_scoped(self, service_type: Type[T], descriptor: ServiceDescriptor) -> T:
        """解析作用域服务"""
        if self._current_scope is None:
            raise ScopeNotFoundError("No active scope for scoped service")
        
        instance = self._current_scope.get_instance(service_type)
        if instance is not None:
            return instance
        
        # 创建实例
        instance = self._create_instance(service_type, descriptor)
        self._current_scope.set_instance(service_type, instance)
        return instance
    
    def _resolve_transient(self, service_type: Type[T], descriptor: ServiceDescriptor) -> T:
        """解析瞬态服务"""
        return self._create_instance(service_type, descriptor)
    
    def _create_instance(self, service_type: Type[T], descriptor: ServiceDescriptor) -> T:
        """创建服务实例"""
        self._resolution_stack.append(service_type)
        
        try:
            if descriptor.factory is not None:
                # 使用工厂函数
                return descriptor.factory()
            else:
                # 使用构造函数
                return self._create_instance_from_type(descriptor.implementation_type)
        finally:
            self._resolution_stack.pop()
    
    def _create_instance_from_type(self, implementation_type: Type[T]) -> T:
        """从类型创建实例，自动注入依赖"""
        # 获取构造函数签名
        constructor = implementation_type.__init__
        signature = inspect.signature(constructor)
        
        # 准备构造函数参数
        kwargs = {}
        for param_name, param in signature.parameters.items():
            if param_name == 'self':
                continue
            
            # 跳过*args和**kwargs参数
            if param.kind in (inspect.Parameter.VAR_POSITIONAL, inspect.Parameter.VAR_KEYWORD):
                continue
            
            # 获取参数类型
            param_type = param.annotation
            if param_type == inspect.Parameter.empty:
                if param.default == inspect.Parameter.empty:
                    raise InvalidServiceError(
                        f"Parameter '{param_name}' of {implementation_type.__name__} has no type annotation and no default value"
                    )
                continue
            
            # 处理字符串类型注解（前向引用）
            if isinstance(param_type, str):
                # 尝试在当前模块的全局命名空间中解析类型
                try:
                    # 获取调用者的frame来访问其局部变量
                    frame = inspect.currentframe()
                    while frame:
                        if 'ServiceA' in frame.f_locals or 'ServiceB' in frame.f_locals:
                            local_vars = frame.f_locals
                            break
                        frame = frame.f_back
                    else:
                        local_vars = {}
                    
                    # 尝试解析字符串类型
                    if param_type in local_vars:
                        param_type = local_vars[param_type]
                    else:
                        # 如果无法解析，跳过这个参数
                        continue
                except:
                    continue
            
            # 解析依赖
            if param.default != inspect.Parameter.empty:
                # 可选依赖
                try:
                    kwargs[param_name] = self._resolve_service(param_type)
                except ServiceNotFoundError:
                    # 使用默认值
                    pass
            else:
                # 必需依赖
                kwargs[param_name] = self._resolve_service(param_type)
        
        # 创建实例
        return implementation_type(**kwargs)
    
    def is_registered(self, service_type: Type) -> bool:
        """检查服务是否已注册"""
        with self._lock:
            return service_type in self._services
    
    def get_registered_services(self) -> List[Type]:
        """获取所有已注册的服务类型"""
        with self._lock:
            return list(self._services.keys())
    
    @contextmanager
    def create_scope(self, scope_id: str = None):
        """创建服务作用域"""
        if scope_id is None:
            scope_id = f"scope_{id(threading.current_thread())}"
        
        scope = ServiceScope(scope_id)
        old_scope = self._current_scope
        
        try:
            with self._lock:
                self._scopes[scope_id] = scope
                self._current_scope = scope
            
            yield scope
        finally:
            with self._lock:
                self._current_scope = old_scope
                if scope_id in self._scopes:
                    del self._scopes[scope_id]
            
            scope.dispose()
    
    def load_from_config(self, config: Dict[str, Any]) -> None:
        """从配置加载服务注册"""
        if not self.validate_config(config):
            raise InvalidServiceError("Invalid configuration")
        
        services_config = config.get('services', [])
        
        for service_config in services_config:
            service_type_name = service_config['service_type']
            implementation_type_name = service_config.get('implementation_type')
            factory_name = service_config.get('factory')
            lifetime_str = service_config['lifetime']
            
            # 解析类型
            service_type = self._resolve_type_from_string(service_type_name)
            
            # 解析生命周期
            lifetime = ServiceLifetime(lifetime_str)
            
            if factory_name:
                # 使用工厂函数
                factory = self._resolve_factory_from_string(factory_name)
                self.register_factory(service_type, factory, lifetime)
            else:
                # 使用实现类型
                implementation_type = self._resolve_type_from_string(implementation_type_name)
                
                if lifetime == ServiceLifetime.SINGLETON:
                    self.register_singleton(service_type, implementation_type)
                elif lifetime == ServiceLifetime.SCOPED:
                    self.register_scoped(service_type, implementation_type)
                else:
                    self.register_transient(service_type, implementation_type)
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置的有效性"""
        if not isinstance(config, dict):
            raise InvalidServiceError("Configuration must be a dictionary")
        
        services = config.get('services', [])
        if not isinstance(services, list):
            raise InvalidServiceError("'services' must be a list")
        
        for i, service_config in enumerate(services):
            if not isinstance(service_config, dict):
                raise InvalidServiceError(f"Service configuration at index {i} must be a dictionary")
            
            # 检查必需字段
            if 'service_type' not in service_config:
                raise InvalidServiceError(f"Service configuration at index {i} missing 'service_type'")
            
            if 'lifetime' not in service_config:
                raise InvalidServiceError(f"Service configuration at index {i} missing 'lifetime'")
            
            # 检查生命周期值
            lifetime = service_config['lifetime']
            if lifetime not in [e.value for e in ServiceLifetime]:
                raise InvalidServiceError(f"Invalid lifetime '{lifetime}' at index {i}")
            
            # 检查实现方式
            has_implementation = 'implementation_type' in service_config
            has_factory = 'factory' in service_config
            
            if not has_implementation and not has_factory:
                raise InvalidServiceError(
                    f"Service configuration at index {i} must have either 'implementation_type' or 'factory'"
                )
            
            if has_implementation and has_factory:
                raise InvalidServiceError(
                    f"Service configuration at index {i} cannot have both 'implementation_type' and 'factory'"
                )
        
        return True
    
    def _resolve_type_from_string(self, type_string: str) -> Type:
        """从字符串解析类型"""
        try:
            module_name, class_name = type_string.rsplit('.', 1)
            module = importlib.import_module(module_name)
            return getattr(module, class_name)
        except (ValueError, ImportError, AttributeError) as e:
            raise InvalidServiceError(f"Cannot resolve type '{type_string}': {e}")
    
    def _resolve_factory_from_string(self, factory_string: str) -> Callable:
        """从字符串解析工厂函数"""
        try:
            module_name, function_name = factory_string.rsplit('.', 1)
            module = importlib.import_module(module_name)
            factory = getattr(module, function_name)
            if not callable(factory):
                raise InvalidServiceError(f"'{factory_string}' is not callable")
            return factory
        except (ValueError, ImportError, AttributeError) as e:
            raise InvalidServiceError(f"Cannot resolve factory '{factory_string}': {e}")
    
    def dispose(self) -> None:
        """释放容器资源"""
        with self._lock:
            # 释放所有单例实例
            for descriptor in self._services.values():
                if descriptor.instance is not None and hasattr(descriptor.instance, 'dispose'):
                    try:
                        descriptor.instance.dispose()
                    except Exception as e:
                        logger.warning(f"Error disposing singleton instance: {e}")
            
            # 释放所有作用域
            for scope in self._scopes.values():
                scope.dispose()
            
            self._services.clear()
            self._scopes.clear()
            self._current_scope = None
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.dispose()


# 全局默认容器实例
_default_container: Optional[DIContainer] = None
_container_lock = threading.Lock()


def get_default_container() -> DIContainer:
    """获取默认的依赖注入容器"""
    global _default_container
    
    if _default_container is None:
        with _container_lock:
            if _default_container is None:
                _default_container = DIContainer()
    
    return _default_container


def set_default_container(container: DIContainer) -> None:
    """设置默认的依赖注入容器"""
    global _default_container
    
    with _container_lock:
        _default_container = container


# 装饰器
def injectable(cls: Type[T]) -> Type[T]:
    """标记类为可注入的服务"""
    # 为类添加元数据，表明它是可注入的
    cls._injectable = True
    return cls


def inject(func: Callable) -> Callable:
    """依赖注入装饰器"""
    def wrapper(*args, **kwargs):
        # 获取函数签名
        signature = inspect.signature(func)
        
        # 自动注入依赖
        for param_name, param in signature.parameters.items():
            if param_name not in kwargs and param.annotation != inspect.Parameter.empty:
                try:
                    # 尝试从默认容器获取服务
                    service = get_default_container().get_service(param.annotation)
                    kwargs[param_name] = service
                except (ServiceNotFoundError, DependencyResolutionError):
                    # 如果无法注入，继续执行（可能有默认值）
                    pass
        
        return func(*args, **kwargs)
    
    return wrapper


# 生命周期装饰器
def Singleton(cls: Type[T]) -> Type[T]:
    """单例生命周期装饰器"""
    cls._lifetime = ServiceLifetime.SINGLETON
    return injectable(cls)


def Transient(cls: Type[T]) -> Type[T]:
    """瞬态生命周期装饰器"""
    cls._lifetime = ServiceLifetime.TRANSIENT
    return injectable(cls)


def Scoped(cls: Type[T]) -> Type[T]:
    """作用域生命周期装饰器"""
    cls._lifetime = ServiceLifetime.SCOPED
    return injectable(cls)


# 便捷函数
def register_singleton(service_type: Type, implementation_type: Optional[Type] = None) -> DIContainer:
    """在默认容器中注册单例服务"""
    return get_default_container().register_singleton(service_type, implementation_type)


def register_transient(service_type: Type, implementation_type: Optional[Type] = None) -> DIContainer:
    """在默认容器中注册瞬态服务"""
    return get_default_container().register_transient(service_type, implementation_type)


def register_scoped(service_type: Type, implementation_type: Optional[Type] = None) -> DIContainer:
    """在默认容器中注册作用域服务"""
    return get_default_container().register_scoped(service_type, implementation_type)


def get_service(service_type: Type[T]) -> T:
    """从默认容器获取服务"""
    return get_default_container().get_service(service_type)