"""训练流程集成测试

测试完整的训练流程，包括数据处理、环境创建、智能体训练等。
"""

import pytest
import pandas as pd
import numpy as np
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import json
import pickle
from typing import Dict, Any, List, Tuple

# 注意：这里假设项目模块的结构，实际实现时需要根据项目结构调整
# from finrl_crypto.data import DataProcessor
# from finrl_crypto.env import make_env, TradingEnvironment
# from finrl_crypto.agent import create_agent, PPOAgent
# from finrl_crypto.strategy import RLStrategy
# from finrl_crypto.backtest import Backtester
# from finrl_crypto.utils import load_config, save_model, load_model

# 临时模拟类用于测试示例
class MockDataProcessor:
    """模拟数据处理器"""
    
    def __init__(self):
        self.data_cache = {}
    
    def fetch_data(self, symbols: List[str], start_date: str, end_date: str, 
                   timeframe: str = '1h') -> pd.DataFrame:
        """获取数据"""
        # 生成模拟数据
        dates = pd.date_range(start_date, end_date, freq=timeframe)
        data_list = []
        
        for symbol in symbols:
            np.random.seed(hash(symbol) % 2**32)
            prices = 50000 * np.exp(np.cumsum(np.random.normal(0, 0.01, len(dates))))
            
            symbol_data = pd.DataFrame({
                'timestamp': dates,
                'symbol': symbol,
                'open': prices,
                'high': prices * 1.01,
                'low': prices * 0.99,
                'close': prices,
                'volume': np.random.randint(1000, 10000, len(dates))
            })
            data_list.append(symbol_data)
        
        return pd.concat(data_list, ignore_index=True)
    
    def add_technical_indicators(self, data: pd.DataFrame, 
                               indicators_config: Dict) -> pd.DataFrame:
        """添加技术指标"""
        result = data.copy()
        
        # 添加简单移动平均
        if 'sma' in indicators_config:
            for window in indicators_config['sma']:
                result[f'sma_{window}'] = result.groupby('symbol')['close'].transform(
                    lambda x: x.rolling(window).mean()
                )
        
        # 添加 RSI
        if 'rsi' in indicators_config:
            for window in indicators_config['rsi']:
                result[f'rsi_{window}'] = result.groupby('symbol')['close'].transform(
                    lambda x: self._calculate_rsi(x, window)
                )
        
        return result
    
    def _calculate_rsi(self, prices: pd.Series, window: int) -> pd.Series:
        """计算 RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def split_data(self, data: pd.DataFrame, 
                   train_ratio: float = 0.8) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """分割数据"""
        split_idx = int(len(data) * train_ratio)
        return data.iloc[:split_idx], data.iloc[split_idx:]

class MockTradingEnvironment:
    """模拟交易环境"""
    
    def __init__(self, data: pd.DataFrame, initial_amount: float = 100000):
        self.data = data
        self.initial_amount = initial_amount
        self.current_step = 0
        self.portfolio_value = initial_amount
        self.cash = initial_amount
        self.positions = {}
        
        # 环境空间定义
        self.observation_space = Mock()
        self.observation_space.shape = (20,)  # 假设观察空间维度
        self.action_space = Mock()
        self.action_space.shape = (3,)  # 假设动作空间维度
        self.action_space.low = np.array([-1, -1, -1])
        self.action_space.high = np.array([1, 1, 1])
    
    def reset(self):
        """重置环境"""
        self.current_step = 0
        self.portfolio_value = self.initial_amount
        self.cash = self.initial_amount
        self.positions = {}
        return self._get_observation()
    
    def step(self, action):
        """执行动作"""
        # 模拟交易执行
        self.current_step += 1
        
        # 简单的奖励计算
        prev_value = self.portfolio_value
        self.portfolio_value += np.random.normal(0, 100)  # 随机变化
        reward = (self.portfolio_value - prev_value) / prev_value
        
        # 检查是否结束
        done = self.current_step >= len(self.data) - 1
        
        info = {
            'portfolio_value': self.portfolio_value,
            'cash': self.cash,
            'step': self.current_step
        }
        
        return self._get_observation(), reward, done, info
    
    def _get_observation(self):
        """获取观察"""
        # 返回随机观察（实际应该基于市场数据）
        return np.random.random(20)

class MockAgent:
    """模拟智能体"""
    
    def __init__(self, env, algorithm: str = 'PPO', **kwargs):
        self.env = env
        self.algorithm = algorithm
        self.model = None
        self.training_history = []
        self.is_trained = False
    
    def train(self, total_timesteps: int, **kwargs):
        """训练智能体"""
        # 模拟训练过程
        for step in range(0, total_timesteps, 1000):
            # 模拟训练指标
            metrics = {
                'step': step,
                'reward_mean': np.random.normal(0, 1),
                'reward_std': np.random.uniform(0.5, 2.0),
                'loss': np.random.uniform(0.1, 1.0),
                'learning_rate': 3e-4
            }
            self.training_history.append(metrics)
        
        self.is_trained = True
        return self
    
    def predict(self, observation, deterministic: bool = True):
        """预测动作"""
        if not self.is_trained:
            # 随机动作
            action = np.random.uniform(-1, 1, self.env.action_space.shape[0])
        else:
            # 模拟训练后的策略
            action = np.random.uniform(-0.5, 0.5, self.env.action_space.shape[0])
        
        return action, None
    
    def save(self, path: str):
        """保存模型"""
        model_data = {
            'algorithm': self.algorithm,
            'is_trained': self.is_trained,
            'training_history': self.training_history
        }
        
        with open(path, 'w') as f:
            json.dump(model_data, f)
    
    def load(self, path: str):
        """加载模型"""
        with open(path, 'r') as f:
            model_data = json.load(f)
        
        self.algorithm = model_data['algorithm']
        self.is_trained = model_data['is_trained']
        self.training_history = model_data['training_history']
        
        return self

def make_env(env_type: str, data: pd.DataFrame, **kwargs):
    """创建环境"""
    return MockTradingEnvironment(data, **kwargs)

def create_agent(algorithm: str, env, **kwargs):
    """创建智能体"""
    return MockAgent(env, algorithm, **kwargs)

class TestTrainingPipeline:
    """训练流程集成测试类"""
    
    @pytest.fixture
    def temp_dir(self):
        """临时目录"""
        temp_dir = tempfile.mkdtemp(prefix="training_test_")
        yield Path(temp_dir)
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def data_processor(self):
        """数据处理器"""
        return MockDataProcessor()
    
    @pytest.fixture
    def sample_config(self):
        """样本配置"""
        return {
            'data': {
                'symbols': ['BTC-USD'],
                'start_date': '2023-01-01',
                'end_date': '2023-03-31',
                'timeframe': '1h',
                'indicators': {
                    'sma': [10, 20],
                    'rsi': [14]
                }
            },
            'environment': {
                'initial_amount': 100000,
                'transaction_cost': 0.001
            },
            'agent': {
                'algorithm': 'PPO',
                'learning_rate': 3e-4
            },
            'training': {
                'total_timesteps': 5000,
                'eval_freq': 1000
            }
        }
    
    def test_basic_training_pipeline(self, data_processor, sample_config, temp_dir):
        """测试基本训练流程"""
        # 1. 数据获取和处理
        data = data_processor.fetch_data(
            symbols=sample_config['data']['symbols'],
            start_date=sample_config['data']['start_date'],
            end_date=sample_config['data']['end_date'],
            timeframe=sample_config['data']['timeframe']
        )
        
        assert not data.empty
        assert 'close' in data.columns
        assert len(data) > 0
        
        # 2. 添加技术指标
        data_with_indicators = data_processor.add_technical_indicators(
            data, sample_config['data']['indicators']
        )
        
        assert 'sma_10' in data_with_indicators.columns
        assert 'sma_20' in data_with_indicators.columns
        assert 'rsi_14' in data_with_indicators.columns
        
        # 3. 分割数据
        train_data, test_data = data_processor.split_data(data_with_indicators, 0.8)
        
        assert len(train_data) > 0
        assert len(test_data) > 0
        assert len(train_data) + len(test_data) == len(data_with_indicators)
        
        # 4. 创建环境
        env = make_env(
            'single_asset', 
            data=train_data,
            initial_amount=sample_config['environment']['initial_amount']
        )
        
        assert env is not None
        assert hasattr(env, 'observation_space')
        assert hasattr(env, 'action_space')
        
        # 5. 创建智能体
        agent = create_agent(
            sample_config['agent']['algorithm'],
            env,
            learning_rate=sample_config['agent']['learning_rate']
        )
        
        assert agent is not None
        assert agent.algorithm == 'PPO'
        
        # 6. 训练
        agent.train(total_timesteps=sample_config['training']['total_timesteps'])
        
        assert agent.is_trained
        assert len(agent.training_history) > 0
        
        # 7. 保存模型
        model_path = temp_dir / 'trained_model.json'
        agent.save(str(model_path))
        
        assert model_path.exists()
        
        # 8. 加载模型并测试
        new_agent = create_agent('PPO', env)
        new_agent.load(str(model_path))
        
        assert new_agent.is_trained
        assert new_agent.algorithm == 'PPO'
        
        # 9. 测试预测
        obs = env.reset()
        action, _ = new_agent.predict(obs)
        
        assert action is not None
        assert len(action) == env.action_space.shape[0]
    
    def test_multi_asset_training(self, data_processor, temp_dir):
        """测试多资产训练"""
        # 多资产配置
        config = {
            'data': {
                'symbols': ['BTC-USD', 'ETH-USD'],
                'start_date': '2023-01-01',
                'end_date': '2023-02-28',
                'timeframe': '1h',
                'indicators': {'sma': [10, 20]}
            },
            'environment': {'initial_amount': 100000},
            'agent': {'algorithm': 'PPO'},
            'training': {'total_timesteps': 2000}
        }
        
        # 获取多资产数据
        data = data_processor.fetch_data(
            symbols=config['data']['symbols'],
            start_date=config['data']['start_date'],
            end_date=config['data']['end_date']
        )
        
        # 检查多资产数据
        symbols_in_data = data['symbol'].unique()
        assert 'BTC-USD' in symbols_in_data
        assert 'ETH-USD' in symbols_in_data
        
        # 添加指标
        data_with_indicators = data_processor.add_technical_indicators(
            data, config['data']['indicators']
        )
        
        # 创建环境和智能体
        env = make_env('multi_asset', data=data_with_indicators)
        agent = create_agent('PPO', env)
        
        # 训练
        agent.train(total_timesteps=config['training']['total_timesteps'])
        
        assert agent.is_trained
        
        # 测试预测
        obs = env.reset()
        action, _ = agent.predict(obs)
        assert action is not None
    
    def test_training_with_different_algorithms(self, data_processor, sample_config):
        """测试不同算法的训练"""
        algorithms = ['PPO', 'A2C', 'SAC']
        
        # 获取数据
        data = data_processor.fetch_data(
            symbols=sample_config['data']['symbols'],
            start_date=sample_config['data']['start_date'],
            end_date=sample_config['data']['end_date']
        )
        
        for algorithm in algorithms:
            # 创建环境
            env = make_env('single_asset', data=data)
            
            # 创建智能体
            agent = create_agent(algorithm, env)
            
            # 短时间训练
            agent.train(total_timesteps=1000)
            
            assert agent.is_trained
            assert agent.algorithm == algorithm
            
            # 测试预测
            obs = env.reset()
            action, _ = agent.predict(obs)
            assert action is not None
    
    def test_training_interruption_and_resume(self, data_processor, sample_config, temp_dir):
        """测试训练中断和恢复"""
        # 获取数据
        data = data_processor.fetch_data(
            symbols=sample_config['data']['symbols'],
            start_date=sample_config['data']['start_date'],
            end_date=sample_config['data']['end_date']
        )
        
        env = make_env('single_asset', data=data)
        agent = create_agent('PPO', env)
        
        # 第一阶段训练
        agent.train(total_timesteps=2000)
        initial_history_length = len(agent.training_history)
        
        # 保存中间模型
        checkpoint_path = temp_dir / 'checkpoint.json'
        agent.save(str(checkpoint_path))
        
        # 创建新智能体并加载检查点
        new_agent = create_agent('PPO', env)
        new_agent.load(str(checkpoint_path))
        
        # 继续训练
        new_agent.train(total_timesteps=1000)
        
        # 验证训练历史被保留和扩展
        assert len(new_agent.training_history) >= initial_history_length
        assert new_agent.is_trained
    
    def test_training_with_validation(self, data_processor, sample_config):
        """测试带验证的训练"""
        # 获取数据
        data = data_processor.fetch_data(
            symbols=sample_config['data']['symbols'],
            start_date=sample_config['data']['start_date'],
            end_date=sample_config['data']['end_date']
        )
        
        # 分割训练和验证数据
        train_data, val_data = data_processor.split_data(data, 0.8)
        
        # 创建训练和验证环境
        train_env = make_env('single_asset', data=train_data)
        val_env = make_env('single_asset', data=val_data)
        
        agent = create_agent('PPO', train_env)
        
        # 训练过程中进行验证
        training_steps = [1000, 2000, 3000]
        validation_scores = []
        
        for steps in training_steps:
            agent.train(total_timesteps=1000)  # 增量训练
            
            # 在验证环境中测试
            val_obs = val_env.reset()
            total_reward = 0
            done = False
            step_count = 0
            
            while not done and step_count < 100:  # 限制步数
                action, _ = agent.predict(val_obs, deterministic=True)
                val_obs, reward, done, _ = val_env.step(action)
                total_reward += reward
                step_count += 1
            
            validation_scores.append(total_reward)
        
        # 验证分数应该被记录
        assert len(validation_scores) == len(training_steps)
    
    @pytest.mark.slow
    def test_long_training_session(self, data_processor, sample_config, temp_dir):
        """测试长时间训练会话"""
        # 获取更多数据
        extended_config = sample_config.copy()
        extended_config['data']['end_date'] = '2023-06-30'
        extended_config['training']['total_timesteps'] = 20000
        
        data = data_processor.fetch_data(
            symbols=extended_config['data']['symbols'],
            start_date=extended_config['data']['start_date'],
            end_date=extended_config['data']['end_date']
        )
        
        data_with_indicators = data_processor.add_technical_indicators(
            data, extended_config['data']['indicators']
        )
        
        env = make_env('single_asset', data=data_with_indicators)
        agent = create_agent('PPO', env)
        
        # 长时间训练
        agent.train(total_timesteps=extended_config['training']['total_timesteps'])
        
        assert agent.is_trained
        assert len(agent.training_history) > 10  # 应该有多个训练记录
        
        # 保存最终模型
        final_model_path = temp_dir / 'final_model.json'
        agent.save(str(final_model_path))
        
        assert final_model_path.exists()
        
        # 验证模型性能
        obs = env.reset()
        actions = []
        for _ in range(10):
            action, _ = agent.predict(obs)
            actions.append(action)
            obs, _, done, _ = env.step(action)
            if done:
                break
        
        assert len(actions) > 0
    
    def test_training_error_handling(self, data_processor):
        """测试训练错误处理"""
        # 测试无效数据
        empty_data = pd.DataFrame()
        
        try:
            env = make_env('single_asset', data=empty_data)
            env.reset()
            # 如果没有抛出异常，检查环境是否正确处理了空数据
            assert hasattr(env, 'data') and len(env.data) == 0
        except (ValueError, IndexError, AttributeError):
            # 预期的异常
            pass
        
        # 测试无效配置
        valid_data = data_processor.fetch_data(
            symbols=['BTC-USD'],
            start_date='2023-01-01',
            end_date='2023-01-31'
        )
        
        env = make_env('single_asset', data=valid_data)
        
        # 测试无效算法
        try:
            agent = create_agent('INVALID_ALGORITHM', env)
            # 如果没有抛出异常，检查是否返回了默认代理
            assert agent is not None
        except (ValueError, KeyError, AttributeError):
            # 预期的异常
            pass
    
    def test_memory_usage_during_training(self, data_processor, sample_config, memory_monitor):
        """测试训练过程中的内存使用"""
        initial_memory = memory_monitor
        
        # 获取数据
        data = data_processor.fetch_data(
            symbols=sample_config['data']['symbols'],
            start_date=sample_config['data']['start_date'],
            end_date=sample_config['data']['end_date']
        )
        
        env = make_env('single_asset', data=data)
        agent = create_agent('PPO', env)
        
        # 训练
        agent.train(total_timesteps=5000)
        
        # 内存使用应该在合理范围内
        # memory_monitor fixture 会在测试结束时检查内存使用
    
    def test_training_reproducibility(self, data_processor, sample_config):
        """测试训练可重现性"""
        # 设置随机种子
        np.random.seed(42)
        
        # 获取数据
        data = data_processor.fetch_data(
            symbols=sample_config['data']['symbols'],
            start_date=sample_config['data']['start_date'],
            end_date=sample_config['data']['end_date']
        )
        
        # 第一次训练
        env1 = make_env('single_asset', data=data)
        agent1 = create_agent('PPO', env1)
        agent1.train(total_timesteps=1000)
        
        # 重置随机种子
        np.random.seed(42)
        
        # 第二次训练
        env2 = make_env('single_asset', data=data)
        agent2 = create_agent('PPO', env2)
        agent2.train(total_timesteps=1000)
        
        # 训练历史应该相似（在模拟环境中）
        assert len(agent1.training_history) == len(agent2.training_history)
        assert agent1.is_trained == agent2.is_trained

class TestTrainingMetrics:
    """训练指标测试类"""
    
    def test_training_metrics_collection(self, sample_price_data):
        """测试训练指标收集"""
        env = make_env('single_asset', data=sample_price_data)
        agent = create_agent('PPO', env)
        
        # 训练
        agent.train(total_timesteps=3000)
        
        # 检查训练历史
        assert len(agent.training_history) > 0
        
        # 检查指标结构
        for metrics in agent.training_history:
            assert 'step' in metrics
            assert 'reward_mean' in metrics
            assert 'reward_std' in metrics
            assert isinstance(metrics['step'], int)
            assert isinstance(metrics['reward_mean'], (int, float))
            assert isinstance(metrics['reward_std'], (int, float))
    
    def test_training_convergence_detection(self, sample_price_data):
        """测试训练收敛检测"""
        env = make_env('single_asset', data=sample_price_data)
        agent = create_agent('PPO', env)
        
        # 训练
        agent.train(total_timesteps=5000)
        
        # 分析训练历史
        rewards = [m['reward_mean'] for m in agent.training_history]
        
        if len(rewards) > 5:
            # 检查是否有改进趋势
            recent_rewards = rewards[-5:]
            early_rewards = rewards[:5]
            
            # 最近的奖励应该不会比早期的差太多（在随机环境中）
            assert len(recent_rewards) == 5
            assert len(early_rewards) == 5
    
    def test_training_performance_tracking(self, sample_price_data, performance_timer):
        """测试训练性能跟踪"""
        env = make_env('single_asset', data=sample_price_data)
        agent = create_agent('PPO', env)
        
        with performance_timer() as timer:
            agent.train(total_timesteps=2000)
        
        # 训练时间应该在合理范围内
        assert timer.elapsed < 30.0  # 30秒内完成
        
        # 检查训练效率
        steps_per_second = 2000 / timer.elapsed
        assert steps_per_second > 10  # 至少每秒10步

class TestTrainingIntegrationWithBacktest:
    """训练与回测集成测试类"""
    
    def test_train_and_backtest_pipeline(self, sample_price_data):
        """测试训练和回测流程"""
        # 分割数据
        split_idx = int(len(sample_price_data) * 0.8)
        train_data = sample_price_data.iloc[:split_idx]
        test_data = sample_price_data.iloc[split_idx:]
        
        # 训练
        train_env = make_env('single_asset', data=train_data)
        agent = create_agent('PPO', train_env)
        agent.train(total_timesteps=3000)
        
        # 回测
        test_env = make_env('single_asset', data=test_data)
        
        # 运行回测
        obs = test_env.reset()
        portfolio_values = [test_env.portfolio_value]
        actions_taken = []
        
        done = False
        step_count = 0
        while not done and step_count < 100:
            action, _ = agent.predict(obs, deterministic=True)
            actions_taken.append(action)
            
            obs, reward, done, info = test_env.step(action)
            portfolio_values.append(info['portfolio_value'])
            step_count += 1
        
        # 验证回测结果
        assert len(portfolio_values) > 1
        assert len(actions_taken) > 0
        
        # 计算简单的性能指标
        total_return = (portfolio_values[-1] - portfolio_values[0]) / portfolio_values[0]
        assert isinstance(total_return, (int, float))
    
    def test_strategy_comparison(self, sample_price_data):
        """测试策略比较"""
        # 分割数据
        split_idx = int(len(sample_price_data) * 0.8)
        train_data = sample_price_data.iloc[:split_idx]
        test_data = sample_price_data.iloc[split_idx:]
        
        strategies_performance = {}
        
        # 测试不同算法
        algorithms = ['PPO', 'A2C']
        
        for algorithm in algorithms:
            # 训练
            train_env = make_env('single_asset', data=train_data)
            agent = create_agent(algorithm, train_env)
            agent.train(total_timesteps=2000)
            
            # 回测
            test_env = make_env('single_asset', data=test_data)
            obs = test_env.reset()
            
            total_reward = 0
            step_count = 0
            done = False
            
            while not done and step_count < 50:
                action, _ = agent.predict(obs, deterministic=True)
                obs, reward, done, info = test_env.step(action)
                total_reward += reward
                step_count += 1
            
            strategies_performance[algorithm] = {
                'total_reward': total_reward,
                'steps': step_count,
                'final_portfolio_value': info['portfolio_value']
            }
        
        # 验证所有策略都有结果
        assert len(strategies_performance) == len(algorithms)
        for algorithm in algorithms:
            assert 'total_reward' in strategies_performance[algorithm]
            assert 'final_portfolio_value' in strategies_performance[algorithm]

# 参数化集成测试
class TestParametrizedTraining:
    """参数化训练测试"""
    
    @pytest.mark.parametrize("algorithm,timesteps", [
        ('PPO', 1000),
        ('A2C', 1000),
        ('SAC', 1000),
    ])
    def test_different_algorithms_training(self, algorithm, timesteps, sample_price_data):
        """测试不同算法的训练"""
        env = make_env('single_asset', data=sample_price_data)
        agent = create_agent(algorithm, env)
        
        agent.train(total_timesteps=timesteps)
        
        assert agent.is_trained
        assert agent.algorithm == algorithm
        assert len(agent.training_history) > 0
    
    @pytest.mark.parametrize("initial_amount,expected_min_value", [
        (50000, 40000),
        (100000, 80000),
        (200000, 160000),
    ])
    def test_different_initial_amounts(self, initial_amount, expected_min_value, sample_price_data):
        """测试不同初始资金的训练"""
        env = make_env('single_asset', data=sample_price_data, initial_amount=initial_amount)
        agent = create_agent('PPO', env)
        
        # 短期训练
        agent.train(total_timesteps=1000)
        
        # 测试环境
        obs = env.reset()
        assert env.portfolio_value == initial_amount
        
        # 执行几步
        for _ in range(5):
            action, _ = agent.predict(obs)
            obs, _, done, info = env.step(action)
            if done:
                break
        
        # 投资组合价值应该在合理范围内
        assert info['portfolio_value'] > expected_min_value * 0.5  # 允许较大波动