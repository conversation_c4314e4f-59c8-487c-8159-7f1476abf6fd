#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置管理和配置验证测试

测试第二阶段第二个子任务：统一配置管理和添加配置验证
包括配置加载、验证、合并、环境变量支持等功能
"""

import unittest
import tempfile
import os
import json
import yaml
from pathlib import Path
from unittest.mock import patch, mock_open
from typing import Dict, Any, Optional, List

# 导入待测试的模块（将在实现阶段创建）
try:
    from core.config_manager import (
        UnifiedConfigManager,
        ConfigValidationError,
        ConfigLoadError,
        ConfigSchema,
        ConfigValidator,
        EnvironmentConfigLoader,
        FileConfigLoader
    )
except ImportError:
    # 测试阶段的占位符
    class UnifiedConfigManager:
        pass
    
    class ConfigValidationError(Exception):
        pass
    
    class ConfigLoadError(Exception):
        pass
    
    class ConfigSchema:
        pass
    
    class ConfigValidator:
        pass
    
    class EnvironmentConfigLoader:
        pass
    
    class FileConfigLoader:
        pass


class TestUnifiedConfigManager(unittest.TestCase):
    """统一配置管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = UnifiedConfigManager()
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_manager_initialization(self):
        """测试配置管理器初始化"""
        manager = UnifiedConfigManager()
        self.assertIsNotNone(manager)
        self.assertIsInstance(manager.get_all_configs(), dict)
        self.assertEqual(len(manager.get_all_configs()), 0)
    
    def test_load_yaml_config(self):
        """测试YAML配置文件加载"""
        yaml_content = """
        database:
          host: localhost
          port: 5432
          name: testdb
        
        api:
          timeout: 30
          retries: 3
        """
        
        yaml_file = Path(self.temp_dir) / "config.yaml"
        yaml_file.write_text(yaml_content)
        
        self.config_manager.load_from_file(str(yaml_file))
        
        config = self.config_manager.get_config("database")
        self.assertEqual(config["host"], "localhost")
        self.assertEqual(config["port"], 5432)
        self.assertEqual(config["name"], "testdb")
        
        api_config = self.config_manager.get_config("api")
        self.assertEqual(api_config["timeout"], 30)
        self.assertEqual(api_config["retries"], 3)
    
    def test_load_json_config(self):
        """测试JSON配置文件加载"""
        json_content = {
            "training": {
                "epochs": 100,
                "batch_size": 32,
                "learning_rate": 0.001
            },
            "model": {
                "type": "PPO",
                "hidden_size": 256
            }
        }
        
        json_file = Path(self.temp_dir) / "config.json"
        json_file.write_text(json.dumps(json_content))
        
        self.config_manager.load_from_file(str(json_file))
        
        training_config = self.config_manager.get_config("training")
        self.assertEqual(training_config["epochs"], 100)
        self.assertEqual(training_config["batch_size"], 32)
        self.assertEqual(training_config["learning_rate"], 0.001)
        
        model_config = self.config_manager.get_config("model")
        self.assertEqual(model_config["type"], "PPO")
        self.assertEqual(model_config["hidden_size"], 256)
    
    def test_load_multiple_configs(self):
        """测试加载多个配置文件"""
        # 基础配置
        base_config = {
            "database": {
                "host": "localhost",
                "port": 5432
            },
            "api": {
                "timeout": 30
            }
        }
        
        # 环境特定配置
        env_config = {
            "database": {
                "host": "prod-server",
                "ssl": True
            },
            "api": {
                "retries": 5
            }
        }
        
        base_file = Path(self.temp_dir) / "base.json"
        env_file = Path(self.temp_dir) / "prod.json"
        
        base_file.write_text(json.dumps(base_config))
        env_file.write_text(json.dumps(env_config))
        
        self.config_manager.load_from_file(str(base_file))
        self.config_manager.load_from_file(str(env_file))
        
        # 验证配置合并
        db_config = self.config_manager.get_config("database")
        self.assertEqual(db_config["host"], "prod-server")  # 被覆盖
        self.assertEqual(db_config["port"], 5432)  # 保留原值
        self.assertTrue(db_config["ssl"])  # 新增值
        
        api_config = self.config_manager.get_config("api")
        self.assertEqual(api_config["timeout"], 30)  # 保留原值
        self.assertEqual(api_config["retries"], 5)  # 新增值
    
    def test_environment_variable_override(self):
        """测试环境变量覆盖配置"""
        config_content = {
            "database": {
                "host": "localhost",
                "port": 5432
            }
        }
        
        config_file = Path(self.temp_dir) / "config.json"
        config_file.write_text(json.dumps(config_content))
        
        with patch.dict(os.environ, {
            "DATABASE_HOST": "env-server",
            "DATABASE_PORT": "3306"
        }):
            self.config_manager.load_from_file(str(config_file))
            self.config_manager.load_from_environment()
            
            db_config = self.config_manager.get_config("database")
            self.assertEqual(db_config["host"], "env-server")
            self.assertEqual(db_config["port"], "3306")  # 环境变量是字符串
    
    def test_config_not_found(self):
        """测试配置不存在的情况"""
        with self.assertRaises(KeyError):
            self.config_manager.get_config("nonexistent")
    
    def test_invalid_file_format(self):
        """测试无效文件格式"""
        invalid_file = Path(self.temp_dir) / "invalid.txt"
        invalid_file.write_text("not a valid config format")
        
        with self.assertRaises(ConfigLoadError):
            self.config_manager.load_from_file(str(invalid_file))


class TestConfigValidation(unittest.TestCase):
    """配置验证测试"""
    
    def setUp(self):
        """测试前准备"""
        self.validator = ConfigValidator()
        self.config_manager = UnifiedConfigManager()
    
    def test_schema_validation_success(self):
        """测试配置模式验证成功"""
        schema = {
            "type": "object",
            "properties": {
                "database": {
                    "type": "object",
                    "properties": {
                        "host": {"type": "string"},
                        "port": {"type": "integer", "minimum": 1, "maximum": 65535}
                    },
                    "required": ["host", "port"]
                }
            },
            "required": ["database"]
        }
        
        config = {
            "database": {
                "host": "localhost",
                "port": 5432
            }
        }
        
        # 应该不抛出异常
        self.validator.validate(config, schema)
    
    def test_schema_validation_failure(self):
        """测试配置模式验证失败"""
        schema = {
            "type": "object",
            "properties": {
                "database": {
                    "type": "object",
                    "properties": {
                        "host": {"type": "string"},
                        "port": {"type": "integer", "minimum": 1, "maximum": 65535}
                    },
                    "required": ["host", "port"]
                }
            },
            "required": ["database"]
        }
        
        # 缺少必需字段
        invalid_config1 = {
            "database": {
                "host": "localhost"
                # 缺少port
            }
        }
        
        with self.assertRaises(ConfigValidationError):
            self.validator.validate(invalid_config1, schema)
        
        # 类型错误
        invalid_config2 = {
            "database": {
                "host": "localhost",
                "port": "not_a_number"
            }
        }
        
        with self.assertRaises(ConfigValidationError):
            self.validator.validate(invalid_config2, schema)
        
        # 值超出范围
        invalid_config3 = {
            "database": {
                "host": "localhost",
                "port": 70000
            }
        }
        
        with self.assertRaises(ConfigValidationError):
            self.validator.validate(invalid_config3, schema)
    
    def test_custom_validation_rules(self):
        """测试自定义验证规则"""
        def validate_positive_number(value):
            if not isinstance(value, (int, float)) or value <= 0:
                raise ConfigValidationError(f"Value must be a positive number, got {value}")
        
        def validate_url(value):
            if not isinstance(value, str) or not value.startswith(('http://', 'https://')):
                raise ConfigValidationError(f"Value must be a valid URL, got {value}")
        
        self.validator.add_custom_rule("positive_number", validate_positive_number)
        self.validator.add_custom_rule("url", validate_url)
        
        # 测试正数验证
        self.validator.apply_custom_rule("positive_number", 10)  # 应该通过
        
        with self.assertRaises(ConfigValidationError):
            self.validator.apply_custom_rule("positive_number", -5)
        
        with self.assertRaises(ConfigValidationError):
            self.validator.apply_custom_rule("positive_number", "not_a_number")
        
        # 测试URL验证
        self.validator.apply_custom_rule("url", "https://example.com")  # 应该通过
        
        with self.assertRaises(ConfigValidationError):
            self.validator.apply_custom_rule("url", "not_a_url")
    
    def test_config_manager_with_validation(self):
        """测试配置管理器集成验证"""
        schema = {
            "type": "object",
            "properties": {
                "training": {
                    "type": "object",
                    "properties": {
                        "epochs": {"type": "integer", "minimum": 1},
                        "learning_rate": {"type": "number", "minimum": 0, "maximum": 1}
                    },
                    "required": ["epochs", "learning_rate"]
                }
            }
        }
        
        self.config_manager.set_validation_schema(schema)
        
        # 有效配置
        valid_config = {
            "training": {
                "epochs": 100,
                "learning_rate": 0.001
            }
        }
        
        self.config_manager.load_from_dict(valid_config)  # 应该成功
        
        # 无效配置
        invalid_config = {
            "training": {
                "epochs": -10,  # 无效值
                "learning_rate": 0.001
            }
        }
        
        with self.assertRaises(ConfigValidationError):
            self.config_manager.load_from_dict(invalid_config)


class TestConfigLoaders(unittest.TestCase):
    """配置加载器测试"""
    
    def test_file_config_loader(self):
        """测试文件配置加载器"""
        loader = FileConfigLoader()
        
        # 测试YAML加载
        yaml_content = "database:\n  host: localhost\n  port: 5432"
        with patch("os.path.exists", return_value=True), \
             patch("builtins.open", mock_open(read_data=yaml_content)):
            config = loader.load("config.yaml")
            self.assertEqual(config["database"]["host"], "localhost")
            self.assertEqual(config["database"]["port"], 5432)
        
        # 测试JSON加载
        json_content = '{"api": {"timeout": 30}}'
        with patch("os.path.exists", return_value=True), \
             patch("builtins.open", mock_open(read_data=json_content)):
            config = loader.load("config.json")
            self.assertEqual(config["api"]["timeout"], 30)
    
    def test_environment_config_loader(self):
        """测试环境变量配置加载器"""
        loader = EnvironmentConfigLoader()
        
        with patch.dict(os.environ, {
            "APP_DATABASE_HOST": "env-host",
            "APP_DATABASE_PORT": "3306",
            "APP_API_TIMEOUT": "60",
            "OTHER_VAR": "ignored"
        }):
            config = loader.load(prefix="APP_")
            
            self.assertEqual(config["database"]["host"], "env-host")
            self.assertEqual(config["database"]["port"], "3306")
            self.assertEqual(config["api"]["timeout"], "60")
            self.assertNotIn("other", config)
    
    def test_config_loader_error_handling(self):
        """测试配置加载器错误处理"""
        loader = FileConfigLoader()
        
        # 文件不存在
        with patch("os.path.exists", return_value=False):
            with self.assertRaises(ConfigLoadError):
                loader.load("nonexistent.yaml")
        
        # 无效YAML
        invalid_yaml = "invalid: yaml: content: ["
        with patch("os.path.exists", return_value=True), \
             patch("builtins.open", mock_open(read_data=invalid_yaml)):
            with self.assertRaises(ConfigLoadError):
                loader.load("invalid.yaml")
        
        # 无效JSON
        invalid_json = '{"invalid": json}'
        with patch("os.path.exists", return_value=True), \
             patch("builtins.open", mock_open(read_data=invalid_json)):
            with self.assertRaises(ConfigLoadError):
                loader.load("invalid.json")


class TestConfigIntegration(unittest.TestCase):
    """配置系统集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = UnifiedConfigManager()
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_complete_config_workflow(self):
        """测试完整的配置工作流程"""
        # 1. 设置验证模式
        schema = {
            "type": "object",
            "properties": {
                "database": {
                    "type": "object",
                    "properties": {
                        "host": {"type": "string"},
                        "port": {"type": "integer"},
                        "ssl": {"type": "boolean"}
                    },
                    "required": ["host", "port"]
                },
                "training": {
                    "type": "object",
                    "properties": {
                        "epochs": {"type": "integer", "minimum": 1},
                        "batch_size": {"type": "integer", "minimum": 1}
                    }
                }
            }
        }
        
        self.config_manager.set_validation_schema(schema)
        
        # 2. 加载基础配置
        base_config = {
            "database": {
                "host": "localhost",
                "port": 5432,
                "ssl": False
            },
            "training": {
                "epochs": 100,
                "batch_size": 32
            }
        }
        
        base_file = Path(self.temp_dir) / "base.json"
        base_file.write_text(json.dumps(base_config))
        
        self.config_manager.load_from_file(str(base_file))
        
        # 3. 环境变量覆盖
        with patch.dict(os.environ, {
            "DATABASE_HOST": "prod-server",
            "DATABASE_SSL": "true",
            "TRAINING_EPOCHS": "200"
        }):
            self.config_manager.load_from_environment()
        
        # 4. 验证最终配置
        db_config = self.config_manager.get_config("database")
        self.assertEqual(db_config["host"], "prod-server")
        self.assertEqual(db_config["port"], 5432)
        self.assertEqual(db_config["ssl"], "true")  # 环境变量是字符串
        
        training_config = self.config_manager.get_config("training")
        self.assertEqual(training_config["epochs"], "200")  # 环境变量是字符串
        self.assertEqual(training_config["batch_size"], 32)
        
        # 5. 测试配置导出
        all_config = self.config_manager.get_all_configs()
        self.assertIn("database", all_config)
        self.assertIn("training", all_config)
    
    def test_config_hot_reload(self):
        """测试配置热重载"""
        config_file = Path(self.temp_dir) / "config.json"
        
        # 初始配置
        initial_config = {"api": {"timeout": 30}}
        config_file.write_text(json.dumps(initial_config))
        
        self.config_manager.load_from_file(str(config_file))
        self.assertEqual(self.config_manager.get_config("api")["timeout"], 30)
        
        # 更新配置文件
        updated_config = {"api": {"timeout": 60}}
        config_file.write_text(json.dumps(updated_config))
        
        # 重新加载
        self.config_manager.reload()
        self.assertEqual(self.config_manager.get_config("api")["timeout"], 60)
    
    def test_config_backup_and_restore(self):
        """测试配置备份和恢复"""
        config = {
            "database": {"host": "localhost"},
            "api": {"timeout": 30}
        }
        
        self.config_manager.load_from_dict(config)
        
        # 创建备份
        backup_id = self.config_manager.create_backup()
        self.assertIsNotNone(backup_id)
        
        # 修改配置
        self.config_manager.update_config("database", {"host": "new-host"})
        self.assertEqual(self.config_manager.get_config("database")["host"], "new-host")
        
        # 恢复备份
        self.config_manager.restore_backup(backup_id)
        self.assertEqual(self.config_manager.get_config("database")["host"], "localhost")


class TestConfigPerformance(unittest.TestCase):
    """配置系统性能测试"""
    
    def setUp(self):
        """测试前准备"""
        self.config_manager = UnifiedConfigManager()
    
    def test_large_config_loading_performance(self):
        """测试大配置文件加载性能"""
        import time
        
        # 创建大配置
        large_config = {}
        for i in range(1000):
            large_config[f"section_{i}"] = {
                f"key_{j}": f"value_{j}" for j in range(100)
            }
        
        start_time = time.time()
        self.config_manager.load_from_dict(large_config)
        load_time = time.time() - start_time
        
        # 加载时间应该在合理范围内（小于1秒）
        self.assertLess(load_time, 1.0)
        
        # 测试访问性能
        start_time = time.time()
        for i in range(100):
            config = self.config_manager.get_config(f"section_{i}")
            self.assertIsNotNone(config)
        access_time = time.time() - start_time
        
        # 访问时间应该很快（小于0.1秒）
        self.assertLess(access_time, 0.1)
    
    def test_concurrent_config_access(self):
        """测试并发配置访问"""
        import threading
        import time
        
        config = {
            "shared": {"value": 42},
            "database": {"host": "localhost"}
        }
        
        self.config_manager.load_from_dict(config)
        
        results = []
        errors = []
        
        def access_config():
            try:
                for _ in range(100):
                    value = self.config_manager.get_config("shared")["value"]
                    results.append(value)
                    time.sleep(0.001)  # 模拟处理时间
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程并发访问
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=access_config)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        self.assertEqual(len(errors), 0, f"Concurrent access errors: {errors}")
        self.assertEqual(len(results), 1000)  # 10个线程 × 100次访问
        self.assertTrue(all(value == 42 for value in results))


if __name__ == '__main__':
    unittest.main()