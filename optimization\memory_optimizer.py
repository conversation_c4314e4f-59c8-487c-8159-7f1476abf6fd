"""内存优化模块

提供内存分析、缓存管理和数据加载优化功能。
"""

import gc
import sys
import psutil
import logging
import threading
import time
import weakref
from typing import Dict, List, Optional, Any, Callable, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import OrderedDict, defaultdict
from functools import wraps, lru_cache
from enum import Enum
import pickle
import json
import hashlib
import os
from pathlib import Path


class CachePolicy(Enum):
    """缓存策略枚举"""
    LRU = "lru"  # 最近最少使用
    LFU = "lfu"  # 最少使用频率
    FIFO = "fifo"  # 先进先出
    TTL = "ttl"  # 生存时间


class MemoryLevel(Enum):
    """内存级别枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class MemoryStats:
    """内存统计信息"""
    total_memory: int  # 总内存（字节）
    available_memory: int  # 可用内存（字节）
    used_memory: int  # 已用内存（字节）
    memory_percent: float  # 内存使用百分比
    process_memory: int  # 进程内存使用（字节）
    process_percent: float  # 进程内存使用百分比
    timestamp: datetime = field(default_factory=datetime.now)
    
    @property
    def memory_level(self) -> MemoryLevel:
        """获取内存使用级别"""
        if self.memory_percent < 50:
            return MemoryLevel.LOW
        elif self.memory_percent < 75:
            return MemoryLevel.NORMAL
        elif self.memory_percent < 90:
            return MemoryLevel.HIGH
        else:
            return MemoryLevel.CRITICAL


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    size: int
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    ttl: Optional[float] = None  # 生存时间（秒）
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return (datetime.now() - self.created_at).total_seconds() > self.ttl
    
    def touch(self) -> None:
        """更新访问时间和计数"""
        self.last_accessed = datetime.now()
        self.access_count += 1


class MemoryProfiler:
    """内存分析器
    
    分析内存使用情况和对象分布。
    """
    
    def __init__(self):
        self._logger = logging.getLogger(__name__)
        self._snapshots: List[MemoryStats] = []
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._monitor_interval = 5.0  # 监控间隔（秒）
        self._callbacks: List[Callable[[MemoryStats], None]] = []
        self._lock = threading.RLock()
    
    def get_current_stats(self) -> MemoryStats:
        """获取当前内存统计信息
        
        Returns:
            MemoryStats: 内存统计信息
        """
        try:
            # 系统内存信息
            memory = psutil.virtual_memory()
            
            # 进程内存信息
            process = psutil.Process()
            process_memory = process.memory_info()
            
            return MemoryStats(
                total_memory=memory.total,
                available_memory=memory.available,
                used_memory=memory.used,
                memory_percent=memory.percent,
                process_memory=process_memory.rss,
                process_percent=process.memory_percent()
            )
        except Exception as e:
            self._logger.error(f"获取内存统计信息失败: {e}")
            return MemoryStats(0, 0, 0, 0.0, 0, 0.0)
    
    def start_monitoring(self, interval: float = 5.0) -> None:
        """开始内存监控
        
        Args:
            interval: 监控间隔（秒）
        """
        with self._lock:
            if self._monitoring:
                self._logger.warning("内存监控已经在运行")
                return
            
            self._monitor_interval = interval
            self._monitoring = True
            self._monitor_thread = threading.Thread(
                target=self._monitor_loop,
                daemon=True
            )
            self._monitor_thread.start()
            self._logger.info(f"内存监控已启动，间隔: {interval}秒")
    
    def stop_monitoring(self) -> None:
        """停止内存监控"""
        with self._lock:
            if not self._monitoring:
                return
            
            self._monitoring = False
            if self._monitor_thread:
                self._monitor_thread.join(timeout=1.0)
            self._logger.info("内存监控已停止")
    
    def _monitor_loop(self) -> None:
        """监控循环"""
        while self._monitoring:
            try:
                stats = self.get_current_stats()
                
                with self._lock:
                    self._snapshots.append(stats)
                    
                    # 限制快照数量
                    if len(self._snapshots) > 1000:
                        self._snapshots = self._snapshots[-500:]
                    
                    # 调用回调函数
                    for callback in self._callbacks:
                        try:
                            callback(stats)
                        except Exception as e:
                            self._logger.error(f"内存监控回调失败: {e}")
                
                time.sleep(self._monitor_interval)
                
            except Exception as e:
                self._logger.error(f"内存监控循环错误: {e}")
                time.sleep(1.0)
    
    def add_callback(self, callback: Callable[[MemoryStats], None]) -> None:
        """添加监控回调
        
        Args:
            callback: 回调函数
        """
        with self._lock:
            self._callbacks.append(callback)
    
    def remove_callback(self, callback: Callable[[MemoryStats], None]) -> None:
        """移除监控回调
        
        Args:
            callback: 回调函数
        """
        with self._lock:
            if callback in self._callbacks:
                self._callbacks.remove(callback)
    
    def get_memory_history(self, duration: Optional[timedelta] = None) -> List[MemoryStats]:
        """获取内存历史记录
        
        Args:
            duration: 时间范围，如果为None则返回所有记录
            
        Returns:
            List[MemoryStats]: 内存历史记录
        """
        with self._lock:
            if duration is None:
                return self._snapshots.copy()
            
            cutoff_time = datetime.now() - duration
            return [s for s in self._snapshots if s.timestamp >= cutoff_time]
    
    def get_memory_trend(self, duration: timedelta = timedelta(minutes=10)) -> Dict[str, float]:
        """获取内存使用趋势
        
        Args:
            duration: 分析时间范围
            
        Returns:
            Dict[str, float]: 趋势信息
        """
        history = self.get_memory_history(duration)
        
        if len(history) < 2:
            return {'trend': 0.0, 'avg_usage': 0.0, 'max_usage': 0.0, 'min_usage': 0.0}
        
        usages = [s.memory_percent for s in history]
        
        # 计算趋势（简单线性回归斜率）
        n = len(usages)
        x_sum = sum(range(n))
        y_sum = sum(usages)
        xy_sum = sum(i * usage for i, usage in enumerate(usages))
        x2_sum = sum(i * i for i in range(n))
        
        trend = (n * xy_sum - x_sum * y_sum) / (n * x2_sum - x_sum * x_sum) if n * x2_sum != x_sum * x_sum else 0.0
        
        return {
            'trend': trend,
            'avg_usage': sum(usages) / len(usages),
            'max_usage': max(usages),
            'min_usage': min(usages)
        }
    
    def analyze_objects(self) -> Dict[str, int]:
        """分析对象分布
        
        Returns:
            Dict[str, int]: 对象类型和数量
        """
        try:
            # 强制垃圾回收
            gc.collect()
            
            # 统计对象类型
            type_counts = defaultdict(int)
            for obj in gc.get_objects():
                type_counts[type(obj).__name__] += 1
            
            return dict(type_counts)
        except Exception as e:
            self._logger.error(f"分析对象分布失败: {e}")
            return {}
    
    def get_largest_objects(self, limit: int = 10) -> List[Tuple[str, int]]:
        """获取最大的对象
        
        Args:
            limit: 返回对象数量限制
            
        Returns:
            List[Tuple[str, int]]: 对象类型和大小的列表
        """
        try:
            objects_info = []
            
            for obj in gc.get_objects():
                try:
                    size = sys.getsizeof(obj)
                    if size > 1024:  # 只关注大于1KB的对象
                        objects_info.append((type(obj).__name__, size))
                except (TypeError, AttributeError):
                    continue
            
            # 按大小排序并返回前N个
            objects_info.sort(key=lambda x: x[1], reverse=True)
            return objects_info[:limit]
            
        except Exception as e:
            self._logger.error(f"获取最大对象失败: {e}")
            return []
    
    def force_garbage_collection(self) -> Dict[str, int]:
        """强制垃圾回收
        
        Returns:
            Dict[str, int]: 回收统计信息
        """
        before_stats = self.get_current_stats()
        
        # 执行垃圾回收
        collected = {
            'generation_0': gc.collect(0),
            'generation_1': gc.collect(1),
            'generation_2': gc.collect(2)
        }
        
        after_stats = self.get_current_stats()
        
        collected['memory_freed'] = before_stats.process_memory - after_stats.process_memory
        
        self._logger.info(f"垃圾回收完成: {collected}")
        return collected


class CacheManager:
    """缓存管理器
    
    提供多种缓存策略的统一管理。
    """
    
    def __init__(self, 
                 max_size: int = 1000,
                 max_memory: int = 100 * 1024 * 1024,  # 100MB
                 policy: CachePolicy = CachePolicy.LRU,
                 default_ttl: Optional[float] = None):
        self.max_size = max_size
        self.max_memory = max_memory
        self.policy = policy
        self.default_ttl = default_ttl
        
        self._cache: Dict[str, CacheEntry] = {}
        self._access_order: OrderedDict = OrderedDict()  # 用于LRU
        self._access_frequency: Dict[str, int] = defaultdict(int)  # 用于LFU
        self._current_memory = 0
        self._lock = threading.RLock()
        self._logger = logging.getLogger(__name__)
        
        # 统计信息
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'size': 0,
            'memory_usage': 0
        }
    
    def _calculate_size(self, obj: Any) -> int:
        """计算对象大小
        
        Args:
            obj: 要计算大小的对象
            
        Returns:
            int: 对象大小（字节）
        """
        try:
            return sys.getsizeof(obj)
        except (TypeError, AttributeError):
            # 对于无法直接计算大小的对象，使用pickle序列化大小作为估算
            try:
                return len(pickle.dumps(obj))
            except Exception:
                return 1024  # 默认1KB
    
    def _generate_key(self, key: Any) -> str:
        """生成缓存键
        
        Args:
            key: 原始键
            
        Returns:
            str: 生成的字符串键
        """
        if isinstance(key, str):
            return key
        
        # 对于非字符串键，使用哈希值
        try:
            key_str = str(key)
            return hashlib.md5(key_str.encode()).hexdigest()
        except Exception:
            return str(hash(key))
    
    def put(self, key: Any, value: Any, ttl: Optional[float] = None) -> None:
        """添加缓存项
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒），如果为None则使用默认TTL
        """
        cache_key = self._generate_key(key)
        size = self._calculate_size(value)
        ttl = ttl or self.default_ttl
        
        with self._lock:
            # 检查是否需要清理过期项
            self._cleanup_expired()
            
            # 如果键已存在，先移除旧值
            if cache_key in self._cache:
                self._remove_entry(cache_key)
            
            # 检查内存限制
            while (self._current_memory + size > self.max_memory or 
                   len(self._cache) >= self.max_size) and self._cache:
                self._evict_one()
            
            # 添加新条目
            entry = CacheEntry(
                key=cache_key,
                value=value,
                size=size,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                ttl=ttl
            )
            
            self._cache[cache_key] = entry
            self._current_memory += size
            
            # 更新访问记录
            if self.policy == CachePolicy.LRU:
                self._access_order[cache_key] = True
            elif self.policy == CachePolicy.LFU:
                self._access_frequency[cache_key] = 0
            
            self._update_stats()
    
    def get(self, key: Any, default: Any = None) -> Any:
        """获取缓存项
        
        Args:
            key: 缓存键
            default: 默认值
            
        Returns:
            Any: 缓存值或默认值
        """
        cache_key = self._generate_key(key)
        
        with self._lock:
            # 检查是否需要清理过期项
            self._cleanup_expired()
            
            if cache_key not in self._cache:
                self._stats['misses'] += 1
                return default
            
            entry = self._cache[cache_key]
            
            # 检查是否过期
            if entry.is_expired():
                self._remove_entry(cache_key)
                self._stats['misses'] += 1
                return default
            
            # 更新访问信息
            entry.touch()
            
            # 更新访问记录
            if self.policy == CachePolicy.LRU:
                # 移动到末尾
                self._access_order.move_to_end(cache_key)
            elif self.policy == CachePolicy.LFU:
                self._access_frequency[cache_key] += 1
            
            self._stats['hits'] += 1
            return entry.value
    
    def remove(self, key: Any) -> bool:
        """移除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否成功移除
        """
        cache_key = self._generate_key(key)
        
        with self._lock:
            if cache_key in self._cache:
                self._remove_entry(cache_key)
                self._update_stats()
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._access_order.clear()
            self._access_frequency.clear()
            self._current_memory = 0
            self._update_stats()
    
    def _remove_entry(self, cache_key: str) -> None:
        """移除缓存条目
        
        Args:
            cache_key: 缓存键
        """
        if cache_key in self._cache:
            entry = self._cache[cache_key]
            self._current_memory -= entry.size
            del self._cache[cache_key]
            
            # 清理访问记录
            if cache_key in self._access_order:
                del self._access_order[cache_key]
            if cache_key in self._access_frequency:
                del self._access_frequency[cache_key]
    
    def _evict_one(self) -> None:
        """根据策略驱逐一个缓存项"""
        if not self._cache:
            return
        
        if self.policy == CachePolicy.LRU:
            # 移除最近最少使用的项
            cache_key = next(iter(self._access_order))
        elif self.policy == CachePolicy.LFU:
            # 移除使用频率最低的项
            cache_key = min(self._access_frequency.keys(), 
                          key=lambda k: self._access_frequency[k])
        elif self.policy == CachePolicy.FIFO:
            # 移除最早添加的项
            cache_key = next(iter(self._cache))
        else:  # TTL
            # 移除最早过期的项
            cache_key = min(self._cache.keys(), 
                          key=lambda k: self._cache[k].created_at)
        
        self._remove_entry(cache_key)
        self._stats['evictions'] += 1
    
    def _cleanup_expired(self) -> None:
        """清理过期的缓存项"""
        if self.default_ttl is None:
            return
        
        expired_keys = []
        for cache_key, entry in self._cache.items():
            if entry.is_expired():
                expired_keys.append(cache_key)
        
        for cache_key in expired_keys:
            self._remove_entry(cache_key)
    
    def _update_stats(self) -> None:
        """更新统计信息"""
        self._stats['size'] = len(self._cache)
        self._stats['memory_usage'] = self._current_memory
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            hit_rate = (self._stats['hits'] / 
                       (self._stats['hits'] + self._stats['misses'])) if (self._stats['hits'] + self._stats['misses']) > 0 else 0
            
            return {
                **self._stats,
                'hit_rate': hit_rate,
                'max_size': self.max_size,
                'max_memory': self.max_memory,
                'policy': self.policy.value
            }
    
    def resize(self, max_size: Optional[int] = None, 
              max_memory: Optional[int] = None) -> None:
        """调整缓存大小
        
        Args:
            max_size: 新的最大条目数
            max_memory: 新的最大内存使用量
        """
        with self._lock:
            if max_size is not None:
                self.max_size = max_size
            
            if max_memory is not None:
                self.max_memory = max_memory
            
            # 如果当前缓存超出新限制，进行清理
            while (len(self._cache) > self.max_size or 
                   self._current_memory > self.max_memory) and self._cache:
                self._evict_one()
            
            self._update_stats()


class DataLoader:
    """数据加载器
    
    优化数据加载和预处理性能。
    """
    
    def __init__(self, cache_manager: Optional[CacheManager] = None):
        self.cache_manager = cache_manager or CacheManager()
        self._logger = logging.getLogger(__name__)
        self._load_stats = {
            'total_loads': 0,
            'cache_hits': 0,
            'load_time': 0.0
        }
    
    def load_data(self, source: Union[str, Path], 
                 loader_func: Optional[Callable] = None,
                 cache_key: Optional[str] = None,
                 force_reload: bool = False) -> Any:
        """加载数据
        
        Args:
            source: 数据源（文件路径等）
            loader_func: 自定义加载函数
            cache_key: 缓存键，如果为None则使用source作为键
            force_reload: 是否强制重新加载
            
        Returns:
            Any: 加载的数据
        """
        start_time = time.time()
        
        try:
            # 生成缓存键
            if cache_key is None:
                cache_key = str(source)
            
            # 检查缓存
            if not force_reload:
                cached_data = self.cache_manager.get(cache_key)
                if cached_data is not None:
                    self._load_stats['cache_hits'] += 1
                    self._load_stats['total_loads'] += 1
                    return cached_data
            
            # 加载数据
            if loader_func:
                data = loader_func(source)
            else:
                data = self._default_loader(source)
            
            # 缓存数据
            self.cache_manager.put(cache_key, data)
            
            self._load_stats['total_loads'] += 1
            load_time = time.time() - start_time
            self._load_stats['load_time'] += load_time
            
            self._logger.debug(f"数据加载完成: {source}, 耗时: {load_time:.3f}秒")
            return data
            
        except Exception as e:
            self._logger.error(f"数据加载失败: {source}, 错误: {e}")
            raise
    
    def _default_loader(self, source: Union[str, Path]) -> Any:
        """默认数据加载器
        
        Args:
            source: 数据源
            
        Returns:
            Any: 加载的数据
        """
        source_path = Path(source)
        
        if not source_path.exists():
            raise FileNotFoundError(f"文件不存在: {source}")
        
        # 根据文件扩展名选择加载方式
        suffix = source_path.suffix.lower()
        
        if suffix == '.json':
            with open(source_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        elif suffix == '.pkl' or suffix == '.pickle':
            with open(source_path, 'rb') as f:
                return pickle.load(f)
        elif suffix == '.txt':
            with open(source_path, 'r', encoding='utf-8') as f:
                return f.read()
        else:
            # 默认以二进制方式读取
            with open(source_path, 'rb') as f:
                return f.read()
    
    def preload_data(self, sources: List[Union[str, Path]], 
                    loader_func: Optional[Callable] = None) -> None:
        """预加载数据
        
        Args:
            sources: 数据源列表
            loader_func: 自定义加载函数
        """
        for source in sources:
            try:
                self.load_data(source, loader_func)
            except Exception as e:
                self._logger.error(f"预加载数据失败: {source}, 错误: {e}")
    
    def get_load_stats(self) -> Dict[str, Any]:
        """获取加载统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        cache_hit_rate = (self._load_stats['cache_hits'] / 
                         self._load_stats['total_loads']) if self._load_stats['total_loads'] > 0 else 0
        
        avg_load_time = (self._load_stats['load_time'] / 
                        (self._load_stats['total_loads'] - self._load_stats['cache_hits'])) if (self._load_stats['total_loads'] - self._load_stats['cache_hits']) > 0 else 0
        
        return {
            **self._load_stats,
            'cache_hit_rate': cache_hit_rate,
            'avg_load_time': avg_load_time,
            'cache_stats': self.cache_manager.get_stats()
        }


class MemoryOptimizer:
    """内存优化器主类
    
    统一管理内存分析、缓存和数据加载优化。
    """
    
    def __init__(self, 
                 cache_size: int = 1000,
                 cache_memory: int = 100 * 1024 * 1024,
                 cache_policy: CachePolicy = CachePolicy.LRU,
                 max_memory_gb: Optional[float] = None):
        self.profiler = MemoryProfiler()
        self.cache_manager = CacheManager(
            max_size=cache_size,
            max_memory=cache_memory,
            policy=cache_policy
        )
        self.data_loader = DataLoader(self.cache_manager)
        self._logger = logging.getLogger(__name__)
        
        # 内存警告阈值
        self._warning_thresholds = {
            'memory_percent': 85.0,
            'process_percent': 10.0
        }
        
        # 自动优化设置
        self._auto_optimize_enabled = False
        self._auto_gc_threshold = 90.0  # 内存使用率超过90%时自动垃圾回收
    
    def start_monitoring(self, interval: float = 5.0) -> None:
        """开始内存监控
        
        Args:
            interval: 监控间隔（秒）
        """
        # 添加内存警告回调
        self.profiler.add_callback(self._memory_warning_callback)
        
        # 如果启用自动优化，添加自动优化回调
        if self._auto_optimize_enabled:
            self.profiler.add_callback(self._auto_optimize_callback)
        
        self.profiler.start_monitoring(interval)
        self._logger.info("内存优化器监控已启动")
    
    def stop_monitoring(self) -> None:
        """停止内存监控"""
        self.profiler.stop_monitoring()
        self._logger.info("内存优化器监控已停止")
    
    def _memory_warning_callback(self, stats: MemoryStats) -> None:
        """内存警告回调
        
        Args:
            stats: 内存统计信息
        """
        if stats.memory_percent > self._warning_thresholds['memory_percent']:
            self._logger.warning(
                f"系统内存使用率过高: {stats.memory_percent:.1f}%"
            )
        
        if stats.process_percent > self._warning_thresholds['process_percent']:
            self._logger.warning(
                f"进程内存使用率过高: {stats.process_percent:.1f}%"
            )
    
    def _auto_optimize_callback(self, stats: MemoryStats) -> None:
        """自动优化回调
        
        Args:
            stats: 内存统计信息
        """
        if stats.memory_percent > self._auto_gc_threshold:
            self._logger.info("触发自动内存优化")
            self.optimize_memory()
    
    def check_memory_usage(self) -> Dict[str, float]:
        """检查当前内存使用情况
        
        Returns:
            Dict[str, float]: 内存使用情况字典
        """
        stats = self.profiler.get_current_stats()
        return {
            'used_percent': stats.memory_percent,
            'available_gb': stats.available_memory / (1024**3),
            'used_gb': stats.used_memory / (1024**3),
            'total_memory_gb': stats.total_memory / (1024**3),
            'process_memory_gb': stats.process_memory / (1024**3),
            'process_percent': stats.process_percent
        }
    
    def optimize_memory(self) -> bool:
        """优化内存使用
        
        Returns:
            bool: 优化是否成功
        """
        try:
            before_stats = self.profiler.get_current_stats()
            
            # 清理缓存中的过期项
            cache_stats_before = self.cache_manager.get_stats()
            self.cache_manager._cleanup_expired()
            cache_stats_after = self.cache_manager.get_stats()
            
            # 强制垃圾回收
            gc_result = self.profiler.force_garbage_collection()
            
            after_stats = self.profiler.get_current_stats()
            
            result = {
                'memory_before': before_stats.process_memory,
                'memory_after': after_stats.process_memory,
                'memory_freed': before_stats.process_memory - after_stats.process_memory,
                'cache_items_removed': cache_stats_before['size'] - cache_stats_after['size'],
                'gc_result': gc_result
            }
            
            self._logger.info(f"内存优化完成: {result}")
            return True
        except Exception as e:
            self._logger.error(f"内存优化失败: {e}")
            return False
    
    def enable_auto_optimize(self, threshold: float = 90.0) -> None:
        """启用自动内存优化
        
        Args:
            threshold: 触发自动优化的内存使用率阈值
        """
        self._auto_optimize_enabled = True
        self._auto_gc_threshold = threshold
        self._logger.info(f"自动内存优化已启用，阈值: {threshold}%")
    
    def disable_auto_optimize(self) -> None:
        """禁用自动内存优化"""
        self._auto_optimize_enabled = False
        self._logger.info("自动内存优化已禁用")
    
    def set_warning_thresholds(self, 
                              memory_percent: Optional[float] = None,
                              process_percent: Optional[float] = None) -> None:
        """设置内存警告阈值
        
        Args:
            memory_percent: 系统内存使用率阈值
            process_percent: 进程内存使用率阈值
        """
        if memory_percent is not None:
            self._warning_thresholds['memory_percent'] = memory_percent
        
        if process_percent is not None:
            self._warning_thresholds['process_percent'] = process_percent
        
        self._logger.info(f"内存警告阈值已更新: {self._warning_thresholds}")
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息
        
        Returns:
            Dict[str, Any]: 综合统计信息
        """
        return {
            'memory_stats': self.profiler.get_current_stats().__dict__,
            'memory_trend': self.profiler.get_memory_trend(),
            'cache_stats': self.cache_manager.get_stats(),
            'data_loader_stats': self.data_loader.get_load_stats(),
            'object_analysis': self.profiler.analyze_objects(),
            'largest_objects': self.profiler.get_largest_objects(),
            'auto_optimize_enabled': self._auto_optimize_enabled,
            'warning_thresholds': self._warning_thresholds.copy()
        }
    
    def create_memory_decorator(self, cache_results: bool = True, 
                               ttl: Optional[float] = None):
        """创建内存优化装饰器
        
        Args:
            cache_results: 是否缓存函数结果
            ttl: 缓存生存时间
            
        Returns:
            Callable: 装饰器函数
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                # 生成缓存键
                if cache_results:
                    cache_key = f"{func.__name__}_{hash((args, tuple(sorted(kwargs.items()))))}"
                    
                    # 尝试从缓存获取结果
                    cached_result = self.cache_manager.get(cache_key)
                    if cached_result is not None:
                        return cached_result
                
                # 执行函数
                result = func(*args, **kwargs)
                
                # 缓存结果
                if cache_results:
                    self.cache_manager.put(cache_key, result, ttl=ttl)
                
                return result
            
            return wrapper
        return decorator