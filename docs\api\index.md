# API 参考

本文档提供 FinRL Crypto 的完整 API 参考，包括所有模块、类和函数的详细说明。

## 📚 模块概览

FinRL Crypto 的 API 按功能模块组织：

### 核心模块
- **[数据模块 (Data)](#数据模块)** - 数据获取、处理和管理
- **[环境模块 (Environment)](#环境模块)** - 强化学习交易环境
- **[智能体模块 (Agent)](#智能体模块)** - 强化学习算法和智能体
- **[策略模块 (Strategy)](#策略模块)** - 交易策略实现
- **[回测模块 (Backtest)](#回测模块)** - 策略回测和性能评估

### 工具模块
- **[指标模块 (Indicators)](#指标模块)** - 技术指标计算
- **[风险模块 (Risk)](#风险模块)** - 风险管理和评估
- **[可视化模块 (Visualization)](#可视化模块)** - 图表和可视化
- **[工具模块 (Utils)](#工具模块)** - 通用工具函数

### 扩展模块
- **[实时交易模块 (Live Trading)](#实时交易模块)** - 实时交易执行
- **[监控模块 (Monitoring)](#监控模块)** - 系统监控和报警
- **[插件模块 (Plugins)](#插件模块)** - 插件系统

---

## 数据模块

### finrl_crypto.data

数据模块负责加密货币数据的获取、处理和管理。

#### 类

##### DataProcessor

```python
class DataProcessor:
    """数据处理器主类"""
    
    def __init__(self, source='yfinance', cache_dir='./cache'):
        """初始化数据处理器
        
        Args:
            source (str): 数据源 ('yfinance', 'binance', 'coinbase')
            cache_dir (str): 缓存目录路径
        """
    
    def fetch_data(self, symbols, start_date, end_date, timeframe='1h'):
        """获取历史数据
        
        Args:
            symbols (list): 交易对列表
            start_date (str): 开始日期 'YYYY-MM-DD'
            end_date (str): 结束日期 'YYYY-MM-DD'
            timeframe (str): 时间周期 ('1m', '5m', '15m', '30m', '1h', '4h', '1d')
            
        Returns:
            pd.DataFrame: 包含OHLCV数据的DataFrame
        """
    
    def add_technical_indicators(self, data, indicators_config):
        """添加技术指标
        
        Args:
            data (pd.DataFrame): 原始价格数据
            indicators_config (dict): 指标配置
            
        Returns:
            pd.DataFrame: 包含技术指标的数据
        """
    
    def preprocess_data(self, data, normalize=True, handle_missing='forward_fill'):
        """数据预处理
        
        Args:
            data (pd.DataFrame): 原始数据
            normalize (bool): 是否归一化
            handle_missing (str): 缺失值处理方法
            
        Returns:
            pd.DataFrame: 预处理后的数据
        """
    
    def split_data(self, data, train_ratio=0.7, val_ratio=0.15):
        """数据分割
        
        Args:
            data (pd.DataFrame): 完整数据集
            train_ratio (float): 训练集比例
            val_ratio (float): 验证集比例
            
        Returns:
            tuple: (train_data, val_data, test_data)
        """
```

##### DataSource

```python
class DataSource:
    """数据源基类"""
    
    def fetch_historical_data(self, symbol, start_date, end_date, timeframe):
        """获取历史数据（抽象方法）"""
        raise NotImplementedError
    
    def fetch_realtime_data(self, symbol):
        """获取实时数据（抽象方法）"""
        raise NotImplementedError

class YFinanceSource(DataSource):
    """Yahoo Finance 数据源"""
    
class BinanceSource(DataSource):
    """Binance 数据源"""
    
class CoinbaseSource(DataSource):
    """Coinbase 数据源"""
```

#### 函数

```python
def load_data(symbols, start_date, end_date, source='yfinance', **kwargs):
    """快速加载数据的便捷函数
    
    Args:
        symbols (list): 交易对列表
        start_date (str): 开始日期
        end_date (str): 结束日期
        source (str): 数据源
        **kwargs: 其他参数
        
    Returns:
        pd.DataFrame: 数据
    """

def validate_data(data, min_periods=100):
    """验证数据质量
    
    Args:
        data (pd.DataFrame): 数据
        min_periods (int): 最小数据点数
        
    Returns:
        dict: 验证结果
    """

def resample_data(data, target_timeframe):
    """重采样数据到目标时间周期
    
    Args:
        data (pd.DataFrame): 原始数据
        target_timeframe (str): 目标时间周期
        
    Returns:
        pd.DataFrame: 重采样后的数据
    """
```

---

## 环境模块

### finrl_crypto.env

环境模块实现强化学习交易环境，符合 OpenAI Gym 接口。

#### 类

##### CryptoTradingEnv

```python
class CryptoTradingEnv(gym.Env):
    """加密货币交易环境"""
    
    def __init__(self, data, initial_amount=100000, transaction_cost_pct=0.001, **kwargs):
        """初始化交易环境
        
        Args:
            data (pd.DataFrame): 交易数据
            initial_amount (float): 初始资金
            transaction_cost_pct (float): 交易费用比例
            **kwargs: 其他环境参数
        """
    
    def reset(self):
        """重置环境
        
        Returns:
            np.ndarray: 初始观察状态
        """
    
    def step(self, action):
        """执行动作
        
        Args:
            action (np.ndarray): 动作向量
            
        Returns:
            tuple: (observation, reward, done, info)
        """
    
    def render(self, mode='human'):
        """渲染环境状态
        
        Args:
            mode (str): 渲染模式
        """
    
    def get_portfolio_value(self):
        """获取当前投资组合价值
        
        Returns:
            float: 投资组合总价值
        """
    
    def get_positions(self):
        """获取当前持仓
        
        Returns:
            dict: 各资产持仓信息
        """
    
    def calculate_reward(self, action, next_state):
        """计算奖励
        
        Args:
            action (np.ndarray): 执行的动作
            next_state (np.ndarray): 下一状态
            
        Returns:
            float: 奖励值
        """
```

##### PortfolioEnv

```python
class PortfolioEnv(CryptoTradingEnv):
    """投资组合交易环境"""
    
    def __init__(self, data, symbols, **kwargs):
        """初始化投资组合环境
        
        Args:
            data (pd.DataFrame): 多资产数据
            symbols (list): 交易对列表
            **kwargs: 其他参数
        """
    
    def rebalance_portfolio(self, target_weights):
        """重新平衡投资组合
        
        Args:
            target_weights (np.ndarray): 目标权重
        """
```

#### 函数

```python
def make_env(env_type='single_asset', **kwargs):
    """创建交易环境的工厂函数
    
    Args:
        env_type (str): 环境类型
        **kwargs: 环境参数
        
    Returns:
        gym.Env: 交易环境实例
    """

def normalize_observation(observation, obs_space):
    """归一化观察状态
    
    Args:
        observation (np.ndarray): 原始观察
        obs_space (gym.Space): 观察空间
        
    Returns:
        np.ndarray: 归一化后的观察
    """
```

---

## 智能体模块

### finrl_crypto.agent

智能体模块实现各种强化学习算法。

#### 类

##### BaseAgent

```python
class BaseAgent:
    """智能体基类"""
    
    def __init__(self, env, **kwargs):
        """初始化智能体
        
        Args:
            env (gym.Env): 交易环境
            **kwargs: 智能体参数
        """
    
    def train(self, total_timesteps, **kwargs):
        """训练智能体
        
        Args:
            total_timesteps (int): 总训练步数
            **kwargs: 训练参数
        """
        raise NotImplementedError
    
    def predict(self, observation, deterministic=True):
        """预测动作
        
        Args:
            observation (np.ndarray): 观察状态
            deterministic (bool): 是否确定性预测
            
        Returns:
            tuple: (action, state)
        """
        raise NotImplementedError
    
    def save(self, path):
        """保存模型
        
        Args:
            path (str): 保存路径
        """
        raise NotImplementedError
    
    def load(self, path):
        """加载模型
        
        Args:
            path (str): 模型路径
        """
        raise NotImplementedError
```

##### PPOAgent

```python
class PPOAgent(BaseAgent):
    """PPO 算法智能体"""
    
    def __init__(self, env, learning_rate=3e-4, n_steps=2048, **kwargs):
        """初始化 PPO 智能体
        
        Args:
            env (gym.Env): 交易环境
            learning_rate (float): 学习率
            n_steps (int): 每次更新的步数
            **kwargs: 其他 PPO 参数
        """
```

##### SACAgent

```python
class SACAgent(BaseAgent):
    """SAC 算法智能体"""
    
    def __init__(self, env, learning_rate=3e-4, buffer_size=1000000, **kwargs):
        """初始化 SAC 智能体
        
        Args:
            env (gym.Env): 交易环境
            learning_rate (float): 学习率
            buffer_size (int): 经验回放缓冲区大小
            **kwargs: 其他 SAC 参数
        """
```

#### 函数

```python
def create_agent(algorithm, env, **kwargs):
    """创建智能体的工厂函数
    
    Args:
        algorithm (str): 算法名称 ('PPO', 'SAC', 'A2C', 'TD3', 'DDPG')
        env (gym.Env): 交易环境
        **kwargs: 智能体参数
        
    Returns:
        BaseAgent: 智能体实例
    """

def evaluate_agent(agent, env, n_episodes=10):
    """评估智能体性能
    
    Args:
        agent (BaseAgent): 智能体
        env (gym.Env): 评估环境
        n_episodes (int): 评估回合数
        
    Returns:
        dict: 评估结果
    """
```

---

## 策略模块

### finrl_crypto.strategy

策略模块实现各种交易策略。

#### 类

##### BaseStrategy

```python
class BaseStrategy:
    """策略基类"""
    
    def __init__(self, **kwargs):
        """初始化策略"""
    
    def generate_signals(self, data):
        """生成交易信号
        
        Args:
            data (pd.DataFrame): 市场数据
            
        Returns:
            pd.DataFrame: 交易信号
        """
        raise NotImplementedError
    
    def calculate_positions(self, signals, current_positions):
        """计算目标仓位
        
        Args:
            signals (pd.DataFrame): 交易信号
            current_positions (dict): 当前仓位
            
        Returns:
            dict: 目标仓位
        """
        raise NotImplementedError
```

##### MovingAverageStrategy

```python
class MovingAverageStrategy(BaseStrategy):
    """移动平均策略"""
    
    def __init__(self, short_window=10, long_window=30, **kwargs):
        """初始化移动平均策略
        
        Args:
            short_window (int): 短期移动平均窗口
            long_window (int): 长期移动平均窗口
            **kwargs: 其他参数
        """
```

##### MeanReversionStrategy

```python
class MeanReversionStrategy(BaseStrategy):
    """均值回归策略"""
    
    def __init__(self, lookback_window=20, z_score_threshold=2.0, **kwargs):
        """初始化均值回归策略
        
        Args:
            lookback_window (int): 回看窗口
            z_score_threshold (float): Z分数阈值
            **kwargs: 其他参数
        """
```

##### RLStrategy

```python
class RLStrategy(BaseStrategy):
    """强化学习策略"""
    
    def __init__(self, agent, **kwargs):
        """初始化强化学习策略
        
        Args:
            agent (BaseAgent): 训练好的智能体
            **kwargs: 其他参数
        """
```

---

## 回测模块

### finrl_crypto.backtest

回测模块提供策略回测和性能评估功能。

#### 类

##### Backtester

```python
class Backtester:
    """回测引擎"""
    
    def __init__(self, initial_amount=100000, transaction_cost_pct=0.001, **kwargs):
        """初始化回测引擎
        
        Args:
            initial_amount (float): 初始资金
            transaction_cost_pct (float): 交易费用比例
            **kwargs: 其他回测参数
        """
    
    def run_backtest(self, strategy, data, start_date=None, end_date=None):
        """运行回测
        
        Args:
            strategy (BaseStrategy): 交易策略
            data (pd.DataFrame): 历史数据
            start_date (str): 回测开始日期
            end_date (str): 回测结束日期
            
        Returns:
            BacktestResult: 回测结果
        """
    
    def calculate_metrics(self, portfolio_values, benchmark_values=None):
        """计算性能指标
        
        Args:
            portfolio_values (pd.Series): 投资组合价值序列
            benchmark_values (pd.Series): 基准价值序列
            
        Returns:
            dict: 性能指标
        """
```

##### BacktestResult

```python
class BacktestResult:
    """回测结果类"""
    
    def __init__(self, portfolio_values, trades, positions, metrics):
        """初始化回测结果
        
        Args:
            portfolio_values (pd.Series): 投资组合价值
            trades (pd.DataFrame): 交易记录
            positions (pd.DataFrame): 持仓记录
            metrics (dict): 性能指标
        """
    
    def plot_results(self, save_path=None):
        """绘制回测结果
        
        Args:
            save_path (str): 保存路径
        """
    
    def generate_report(self, output_path=None):
        """生成回测报告
        
        Args:
            output_path (str): 输出路径
            
        Returns:
            str: 报告内容
        """
    
    def to_dict(self):
        """转换为字典格式
        
        Returns:
            dict: 结果字典
        """
```

#### 函数

```python
def quick_backtest(strategy, data, **kwargs):
    """快速回测函数
    
    Args:
        strategy (BaseStrategy): 交易策略
        data (pd.DataFrame): 历史数据
        **kwargs: 回测参数
        
    Returns:
        BacktestResult: 回测结果
    """

def compare_strategies(strategies, data, **kwargs):
    """比较多个策略
    
    Args:
        strategies (list): 策略列表
        data (pd.DataFrame): 历史数据
        **kwargs: 回测参数
        
    Returns:
        dict: 比较结果
    """
```

---

## 指标模块

### finrl_crypto.indicators

指标模块提供各种技术指标的计算。

#### 函数

```python
def sma(data, window):
    """简单移动平均
    
    Args:
        data (pd.Series): 价格数据
        window (int): 窗口大小
        
    Returns:
        pd.Series: SMA 值
    """

def ema(data, window):
    """指数移动平均
    
    Args:
        data (pd.Series): 价格数据
        window (int): 窗口大小
        
    Returns:
        pd.Series: EMA 值
    """

def rsi(data, window=14):
    """相对强弱指数
    
    Args:
        data (pd.Series): 价格数据
        window (int): 窗口大小
        
    Returns:
        pd.Series: RSI 值
    """

def macd(data, fast=12, slow=26, signal=9):
    """MACD 指标
    
    Args:
        data (pd.Series): 价格数据
        fast (int): 快线周期
        slow (int): 慢线周期
        signal (int): 信号线周期
        
    Returns:
        tuple: (macd_line, signal_line, histogram)
    """

def bollinger_bands(data, window=20, num_std=2):
    """布林带
    
    Args:
        data (pd.Series): 价格数据
        window (int): 窗口大小
        num_std (float): 标准差倍数
        
    Returns:
        tuple: (upper_band, middle_band, lower_band)
    """

def atr(high, low, close, window=14):
    """平均真实波幅
    
    Args:
        high (pd.Series): 最高价
        low (pd.Series): 最低价
        close (pd.Series): 收盘价
        window (int): 窗口大小
        
    Returns:
        pd.Series: ATR 值
    """

def stochastic(high, low, close, k_window=14, d_window=3):
    """随机指标
    
    Args:
        high (pd.Series): 最高价
        low (pd.Series): 最低价
        close (pd.Series): 收盘价
        k_window (int): %K 窗口
        d_window (int): %D 窗口
        
    Returns:
        tuple: (k_percent, d_percent)
    """

def williams_r(high, low, close, window=14):
    """威廉指标
    
    Args:
        high (pd.Series): 最高价
        low (pd.Series): 最低价
        close (pd.Series): 收盘价
        window (int): 窗口大小
        
    Returns:
        pd.Series: Williams %R 值
    """

def cci(high, low, close, window=20):
    """商品通道指数
    
    Args:
        high (pd.Series): 最高价
        low (pd.Series): 最低价
        close (pd.Series): 收盘价
        window (int): 窗口大小
        
    Returns:
        pd.Series: CCI 值
    """

def adx(high, low, close, window=14):
    """平均趋向指数
    
    Args:
        high (pd.Series): 最高价
        low (pd.Series): 最低价
        close (pd.Series): 收盘价
        window (int): 窗口大小
        
    Returns:
        pd.Series: ADX 值
    """

def obv(close, volume):
    """成交量平衡指标
    
    Args:
        close (pd.Series): 收盘价
        volume (pd.Series): 成交量
        
    Returns:
        pd.Series: OBV 值
    """

def vwap(high, low, close, volume):
    """成交量加权平均价
    
    Args:
        high (pd.Series): 最高价
        low (pd.Series): 最低价
        close (pd.Series): 收盘价
        volume (pd.Series): 成交量
        
    Returns:
        pd.Series: VWAP 值
    """
```

---

## 风险模块

### finrl_crypto.risk

风险模块提供风险管理和评估功能。

#### 类

##### RiskManager

```python
class RiskManager:
    """风险管理器"""
    
    def __init__(self, max_position_size=0.1, max_drawdown=0.2, **kwargs):
        """初始化风险管理器
        
        Args:
            max_position_size (float): 最大仓位大小
            max_drawdown (float): 最大回撤
            **kwargs: 其他风险参数
        """
    
    def check_position_size(self, position, portfolio_value):
        """检查仓位大小
        
        Args:
            position (float): 仓位大小
            portfolio_value (float): 投资组合价值
            
        Returns:
            bool: 是否符合风险要求
        """
    
    def check_drawdown(self, current_value, peak_value):
        """检查回撤
        
        Args:
            current_value (float): 当前价值
            peak_value (float): 峰值
            
        Returns:
            bool: 是否超过最大回撤
        """
    
    def calculate_var(self, returns, confidence_level=0.05):
        """计算风险价值 (VaR)
        
        Args:
            returns (pd.Series): 收益率序列
            confidence_level (float): 置信水平
            
        Returns:
            float: VaR 值
        """
    
    def calculate_cvar(self, returns, confidence_level=0.05):
        """计算条件风险价值 (CVaR)
        
        Args:
            returns (pd.Series): 收益率序列
            confidence_level (float): 置信水平
            
        Returns:
            float: CVaR 值
        """
```

#### 函数

```python
def calculate_sharpe_ratio(returns, risk_free_rate=0.02):
    """计算夏普比率
    
    Args:
        returns (pd.Series): 收益率序列
        risk_free_rate (float): 无风险利率
        
    Returns:
        float: 夏普比率
    """

def calculate_sortino_ratio(returns, risk_free_rate=0.02):
    """计算索提诺比率
    
    Args:
        returns (pd.Series): 收益率序列
        risk_free_rate (float): 无风险利率
        
    Returns:
        float: 索提诺比率
    """

def calculate_calmar_ratio(returns, max_drawdown):
    """计算卡玛比率
    
    Args:
        returns (pd.Series): 收益率序列
        max_drawdown (float): 最大回撤
        
    Returns:
        float: 卡玛比率
    """

def calculate_max_drawdown(portfolio_values):
    """计算最大回撤
    
    Args:
        portfolio_values (pd.Series): 投资组合价值序列
        
    Returns:
        tuple: (max_drawdown, start_date, end_date)
    """

def calculate_volatility(returns, annualize=True):
    """计算波动率
    
    Args:
        returns (pd.Series): 收益率序列
        annualize (bool): 是否年化
        
    Returns:
        float: 波动率
    """

def calculate_beta(portfolio_returns, market_returns):
    """计算贝塔系数
    
    Args:
        portfolio_returns (pd.Series): 投资组合收益率
        market_returns (pd.Series): 市场收益率
        
    Returns:
        float: 贝塔系数
    """
```

---

## 可视化模块

### finrl_crypto.visualization

可视化模块提供图表和可视化功能。

#### 函数

```python
def plot_price_data(data, symbols=None, save_path=None):
    """绘制价格数据
    
    Args:
        data (pd.DataFrame): 价格数据
        symbols (list): 要绘制的交易对
        save_path (str): 保存路径
    """

def plot_portfolio_performance(portfolio_values, benchmark_values=None, save_path=None):
    """绘制投资组合性能
    
    Args:
        portfolio_values (pd.Series): 投资组合价值
        benchmark_values (pd.Series): 基准价值
        save_path (str): 保存路径
    """

def plot_drawdown(portfolio_values, save_path=None):
    """绘制回撤图
    
    Args:
        portfolio_values (pd.Series): 投资组合价值
        save_path (str): 保存路径
    """

def plot_returns_distribution(returns, save_path=None):
    """绘制收益率分布
    
    Args:
        returns (pd.Series): 收益率序列
        save_path (str): 保存路径
    """

def plot_correlation_matrix(data, save_path=None):
    """绘制相关性矩阵
    
    Args:
        data (pd.DataFrame): 数据
        save_path (str): 保存路径
    """

def plot_technical_indicators(data, indicators, save_path=None):
    """绘制技术指标
    
    Args:
        data (pd.DataFrame): 价格数据
        indicators (dict): 技术指标数据
        save_path (str): 保存路径
    """

def plot_trading_signals(data, signals, save_path=None):
    """绘制交易信号
    
    Args:
        data (pd.DataFrame): 价格数据
        signals (pd.DataFrame): 交易信号
        save_path (str): 保存路径
    """

def create_dashboard(backtest_result, save_path=None):
    """创建交互式仪表板
    
    Args:
        backtest_result (BacktestResult): 回测结果
        save_path (str): 保存路径
    """
```

---

## 工具模块

### finrl_crypto.utils

工具模块提供通用工具函数。

#### 函数

```python
def setup_logging(config=None):
    """设置日志配置
    
    Args:
        config (dict): 日志配置
    """

def load_config(config_path):
    """加载配置文件
    
    Args:
        config_path (str): 配置文件路径
        
    Returns:
        dict: 配置字典
    """

def save_model(model, path, metadata=None):
    """保存模型
    
    Args:
        model: 模型对象
        path (str): 保存路径
        metadata (dict): 元数据
    """

def load_model(path):
    """加载模型
    
    Args:
        path (str): 模型路径
        
    Returns:
        tuple: (model, metadata)
    """

def calculate_returns(prices):
    """计算收益率
    
    Args:
        prices (pd.Series): 价格序列
        
    Returns:
        pd.Series: 收益率序列
    """

def normalize_data(data, method='minmax'):
    """数据归一化
    
    Args:
        data (pd.DataFrame): 原始数据
        method (str): 归一化方法
        
    Returns:
        pd.DataFrame: 归一化后的数据
    """

def split_train_test(data, train_ratio=0.8):
    """分割训练测试集
    
    Args:
        data (pd.DataFrame): 数据
        train_ratio (float): 训练集比例
        
    Returns:
        tuple: (train_data, test_data)
    """

def create_features(data, feature_config):
    """创建特征
    
    Args:
        data (pd.DataFrame): 原始数据
        feature_config (dict): 特征配置
        
    Returns:
        pd.DataFrame: 特征数据
    """

def validate_data_quality(data):
    """验证数据质量
    
    Args:
        data (pd.DataFrame): 数据
        
    Returns:
        dict: 验证结果
    """
```

---

## 实时交易模块

### finrl_crypto.live

实时交易模块提供实时交易执行功能。

#### 类

##### LiveTrader

```python
class LiveTrader:
    """实时交易器"""
    
    def __init__(self, exchange, strategy, risk_manager=None, **kwargs):
        """初始化实时交易器
        
        Args:
            exchange (Exchange): 交易所接口
            strategy (BaseStrategy): 交易策略
            risk_manager (RiskManager): 风险管理器
            **kwargs: 其他参数
        """
    
    def start_trading(self):
        """开始交易"""
    
    def stop_trading(self):
        """停止交易"""
    
    def place_order(self, symbol, side, amount, order_type='market'):
        """下单
        
        Args:
            symbol (str): 交易对
            side (str): 买卖方向 ('buy', 'sell')
            amount (float): 数量
            order_type (str): 订单类型
            
        Returns:
            dict: 订单信息
        """
    
    def cancel_order(self, order_id):
        """取消订单
        
        Args:
            order_id (str): 订单ID
            
        Returns:
            bool: 是否成功取消
        """
    
    def get_portfolio(self):
        """获取投资组合
        
        Returns:
            dict: 投资组合信息
        """
    
    def get_open_orders(self):
        """获取未成交订单
        
        Returns:
            list: 订单列表
        """
```

##### Exchange

```python
class Exchange:
    """交易所接口基类"""
    
    def connect(self):
        """连接交易所"""
        raise NotImplementedError
    
    def disconnect(self):
        """断开连接"""
        raise NotImplementedError
    
    def get_ticker(self, symbol):
        """获取行情数据"""
        raise NotImplementedError
    
    def place_order(self, symbol, side, amount, price=None, order_type='market'):
        """下单"""
        raise NotImplementedError
    
    def cancel_order(self, order_id):
        """取消订单"""
        raise NotImplementedError
    
    def get_balance(self):
        """获取账户余额"""
        raise NotImplementedError

class BinanceExchange(Exchange):
    """Binance 交易所"""
    
class CoinbaseExchange(Exchange):
    """Coinbase 交易所"""
```

---

## 监控模块

### finrl_crypto.monitoring

监控模块提供系统监控和报警功能。

#### 类

##### SystemMonitor

```python
class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, config=None):
        """初始化系统监控器
        
        Args:
            config (dict): 监控配置
        """
    
    def start_monitoring(self):
        """开始监控"""
    
    def stop_monitoring(self):
        """停止监控"""
    
    def get_system_metrics(self):
        """获取系统指标
        
        Returns:
            dict: 系统指标
        """
    
    def check_alerts(self):
        """检查报警条件
        
        Returns:
            list: 报警列表
        """
```

##### AlertManager

```python
class AlertManager:
    """报警管理器"""
    
    def __init__(self, channels=None):
        """初始化报警管理器
        
        Args:
            channels (list): 报警渠道
        """
    
    def send_alert(self, message, level='info'):
        """发送报警
        
        Args:
            message (str): 报警消息
            level (str): 报警级别
        """
    
    def add_channel(self, channel):
        """添加报警渠道
        
        Args:
            channel: 报警渠道对象
        """
```

---

## 插件模块

### finrl_crypto.plugins

插件模块提供插件系统支持。

#### 类

##### PluginManager

```python
class PluginManager:
    """插件管理器"""
    
    def __init__(self, plugin_dir='./plugins'):
        """初始化插件管理器
        
        Args:
            plugin_dir (str): 插件目录
        """
    
    def load_plugins(self):
        """加载插件"""
    
    def get_plugin(self, name):
        """获取插件
        
        Args:
            name (str): 插件名称
            
        Returns:
            Plugin: 插件对象
        """
    
    def list_plugins(self):
        """列出所有插件
        
        Returns:
            list: 插件列表
        """
```

##### BasePlugin

```python
class BasePlugin:
    """插件基类"""
    
    def __init__(self):
        """初始化插件"""
    
    def initialize(self):
        """初始化插件"""
        pass
    
    def execute(self, *args, **kwargs):
        """执行插件功能"""
        raise NotImplementedError
    
    def cleanup(self):
        """清理插件资源"""
        pass
```

---

## 🔧 使用示例

### 完整的交易流程

```python
import finrl_crypto as fc
from finrl_crypto.config import get_config

# 1. 加载配置
config = get_config()

# 2. 获取数据
data_processor = fc.data.DataProcessor(source='yfinance')
data = data_processor.fetch_data(
    symbols=['BTC-USD', 'ETH-USD'],
    start_date='2022-01-01',
    end_date='2023-12-31',
    timeframe='1h'
)

# 3. 添加技术指标
data = data_processor.add_technical_indicators(data, config['data']['indicators'])

# 4. 创建环境
env = fc.env.make_env('portfolio', data=data, **config['env'])

# 5. 创建智能体
agent = fc.agent.create_agent('PPO', env, **config['model'])

# 6. 训练智能体
agent.train(total_timesteps=config['model']['total_timesteps'])

# 7. 保存模型
agent.save('models/trained/ppo_portfolio')

# 8. 回测策略
strategy = fc.strategy.RLStrategy(agent)
backtester = fc.backtest.Backtester(**config['backtest'])
result = backtester.run_backtest(strategy, data)

# 9. 分析结果
result.plot_results()
print(result.metrics)

# 10. 实时交易（可选）
if config['live_trading']['mode'] == 'live':
    exchange = fc.live.BinanceExchange(**config['live_trading'])
    trader = fc.live.LiveTrader(exchange, strategy)
    trader.start_trading()
```

### 自定义策略示例

```python
class CustomStrategy(fc.strategy.BaseStrategy):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.short_window = kwargs.get('short_window', 10)
        self.long_window = kwargs.get('long_window', 30)
    
    def generate_signals(self, data):
        signals = pd.DataFrame(index=data.index)
        
        # 计算移动平均
        short_ma = fc.indicators.sma(data['close'], self.short_window)
        long_ma = fc.indicators.sma(data['close'], self.long_window)
        
        # 生成信号
        signals['signal'] = 0
        signals.loc[short_ma > long_ma, 'signal'] = 1  # 买入
        signals.loc[short_ma < long_ma, 'signal'] = -1  # 卖出
        
        return signals
    
    def calculate_positions(self, signals, current_positions):
        # 根据信号计算目标仓位
        target_positions = {}
        for symbol in current_positions.keys():
            if signals.loc[signals.index[-1], 'signal'] == 1:
                target_positions[symbol] = 0.5  # 50% 仓位
            elif signals.loc[signals.index[-1], 'signal'] == -1:
                target_positions[symbol] = 0.0  # 空仓
            else:
                target_positions[symbol] = current_positions[symbol]  # 保持不变
        
        return target_positions

# 使用自定义策略
custom_strategy = CustomStrategy(short_window=5, long_window=20)
result = fc.backtest.quick_backtest(custom_strategy, data)
```

这个 API 参考文档提供了 FinRL Crypto 所有主要模块和功能的详细说明。每个模块都有清晰的类和函数定义，包括参数说明和返回值类型，方便开发者快速上手和深入使用。