#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心模块

包含依赖注入容器、统一配置管理和其他核心功能
"""

# 依赖注入相关
from .dependency_injection import (
    DIContainer,
    ServiceLifetime,
    ServiceDescriptor,
    ServiceNotFoundError,
    CircularDependencyError,
    InvalidServiceError
)

# 配置管理相关
from .config_manager import (
    UnifiedConfigManager,
    ConfigValidationError,
    ConfigLoadError,
    ConfigSchema,
    ConfigValidator,
    EnvironmentConfigLoader,
    FileConfigLoader,
    ConfigLoader
)

from .drl_agent import (
    AgentFactory,
    ModelBuilder,
    TrainingService,
    PredictionService,
    RefactoredDRLAgent,
    DRLAgentConfigValidator,
    register_drl_agent_dependencies,
    create_drl_agent_with_config,
    create_drl_agent_from_file
)

__all__ = [
    # Dependency Injection
    'DIContainer',
    'injectable',
    'inject',
    'Singleton',
    'Transient',
    'Scoped',
    'ServiceLifetime',
    'DependencyResolutionError',
    'CircularDependencyError',
    
    # Configuration Management
    'UnifiedConfigManager',
    'ConfigValidationError',
    'ConfigLoadError',
    'ConfigSchema',
    'ConfigValidator',
    'EnvironmentConfigLoader',
    'FileConfigLoader',
    'ConfigLoader',
    
    # DRL Agent Refactored Components
    'AgentFactory',
    'ModelBuilder',
    'TrainingService',
    'PredictionService',
    'RefactoredDRLAgent',
    'DRLAgentConfigValidator',
    'register_drl_agent_dependencies',
    'create_drl_agent_with_config',
    'create_drl_agent_from_file'
]

__version__ = '1.0.0'
__author__ = 'AI Assistant'
__description__ = 'Core infrastructure components for FinRL Crypto project'