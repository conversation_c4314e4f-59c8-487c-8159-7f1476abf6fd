#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器使用示例
文件名：config_manager_example.py
目标：展示如何使用新的配置管理器替代原有的全局变量导入方式
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config_manager import ConfigManager, get_config_manager, init_config_manager, get_config, set_config


def example_basic_usage():
    """
    基础使用示例
    """
    print("=== 基础使用示例 ===")
    
    # 方式1：直接创建配置管理器实例
    config_manager = ConfigManager()
    
    # 获取配置值
    timeframe = config_manager.get('timeframe')
    ticker_list = config_manager.get('ticker_list')
    
    print(f"时间框架: {timeframe}")
    print(f"交易对数量: {len(ticker_list)}")
    print(f"第一个交易对: {ticker_list[0]}")
    
    # 获取计算配置
    train_start = config_manager.get('train_start_date')
    print(f"训练开始日期: {train_start}")
    
    # 设置配置值
    config_manager.set('timeframe', '1h')
    print(f"修改后的时间框架: {config_manager.get('timeframe')}")
    print(f"重新计算的训练开始日期: {config_manager.get('train_start_date')}")


def example_global_config():
    """
    全局配置管理器示例
    """
    print("\n=== 全局配置管理器示例 ===")
    
    # 初始化全局配置管理器
    config_manager = get_config_manager()
    
    # 使用便捷函数
    print(f"训练蜡烛数: {get_config('no_candles_for_train')}")
    print(f"验证蜡烛数: {get_config('no_candles_for_val')}")
    
    # 修改配置
    set_config('h_trials', 100)
    print(f"修改后的试验数: {get_config('h_trials')}")


def example_file_config():
    """
    从文件加载配置示例
    """
    print("\n=== 从文件加载配置示例 ===")
    
    # 配置文件路径
    config_file = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        'config', 'default_config.yaml'
    )
    
    if os.path.exists(config_file):
        # 从文件初始化配置管理器
        config_manager = init_config_manager(config_file)
        
        print(f"从文件加载的配置:")
        print(f"时间框架: {get_config('timeframe')}")
        print(f"优化试验数: {get_config('h_trials')}")
        print(f"环境初始资金: {get_config('environment.initial_capital', 'N/A')}")
        
        # 打印完整配置
        config_manager.print_config("从YAML文件加载的配置")
    else:
        print(f"配置文件不存在: {config_file}")


def example_config_validation():
    """
    配置验证示例
    """
    print("\n=== 配置验证示例 ===")
    
    config_manager = get_config_manager()
    
    # 验证当前配置
    is_valid = config_manager.validate_config()
    print(f"当前配置是否有效: {is_valid}")
    
    # 测试无效配置
    config_manager.set('no_candles_for_train', -100)
    is_valid = config_manager.validate_config()
    print(f"设置无效训练蜡烛数后配置是否有效: {is_valid}")
    
    # 恢复有效配置
    config_manager.set('no_candles_for_train', 20000)
    is_valid = config_manager.validate_config()
    print(f"恢复有效配置后是否有效: {is_valid}")


def example_save_config():
    """
    保存配置示例
    """
    print("\n=== 保存配置示例 ===")
    
    config_manager = get_config_manager()
    
    # 修改一些配置
    config_manager.set('timeframe', '15m')
    config_manager.set('h_trials', 200)
    config_manager.set('no_candles_for_train', 30000)
    
    # 保存到JSON文件
    output_dir = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        'config'
    )
    os.makedirs(output_dir, exist_ok=True)
    
    json_file = os.path.join(output_dir, 'modified_config.json')
    yaml_file = os.path.join(output_dir, 'modified_config.yaml')
    
    try:
        config_manager.save_to_file(json_file)
        config_manager.save_to_file(yaml_file)
        print(f"配置已保存到:")
        print(f"  JSON: {json_file}")
        print(f"  YAML: {yaml_file}")
    except Exception as e:
        print(f"保存配置失败: {e}")


def example_migration_comparison():
    """
    迁移对比示例：展示新旧方式的对比
    """
    print("\n=== 迁移对比示例 ===")
    
    print("旧方式（全局变量导入）:")
    print("```python")
    print("from config_main import TIMEFRAME, TICKER_LIST, no_candles_for_train")
    print("print(f'时间框架: {TIMEFRAME}')")
    print("print(f'训练蜡烛数: {no_candles_for_train}')")
    print("```")
    
    print("\n新方式（配置管理器）:")
    print("```python")
    print("from utils.config_manager import get_config")
    print("print(f'时间框架: {get_config(\"timeframe\")}')")
    print("print(f'训练蜡烛数: {get_config(\"no_candles_for_train\")}')")
    print("```")
    
    print("\n新方式的优势:")
    print("1. 统一的配置管理接口")
    print("2. 支持配置文件加载")
    print("3. 配置验证功能")
    print("4. 动态配置计算")
    print("5. 更好的可维护性")


def main():
    """
    主函数：运行所有示例
    """
    print("FinRL-Crypto 配置管理器使用示例")
    print("=" * 50)
    
    try:
        example_basic_usage()
        example_global_config()
        example_file_config()
        example_config_validation()
        example_save_config()
        example_migration_comparison()
        
        print("\n=== 所有示例运行完成 ===")
        
    except Exception as e:
        print(f"运行示例时出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()