# 贡献指南

感谢您对 FinRL Crypto 项目的关注！我们欢迎所有形式的贡献，包括但不限于代码、文档、测试、问题报告和功能建议。

## 🤝 如何贡献

### 贡献类型

我们欢迎以下类型的贡献：

- **🐛 Bug 修复**: 修复已知问题
- **✨ 新功能**: 添加新的功能或改进
- **📚 文档**: 改进文档、添加示例
- **🧪 测试**: 增加测试覆盖率
- **🎨 代码质量**: 重构、性能优化
- **🌐 国际化**: 翻译和本地化
- **📦 依赖管理**: 更新依赖、安全修复

### 贡献流程

1. **Fork 项目**
   ```bash
   # 在 GitHub 上 Fork 项目
   # 克隆您的 Fork
   git clone https://github.com/YOUR_USERNAME/FinRL_Crypto.git
   cd FinRL_Crypto
   ```

2. **设置开发环境**
   ```bash
   # 创建虚拟环境
   python -m venv venv
   source venv/bin/activate  # Windows: venv\Scripts\activate
   
   # 安装开发依赖
   pip install -e ".[dev]"
   
   # 安装 pre-commit hooks
   pre-commit install
   ```

3. **创建功能分支**
   ```bash
   # 从 main 分支创建新分支
   git checkout -b feature/your-feature-name
   # 或者修复 bug
   git checkout -b fix/issue-number
   ```

4. **进行开发**
   - 编写代码
   - 添加测试
   - 更新文档
   - 确保代码质量

5. **提交更改**
   ```bash
   # 添加更改
   git add .
   
   # 提交（遵循提交消息规范）
   git commit -m "feat: add new trading strategy"
   ```

6. **推送并创建 PR**
   ```bash
   # 推送到您的 Fork
   git push origin feature/your-feature-name
   
   # 在 GitHub 上创建 Pull Request
   ```

## 📝 开发规范

### 代码风格

我们使用以下工具确保代码质量：

- **Black**: 代码格式化
- **isort**: 导入排序
- **flake8**: 代码检查
- **mypy**: 类型检查
- **pylint**: 代码质量分析

#### 代码格式化

```bash
# 格式化代码
black .

# 排序导入
isort .

# 检查代码风格
flake8 .

# 类型检查
mypy finrl_crypto/

# 代码质量检查
pylint finrl_crypto/
```

#### 代码风格指南

1. **命名规范**
   ```python
   # 类名：PascalCase
   class TradingStrategy:
       pass
   
   # 函数和变量：snake_case
   def calculate_returns(price_data):
       portfolio_value = 100000
       return portfolio_value
   
   # 常量：UPPER_SNAKE_CASE
   MAX_POSITION_SIZE = 0.1
   DEFAULT_TIMEFRAME = '1h'
   
   # 私有方法：前缀下划线
   def _internal_method(self):
       pass
   ```

2. **文档字符串**
   ```python
   def calculate_sharpe_ratio(returns, risk_free_rate=0.02):
       """计算夏普比率
       
       Args:
           returns (pd.Series): 收益率序列
           risk_free_rate (float, optional): 无风险利率. Defaults to 0.02.
           
       Returns:
           float: 夏普比率
           
       Raises:
           ValueError: 当收益率序列为空时
           
       Example:
           >>> returns = pd.Series([0.01, 0.02, -0.01, 0.03])
           >>> sharpe = calculate_sharpe_ratio(returns)
           >>> print(f"Sharpe Ratio: {sharpe:.4f}")
       """
   ```

3. **类型注解**
   ```python
   from typing import List, Dict, Optional, Union
   import pandas as pd
   import numpy as np
   
   def process_data(
       data: pd.DataFrame,
       symbols: List[str],
       config: Dict[str, Union[str, int, float]],
       normalize: bool = True
   ) -> pd.DataFrame:
       """处理交易数据"""
       pass
   
   class Portfolio:
       def __init__(self, initial_amount: float) -> None:
           self.cash: float = initial_amount
           self.positions: Dict[str, float] = {}
       
       def get_value(self) -> float:
           """获取投资组合总价值"""
           return self.cash + sum(self.positions.values())
   ```

### 提交消息规范

我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### 提交类型

- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式（不影响功能）
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动
- `perf`: 性能优化
- `ci`: CI/CD 相关

#### 示例

```bash
# 新功能
git commit -m "feat(agent): add SAC algorithm implementation"

# Bug 修复
git commit -m "fix(data): handle missing values in price data"

# 文档更新
git commit -m "docs(api): update trading environment documentation"

# 重构
git commit -m "refactor(backtest): improve performance calculation"

# 破坏性变更
git commit -m "feat(env)!: change reward function signature

BREAKING CHANGE: reward function now requires additional parameter"
```

## 🧪 测试指南

### 测试结构

```
tests/
├── unit/                 # 单元测试
│   ├── test_data.py
│   ├── test_env.py
│   ├── test_agent.py
│   └── test_strategy.py
├── integration/          # 集成测试
│   ├── test_training.py
│   └── test_backtest.py
├── e2e/                  # 端到端测试
│   └── test_full_pipeline.py
├── fixtures/             # 测试数据
│   ├── sample_data.csv
│   └── test_config.py
└── conftest.py          # pytest 配置
```

### 编写测试

#### 单元测试示例

```python
# tests/unit/test_indicators.py
import pytest
import pandas as pd
import numpy as np
from finrl_crypto.indicators import sma, ema, rsi

class TestTechnicalIndicators:
    """技术指标测试类"""
    
    @pytest.fixture
    def sample_prices(self):
        """测试用价格数据"""
        return pd.Series([100, 102, 101, 103, 105, 104, 106, 108, 107, 109])
    
    def test_sma_calculation(self, sample_prices):
        """测试简单移动平均计算"""
        window = 3
        result = sma(sample_prices, window)
        
        # 检查结果长度
        assert len(result) == len(sample_prices)
        
        # 检查前几个值为 NaN
        assert pd.isna(result.iloc[:window-1]).all()
        
        # 检查具体计算值
        expected_first_value = sample_prices.iloc[:window].mean()
        assert abs(result.iloc[window-1] - expected_first_value) < 1e-10
    
    def test_sma_edge_cases(self):
        """测试边界情况"""
        # 空序列
        empty_series = pd.Series([])
        result = sma(empty_series, 5)
        assert len(result) == 0
        
        # 窗口大于数据长度
        short_series = pd.Series([1, 2, 3])
        result = sma(short_series, 5)
        assert pd.isna(result).all()
        
        # 包含 NaN 的数据
        nan_series = pd.Series([1, np.nan, 3, 4, 5])
        result = sma(nan_series, 3)
        # 应该正确处理 NaN 值
        assert not pd.isna(result.iloc[-1])
    
    @pytest.mark.parametrize("window,expected_length", [
        (5, 10),
        (10, 10),
        (1, 10),
    ])
    def test_sma_different_windows(self, sample_prices, window, expected_length):
        """测试不同窗口大小"""
        result = sma(sample_prices, window)
        assert len(result) == expected_length
    
    def test_rsi_bounds(self, sample_prices):
        """测试 RSI 值范围"""
        result = rsi(sample_prices, 14)
        
        # RSI 应该在 0-100 之间
        valid_values = result.dropna()
        assert (valid_values >= 0).all()
        assert (valid_values <= 100).all()
```

#### 集成测试示例

```python
# tests/integration/test_training.py
import pytest
import tempfile
import shutil
from pathlib import Path
from finrl_crypto.data import DataProcessor
from finrl_crypto.env import make_env
from finrl_crypto.agent import create_agent

class TestTrainingPipeline:
    """训练流程集成测试"""
    
    @pytest.fixture
    def temp_dir(self):
        """临时目录"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def sample_data(self):
        """样本数据"""
        data_processor = DataProcessor()
        return data_processor.fetch_data(
            symbols=['BTC-USD'],
            start_date='2023-01-01',
            end_date='2023-01-31',
            timeframe='1h'
        )
    
    def test_full_training_pipeline(self, sample_data, temp_dir):
        """测试完整训练流程"""
        # 创建环境
        env = make_env('single_asset', data=sample_data, initial_amount=10000)
        
        # 创建智能体
        agent = create_agent('PPO', env, learning_rate=1e-3)
        
        # 训练（少量步数用于测试）
        agent.train(total_timesteps=1000)
        
        # 保存模型
        model_path = temp_dir / 'test_model'
        agent.save(str(model_path))
        
        # 验证模型文件存在
        assert model_path.exists()
        
        # 加载模型并测试预测
        agent.load(str(model_path))
        obs = env.reset()
        action, _ = agent.predict(obs)
        
        # 验证动作格式
        assert action is not None
        assert len(action) == env.action_space.shape[0]
    
    def test_training_with_different_algorithms(self, sample_data):
        """测试不同算法的训练"""
        algorithms = ['PPO', 'A2C', 'SAC']
        
        for algorithm in algorithms:
            env = make_env('single_asset', data=sample_data)
            agent = create_agent(algorithm, env)
            
            # 短时间训练
            agent.train(total_timesteps=100)
            
            # 测试预测
            obs = env.reset()
            action, _ = agent.predict(obs)
            assert action is not None
```

#### 端到端测试示例

```python
# tests/e2e/test_full_pipeline.py
import pytest
from finrl_crypto import (
    DataProcessor, make_env, create_agent, 
    Backtester, RLStrategy
)

class TestFullPipeline:
    """端到端测试"""
    
    @pytest.mark.slow
    def test_complete_workflow(self):
        """测试完整工作流程"""
        # 1. 数据获取和处理
        data_processor = DataProcessor()
        data = data_processor.fetch_data(
            symbols=['BTC-USD'],
            start_date='2023-01-01',
            end_date='2023-03-31',
            timeframe='1h'
        )
        
        # 添加技术指标
        indicators_config = {
            'sma': [10, 20],
            'rsi': [14],
            'macd': [(12, 26, 9)]
        }
        data = data_processor.add_technical_indicators(data, indicators_config)
        
        # 2. 分割数据
        train_data, test_data = data_processor.split_data(data, train_ratio=0.8)
        
        # 3. 创建环境和智能体
        train_env = make_env('single_asset', data=train_data)
        agent = create_agent('PPO', train_env, learning_rate=3e-4)
        
        # 4. 训练
        agent.train(total_timesteps=5000)
        
        # 5. 创建策略并回测
        strategy = RLStrategy(agent)
        backtester = Backtester(initial_amount=100000)
        result = backtester.run_backtest(strategy, test_data)
        
        # 6. 验证结果
        assert result is not None
        assert 'total_return' in result.metrics
        assert 'sharpe_ratio' in result.metrics
        assert len(result.portfolio_values) > 0
        
        # 验证性能指标合理性
        assert -1 <= result.metrics['total_return'] <= 10  # 合理的收益范围
        assert -5 <= result.metrics['sharpe_ratio'] <= 5   # 合理的夏普比率范围
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/unit/test_indicators.py

# 运行特定测试类
pytest tests/unit/test_indicators.py::TestTechnicalIndicators

# 运行特定测试方法
pytest tests/unit/test_indicators.py::TestTechnicalIndicators::test_sma_calculation

# 运行并显示覆盖率
pytest --cov=finrl_crypto --cov-report=html

# 运行慢速测试
pytest -m slow

# 跳过慢速测试
pytest -m "not slow"

# 并行运行测试
pytest -n auto

# 详细输出
pytest -v

# 在第一个失败时停止
pytest -x
```

### 测试配置

```python
# tests/conftest.py
import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock

@pytest.fixture(scope="session")
def sample_price_data():
    """会话级别的样本价格数据"""
    dates = pd.date_range('2023-01-01', periods=1000, freq='1H')
    np.random.seed(42)  # 确保可重现性
    
    # 生成模拟价格数据
    returns = np.random.normal(0, 0.02, 1000)
    prices = 100 * np.exp(np.cumsum(returns))
    
    data = pd.DataFrame({
        'open': prices * (1 + np.random.normal(0, 0.001, 1000)),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.01, 1000))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.01, 1000))),
        'close': prices,
        'volume': np.random.randint(1000, 10000, 1000)
    }, index=dates)
    
    return data

@pytest.fixture
def mock_exchange():
    """模拟交易所"""
    exchange = Mock()
    exchange.get_ticker.return_value = {
        'symbol': 'BTC-USD',
        'price': 50000,
        'volume': 1000
    }
    exchange.place_order.return_value = {
        'id': '12345',
        'status': 'filled',
        'symbol': 'BTC-USD',
        'side': 'buy',
        'amount': 0.1,
        'price': 50000
    }
    return exchange

# 测试标记
pytest.mark.slow = pytest.mark.slow
pytest.mark.integration = pytest.mark.integration
pytest.mark.unit = pytest.mark.unit
```

## 📚 文档贡献

### 文档结构

```
docs/
├── index.md                 # 主页
├── getting-started/         # 入门指南
│   ├── installation.md
│   ├── quickstart.md
│   └── configuration.md
├── api/                     # API 参考
│   ├── index.md
│   ├── data.md
│   ├── env.md
│   └── agent.md
├── tutorials/               # 教程
│   ├── basic-trading.md
│   ├── custom-strategy.md
│   └── advanced-features.md
├── development/             # 开发文档
│   ├── contributing.md
│   ├── architecture.md
│   └── testing.md
└── examples/                # 示例
    ├── simple-trading.py
    └── portfolio-optimization.py
```

### 文档编写规范

1. **使用 Markdown 格式**
2. **添加适当的标题层级**
3. **包含代码示例**
4. **添加图表和图像（使用 SVG 格式）**
5. **保持内容更新**

### 文档本地预览

```bash
# 安装 MkDocs
pip install mkdocs mkdocs-material

# 本地预览
mkdocs serve

# 构建文档
mkdocs build
```

## 🔍 代码审查

### 审查清单

#### 功能性
- [ ] 代码实现了预期功能
- [ ] 处理了边界情况和错误情况
- [ ] 性能表现良好
- [ ] 没有引入安全漏洞

#### 代码质量
- [ ] 代码清晰易读
- [ ] 遵循项目编码规范
- [ ] 适当的注释和文档
- [ ] 没有重复代码

#### 测试
- [ ] 包含充分的单元测试
- [ ] 测试覆盖率满足要求
- [ ] 集成测试通过
- [ ] 性能测试（如适用）

#### 文档
- [ ] API 文档完整
- [ ] 更新了相关教程
- [ ] 包含使用示例
- [ ] 更新了 CHANGELOG

### 审查流程

1. **自动检查**: CI/CD 流水线自动运行
2. **代码审查**: 至少一名维护者审查
3. **测试验证**: 所有测试必须通过
4. **文档检查**: 确保文档完整准确
5. **最终批准**: 维护者批准后合并

## 🚀 发布流程

### 版本号规范

我们使用 [语义化版本](https://semver.org/) (SemVer)：

- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

示例：`1.2.3`

### 发布步骤

1. **更新版本号**
   ```bash
   # 更新 setup.py 和 __init__.py 中的版本号
   # 更新 CHANGELOG.md
   ```

2. **创建发布分支**
   ```bash
   git checkout -b release/v1.2.3
   git commit -m "chore: bump version to 1.2.3"
   ```

3. **测试发布**
   ```bash
   # 运行完整测试套件
   pytest
   
   # 构建包
   python setup.py sdist bdist_wheel
   
   # 测试安装
   pip install dist/finrl_crypto-1.2.3-py3-none-any.whl
   ```

4. **创建 PR 并合并**

5. **创建 Git 标签**
   ```bash
   git tag -a v1.2.3 -m "Release version 1.2.3"
   git push origin v1.2.3
   ```

6. **发布到 PyPI**
   ```bash
   twine upload dist/*
   ```

## 🎯 贡献指导原则

### 优先级

1. **Bug 修复** - 最高优先级
2. **安全问题** - 高优先级
3. **性能优化** - 中等优先级
4. **新功能** - 中等优先级
5. **文档改进** - 中等优先级
6. **代码重构** - 低优先级

### 质量标准

- **测试覆盖率**: 至少 80%
- **代码质量**: Pylint 评分 > 8.0
- **文档完整性**: 所有公共 API 都有文档
- **性能**: 不能显著降低现有功能性能

### 沟通渠道

- **GitHub Issues**: 问题报告和功能请求
- **GitHub Discussions**: 一般讨论和问答
- **Pull Requests**: 代码审查和讨论
- **Email**: 安全问题和私人联系

## 🏆 贡献者认可

我们重视每一个贡献，并通过以下方式认可贡献者：

- **贡献者列表**: 在 README 中列出所有贡献者
- **发布说明**: 在发布说明中感谢贡献者
- **特殊徽章**: 为重要贡献者提供特殊认可
- **年度总结**: 年度贡献者总结报告

## 📞 获取帮助

如果您在贡献过程中遇到任何问题，请通过以下方式获取帮助：

1. **查看文档**: 首先查看相关文档
2. **搜索 Issues**: 查看是否有类似问题
3. **创建 Issue**: 描述您的问题
4. **参与讨论**: 在 GitHub Discussions 中提问
5. **联系维护者**: 通过邮件联系项目维护者

## 📋 贡献者协议

通过向本项目贡献代码，您同意：

1. 您拥有所贡献代码的版权
2. 您同意在项目许可证下发布您的贡献
3. 您的贡献不侵犯任何第三方权利
4. 您同意遵守项目的行为准则

---

感谢您对 FinRL Crypto 项目的贡献！您的参与使这个项目变得更好。如果您有任何问题或建议，请随时联系我们。

**Happy Coding! 🚀**