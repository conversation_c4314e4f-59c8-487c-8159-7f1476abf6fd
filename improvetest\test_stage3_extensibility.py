# -*- coding: utf-8 -*-
"""
第三阶段扩展性改进测试

测试内容：
1. 插件架构测试
2. 监控系统测试
3. 统一日志系统测试
4. 性能优化测试

使用TDD模式，先定义测试，再实现功能
"""

import unittest
import tempfile
import shutil
import os
import sys
import time
import json
import threading
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any, List

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class TestPluginArchitecture(unittest.TestCase):
    """测试插件架构功能"""
    
    def setUp(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.plugin_dir = os.path.join(self.temp_dir, 'plugins')
        os.makedirs(self.plugin_dir, exist_ok=True)
    
    def tearDown(self):
        """测试后的清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_plugin_interface_definition(self):
        """测试插件接口定义"""
        # 这个测试将驱动我们创建插件接口
        try:
            from plugins.plugin_interface import IPlugin
            
            # 验证接口方法存在
            self.assertTrue(hasattr(IPlugin, 'name'))
            self.assertTrue(hasattr(IPlugin, 'version'))
            self.assertTrue(hasattr(IPlugin, 'initialize'))
            self.assertTrue(hasattr(IPlugin, 'execute'))
            self.assertTrue(hasattr(IPlugin, 'cleanup'))
            
        except ImportError:
            self.fail("插件接口模块不存在，需要创建 plugins/plugin_interface.py")
    
    def test_plugin_manager_creation(self):
        """测试插件管理器创建"""
        try:
            from plugins.plugin_manager import PluginManager
            
            manager = PluginManager()
            self.assertIsNotNone(manager)
            self.assertTrue(hasattr(manager, 'plugins'))
            self.assertTrue(hasattr(manager, 'load_plugin'))
            self.assertTrue(hasattr(manager, 'unload_plugin'))
            self.assertTrue(hasattr(manager, 'execute_plugin'))
            
        except ImportError:
            self.fail("插件管理器模块不存在，需要创建 plugins/plugin_manager.py")
    
    def test_plugin_loading(self):
        """测试插件加载功能"""
        try:
            from plugins.plugin_manager import PluginManager
            from plugins.plugin_interface import IPlugin
            
            # 创建测试插件
            test_plugin_code = '''
from plugins.plugin_interface import IPlugin, PluginMetadata, PluginType
from typing import Dict, Any

class TestPlugin(IPlugin):
    def __init__(self, config=None):
        self.config = config or {}
        self._metadata = PluginMetadata(
            name="test_plugin",
            version="1.0.0",
            description="Test plugin for unit testing",
            author="Test",
            plugin_type=PluginType.STRATEGY
        )
    
    @property
    def name(self) -> str:
        return "test_plugin"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    @property
    def metadata(self) -> PluginMetadata:
        return self._metadata
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        self.config = config
        return True
    
    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        return {"result": "test_executed", "input": context}
    
    def cleanup(self) -> None:
        pass
    
    def activate(self) -> bool:
        return True
    
    def deactivate(self) -> bool:
        return True
'''
            
            # 写入测试插件文件
            test_plugin_path = os.path.join(self.plugin_dir, 'test_plugin.py')
            with open(test_plugin_path, 'w', encoding='utf-8') as f:
                f.write(test_plugin_code)
            
            # 测试加载
            manager = PluginManager()
            result = manager.load_plugin(test_plugin_path, {'test_config': True})
            
            self.assertTrue(result)
            self.assertIn('test_plugin', manager.plugins)
            
        except ImportError:
            self.skipTest("插件系统模块未实现")
    
    def test_plugin_execution(self):
        """测试插件执行功能"""
        try:
            from plugins.plugin_manager import PluginManager
            
            manager = PluginManager()
            
            # 假设已经加载了测试插件
            # 这里我们模拟一个已加载的插件
            mock_plugin = Mock()
            mock_plugin.execute.return_value = {"status": "success"}
            manager.plugins['test_plugin'] = mock_plugin
            
            # 执行插件
            context = {"data": "test_data"}
            result = manager.execute_plugin('test_plugin', context)
            
            self.assertEqual(result["status"], "success")
            mock_plugin.execute.assert_called_once_with(context)
            
        except ImportError:
            self.skipTest("插件系统模块未实现")
    
    def test_plugin_unloading(self):
        """测试插件卸载功能"""
        try:
            from plugins.plugin_manager import PluginManager
            
            manager = PluginManager()
            
            # 模拟已加载的插件
            mock_plugin = Mock()
            manager.plugins['test_plugin'] = mock_plugin
            
            # 卸载插件
            result = manager.unload_plugin('test_plugin')
            
            self.assertTrue(result)
            self.assertNotIn('test_plugin', manager.plugins)
            mock_plugin.cleanup.assert_called_once()
            
        except ImportError:
            self.skipTest("插件系统模块未实现")


class TestMonitoringSystem(unittest.TestCase):
    """测试监控系统功能"""
    
    def setUp(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后的清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_system_monitor_creation(self):
        """测试系统监控器创建"""
        try:
            from monitoring.system_monitor import SystemMonitor
            
            monitor = SystemMonitor()
            self.assertIsNotNone(monitor)
            self.assertTrue(hasattr(monitor, 'get_cpu_usage'))
            self.assertTrue(hasattr(monitor, 'get_memory_usage'))
            self.assertTrue(hasattr(monitor, 'get_gpu_usage'))
            
        except ImportError:
            self.fail("系统监控模块不存在，需要创建 monitoring/system_monitor.py")
    
    def test_cpu_monitoring(self):
        """测试CPU监控功能"""
        try:
            from monitoring.system_monitor import SystemMonitor
            
            monitor = SystemMonitor()
            cpu_usage = monitor.get_cpu_usage()
            
            self.assertIsInstance(cpu_usage, float)
            self.assertGreaterEqual(cpu_usage, 0.0)
            self.assertLessEqual(cpu_usage, 100.0)
            
        except ImportError:
            self.skipTest("系统监控模块未实现")
    
    def test_memory_monitoring(self):
        """测试内存监控功能"""
        try:
            from monitoring.system_monitor import SystemMonitor
            
            monitor = SystemMonitor()
            memory_info = monitor.get_memory_usage()
            
            self.assertIsInstance(memory_info, dict)
            self.assertIn('total', memory_info)
            self.assertIn('used', memory_info)
            self.assertIn('percent', memory_info)
            
            self.assertGreater(memory_info['total'], 0)
            self.assertGreaterEqual(memory_info['percent'], 0)
            self.assertLessEqual(memory_info['percent'], 100)
            
        except ImportError:
            self.skipTest("系统监控模块未实现")
    
    def test_training_monitor_creation(self):
        """测试训练监控器创建"""
        try:
            from monitoring.training_monitor import TrainingMonitor
            
            monitor = TrainingMonitor()
            self.assertIsNotNone(monitor)
            self.assertTrue(hasattr(monitor, 'log_episode'))
            self.assertTrue(hasattr(monitor, 'get_training_summary'))
            self.assertTrue(hasattr(monitor, 'detect_anomalies'))
            
        except ImportError:
            self.fail("训练监控模块不存在，需要创建 monitoring/training_monitor.py")
    
    def test_training_metrics_logging(self):
        """测试训练指标记录"""
        try:
            from monitoring.training_monitor import TrainingMonitor
            
            monitor = TrainingMonitor()
            
            # 记录训练数据
            monitor.log_episode(1, 100.5, 0.05, {'accuracy': 0.95})
            monitor.log_episode(2, 105.2, 0.04, {'accuracy': 0.96})
            
            # 获取训练摘要
            summary = monitor.get_training_summary()
            
            self.assertIsInstance(summary, dict)
            self.assertIn('total_episodes', summary)
            self.assertIn('average_reward', summary)
            self.assertIn('average_loss', summary)
            
        except ImportError:
            self.skipTest("训练监控模块未实现")
    
    def test_anomaly_detection(self):
        """测试异常检测功能"""
        try:
            from monitoring.training_monitor import TrainingMonitor
            
            monitor = TrainingMonitor()
            
            # 记录正常数据
            for i in range(10):
                monitor.log_episode(i, 100 + i, 0.05 - i*0.001)
            
            # 记录异常数据
            monitor.log_episode(11, -1000, 10.0)  # 异常的奖励和损失
            
            # 检测异常
            anomalies = monitor.detect_anomalies()
            
            self.assertIsInstance(anomalies, list)
            # 应该检测到异常
            self.assertGreater(len(anomalies), 0)
            
        except ImportError:
            self.skipTest("训练监控模块未实现")


class TestUnifiedLogging(unittest.TestCase):
    """测试统一日志系统功能"""
    
    def setUp(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.log_dir = os.path.join(self.temp_dir, 'logs')
        os.makedirs(self.log_dir, exist_ok=True)
    
    def tearDown(self):
        """测试后的清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_logger_config_creation(self):
        """测试日志配置创建"""
        try:
            from logging_system.logger_config import LoggerConfig
            
            config = LoggerConfig()
            self.assertIsNotNone(config)
            self.assertTrue(hasattr(config, 'setup_logger'))
            
        except ImportError:
            self.fail("日志配置模块不存在，需要创建 logging_system/logger_config.py")
    
    def test_logger_setup(self):
        """测试日志记录器设置"""
        try:
            from logging_system.logger_config import LoggerConfig
            
            log_file = os.path.join(self.log_dir, 'test.log')
            logger = LoggerConfig.setup_logger('test_logger', log_file, 'INFO')
            
            self.assertIsNotNone(logger)
            self.assertEqual(logger.name, 'test_logger')
            
            # 测试日志写入
            logger.info("测试日志消息")
            
            # 验证日志文件创建
            self.assertTrue(os.path.exists(log_file))
            
            # 验证日志内容
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                self.assertIn("测试日志消息", content)
            
        except ImportError:
            self.skipTest("日志配置模块未实现")
    
    def test_structured_logger_creation(self):
        """测试结构化日志记录器创建"""
        try:
            from logging_system.structured_logger import StructuredLogger
            
            logger = StructuredLogger('test_structured')
            self.assertIsNotNone(logger)
            self.assertTrue(hasattr(logger, 'log_structured'))
            
        except ImportError:
            self.fail("结构化日志模块不存在，需要创建 logging_system/structured_logger.py")
    
    def test_structured_logging(self):
        """测试结构化日志记录"""
        try:
            from logging_system.structured_logger import StructuredLogger
            from logging_system.logger_config import LoggerConfig
            
            # 设置日志文件
            log_file = os.path.join(self.log_dir, 'structured.log')
            LoggerConfig.setup_logger('test_structured', log_file, 'INFO')
            
            # 创建结构化日志记录器
            logger = StructuredLogger('test_structured')
            
            # 记录结构化日志
            logger.log_structured('info', 'training_started', 
                                 episode=1, reward=100.5, model='PPO')
            
            # 验证日志文件
            self.assertTrue(os.path.exists(log_file))
            
            # 验证日志格式
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                self.assertIn('training_started', content)
                self.assertIn('episode', content)
                self.assertIn('reward', content)
            
        except ImportError:
            self.skipTest("结构化日志模块未实现")
    
    def test_log_rotation(self):
        """测试日志轮转功能"""
        try:
            from logging_system.logger_config import LoggerConfig
            
            log_file = os.path.join(self.log_dir, 'rotation_test.log')
            logger = LoggerConfig.setup_logger(
                'rotation_test', log_file, 'INFO',
                max_bytes=1024, backup_count=3  # 小文件大小用于测试
            )
            
            # 写入大量日志触发轮转
            for i in range(100):
                logger.info(f"这是测试日志消息 {i} " + "x" * 50)
            
            # 检查是否创建了备份文件
            backup_files = [f for f in os.listdir(self.log_dir) 
                          if f.startswith('rotation_test.log.')]
            
            # 应该有备份文件创建
            self.assertGreater(len(backup_files), 0)
            
        except ImportError:
            self.skipTest("日志配置模块未实现")


class TestPerformanceOptimization(unittest.TestCase):
    """测试性能优化功能"""
    
    def setUp(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后的清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_memory_optimizer_creation(self):
        """测试内存优化器创建"""
        try:
            from optimization.memory_optimizer import MemoryOptimizer
            
            optimizer = MemoryOptimizer(max_memory_gb=4.0)
            self.assertIsNotNone(optimizer)
            self.assertTrue(hasattr(optimizer, 'check_memory_usage'))
            self.assertTrue(hasattr(optimizer, 'optimize_memory'))
            
        except ImportError:
            self.fail("内存优化模块不存在，需要创建 optimization/memory_optimizer.py")
    
    def test_memory_usage_check(self):
        """测试内存使用检查"""
        try:
            from optimization.memory_optimizer import MemoryOptimizer
            
            optimizer = MemoryOptimizer()
            memory_info = optimizer.check_memory_usage()
            
            self.assertIsInstance(memory_info, dict)
            self.assertIn('used_percent', memory_info)
            self.assertIn('available_gb', memory_info)
            self.assertIn('used_gb', memory_info)
            
            self.assertGreaterEqual(memory_info['used_percent'], 0)
            self.assertLessEqual(memory_info['used_percent'], 100)
            
        except ImportError:
            self.skipTest("内存优化模块未实现")
    
    def test_memory_optimization(self):
        """测试内存优化功能"""
        try:
            from optimization.memory_optimizer import MemoryOptimizer
            
            optimizer = MemoryOptimizer()
            
            # 记录优化前的内存使用
            before_memory = optimizer.check_memory_usage()
            
            # 执行内存优化
            result = optimizer.optimize_memory()
            
            # 验证优化结果
            self.assertTrue(result)
            
            # 记录优化后的内存使用
            after_memory = optimizer.check_memory_usage()
            
            # 内存使用应该有所改善（或至少不变差）
            self.assertLessEqual(
                after_memory['used_percent'], 
                before_memory['used_percent'] + 5  # 允许5%的误差
            )
            
        except ImportError:
            self.skipTest("内存优化模块未实现")
    
    def test_compute_optimizer_creation(self):
        """测试计算优化器创建"""
        try:
            from optimization.compute_optimizer import ComputeOptimizer
            
            optimizer = ComputeOptimizer()
            self.assertIsNotNone(optimizer)
            self.assertTrue(hasattr(optimizer, 'device'))
            self.assertTrue(hasattr(optimizer, 'num_workers'))
            self.assertTrue(hasattr(optimizer, 'optimize_tensor_operations'))
            
        except ImportError:
            self.fail("计算优化模块不存在，需要创建 optimization/compute_optimizer.py")
    
    @patch('torch.cuda.is_available')
    def test_device_selection(self, mock_cuda_available):
        """测试设备选择逻辑"""
        try:
            from optimization.compute_optimizer import ComputeOptimizer
            import torch
            
            # 测试CUDA可用的情况
            mock_cuda_available.return_value = True
            optimizer = ComputeOptimizer()
            self.assertEqual(optimizer.device.type, 'cuda')
            
            # 测试CUDA不可用的情况
            mock_cuda_available.return_value = False
            optimizer = ComputeOptimizer()
            self.assertEqual(optimizer.device.type, 'cpu')
            
        except ImportError:
            self.skipTest("计算优化模块未实现")
    
    def test_concurrency_optimizer_creation(self):
        """测试并发优化器创建"""
        try:
            from optimization.concurrency_optimizer import ConcurrencyOptimizer
            
            optimizer = ConcurrencyOptimizer(max_workers=4)
            self.assertIsNotNone(optimizer)
            self.assertTrue(hasattr(optimizer, 'parallel_execute'))
            self.assertTrue(hasattr(optimizer, 'async_execute'))
            self.assertEqual(optimizer.max_workers, 4)
            
        except ImportError:
            self.fail("并发优化模块不存在，需要创建 optimization/concurrency_optimizer.py")
    
    def test_parallel_execution(self):
        """测试并行执行功能"""
        try:
            from optimization.concurrency_optimizer import ConcurrencyOptimizer
            
            def test_function(x):
                time.sleep(0.1)  # 模拟计算时间
                return x * 2
            
            optimizer = ConcurrencyOptimizer(max_workers=2)
            tasks = [1, 2, 3, 4, 5]
            
            # 测试并行执行
            start_time = time.time()
            results = optimizer.parallel_execute(test_function, tasks)
            end_time = time.time()
            
            # 验证结果
            expected_results = [2, 4, 6, 8, 10]
            self.assertEqual(sorted(results), sorted(expected_results))
            
            # 验证并行执行确实节省了时间
            # 串行执行需要0.5秒，并行执行应该更快
            self.assertLess(end_time - start_time, 0.4)
            
        except ImportError:
            self.skipTest("并发优化模块未实现")


class TestIntegration(unittest.TestCase):
    """测试各模块集成功能"""
    
    def setUp(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后的清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_plugin_with_monitoring(self):
        """测试插件与监控系统集成"""
        try:
            from plugins.plugin_manager import PluginManager
            from monitoring.system_monitor import SystemMonitor
            
            # 创建插件管理器和监控器
            plugin_manager = PluginManager()
            system_monitor = SystemMonitor()
            
            # 模拟插件执行过程中的监控
            mock_plugin = Mock()
            mock_plugin.execute.return_value = {"status": "success"}
            plugin_manager.plugins['test_plugin'] = mock_plugin
            
            # 执行插件并监控
            start_memory = system_monitor.get_memory_usage()
            result = plugin_manager.execute_plugin('test_plugin', {})
            end_memory = system_monitor.get_memory_usage()
            
            # 验证执行成功
            self.assertEqual(result["status"], "success")
            
            # 验证监控数据有效
            self.assertIsInstance(start_memory, dict)
            self.assertIsInstance(end_memory, dict)
            
        except ImportError:
            self.skipTest("相关模块未实现")
    
    def test_logging_with_optimization(self):
        """测试日志系统与性能优化集成"""
        try:
            from logging_system.structured_logger import StructuredLogger
            from optimization.memory_optimizer import MemoryOptimizer
            
            # 创建日志记录器和内存优化器
            logger = StructuredLogger('optimization_test')
            optimizer = MemoryOptimizer()
            
            # 记录优化过程
            before_memory = optimizer.check_memory_usage()
            logger.log_structured('info', 'optimization_started', 
                                 memory_before=before_memory)
            
            # 执行优化
            optimization_result = optimizer.optimize_memory()
            
            after_memory = optimizer.check_memory_usage()
            logger.log_structured('info', 'optimization_completed',
                                 memory_after=after_memory,
                                 success=optimization_result)
            
            # 验证优化成功
            self.assertTrue(optimization_result)
            
        except ImportError:
            self.skipTest("相关模块未实现")
    
    def test_full_system_integration(self):
        """测试完整系统集成"""
        try:
            from plugins.plugin_manager import PluginManager
            from monitoring.system_monitor import SystemMonitor
            from monitoring.training_monitor import TrainingMonitor
            from logging_system.structured_logger import StructuredLogger
            from optimization.memory_optimizer import MemoryOptimizer
            
            # 创建所有组件
            plugin_manager = PluginManager()
            system_monitor = SystemMonitor()
            training_monitor = TrainingMonitor()
            logger = StructuredLogger('integration_test')
            memory_optimizer = MemoryOptimizer()
            
            # 模拟完整的训练流程
            logger.log_structured('info', 'training_session_started')
            
            # 监控系统资源
            system_info = system_monitor.get_memory_usage()
            logger.log_structured('info', 'system_status', **system_info)
            
            # 模拟训练过程
            for episode in range(3):
                # 记录训练指标
                training_monitor.log_episode(episode, 100 + episode, 0.05 - episode*0.01)
                
                # 执行内存优化
                if episode % 2 == 0:
                    memory_optimizer.optimize_memory()
            
            # 获取训练摘要
            training_summary = training_monitor.get_training_summary()
            logger.log_structured('info', 'training_completed', **training_summary)
            
            # 验证集成成功
            self.assertIsInstance(training_summary, dict)
            self.assertIn('total_episodes', training_summary)
            
        except ImportError:
            self.skipTest("相关模块未实现")


if __name__ == '__main__':
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestPluginArchitecture,
        TestMonitoringSystem,
        TestUnifiedLogging,
        TestPerformanceOptimization,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果摘要
    print(f"\n{'='*50}")
    print(f"测试摘要:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"跳过: {len(result.skipped)}")
    
    if result.failures:
        print(f"\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback.split('AssertionError: ')[-1].split('\n')[0]}")
    
    if result.errors:
        print(f"\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback.split('\n')[-2]}")
    
    print(f"{'='*50}")