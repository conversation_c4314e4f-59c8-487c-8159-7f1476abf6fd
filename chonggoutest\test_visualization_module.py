#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化模块测试 - TDD测试驱动开发

测试finrl_crypto.visualization模块的所有功能：
- ChartGenerator图表生成器
- PerformanceVisualizer性能可视化
- RiskVisualizer风险可视化
- PortfolioVisualizer组合可视化
- InteractiveDashboard交互式仪表板
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Mock外部依赖
sys.modules['matplotlib'] = Mock()
sys.modules['matplotlib.pyplot'] = Mock()
sys.modules['seaborn'] = Mock()
sys.modules['plotly'] = Mock()
sys.modules['plotly.graph_objects'] = Mock()
sys.modules['plotly.express'] = Mock()
sys.modules['plotly.subplots'] = Mock()
sys.modules['bokeh'] = Mock()
sys.modules['dash'] = Mock()
sys.modules['streamlit'] = Mock()

class TestChartGenerator(unittest.TestCase):
    """测试ChartGenerator图表生成器"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        np.random.seed(42)
        
        self.price_data = pd.DataFrame({
            'timestamp': dates,
            'open': 100 + np.cumsum(np.random.normal(0, 1, 252)),
            'high': 105 + np.cumsum(np.random.normal(0, 1, 252)),
            'low': 95 + np.cumsum(np.random.normal(0, 1, 252)),
            'close': 100 + np.cumsum(np.random.normal(0, 1, 252)),
            'volume': np.random.uniform(10000, 100000, 252)
        })
        
        self.portfolio_data = pd.DataFrame({
            'timestamp': dates,
            'portfolio_value': 100000 * np.cumprod(1 + np.random.normal(0.0008, 0.02, 252)),
            'benchmark_value': 100000 * np.cumprod(1 + np.random.normal(0.0005, 0.015, 252))
        })
        
        self.chart_config = {
            'style': 'seaborn',
            'figsize': (12, 8),
            'dpi': 300,
            'color_palette': 'viridis',
            'save_format': 'png',
            'interactive': True
        }
    
    def test_chart_generator_creation(self):
        """测试图表生成器创建"""
        try:
            from finrl_crypto.visualization.chart_generator import ChartGenerator
            
            generator = ChartGenerator(self.chart_config)
            
            self.assertEqual(generator.style, 'seaborn')
            self.assertEqual(generator.figsize, (12, 8))
            self.assertEqual(generator.dpi, 300)
            
        except ImportError:
            self.fail("ChartGenerator类未实现")
    
    def test_candlestick_chart(self):
        """测试K线图生成"""
        try:
            from finrl_crypto.visualization.chart_generator import ChartGenerator
            
            generator = ChartGenerator(self.chart_config)
            
            # 测试K线图生成
            chart = generator.create_candlestick_chart(
                data=self.price_data,
                title='BTC Price Chart',
                volume=True
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("ChartGenerator类未实现")
    
    def test_line_chart(self):
        """测试线图生成"""
        try:
            from finrl_crypto.visualization.chart_generator import ChartGenerator
            
            generator = ChartGenerator(self.chart_config)
            
            # 测试线图生成
            chart = generator.create_line_chart(
                data=self.portfolio_data,
                x_column='timestamp',
                y_columns=['portfolio_value', 'benchmark_value'],
                title='Portfolio Performance'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("ChartGenerator类未实现")
    
    def test_scatter_plot(self):
        """测试散点图生成"""
        try:
            from finrl_crypto.visualization.chart_generator import ChartGenerator
            
            generator = ChartGenerator(self.chart_config)
            
            # 创建收益率vs波动率数据
            returns_vol_data = pd.DataFrame({
                'returns': np.random.normal(0.1, 0.05, 50),
                'volatility': np.random.uniform(0.1, 0.3, 50),
                'sharpe_ratio': np.random.uniform(0.5, 2.0, 50)
            })
            
            # 测试散点图生成
            chart = generator.create_scatter_plot(
                data=returns_vol_data,
                x_column='volatility',
                y_column='returns',
                color_column='sharpe_ratio',
                title='Risk-Return Scatter Plot'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("ChartGenerator类未实现")
    
    def test_heatmap(self):
        """测试热力图生成"""
        try:
            from finrl_crypto.visualization.chart_generator import ChartGenerator
            
            generator = ChartGenerator(self.chart_config)
            
            # 创建相关性矩阵数据
            correlation_data = pd.DataFrame(
                np.random.uniform(-1, 1, (5, 5)),
                columns=['BTC', 'ETH', 'ADA', 'DOT', 'LINK'],
                index=['BTC', 'ETH', 'ADA', 'DOT', 'LINK']
            )
            
            # 测试热力图生成
            chart = generator.create_heatmap(
                data=correlation_data,
                title='Asset Correlation Matrix',
                annot=True
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("ChartGenerator类未实现")
    
    def test_chart_customization(self):
        """测试图表自定义"""
        try:
            from finrl_crypto.visualization.chart_generator import ChartGenerator
            
            generator = ChartGenerator(self.chart_config)
            
            # 测试图表自定义选项
            custom_config = {
                'title_fontsize': 16,
                'axis_fontsize': 12,
                'legend_position': 'upper right',
                'grid': True,
                'transparency': 0.8
            }
            
            chart = generator.create_line_chart(
                data=self.portfolio_data,
                x_column='timestamp',
                y_columns=['portfolio_value'],
                title='Custom Chart',
                custom_config=custom_config
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("ChartGenerator类未实现")

class TestPerformanceVisualizer(unittest.TestCase):
    """测试PerformanceVisualizer性能可视化"""
    
    def setUp(self):
        """测试前准备"""
        # 创建性能数据
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        np.random.seed(42)
        
        self.performance_data = {
            'portfolio_value': pd.Series(
                100000 * np.cumprod(1 + np.random.normal(0.0008, 0.02, 252)),
                index=dates
            ),
            'benchmark_value': pd.Series(
                100000 * np.cumprod(1 + np.random.normal(0.0005, 0.015, 252)),
                index=dates
            ),
            'returns': pd.Series(
                np.random.normal(0.0008, 0.02, 252),
                index=dates
            ),
            'benchmark_returns': pd.Series(
                np.random.normal(0.0005, 0.015, 252),
                index=dates
            )
        }
        
        self.metrics = {
            'total_return': 0.15,
            'annualized_return': 0.12,
            'volatility': 0.18,
            'sharpe_ratio': 0.67,
            'max_drawdown': -0.08,
            'calmar_ratio': 1.5,
            'win_rate': 0.55,
            'profit_factor': 1.3
        }
    
    def test_performance_visualizer_creation(self):
        """测试性能可视化器创建"""
        try:
            from finrl_crypto.visualization.performance import PerformanceVisualizer
            
            visualizer = PerformanceVisualizer(self.performance_data)
            
            self.assertIsNotNone(visualizer.portfolio_value)
            self.assertIsNotNone(visualizer.returns)
            
        except ImportError:
            self.fail("PerformanceVisualizer类未实现")
    
    def test_cumulative_returns_plot(self):
        """测试累积收益图"""
        try:
            from finrl_crypto.visualization.performance import PerformanceVisualizer
            
            visualizer = PerformanceVisualizer(self.performance_data)
            
            # 测试累积收益图
            chart = visualizer.plot_cumulative_returns(
                include_benchmark=True,
                title='Cumulative Returns Comparison'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("PerformanceVisualizer类未实现")
    
    def test_drawdown_plot(self):
        """测试回撤图"""
        try:
            from finrl_crypto.visualization.performance import PerformanceVisualizer
            
            visualizer = PerformanceVisualizer(self.performance_data)
            
            # 测试回撤图
            chart = visualizer.plot_drawdown(
                title='Portfolio Drawdown',
                fill_area=True
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("PerformanceVisualizer类未实现")
    
    def test_returns_distribution_plot(self):
        """测试收益分布图"""
        try:
            from finrl_crypto.visualization.performance import PerformanceVisualizer
            
            visualizer = PerformanceVisualizer(self.performance_data)
            
            # 测试收益分布图
            chart = visualizer.plot_returns_distribution(
                bins=50,
                include_normal_curve=True,
                title='Returns Distribution'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("PerformanceVisualizer类未实现")
    
    def test_rolling_metrics_plot(self):
        """测试滚动指标图"""
        try:
            from finrl_crypto.visualization.performance import PerformanceVisualizer
            
            visualizer = PerformanceVisualizer(self.performance_data)
            
            # 测试滚动夏普比率图
            chart = visualizer.plot_rolling_sharpe_ratio(
                window=30,
                title='Rolling Sharpe Ratio (30-day)'
            )
            
            self.assertIsNotNone(chart)
            
            # 测试滚动波动率图
            chart = visualizer.plot_rolling_volatility(
                window=30,
                title='Rolling Volatility (30-day)'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("PerformanceVisualizer类未实现")
    
    def test_performance_metrics_table(self):
        """测试性能指标表格"""
        try:
            from finrl_crypto.visualization.performance import PerformanceVisualizer
            
            visualizer = PerformanceVisualizer(self.performance_data)
            
            # 测试性能指标表格
            table = visualizer.create_performance_table(
                metrics=self.metrics,
                format_percentage=True
            )
            
            self.assertIsNotNone(table)
            
        except ImportError:
            self.fail("PerformanceVisualizer类未实现")
    
    def test_monthly_returns_heatmap(self):
        """测试月度收益热力图"""
        try:
            from finrl_crypto.visualization.performance import PerformanceVisualizer
            
            visualizer = PerformanceVisualizer(self.performance_data)
            
            # 测试月度收益热力图
            chart = visualizer.plot_monthly_returns_heatmap(
                title='Monthly Returns Heatmap'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("PerformanceVisualizer类未实现")

class TestRiskVisualizer(unittest.TestCase):
    """测试RiskVisualizer风险可视化"""
    
    def setUp(self):
        """测试前准备"""
        # 创建风险数据
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        np.random.seed(42)
        
        self.risk_data = {
            'returns': pd.Series(
                np.random.normal(0.0008, 0.02, 252),
                index=dates
            ),
            'var_95': pd.Series(
                np.random.uniform(-0.05, -0.02, 252),
                index=dates
            ),
            'cvar_95': pd.Series(
                np.random.uniform(-0.08, -0.05, 252),
                index=dates
            )
        }
        
        self.correlation_matrix = pd.DataFrame(
            np.random.uniform(-1, 1, (5, 5)),
            columns=['BTC', 'ETH', 'ADA', 'DOT', 'LINK'],
            index=['BTC', 'ETH', 'ADA', 'DOT', 'LINK']
        )
    
    def test_risk_visualizer_creation(self):
        """测试风险可视化器创建"""
        try:
            from finrl_crypto.visualization.risk import RiskVisualizer
            
            visualizer = RiskVisualizer(self.risk_data)
            
            self.assertIsNotNone(visualizer.returns)
            
        except ImportError:
            self.fail("RiskVisualizer类未实现")
    
    def test_var_plot(self):
        """测试VaR图表"""
        try:
            from finrl_crypto.visualization.risk import RiskVisualizer
            
            visualizer = RiskVisualizer(self.risk_data)
            
            # 测试VaR时间序列图
            chart = visualizer.plot_var_timeseries(
                confidence_levels=[0.95, 0.99],
                title='Value at Risk Over Time'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("RiskVisualizer类未实现")
    
    def test_correlation_heatmap(self):
        """测试相关性热力图"""
        try:
            from finrl_crypto.visualization.risk import RiskVisualizer
            
            visualizer = RiskVisualizer(self.risk_data)
            
            # 测试相关性热力图
            chart = visualizer.plot_correlation_heatmap(
                correlation_matrix=self.correlation_matrix,
                title='Asset Correlation Matrix'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("RiskVisualizer类未实现")
    
    def test_risk_contribution_plot(self):
        """测试风险贡献图"""
        try:
            from finrl_crypto.visualization.risk import RiskVisualizer
            
            visualizer = RiskVisualizer(self.risk_data)
            
            # 创建风险贡献数据
            risk_contributions = pd.Series({
                'BTC': 0.4,
                'ETH': 0.3,
                'ADA': 0.2,
                'DOT': 0.1
            })
            
            # 测试风险贡献饼图
            chart = visualizer.plot_risk_contribution_pie(
                risk_contributions=risk_contributions,
                title='Portfolio Risk Contribution'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("RiskVisualizer类未实现")
    
    def test_stress_test_visualization(self):
        """测试压力测试可视化"""
        try:
            from finrl_crypto.visualization.risk import RiskVisualizer
            
            visualizer = RiskVisualizer(self.risk_data)
            
            # 创建压力测试结果数据
            stress_results = {
                'market_crash': -0.25,
                'volatility_spike': -0.15,
                'correlation_breakdown': -0.18,
                'interest_rate_shock': -0.12
            }
            
            # 测试压力测试结果图
            chart = visualizer.plot_stress_test_results(
                stress_results=stress_results,
                title='Stress Test Results'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("RiskVisualizer类未实现")
    
    def test_tail_risk_visualization(self):
        """测试尾部风险可视化"""
        try:
            from finrl_crypto.visualization.risk import RiskVisualizer
            
            visualizer = RiskVisualizer(self.risk_data)
            
            # 测试尾部风险分布图
            chart = visualizer.plot_tail_risk_distribution(
                tail_percentile=0.05,
                title='Tail Risk Distribution'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("RiskVisualizer类未实现")

class TestPortfolioVisualizer(unittest.TestCase):
    """测试PortfolioVisualizer组合可视化"""
    
    def setUp(self):
        """测试前准备"""
        # 创建组合数据
        self.portfolio_weights = pd.Series({
            'BTC': 0.4,
            'ETH': 0.3,
            'ADA': 0.15,
            'DOT': 0.1,
            'LINK': 0.05
        })
        
        self.asset_returns = pd.DataFrame({
            'BTC': np.random.normal(0.001, 0.03, 252),
            'ETH': np.random.normal(0.0008, 0.025, 252),
            'ADA': np.random.normal(0.0005, 0.04, 252),
            'DOT': np.random.normal(0.0006, 0.035, 252),
            'LINK': np.random.normal(0.0007, 0.03, 252)
        })
        
        self.rebalancing_history = pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=12, freq='M'),
            'BTC': np.random.uniform(0.3, 0.5, 12),
            'ETH': np.random.uniform(0.2, 0.4, 12),
            'ADA': np.random.uniform(0.1, 0.2, 12),
            'DOT': np.random.uniform(0.05, 0.15, 12),
            'LINK': np.random.uniform(0.02, 0.08, 12)
        })
    
    def test_portfolio_visualizer_creation(self):
        """测试组合可视化器创建"""
        try:
            from finrl_crypto.visualization.portfolio import PortfolioVisualizer
            
            visualizer = PortfolioVisualizer(
                weights=self.portfolio_weights,
                returns=self.asset_returns
            )
            
            self.assertIsNotNone(visualizer.weights)
            self.assertIsNotNone(visualizer.returns)
            
        except ImportError:
            self.fail("PortfolioVisualizer类未实现")
    
    def test_portfolio_composition_plot(self):
        """测试组合构成图"""
        try:
            from finrl_crypto.visualization.portfolio import PortfolioVisualizer
            
            visualizer = PortfolioVisualizer(
                weights=self.portfolio_weights,
                returns=self.asset_returns
            )
            
            # 测试饼图
            pie_chart = visualizer.plot_portfolio_pie(
                title='Portfolio Composition'
            )
            self.assertIsNotNone(pie_chart)
            
            # 测试条形图
            bar_chart = visualizer.plot_portfolio_bar(
                title='Portfolio Weights'
            )
            self.assertIsNotNone(bar_chart)
            
        except ImportError:
            self.fail("PortfolioVisualizer类未实现")
    
    def test_efficient_frontier_plot(self):
        """测试有效前沿图"""
        try:
            from finrl_crypto.visualization.portfolio import PortfolioVisualizer
            
            visualizer = PortfolioVisualizer(
                weights=self.portfolio_weights,
                returns=self.asset_returns
            )
            
            # 测试有效前沿图
            chart = visualizer.plot_efficient_frontier(
                num_portfolios=1000,
                title='Efficient Frontier'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("PortfolioVisualizer类未实现")
    
    def test_asset_allocation_over_time(self):
        """测试资产配置时间序列"""
        try:
            from finrl_crypto.visualization.portfolio import PortfolioVisualizer
            
            visualizer = PortfolioVisualizer(
                weights=self.portfolio_weights,
                returns=self.asset_returns
            )
            
            # 测试资产配置时间序列图
            chart = visualizer.plot_allocation_over_time(
                rebalancing_history=self.rebalancing_history,
                title='Asset Allocation Over Time'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("PortfolioVisualizer类未实现")
    
    def test_portfolio_performance_attribution(self):
        """测试组合绩效归因"""
        try:
            from finrl_crypto.visualization.portfolio import PortfolioVisualizer
            
            visualizer = PortfolioVisualizer(
                weights=self.portfolio_weights,
                returns=self.asset_returns
            )
            
            # 测试绩效归因图
            chart = visualizer.plot_performance_attribution(
                title='Portfolio Performance Attribution'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("PortfolioVisualizer类未实现")
    
    def test_correlation_network_plot(self):
        """测试相关性网络图"""
        try:
            from finrl_crypto.visualization.portfolio import PortfolioVisualizer
            
            visualizer = PortfolioVisualizer(
                weights=self.portfolio_weights,
                returns=self.asset_returns
            )
            
            # 测试相关性网络图
            chart = visualizer.plot_correlation_network(
                threshold=0.3,
                title='Asset Correlation Network'
            )
            
            self.assertIsNotNone(chart)
            
        except ImportError:
            self.fail("PortfolioVisualizer类未实现")

class TestInteractiveDashboard(unittest.TestCase):
    """测试InteractiveDashboard交互式仪表板"""
    
    def setUp(self):
        """测试前准备"""
        # 创建仪表板数据
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        np.random.seed(42)
        
        self.dashboard_data = {
            'portfolio_value': pd.Series(
                100000 * np.cumprod(1 + np.random.normal(0.0008, 0.02, 252)),
                index=dates
            ),
            'positions': pd.DataFrame({
                'asset': ['BTC', 'ETH', 'ADA', 'DOT', 'LINK'],
                'weight': [0.4, 0.3, 0.15, 0.1, 0.05],
                'value': [40000, 30000, 15000, 10000, 5000],
                'pnl': [2000, 1500, -500, 800, 200]
            }),
            'trades': pd.DataFrame({
                'timestamp': pd.date_range('2023-01-01', periods=50, freq='5D'),
                'asset': np.random.choice(['BTC', 'ETH', 'ADA'], 50),
                'side': np.random.choice(['buy', 'sell'], 50),
                'quantity': np.random.uniform(0.1, 10, 50),
                'price': np.random.uniform(100, 50000, 50)
            })
        }
    
    def test_dashboard_creation(self):
        """测试仪表板创建"""
        try:
            from finrl_crypto.visualization.dashboard import InteractiveDashboard
            
            dashboard = InteractiveDashboard(self.dashboard_data)
            
            self.assertIsNotNone(dashboard.data)
            
        except ImportError:
            self.fail("InteractiveDashboard类未实现")
    
    def test_dashboard_components(self):
        """测试仪表板组件"""
        try:
            from finrl_crypto.visualization.dashboard import InteractiveDashboard
            
            dashboard = InteractiveDashboard(self.dashboard_data)
            
            # 测试主要组件
            self.assertTrue(hasattr(dashboard, 'create_portfolio_overview'))
            self.assertTrue(hasattr(dashboard, 'create_performance_charts'))
            self.assertTrue(hasattr(dashboard, 'create_risk_metrics'))
            self.assertTrue(hasattr(dashboard, 'create_trade_analysis'))
            
        except ImportError:
            self.fail("InteractiveDashboard类未实现")
    
    def test_real_time_updates(self):
        """测试实时更新功能"""
        try:
            from finrl_crypto.visualization.dashboard import InteractiveDashboard
            
            dashboard = InteractiveDashboard(self.dashboard_data)
            
            # 测试实时更新功能
            self.assertTrue(hasattr(dashboard, 'update_data'))
            self.assertTrue(hasattr(dashboard, 'refresh_charts'))
            
        except ImportError:
            self.fail("InteractiveDashboard类未实现")
    
    def test_dashboard_export(self):
        """测试仪表板导出"""
        try:
            from finrl_crypto.visualization.dashboard import InteractiveDashboard
            
            dashboard = InteractiveDashboard(self.dashboard_data)
            
            # 测试导出功能
            self.assertTrue(hasattr(dashboard, 'export_to_html'))
            self.assertTrue(hasattr(dashboard, 'export_to_pdf'))
            self.assertTrue(hasattr(dashboard, 'save_screenshot'))
            
        except ImportError:
            self.fail("InteractiveDashboard类未实现")

class TestVisualizationUtils(unittest.TestCase):
    """测试可视化工具函数"""
    
    def test_color_palette_generation(self):
        """测试颜色调色板生成"""
        try:
            from finrl_crypto.visualization.utils import generate_color_palette
            
            # 测试颜色调色板生成
            colors = generate_color_palette(n_colors=5, palette='viridis')
            
            self.assertIsInstance(colors, list)
            self.assertEqual(len(colors), 5)
            
        except ImportError:
            self.fail("generate_color_palette函数未实现")
    
    def test_chart_styling(self):
        """测试图表样式设置"""
        try:
            from finrl_crypto.visualization.utils import apply_chart_style
            
            # 测试图表样式应用
            style_config = {
                'theme': 'dark',
                'font_family': 'Arial',
                'grid': True,
                'legend': True
            }
            
            # 这里应该测试样式应用功能
            self.assertTrue(hasattr(apply_chart_style, '__call__'))
            
        except ImportError:
            self.fail("apply_chart_style函数未实现")
    
    def test_data_formatting(self):
        """测试数据格式化"""
        try:
            from finrl_crypto.visualization.utils import format_financial_data
            
            # 测试金融数据格式化
            test_value = 1234567.89
            formatted = format_financial_data(test_value, format_type='currency')
            
            self.assertIsInstance(formatted, str)
            
        except ImportError:
            self.fail("format_financial_data函数未实现")
    
    def test_chart_annotations(self):
        """测试图表注释"""
        try:
            from finrl_crypto.visualization.utils import add_chart_annotations
            
            # 测试图表注释功能
            annotations = [
                {'x': '2023-01-01', 'y': 100, 'text': 'Start'},
                {'x': '2023-12-31', 'y': 120, 'text': 'End'}
            ]
            
            # 这里应该测试注释添加功能
            self.assertTrue(hasattr(add_chart_annotations, '__call__'))
            
        except ImportError:
            self.fail("add_chart_annotations函数未实现")

if __name__ == '__main__':
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加所有测试类
    test_classes = [
        TestChartGenerator,
        TestPerformanceVisualizer,
        TestRiskVisualizer,
        TestPortfolioVisualizer,
        TestInteractiveDashboard,
        TestVisualizationUtils
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果统计
    print(f"\n测试结果统计:")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")