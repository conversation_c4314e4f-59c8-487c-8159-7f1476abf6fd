import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional


class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str):
        """初始化结构化日志记录器
        
        Args:
            name: 日志记录器名称
        """
        self.name = name
        self.logger = logging.getLogger(name)
    
    def log_structured(self, level: str, event: str, **kwargs):
        """记录结构化日志
        
        Args:
            level: 日志级别 (debug, info, warning, error, critical)
            event: 事件名称
            **kwargs: 结构化数据
        """
        # 创建结构化日志数据
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'event': event,
            'level': level.upper(),
            'logger': self.name,
            **kwargs
        }
        
        # 转换为JSON格式
        json_message = json.dumps(log_data, ensure_ascii=False, default=str)
        
        # 根据级别记录日志
        log_level = getattr(logging, level.upper(), logging.INFO)
        self.logger.log(log_level, json_message)
    
    def debug(self, event: str, **kwargs):
        """记录DEBUG级别的结构化日志"""
        self.log_structured('debug', event, **kwargs)
    
    def info(self, event: str, **kwargs):
        """记录INFO级别的结构化日志"""
        self.log_structured('info', event, **kwargs)
    
    def warning(self, event: str, **kwargs):
        """记录WARNING级别的结构化日志"""
        self.log_structured('warning', event, **kwargs)
    
    def error(self, event: str, **kwargs):
        """记录ERROR级别的结构化日志"""
        self.log_structured('error', event, **kwargs)
    
    def critical(self, event: str, **kwargs):
        """记录CRITICAL级别的结构化日志"""
        self.log_structured('critical', event, **kwargs)
    
    def get_logger(self) -> logging.Logger:
        """获取底层的Logger对象"""
        return self.logger
    
    def set_level(self, level: str):
        """设置日志级别"""
        log_level = getattr(logging, level.upper(), logging.INFO)
        self.logger.setLevel(log_level)
    
    def add_handler(self, handler: logging.Handler):
        """添加日志处理器"""
        self.logger.addHandler(handler)
    
    def remove_handler(self, handler: logging.Handler):
        """移除日志处理器"""
        self.logger.removeHandler(handler)