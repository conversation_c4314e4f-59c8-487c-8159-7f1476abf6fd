# FinRL Crypto 项目文档配置
site_name: FinRL Crypto Documentation
site_description: 'FinRL Crypto - 加密货币强化学习交易系统'
site_author: 'FinRL Crypto Team'
site_url: 'https://finrl-crypto.readthedocs.io/'

# 仓库信息
repo_name: 'FinRL-Crypto'
repo_url: 'https://github.com/your-org/FinRL-Crypto'
edit_uri: 'edit/main/docs/'

# 版权信息
copyright: 'Copyright &copy; 2024 FinRL Crypto Team'

# 配置
strict: true
use_directory_urls: false

# 主题配置
theme:
  name: 'material'
  language: 'zh'
  custom_dir: 'docs/overrides'
  
  # 调色板
  palette:
    - scheme: default
      primary: blue
      accent: light blue
      toggle:
        icon: material/brightness-7
        name: 切换到深色模式
    - scheme: slate
      primary: blue
      accent: light blue
      toggle:
        icon: material/brightness-4
        name: 切换到浅色模式
  
  # 字体
  font:
    text: 'Roboto'
    code: 'Roboto Mono'
  
  # 图标
  icon:
    logo: material/chart-line
    repo: fontawesome/brands/github
  
  # 功能
  features:
    - navigation.instant
    - navigation.instant.prefetch
    - navigation.tracking
    - navigation.tabs
    - navigation.tabs.sticky
    - navigation.sections
    - navigation.expand
    - navigation.path
    - navigation.indexes
    - navigation.top
    - navigation.footer
    - search.highlight
    - search.share
    - search.suggest
    - header.autohide
    - content.code.copy
    - content.code.select
    - content.code.annotate
    - content.tabs.link
    - content.tooltips
    - content.action.edit
    - content.action.view
    - toc.follow
    - toc.integrate

# 导航结构
nav:
  - 首页: index.md
  - 快速开始:
    - 安装指南: getting-started/installation.md
    - 快速教程: getting-started/quickstart.md
    - 配置说明: getting-started/configuration.md
  - 用户指南:
    - 数据处理: user-guide/data-processing.md
    - 模型训练: user-guide/model-training.md
    - 策略回测: user-guide/backtesting.md
    - 实时交易: user-guide/live-trading.md
    - 性能评估: user-guide/performance-evaluation.md
  - API参考:
    - 核心模块: api/core.md
    - 数据模块: api/data.md
    - 模型模块: api/models.md
    - 策略模块: api/strategies.md
    - 工具模块: api/utils.md
  - 开发指南:
    - 贡献指南: development/contributing.md
    - 代码规范: development/code-style.md
    - 测试指南: development/testing.md
    - 发布流程: development/release-process.md
  - 教程:
    - 基础教程: tutorials/basic-tutorial.md
    - 高级教程: tutorials/advanced-tutorial.md
    - 自定义策略: tutorials/custom-strategies.md
    - 插件开发: tutorials/plugin-development.md
  - 示例:
    - 完整示例: examples/complete-examples.md
    - 代码片段: examples/code-snippets.md
    - 最佳实践: examples/best-practices.md
  - 部署:
    - Docker部署: deployment/docker.md
    - 云端部署: deployment/cloud.md
    - 监控配置: deployment/monitoring.md
  - 故障排除:
    - 常见问题: troubleshooting/faq.md
    - 错误代码: troubleshooting/error-codes.md
    - 性能优化: troubleshooting/performance.md
  - 更新日志: changelog.md
  - 许可证: license.md

# Markdown扩展
markdown_extensions:
  # Python Markdown
  - abbr
  - admonition
  - attr_list
  - def_list
  - footnotes
  - md_in_html
  - toc:
      permalink: true
      title: 目录
  - tables
  - meta
  
  # PyMdown Extensions
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.betterem:
      smart_enable: all
  - pymdownx.caret
  - pymdownx.details
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.keys
  - pymdownx.magiclink:
      normalize_issue_symbols: true
      repo_url_shorthand: true
      user: your-org
      repo: FinRL-Crypto
  - pymdownx.mark
  - pymdownx.smartsymbols
  - pymdownx.snippets:
      check_paths: true
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
      combine_header_slug: true
      slugify: !!python/object/apply:pymdownx.slugs.slugify
        kwds:
          case: lower
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.tilde

# 插件
plugins:
  - search:
      lang: 
        - zh
        - en
      separator: '[\s\u200b\-_,:!=\[\]()"/]+|\.(?!\d)|&[lg]t;|(?!\b)(?=[A-Z][a-z])'
  
  - awesome-pages
  
  - git-revision-date-localized:
      enable_creation_date: true
      type: timeago
      timezone: Asia/Shanghai
      locale: zh
      fallback_to_build_date: false
      exclude:
        - index.md
  
  - minify:
      minify_html: true
      minify_js: true
      minify_css: true
      htmlmin_opts:
        remove_comments: true
      cache_safe: true
  
  - redirects:
      redirect_maps:
        'old-page.md': 'new-page.md'
  
  - macros:
      include_dir: docs/includes
      include_yaml:
        - docs/data/variables.yml
  
  - include-markdown:
      opening_tag: '{!'
      closing_tag: '!}'
      encoding: utf-8
      preserve_includer_indent: false
      dedent: false
      trailing_newlines: true
      comments: true

# 额外配置
extra:
  # 版本
  version:
    provider: mike
    default: latest
  
  # 社交链接
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/your-org/FinRL-Crypto
      name: GitHub
    - icon: fontawesome/brands/docker
      link: https://hub.docker.com/r/finrl/crypto
      name: Docker Hub
    - icon: fontawesome/brands/python
      link: https://pypi.org/project/finrl-crypto/
      name: PyPI
    - icon: fontawesome/solid/paper-plane
      link: mailto:<EMAIL>
      name: 联系我们
  
  # 分析
  analytics:
    provider: google
    property: G-XXXXXXXXXX
    feedback:
      title: 这个页面有帮助吗？
      ratings:
        - icon: material/emoticon-happy-outline
          name: 有帮助
          data: 1
          note: >
            感谢您的反馈！
        - icon: material/emoticon-sad-outline
          name: 没帮助
          data: 0
          note: >
            感谢您的反馈！请告诉我们如何改进这个页面。
  
  # 同意
  consent:
    title: Cookie同意
    description: >
      我们使用cookies来识别您的重复访问和偏好，以及
      衡量我们文档的有效性和用户是否找到他们正在寻找的内容。
      通过您的同意，您帮助我们改进我们的文档。
    actions:
      - accept
      - reject
      - manage

# 额外CSS和JS
extra_css:
  - assets/stylesheets/extra.css
  - assets/stylesheets/custom.css

extra_javascript:
  - assets/javascripts/mathjax.js
  - https://polyfill.io/v3/polyfill.min.js?features=es6
  - https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js
  - assets/javascripts/custom.js

# 监视文件
watch:
  - docs/
  - mkdocs.yml
  - README.md