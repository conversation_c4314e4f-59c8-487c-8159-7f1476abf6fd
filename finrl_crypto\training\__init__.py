"""训练模块

提供强化学习训练的核心功能，包括：
- BaseTrainer: 基础训练器抽象类
- SingleAssetTrainer: 单资产交易训练器
- MultiAssetTrainer: 多资产交易训练器
- PortfolioTrainer: 投资组合训练器
- TrainerFactory: 训练器工厂
- 训练配置和工具函数

基本用法:
    ```python
    from finrl_crypto.training import create_trainer
    
    # 创建训练器
    trainer = create_trainer(
        trainer_type='single_asset',
        agent=agent,
        env=env,
        total_timesteps=100000
    )
    
    # 开始训练
    trainer.train()
    
    # 评估模型
    results = trainer.evaluate()
    ```
"""

from .base import BaseTrainer, TrainingConfig, EvaluationMetrics
from .single_asset import SingleAssetTrainer
from .multi_asset import MultiAssetTrainer
from .portfolio import PortfolioTrainer
from .factory import (
    TrainerFactory,
    create_trainer,
    create_single_asset_trainer,
    create_multi_asset_trainer,
    create_portfolio_trainer,
    get_available_trainers,
    get_trainer_info,
    get_recommended_config,
)

# 版本信息
__version__ = '1.0.0'

# 导出的类和函数
__all__ = [
    # 核心类
    'BaseTrainer',
    'SingleAssetTrainer', 
    'MultiAssetTrainer',
    'PortfolioTrainer',
    'TrainerFactory',
    
    # 工厂函数
    'create_trainer',
    'get_available_trainers',
    'get_trainer_info',
    
    # 工具类和函数
    'TrainingConfig',
    'EvaluationMetrics',
    'save_training_results',
    'load_training_results',
    'plot_training_curves',
]

# 支持的训练器类型
SUPPORTED_TRAINERS = [
    'single_asset',
    'multi_asset', 
    'portfolio',
]

# 快速创建函数
def create_single_asset_trainer(agent, env, **kwargs):
    """创建单资产训练器的便捷函数"""
    return create_trainer('single_asset', agent, env, **kwargs)

def create_multi_asset_trainer(agent, env, **kwargs):
    """创建多资产训练器的便捷函数"""
    return create_trainer('multi_asset', agent, env, **kwargs)

def create_portfolio_trainer(agent, env, **kwargs):
    """创建投资组合训练器的便捷函数"""
    return create_trainer('portfolio', agent, env, **kwargs)

def get_supported_trainers():
    """获取支持的训练器类型"""
    return SUPPORTED_TRAINERS.copy()