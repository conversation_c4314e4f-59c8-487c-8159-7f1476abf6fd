"""Deep Q-Network (DQN) 智能体实现

实现DQN算法用于离散动作空间的强化学习。
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional
import random

from .base import BaseAgent, ReplayBuffer, create_mlp


class DQNNetwork(nn.Module):
    """DQN网络"""
    
    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 hidden_dims: List[int] = [256, 256],
                 activation: str = 'relu',
                 dropout: float = 0.0):
        """初始化DQN网络
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            hidden_dims: 隐藏层维度
            activation: 激活函数
            dropout: Dropout概率
        """
        super().__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # 创建Q网络
        self.q_network = create_mlp(
            input_dim=state_dim,
            output_dim=action_dim,
            hidden_dims=hidden_dims,
            activation=activation,
            dropout=dropout
        )
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """前向传播
        
        Args:
            state: 状态张量
            
        Returns:
            Q值
        """
        return self.q_network(state)


class DQNAgent(BaseAgent):
    """DQN智能体
    
    实现Deep Q-Network算法，适用于离散动作空间。
    """
    
    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 learning_rate: float = 1e-3,
                 gamma: float = 0.99,
                 epsilon: float = 1.0,
                 epsilon_min: float = 0.01,
                 epsilon_decay: float = 0.995,
                 buffer_size: int = 100000,
                 batch_size: int = 64,
                 target_update_freq: int = 1000,
                 hidden_dims: List[int] = [256, 256],
                 activation: str = 'relu',
                 dropout: float = 0.0,
                 double_dqn: bool = True,
                 dueling_dqn: bool = False,
                 device: str = 'auto',
                 **kwargs):
        """初始化DQN智能体
        
        Args:
            state_dim: 状态空间维度
            action_dim: 动作空间维度
            learning_rate: 学习率
            gamma: 折扣因子
            epsilon: 探索率
            epsilon_min: 最小探索率
            epsilon_decay: 探索率衰减
            buffer_size: 经验回放缓冲区大小
            batch_size: 批次大小
            target_update_freq: 目标网络更新频率
            hidden_dims: 隐藏层维度
            activation: 激活函数
            dropout: Dropout概率
            double_dqn: 是否使用Double DQN
            dueling_dqn: 是否使用Dueling DQN
            device: 计算设备
            **kwargs: 其他参数
        """
        # DQN特定参数
        self.gamma = gamma
        self.epsilon = epsilon
        self.epsilon_min = epsilon_min
        self.epsilon_decay = epsilon_decay
        self.batch_size = batch_size
        self.target_update_freq = target_update_freq
        self.hidden_dims = hidden_dims
        self.activation = activation
        self.dropout = dropout
        self.double_dqn = double_dqn
        self.dueling_dqn = dueling_dqn
        
        # 调用父类初始化
        super().__init__(
            state_dim=state_dim,
            action_dim=action_dim,
            learning_rate=learning_rate,
            device=device,
            **kwargs
        )
        
        # 经验回放缓冲区
        self.replay_buffer = ReplayBuffer(
            capacity=buffer_size,
            state_dim=state_dim,
            action_dim=1  # DQN使用离散动作
        )
        
        # 目标网络更新计数器
        self.target_update_counter = 0
    
    def _build_networks(self):
        """构建神经网络"""
        if self.dueling_dqn:
            self.q_network = DuelingDQNNetwork(
                state_dim=self.state_dim,
                action_dim=self.action_dim,
                hidden_dims=self.hidden_dims,
                activation=self.activation,
                dropout=self.dropout
            ).to(self.device)
            
            self.target_network = DuelingDQNNetwork(
                state_dim=self.state_dim,
                action_dim=self.action_dim,
                hidden_dims=self.hidden_dims,
                activation=self.activation,
                dropout=self.dropout
            ).to(self.device)
        else:
            self.q_network = DQNNetwork(
                state_dim=self.state_dim,
                action_dim=self.action_dim,
                hidden_dims=self.hidden_dims,
                activation=self.activation,
                dropout=self.dropout
            ).to(self.device)
            
            self.target_network = DQNNetwork(
                state_dim=self.state_dim,
                action_dim=self.action_dim,
                hidden_dims=self.hidden_dims,
                activation=self.activation,
                dropout=self.dropout
            ).to(self.device)
        
        # 初始化目标网络
        self.target_network.load_state_dict(self.q_network.state_dict())
        self.target_network.eval()
    
    def _build_optimizers(self):
        """构建优化器"""
        self.optimizer = torch.optim.Adam(
            self.q_network.parameters(),
            lr=self.learning_rate
        )
    
    def act(self, state: np.ndarray, training: bool = True) -> np.ndarray:
        """选择动作
        
        Args:
            state: 当前状态
            training: 是否为训练模式
            
        Returns:
            选择的动作
        """
        # ε-贪婪策略
        if training and random.random() < self.epsilon:
            # 随机探索
            action = random.randint(0, self.action_dim - 1)
        else:
            # 贪婪选择
            with torch.no_grad():
                state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
                q_values = self.q_network(state_tensor)
                action = q_values.argmax().item()
        
        return np.array([action])
    
    def learn(self,
              state: np.ndarray,
              action: np.ndarray,
              reward: float,
              next_state: np.ndarray,
              done: bool) -> Dict[str, float]:
        """学习更新
        
        Args:
            state: 当前状态
            action: 执行的动作
            reward: 获得的奖励
            next_state: 下一状态
            done: 是否结束
            
        Returns:
            学习统计信息
        """
        # 存储经验
        self.replay_buffer.add(state, action, reward, next_state, done)
        
        # 检查是否可以开始学习
        if not self.replay_buffer.is_ready(self.batch_size):
            return {'loss': 0.0}
        
        # 从经验回放缓冲区采样
        batch_states, batch_actions, batch_rewards, batch_next_states, batch_dones = \
            self.replay_buffer.sample(self.batch_size)
        
        # 转换为张量
        states = torch.FloatTensor(batch_states).to(self.device)
        actions = torch.LongTensor(batch_actions.astype(int)).to(self.device)
        rewards = torch.FloatTensor(batch_rewards).to(self.device)
        next_states = torch.FloatTensor(batch_next_states).to(self.device)
        dones = torch.BoolTensor(batch_dones).to(self.device)
        
        # 计算当前Q值
        current_q_values = self.q_network(states).gather(1, actions)
        
        # 计算目标Q值
        with torch.no_grad():
            if self.double_dqn:
                # Double DQN
                next_actions = self.q_network(next_states).argmax(1, keepdim=True)
                next_q_values = self.target_network(next_states).gather(1, next_actions)
            else:
                # 标准DQN
                next_q_values = self.target_network(next_states).max(1, keepdim=True)[0]
            
            target_q_values = rewards.unsqueeze(1) + (self.gamma * next_q_values * (~dones).unsqueeze(1))
        
        # 计算损失
        loss = F.mse_loss(current_q_values, target_q_values)
        
        # 反向传播
        self.optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), max_norm=1.0)
        
        self.optimizer.step()
        
        # 更新目标网络
        self.target_update_counter += 1
        if self.target_update_counter >= self.target_update_freq:
            self.target_network.load_state_dict(self.q_network.state_dict())
            self.target_update_counter = 0
        
        # 衰减探索率
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
        
        # 更新训练步数
        self.training_step += 1
        
        # 记录损失
        loss_value = loss.item()
        self.losses.append(loss_value)
        
        return {
            'loss': loss_value,
            'epsilon': self.epsilon,
            'q_value_mean': current_q_values.mean().item(),
            'target_q_mean': target_q_values.mean().item()
        }
    
    def _get_save_dict(self) -> Dict[str, Any]:
        """获取保存字典"""
        return {
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'epsilon': self.epsilon,
            'target_update_counter': self.target_update_counter,
            'gamma': self.gamma,
            'epsilon_min': self.epsilon_min,
            'epsilon_decay': self.epsilon_decay,
            'batch_size': self.batch_size,
            'target_update_freq': self.target_update_freq,
            'double_dqn': self.double_dqn,
            'dueling_dqn': self.dueling_dqn,
        }
    
    def _load_from_dict(self, save_dict: Dict[str, Any]):
        """从保存字典加载"""
        self.q_network.load_state_dict(save_dict['q_network_state_dict'])
        self.target_network.load_state_dict(save_dict['target_network_state_dict'])
        self.optimizer.load_state_dict(save_dict['optimizer_state_dict'])
        self.epsilon = save_dict.get('epsilon', self.epsilon)
        self.target_update_counter = save_dict.get('target_update_counter', 0)
    
    def set_training_mode(self, training: bool = True):
        """设置训练模式"""
        if training:
            self.q_network.train()
        else:
            self.q_network.eval()
    
    def get_action_distribution(self, state: np.ndarray) -> Dict[str, Any]:
        """获取动作分布"""
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            q_values = self.q_network(state_tensor).squeeze(0)
            
            # 计算softmax概率分布
            probs = F.softmax(q_values, dim=0)
            
            return {
                'q_values': q_values.cpu().numpy(),
                'probabilities': probs.cpu().numpy(),
                'best_action': q_values.argmax().item(),
                'action_type': 'discrete'
            }


class DuelingDQNNetwork(nn.Module):
    """Dueling DQN网络
    
    将Q值分解为状态价值和动作优势。
    """
    
    def __init__(self,
                 state_dim: int,
                 action_dim: int,
                 hidden_dims: List[int] = [256, 256],
                 activation: str = 'relu',
                 dropout: float = 0.0):
        """初始化Dueling DQN网络
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            hidden_dims: 隐藏层维度
            activation: 激活函数
            dropout: Dropout概率
        """
        super().__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # 共享特征提取层
        self.feature_layer = create_mlp(
            input_dim=state_dim,
            output_dim=hidden_dims[-1],
            hidden_dims=hidden_dims[:-1],
            activation=activation,
            dropout=dropout
        )
        
        # 状态价值流
        self.value_stream = nn.Linear(hidden_dims[-1], 1)
        
        # 动作优势流
        self.advantage_stream = nn.Linear(hidden_dims[-1], action_dim)
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """前向传播
        
        Args:
            state: 状态张量
            
        Returns:
            Q值
        """
        features = self.feature_layer(state)
        
        # 计算状态价值
        value = self.value_stream(features)
        
        # 计算动作优势
        advantage = self.advantage_stream(features)
        
        # 组合Q值：Q(s,a) = V(s) + A(s,a) - mean(A(s,·))
        q_values = value + advantage - advantage.mean(dim=1, keepdim=True)
        
        return q_values