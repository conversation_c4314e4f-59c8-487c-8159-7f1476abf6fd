# Pull Request Template

## 📋 描述

请简要描述此PR的目的和内容：

- [ ] 新功能
- [ ] Bug修复
- [ ] 重构
- [ ] 文档更新
- [ ] 性能优化
- [ ] 测试改进
- [ ] 其他：_____________

## 🔄 变更内容

### 主要变更
- 
- 
- 

### 次要变更
- 
- 

## 🧪 测试

### 测试类型
- [ ] 单元测试
- [ ] 集成测试
- [ ] 端到端测试
- [ ] 性能测试
- [ ] 手动测试

### 测试覆盖率
- [ ] 新代码有相应的测试
- [ ] 现有测试仍然通过
- [ ] 测试覆盖率没有下降

### 测试步骤
1. 
2. 
3. 

## 📸 截图/演示

如果有UI变更或新功能演示，请提供截图或GIF：

## 🔗 相关Issue

- Closes #
- Fixes #
- Related to #

## 📚 文档

- [ ] 代码有适当的注释
- [ ] README已更新（如需要）
- [ ] API文档已更新（如需要）
- [ ] 变更日志已更新

## ⚡ 性能影响

- [ ] 无性能影响
- [ ] 性能提升
- [ ] 可能的性能影响（请说明）

性能测试结果：

## 🔒 安全考虑

- [ ] 无安全影响
- [ ] 已进行安全审查
- [ ] 需要安全团队审查

安全相关变更：

## 🚀 部署注意事项

- [ ] 无特殊部署要求
- [ ] 需要数据库迁移
- [ ] 需要配置更新
- [ ] 需要依赖更新
- [ ] 其他部署注意事项

部署步骤：
1. 
2. 
3. 

## ✅ 检查清单

### 代码质量
- [ ] 代码遵循项目编码规范
- [ ] 没有硬编码的值
- [ ] 错误处理适当
- [ ] 日志记录适当
- [ ] 代码可读性良好

### 测试
- [ ] 所有测试通过
- [ ] 新功能有测试覆盖
- [ ] 边界情况已测试
- [ ] 错误情况已测试

### 文档
- [ ] 代码注释充分
- [ ] 复杂逻辑有说明
- [ ] 公共API有文档
- [ ] 变更记录已更新

### 兼容性
- [ ] 向后兼容
- [ ] 跨平台兼容
- [ ] 依赖版本兼容
- [ ] API版本兼容

### 安全
- [ ] 输入验证
- [ ] 权限检查
- [ ] 敏感数据保护
- [ ] 安全最佳实践

## 👥 审查者

请指定需要审查此PR的人员：

- @reviewer1
- @reviewer2
- @team-lead

## 📝 额外说明

任何其他需要审查者知道的信息：

---

## 审查者指南

### 🔍 审查重点

1. **代码质量**
   - 代码是否清晰易懂？
   - 是否遵循项目编码规范？
   - 是否有代码重复？
   - 错误处理是否适当？

2. **功能正确性**
   - 代码是否实现了预期功能？
   - 是否有潜在的bug？
   - 边界情况是否处理？

3. **性能**
   - 是否有性能问题？
   - 算法复杂度是否合理？
   - 资源使用是否高效？

4. **安全性**
   - 是否有安全漏洞？
   - 输入验证是否充分？
   - 权限控制是否正确？

5. **测试**
   - 测试覆盖是否充分？
   - 测试用例是否有意义？
   - 是否测试了错误情况？

6. **文档**
   - 代码注释是否充分？
   - 文档是否需要更新？
   - API变更是否有说明？

### 📋 审查检查清单

- [ ] 代码逻辑正确
- [ ] 编码规范遵循
- [ ] 测试充分
- [ ] 文档完整
- [ ] 性能可接受
- [ ] 安全无问题
- [ ] 兼容性良好
- [ ] 部署可行

### 💬 反馈指南

- 提供具体、可操作的建议
- 解释问题的原因和影响
- 建议改进方案
- 保持建设性和友好的语调
- 区分必须修复和建议改进

### ✅ 批准标准

只有在以下条件都满足时才应批准PR：

1. 所有CI检查通过
2. 代码质量符合标准
3. 功能正确实现
4. 测试覆盖充分
5. 文档更新完整
6. 无明显安全问题
7. 性能影响可接受