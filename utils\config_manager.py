#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础配置管理器
文件名：config_manager.py
目标：提供统一的配置管理功能，替代分散的全局变量导入
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, Union
from datetime import datetime, timedelta
import numpy as np
from functools import reduce
import operator as op


class ConfigManager:
    """
    基础配置管理器
    
    功能：
    1. 统一管理所有配置参数
    2. 支持从文件加载配置
    3. 提供配置验证
    4. 支持配置的动态计算
    5. 提供配置访问接口
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，支持JSON和YAML格式
        """
        self._config = {}
        self._computed_config = {}
        
        # 加载默认配置
        self._load_default_config()
        
        # 如果提供了配置文件，则加载文件配置
        if config_file and os.path.exists(config_file):
            self.load_from_file(config_file)
        
        # 计算派生配置
        self._compute_derived_config()
    
    def _load_default_config(self):
        """
        加载默认配置（从原config_main.py迁移）
        """
        self._config = {
            # 基础设置
            'seed': 2390408,
            'timeframe': '5m',
            
            # 优化设置
            'h_trials': 50,
            'kcv_groups': 5,
            'k_test_groups': 2,
            'num_paths': 4,
            
            # 数据设置
            'trade_start_date': '2022-04-30 00:00:00',
            'trade_end_date': '2022-06-27 00:00:00',
            'no_candles_for_train': 20000,
            'no_candles_for_val': 5000,
            
            # 交易对设置
            'ticker_list': [
                'AAVEUSDT', 'AVAXUSDT', 'BTCUSDT', 'NEARUSDT', 'LINKUSDT',
                'ETHUSDT', 'LTCUSDT', 'MATICUSDT', 'UNIUSDT', 'SOLUSDT'
            ],
            
            # 最小购买限制
            'alpaca_limits': [0.01, 0.10, 0.0001, 0.1, 0.1, 0.001, 0.01, 10, 0.1, 0.01],
            
            # 技术指标
            'technical_indicators': [
                'open', 'high', 'low', 'close', 'volume',
                'macd', 'macd_signal', 'macd_hist', 'rsi', 'cci', 'dx'
            ],
            
            # 时间框架映射
            'timeframe_minutes': {
                '1m': 1, '5m': 5, '10m': 10, '30m': 30,
                '1h': 60, '2h': 120, '4h': 240, '12h': 720
            }
        }
    
    def _compute_derived_config(self):
        """
        计算派生配置参数
        """
        # 计算组合数
        n_groups = self._config['num_paths'] + 1
        k_test_groups = self._config['k_test_groups']
        number_of_splits = self._nCr(n_groups, n_groups - k_test_groups)
        
        # 计算日期
        train_start, train_end, val_start, val_end = self._calculate_dates()
        
        self._computed_config = {
            'n_groups': n_groups,
            'number_of_splits': number_of_splits,
            'train_start_date': train_start,
            'train_end_date': train_end,
            'val_start_date': val_start,
            'val_end_date': val_end
        }
    
    def _nCr(self, n: int, r: int) -> int:
        """
        计算组合数 C(n,r)
        """
        r = min(r, n - r)
        numer = reduce(op.mul, range(n, n - r, -1), 1)
        denom = reduce(op.mul, range(1, r + 1), 1)
        return numer // denom
    
    def _calculate_dates(self) -> tuple:
        """
        基于蜡烛图分布计算所有必要的日期
        
        Returns:
            tuple: (train_start_date, train_end_date, val_start_date, val_end_date)
        """
        timeframe = self._config['timeframe']
        timeframe_minutes = self._config['timeframe_minutes']
        
        if timeframe not in timeframe_minutes:
            # 如果时间框架无效，使用默认值
            timeframe = '5m'
            
        no_minutes = timeframe_minutes[timeframe]
        
        trade_start_date_obj = datetime.strptime(
            self._config['trade_start_date'], "%Y-%m-%d %H:%M:%S"
        )
        
        no_candles_train = self._config['no_candles_for_train']
        no_candles_val = self._config['no_candles_for_val']
        
        # 训练开始日期 = 交易开始日期 - (训练蜡烛数 + 验证蜡烛数)
        train_start_date = (
            trade_start_date_obj - 
            timedelta(minutes=no_minutes * (no_candles_train + no_candles_val))
        ).strftime("%Y-%m-%d %H:%M:%S")
        
        # 训练结束日期 = 交易开始日期 - (验证蜡烛数 + 1)
        train_end_date = (
            trade_start_date_obj - 
            timedelta(minutes=no_minutes * (no_candles_val + 1))
        ).strftime("%Y-%m-%d %H:%M:%S")
        
        # 验证开始日期 = 交易开始日期 - 验证蜡烛数
        val_start_date = (
            trade_start_date_obj - 
            timedelta(minutes=no_minutes * no_candles_val)
        ).strftime("%Y-%m-%d %H:%M:%S")
        
        # 验证结束日期 = 交易开始日期 - 1
        val_end_date = (
            trade_start_date_obj - 
            timedelta(minutes=no_minutes * 1)
        ).strftime("%Y-%m-%d %H:%M:%S")
        
        return train_start_date, train_end_date, val_start_date, val_end_date
    
    def load_from_file(self, config_file: str):
        """
        从文件加载配置
        
        Args:
            config_file: 配置文件路径
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                if config_file.endswith('.json'):
                    file_config = json.load(f)
                elif config_file.endswith(('.yml', '.yaml')):
                    file_config = yaml.safe_load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {config_file}")
            
            # 更新配置
            self._config.update(file_config)
            
            # 重新计算派生配置
            self._compute_derived_config()
            
        except Exception as e:
            raise RuntimeError(f"加载配置文件失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键名
            default: 默认值
            
        Returns:
            配置值
        """
        # 首先检查计算配置
        if key in self._computed_config:
            return self._computed_config[key]
        
        # 然后检查基础配置
        return self._config.get(key, default)
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键名
            value: 配置值
        """
        self._config[key] = value
        
        # 如果是影响派生配置的键，重新计算
        if key in ['num_paths', 'k_test_groups', 'timeframe', 
                   'trade_start_date', 'no_candles_for_train', 'no_candles_for_val']:
            self._compute_derived_config()
    
    def get_all_config(self) -> Dict[str, Any]:
        """
        获取所有配置
        
        Returns:
            完整的配置字典
        """
        result = self._config.copy()
        result.update(self._computed_config)
        return result
    
    def validate_config(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 检查必需的配置项
            required_keys = [
                'timeframe', 'ticker_list', 'no_candles_for_train',
                'no_candles_for_val', 'trade_start_date'
            ]
            
            for key in required_keys:
                if key not in self._config:
                    raise ValueError(f"缺少必需的配置项: {key}")
            
            # 检查时间框架是否有效
            if self._config['timeframe'] not in self._config['timeframe_minutes']:
                raise ValueError(f"无效的时间框架: {self._config['timeframe']}")
            
            # 检查日期格式
            datetime.strptime(self._config['trade_start_date'], "%Y-%m-%d %H:%M:%S")
            
            # 检查数值配置
            if self._config['no_candles_for_train'] <= 0:
                raise ValueError("训练蜡烛数必须大于0")
            
            if self._config['no_candles_for_val'] <= 0:
                raise ValueError("验证蜡烛数必须大于0")
            
            return True
            
        except Exception as e:
            print(f"配置验证失败: {e}")
            return False
    
    def save_to_file(self, config_file: str):
        """
        保存配置到文件
        
        Args:
            config_file: 配置文件路径
        """
        try:
            # 准备要保存的配置（排除计算配置）
            config_to_save = self._config.copy()
            
            # 转换numpy数组为列表
            if 'alpaca_limits' in config_to_save:
                if isinstance(config_to_save['alpaca_limits'], np.ndarray):
                    config_to_save['alpaca_limits'] = config_to_save['alpaca_limits'].tolist()
            
            with open(config_file, 'w', encoding='utf-8') as f:
                if config_file.endswith('.json'):
                    json.dump(config_to_save, f, indent=2, ensure_ascii=False)
                elif config_file.endswith(('.yml', '.yaml')):
                    yaml.dump(config_to_save, f, default_flow_style=False, allow_unicode=True)
                else:
                    raise ValueError(f"不支持的配置文件格式: {config_file}。支持的格式: .json, .yml, .yaml")
                    
        except Exception as e:
            raise RuntimeError(f"保存配置文件失败: {e}")
    
    def print_config(self, title: str = "配置信息"):
        """
        打印配置信息
        
        Args:
            title: 标题
        """
        print(f"\n{'='*50}")
        print(f"{title:^50}")
        print(f"{'='*50}")
        
        # 基础配置
        print("\n[基础配置]")
        print(f"时间框架: {self.get('timeframe')}")
        print(f"随机种子: {self.get('seed')}")
        print(f"优化试验数: {self.get('h_trials')}")
        
        # 数据配置
        print("\n[数据配置]")
        print(f"训练蜡烛数: {self.get('no_candles_for_train')}")
        print(f"验证蜡烛数: {self.get('no_candles_for_val')}")
        print(f"交易开始日期: {self.get('trade_start_date')}")
        print(f"交易结束日期: {self.get('trade_end_date')}")
        
        # 计算配置
        print("\n[计算配置]")
        print(f"训练开始日期: {self.get('train_start_date')}")
        print(f"训练结束日期: {self.get('train_end_date')}")
        print(f"验证开始日期: {self.get('val_start_date')}")
        print(f"验证结束日期: {self.get('val_end_date')}")
        print(f"分组数: {self.get('n_groups')}")
        print(f"分割数: {self.get('number_of_splits')}")
        
        # 交易对
        print("\n[交易对]")
        ticker_list = self.get('ticker_list')
        print(f"交易对数量: {len(ticker_list)}")
        print(f"交易对列表: {ticker_list}")
        
        print(f"\n{'='*50}\n")


# 全局配置管理器实例
_global_config_manager = None


def get_config_manager() -> ConfigManager:
    """
    获取全局配置管理器实例（单例模式）
    
    Returns:
        ConfigManager: 配置管理器实例
    """
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = ConfigManager()
    return _global_config_manager


def init_config_manager(config_file: Optional[str] = None) -> ConfigManager:
    """
    初始化全局配置管理器
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        ConfigManager: 配置管理器实例
    """
    global _global_config_manager
    _global_config_manager = ConfigManager(config_file)
    return _global_config_manager


# 便捷函数
def get_config(key: str, default: Any = None) -> Any:
    """
    获取配置值的便捷函数
    
    Args:
        key: 配置键名
        default: 默认值
        
    Returns:
        配置值
    """
    return get_config_manager().get(key, default)


def set_config(key: str, value: Any):
    """
    设置配置值的便捷函数
    
    Args:
        key: 配置键名
        value: 配置值
    """
    get_config_manager().set(key, value)