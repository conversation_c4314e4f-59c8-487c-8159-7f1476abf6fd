"""pytest 配置文件

提供全局测试配置、fixtures 和工具函数。
"""

import pytest
import pandas as pd
import numpy as np
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, MagicMock
from typing import Dict, Any, List
import os
import sys

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 测试标记定义
def pytest_configure(config):
    """配置 pytest 标记"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "e2e: marks tests as end-to-end tests"
    )
    config.addinivalue_line(
        "markers", "gpu: marks tests that require GPU"
    )
    config.addinivalue_line(
        "markers", "network: marks tests that require network access"
    )

# 全局 fixtures

@pytest.fixture(scope="session")
def temp_dir():
    """会话级别的临时目录"""
    temp_dir = tempfile.mkdtemp(prefix="finrl_crypto_test_")
    yield Path(temp_dir)
    shutil.rmtree(temp_dir, ignore_errors=True)

@pytest.fixture
def temp_file(temp_dir):
    """临时文件"""
    temp_file = temp_dir / "test_file.tmp"
    yield temp_file
    if temp_file.exists():
        temp_file.unlink()

@pytest.fixture(scope="session")
def sample_price_data():
    """会话级别的样本价格数据"""
    np.random.seed(42)  # 确保可重现性
    
    # 生成时间序列
    start_date = pd.Timestamp('2023-01-01')
    end_date = pd.Timestamp('2023-12-31')
    dates = pd.date_range(start_date, end_date, freq='1h')
    
    # 生成模拟价格数据（几何布朗运动）
    n_periods = len(dates)
    dt = 1/24/365  # 1小时
    mu = 0.1  # 年化收益率
    sigma = 0.3  # 年化波动率
    
    # 生成随机游走
    returns = np.random.normal(
        (mu - 0.5 * sigma**2) * dt,
        sigma * np.sqrt(dt),
        n_periods
    )
    
    # 计算价格
    initial_price = 50000  # BTC 初始价格
    prices = initial_price * np.exp(np.cumsum(returns))
    
    # 生成 OHLCV 数据
    noise_factor = 0.001
    data = pd.DataFrame({
        'timestamp': dates,
        'open': prices * (1 + np.random.normal(0, noise_factor, n_periods)),
        'high': prices * (1 + np.abs(np.random.normal(0, noise_factor * 2, n_periods))),
        'low': prices * (1 - np.abs(np.random.normal(0, noise_factor * 2, n_periods))),
        'close': prices,
        'volume': np.random.lognormal(10, 1, n_periods),  # 对数正态分布的成交量
        'symbol': 'BTC-USD'
    })
    
    # 确保 high >= max(open, close) 和 low <= min(open, close)
    data['high'] = np.maximum(data['high'], np.maximum(data['open'], data['close']))
    data['low'] = np.minimum(data['low'], np.minimum(data['open'], data['close']))
    
    return data.set_index('timestamp')

@pytest.fixture
def sample_multi_asset_data(sample_price_data):
    """多资产样本数据"""
    symbols = ['BTC-USD', 'ETH-USD', 'ADA-USD']
    data_list = []
    
    for i, symbol in enumerate(symbols):
        # 为每个资产生成不同的价格数据
        symbol_data = sample_price_data.copy()
        
        # 调整价格水平
        price_multipliers = [1.0, 0.1, 0.001]  # BTC, ETH, ADA 的相对价格
        multiplier = price_multipliers[i]
        
        for col in ['open', 'high', 'low', 'close']:
            symbol_data[col] *= multiplier
        
        symbol_data['symbol'] = symbol
        data_list.append(symbol_data)
    
    return pd.concat(data_list, ignore_index=False).sort_index()

@pytest.fixture
def sample_indicators_data(sample_price_data):
    """包含技术指标的样本数据"""
    data = sample_price_data.copy()
    
    # 添加简单移动平均
    data['sma_10'] = data['close'].rolling(window=10).mean()
    data['sma_20'] = data['close'].rolling(window=20).mean()
    data['sma_50'] = data['close'].rolling(window=50).mean()
    
    # 添加指数移动平均
    data['ema_12'] = data['close'].ewm(span=12).mean()
    data['ema_26'] = data['close'].ewm(span=26).mean()
    
    # 添加 RSI
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    data['rsi'] = 100 - (100 / (1 + rs))
    
    # 添加 MACD
    data['macd'] = data['ema_12'] - data['ema_26']
    data['macd_signal'] = data['macd'].ewm(span=9).mean()
    data['macd_histogram'] = data['macd'] - data['macd_signal']
    
    # 添加布林带
    data['bb_middle'] = data['sma_20']
    bb_std = data['close'].rolling(window=20).std()
    data['bb_upper'] = data['bb_middle'] + (bb_std * 2)
    data['bb_lower'] = data['bb_middle'] - (bb_std * 2)
    
    return data

@pytest.fixture
def sample_config():
    """样本配置"""
    return {
        'data': {
            'symbols': ['BTC-USD'],
            'timeframe': '1h',
            'start_date': '2023-01-01',
            'end_date': '2023-12-31',
            'indicators': {
                'sma': [10, 20, 50],
                'ema': [12, 26],
                'rsi': [14],
                'macd': [(12, 26, 9)],
                'bollinger_bands': [(20, 2)]
            }
        },
        'environment': {
            'initial_amount': 100000,
            'transaction_cost': 0.001,
            'max_position': 1.0,
            'reward_function': 'sharpe_ratio'
        },
        'agent': {
            'algorithm': 'PPO',
            'learning_rate': 3e-4,
            'batch_size': 64,
            'n_steps': 2048,
            'gamma': 0.99,
            'gae_lambda': 0.95
        },
        'training': {
            'total_timesteps': 100000,
            'eval_freq': 10000,
            'save_freq': 50000,
            'log_interval': 1000
        },
        'backtest': {
            'initial_amount': 100000,
            'transaction_cost': 0.001,
            'slippage': 0.0005
        }
    }

@pytest.fixture
def mock_exchange():
    """模拟交易所"""
    exchange = Mock()
    
    # 模拟获取行情数据
    exchange.get_ticker.return_value = {
        'symbol': 'BTC-USD',
        'price': 50000.0,
        'volume': 1000.0,
        'bid': 49995.0,
        'ask': 50005.0,
        'timestamp': pd.Timestamp.now()
    }
    
    # 模拟下单
    exchange.place_order.return_value = {
        'id': '12345',
        'status': 'filled',
        'symbol': 'BTC-USD',
        'side': 'buy',
        'amount': 0.1,
        'price': 50000.0,
        'timestamp': pd.Timestamp.now(),
        'fee': 0.5
    }
    
    # 模拟获取订单状态
    exchange.get_order.return_value = {
        'id': '12345',
        'status': 'filled',
        'filled': 0.1,
        'remaining': 0.0
    }
    
    # 模拟获取账户余额
    exchange.get_balance.return_value = {
        'USD': {'free': 50000.0, 'used': 0.0, 'total': 50000.0},
        'BTC': {'free': 1.0, 'used': 0.0, 'total': 1.0}
    }
    
    # 模拟获取历史数据
    def mock_get_ohlcv(symbol, timeframe, since=None, limit=None):
        # 返回简单的测试数据
        dates = pd.date_range('2023-01-01', periods=100, freq='1H')
        data = []
        for i, date in enumerate(dates):
            price = 50000 + i * 10  # 简单的价格趋势
            data.append([
                int(date.timestamp() * 1000),  # timestamp
                price,      # open
                price + 50, # high
                price - 50, # low
                price + 10, # close
                1000        # volume
            ])
        return data
    
    exchange.get_ohlcv.side_effect = mock_get_ohlcv
    
    return exchange

@pytest.fixture
def mock_data_source():
    """模拟数据源"""
    data_source = Mock()
    
    def mock_fetch_data(symbols, start_date, end_date, timeframe='1h'):
        # 生成模拟数据
        dates = pd.date_range(start_date, end_date, freq=timeframe)
        data_list = []
        
        for symbol in symbols:
            np.random.seed(hash(symbol) % 2**32)  # 为每个符号生成一致的随机数
            prices = 50000 * np.exp(np.cumsum(np.random.normal(0, 0.01, len(dates))))
            
            symbol_data = pd.DataFrame({
                'timestamp': dates,
                'open': prices,
                'high': prices * 1.01,
                'low': prices * 0.99,
                'close': prices,
                'volume': np.random.randint(1000, 10000, len(dates)),
                'symbol': symbol
            })
            data_list.append(symbol_data)
        
        return pd.concat(data_list, ignore_index=True)
    
    data_source.fetch_data.side_effect = mock_fetch_data
    return data_source

@pytest.fixture
def mock_model():
    """模拟机器学习模型"""
    model = Mock()
    
    # 模拟预测
    def mock_predict(observation):
        # 返回随机动作
        if isinstance(observation, np.ndarray):
            action_dim = 3  # 假设动作空间维度为3
            action = np.random.uniform(-1, 1, action_dim)
        else:
            action = np.array([0.0, 0.5, 0.0])  # 默认动作
        
        return action, None
    
    model.predict.side_effect = mock_predict
    
    # 模拟训练
    model.learn.return_value = model
    
    # 模拟保存和加载
    model.save.return_value = None
    model.load.return_value = model
    
    return model

@pytest.fixture
def mock_environment():
    """模拟交易环境"""
    env = Mock()
    
    # 环境属性
    env.observation_space = Mock()
    env.observation_space.shape = (10,)  # 假设观察空间维度
    env.action_space = Mock()
    env.action_space.shape = (3,)  # 假设动作空间维度
    env.action_space.low = np.array([-1, -1, -1])
    env.action_space.high = np.array([1, 1, 1])
    
    # 模拟重置
    def mock_reset():
        return np.random.random(10)  # 返回随机观察
    
    env.reset.side_effect = mock_reset
    
    # 模拟步进
    def mock_step(action):
        observation = np.random.random(10)
        reward = np.random.normal(0, 1)  # 随机奖励
        done = np.random.random() < 0.01  # 1% 概率结束
        info = {'portfolio_value': 100000 + np.random.normal(0, 1000)}
        return observation, reward, done, info
    
    env.step.side_effect = mock_step
    
    return env

# 工具函数

def assert_dataframe_equal(df1: pd.DataFrame, df2: pd.DataFrame, **kwargs):
    """比较两个 DataFrame 是否相等"""
    pd.testing.assert_frame_equal(df1, df2, **kwargs)

def assert_series_equal(s1: pd.Series, s2: pd.Series, **kwargs):
    """比较两个 Series 是否相等"""
    pd.testing.assert_series_equal(s1, s2, **kwargs)

def assert_array_almost_equal(arr1: np.ndarray, arr2: np.ndarray, decimal: int = 7):
    """比较两个数组是否近似相等"""
    np.testing.assert_array_almost_equal(arr1, arr2, decimal=decimal)

def create_sample_portfolio_data(n_periods: int = 100) -> pd.DataFrame:
    """创建样本投资组合数据"""
    dates = pd.date_range('2023-01-01', periods=n_periods, freq='D')
    
    # 生成投资组合价值序列
    returns = np.random.normal(0.001, 0.02, n_periods)  # 日收益率
    portfolio_values = 100000 * np.exp(np.cumsum(returns))
    
    data = pd.DataFrame({
        'date': dates,
        'portfolio_value': portfolio_values,
        'cash': portfolio_values * 0.1,  # 10% 现金
        'positions_value': portfolio_values * 0.9,  # 90% 持仓
        'daily_return': returns,
        'cumulative_return': np.exp(np.cumsum(returns)) - 1
    })
    
    return data.set_index('date')

def create_sample_trades_data(n_trades: int = 50) -> pd.DataFrame:
    """创建样本交易数据"""
    dates = pd.date_range('2023-01-01', periods=n_trades, freq='6H')
    
    trades = []
    for i, date in enumerate(dates):
        trade = {
            'timestamp': date,
            'symbol': np.random.choice(['BTC-USD', 'ETH-USD']),
            'side': np.random.choice(['buy', 'sell']),
            'amount': np.random.uniform(0.01, 1.0),
            'price': 50000 + np.random.normal(0, 5000),
            'fee': np.random.uniform(1, 50),
            'trade_id': f'trade_{i:04d}'
        }
        trades.append(trade)
    
    return pd.DataFrame(trades)

# 性能测试工具

class PerformanceTimer:
    """性能计时器"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        import time
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        import time
        self.end_time = time.time()
    
    @property
    def elapsed(self):
        """获取经过的时间（秒）"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None

@pytest.fixture
def performance_timer():
    """性能计时器 fixture"""
    return PerformanceTimer

# 内存使用监控

def get_memory_usage():
    """获取当前内存使用情况"""
    import psutil
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024  # MB

@pytest.fixture
def memory_monitor():
    """内存监控 fixture"""
    initial_memory = get_memory_usage()
    yield initial_memory
    final_memory = get_memory_usage()
    memory_diff = final_memory - initial_memory
    if memory_diff > 100:  # 如果内存增长超过 100MB，发出警告
        pytest.warns(UserWarning, f"Memory usage increased by {memory_diff:.2f} MB")

# 数据验证工具

def validate_ohlcv_data(data: pd.DataFrame) -> bool:
    """验证 OHLCV 数据的有效性"""
    required_columns = ['open', 'high', 'low', 'close', 'volume']
    
    # 检查必需列
    if not all(col in data.columns for col in required_columns):
        return False
    
    # 检查价格关系
    if not (data['high'] >= data[['open', 'close']].max(axis=1)).all():
        return False
    
    if not (data['low'] <= data[['open', 'close']].min(axis=1)).all():
        return False
    
    # 检查非负值
    if not (data[required_columns] >= 0).all().all():
        return False
    
    return True

def validate_portfolio_data(data: pd.DataFrame) -> bool:
    """验证投资组合数据的有效性"""
    required_columns = ['portfolio_value', 'cash', 'positions_value']
    
    # 检查必需列
    if not all(col in data.columns for col in required_columns):
        return False
    
    # 检查非负值
    if not (data[required_columns] >= 0).all().all():
        return False
    
    # 检查投资组合价值等于现金加持仓价值
    tolerance = 1e-6
    total_check = np.abs(
        data['portfolio_value'] - (data['cash'] + data['positions_value'])
    ) < tolerance
    
    return total_check.all()

# 测试数据生成器

class TestDataGenerator:
    """测试数据生成器"""
    
    def __init__(self, seed: int = 42):
        self.seed = seed
        np.random.seed(seed)
    
    def generate_price_series(
        self, 
        length: int = 1000, 
        initial_price: float = 100.0,
        volatility: float = 0.02,
        trend: float = 0.0
    ) -> pd.Series:
        """生成价格序列"""
        returns = np.random.normal(trend, volatility, length)
        prices = initial_price * np.exp(np.cumsum(returns))
        dates = pd.date_range('2023-01-01', periods=length, freq='1H')
        return pd.Series(prices, index=dates)
    
    def generate_ohlcv_data(
        self,
        symbols: List[str],
        start_date: str = '2023-01-01',
        end_date: str = '2023-12-31',
        timeframe: str = '1H'
    ) -> pd.DataFrame:
        """生成 OHLCV 数据"""
        dates = pd.date_range(start_date, end_date, freq=timeframe)
        data_list = []
        
        for symbol in symbols:
            # 为每个符号设置不同的种子
            np.random.seed(self.seed + hash(symbol) % 1000)
            
            # 生成价格序列
            returns = np.random.normal(0, 0.02, len(dates))
            close_prices = 50000 * np.exp(np.cumsum(returns))
            
            # 生成 OHLC
            noise = np.random.normal(0, 0.001, len(dates))
            open_prices = close_prices * (1 + noise)
            
            high_noise = np.abs(np.random.normal(0, 0.005, len(dates)))
            low_noise = np.abs(np.random.normal(0, 0.005, len(dates)))
            
            high_prices = np.maximum(open_prices, close_prices) * (1 + high_noise)
            low_prices = np.minimum(open_prices, close_prices) * (1 - low_noise)
            
            # 生成成交量
            volumes = np.random.lognormal(10, 1, len(dates))
            
            symbol_data = pd.DataFrame({
                'timestamp': dates,
                'symbol': symbol,
                'open': open_prices,
                'high': high_prices,
                'low': low_prices,
                'close': close_prices,
                'volume': volumes
            })
            
            data_list.append(symbol_data)
        
        return pd.concat(data_list, ignore_index=True)

@pytest.fixture
def test_data_generator():
    """测试数据生成器 fixture"""
    return TestDataGenerator()

# 清理函数

def pytest_runtest_teardown(item, nextitem):
    """测试运行后清理"""
    # 清理临时文件
    import gc
    gc.collect()
    
    # 重置随机种子
    np.random.seed(None)

# 跳过条件

def skip_if_no_gpu():
    """如果没有 GPU 则跳过测试"""
    try:
        import torch
        if not torch.cuda.is_available():
            pytest.skip("GPU not available")
    except ImportError:
        pytest.skip("PyTorch not installed")

def skip_if_no_network():
    """如果没有网络连接则跳过测试"""
    import socket
    try:
        socket.create_connection(("*******", 53), timeout=3)
    except OSError:
        pytest.skip("Network not available")

# 自定义断言

def assert_valid_returns(returns: pd.Series, max_return: float = 1.0):
    """断言收益率序列有效"""
    assert isinstance(returns, pd.Series), "Returns must be a pandas Series"
    assert not returns.empty, "Returns series cannot be empty"
    assert returns.abs().max() <= max_return, f"Returns exceed maximum threshold: {max_return}"
    assert not returns.isna().all(), "All returns cannot be NaN"

def assert_valid_prices(prices: pd.Series, min_price: float = 0.0):
    """断言价格序列有效"""
    assert isinstance(prices, pd.Series), "Prices must be a pandas Series"
    assert not prices.empty, "Prices series cannot be empty"
    assert (prices >= min_price).all(), f"Prices must be >= {min_price}"
    assert not prices.isna().all(), "All prices cannot be NaN"

def assert_valid_portfolio_metrics(metrics: Dict[str, float]):
    """断言投资组合指标有效"""
    required_metrics = ['total_return', 'sharpe_ratio', 'max_drawdown']
    
    for metric in required_metrics:
        assert metric in metrics, f"Missing required metric: {metric}"
        assert isinstance(metrics[metric], (int, float)), f"Metric {metric} must be numeric"
        assert not np.isnan(metrics[metric]), f"Metric {metric} cannot be NaN"
    
    # 检查合理范围
    assert -1 <= metrics['total_return'] <= 10, "Total return out of reasonable range"
    assert -10 <= metrics['sharpe_ratio'] <= 10, "Sharpe ratio out of reasonable range"
    assert 0 <= metrics['max_drawdown'] <= 1, "Max drawdown must be between 0 and 1"