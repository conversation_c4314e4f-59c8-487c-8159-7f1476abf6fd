#!/usr/bin/env python3
"""
接口包

这个包定义了系统中使用的各种抽象接口，为不同的实现提供统一的契约。

模块:
    trainer_interface: 训练器和模型的抽象接口定义

作者: FinRL-Crypto 重构项目
日期: 2024
"""

from .trainer_interface import (
    TrainerInterface,
    ModelInterface,
    TrainingError,
    EvaluationError,
    PredictionError,
    ModelNotFoundError,
    ConfigurationError
)

__all__ = [
    'TrainerInterface',
    'ModelInterface',
    'TrainingError',
    'EvaluationError', 
    'PredictionError',
    'ModelNotFoundError',
    'ConfigurationError'
]

__version__ = '1.0.0'
__author__ = 'FinRL-Crypto 重构项目'