"""并发优化模块

提供线程池、进程池和异步任务管理功能，优化并发性能。
"""

import logging
import threading
import multiprocessing
import asyncio
import time
from typing import Dict, List, Optional, Callable, Any, Union, Awaitable
from dataclasses import dataclass
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, Future, as_completed
from queue import Queue, Empty
from enum import Enum
import psutil


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class TaskInfo:
    """任务信息数据类"""
    task_id: str
    name: str
    priority: TaskPriority
    status: TaskStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Any = None
    error: Optional[Exception] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class ThreadPoolManager:
    """线程池管理器
    
    管理线程池的创建、任务分配和资源监控。
    """
    
    def __init__(self, max_workers: Optional[int] = None, 
                 thread_name_prefix: str = "ThreadPool"):
        self.max_workers = max_workers or min(32, (multiprocessing.cpu_count() or 1) + 4)
        self.thread_name_prefix = thread_name_prefix
        self._executor: Optional[ThreadPoolExecutor] = None
        self._tasks: Dict[str, TaskInfo] = {}
        self._futures: Dict[str, Future] = {}
        self._lock = threading.RLock()
        self._logger = logging.getLogger(__name__)
        self._task_counter = 0
        
        # 性能统计
        self._stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'active_threads': 0,
            'queue_size': 0
        }
    
    def start(self) -> None:
        """启动线程池"""
        with self._lock:
            if self._executor is None:
                self._executor = ThreadPoolExecutor(
                    max_workers=self.max_workers,
                    thread_name_prefix=self.thread_name_prefix
                )
                self._logger.info(f"线程池已启动，最大工作线程数: {self.max_workers}")
            else:
                self._logger.warning("线程池已经在运行")
    
    def shutdown(self, wait: bool = True) -> None:
        """关闭线程池
        
        Args:
            wait: 是否等待所有任务完成
        """
        with self._lock:
            if self._executor is not None:
                self._executor.shutdown(wait=wait)
                self._executor = None
                self._logger.info("线程池已关闭")
    
    def submit_task(self, func: Callable, *args, 
                   task_name: str = "", 
                   priority: TaskPriority = TaskPriority.NORMAL,
                   **kwargs) -> str:
        """提交任务到线程池
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            task_name: 任务名称
            priority: 任务优先级
            **kwargs: 函数关键字参数
            
        Returns:
            str: 任务ID
        """
        if self._executor is None:
            self.start()
        
        with self._lock:
            self._task_counter += 1
            task_id = f"thread_task_{self._task_counter}"
            
            task_info = TaskInfo(
                task_id=task_id,
                name=task_name or func.__name__,
                priority=priority,
                status=TaskStatus.PENDING,
                created_at=datetime.now()
            )
            
            # 包装函数以便跟踪执行状态
            wrapped_func = self._wrap_function(task_id, func)
            
            # 提交任务
            future = self._executor.submit(wrapped_func, *args, **kwargs)
            
            self._tasks[task_id] = task_info
            self._futures[task_id] = future
            self._stats['total_tasks'] += 1
            
            self._logger.debug(f"任务已提交: {task_id} ({task_name})")
            return task_id
    
    def submit_batch(self, func: Callable, tasks: List[Any]) -> List[Any]:
        """批量提交任务
        
        Args:
            func: 要执行的函数
            tasks: 任务参数列表
            
        Returns:
            List[Any]: 执行结果列表
        """
        if self._executor is None:
            self.start()
        
        # 提交所有任务
        futures = []
        for task_arg in tasks:
            future = self._executor.submit(func, task_arg)
            futures.append(future)
        
        # 收集结果
        results = []
        for future in futures:
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                self._logger.error(f"任务执行失败: {e}")
                results.append(None)
        
        return results
    
    def _wrap_function(self, task_id: str, func: Callable) -> Callable:
        """包装函数以便跟踪执行状态
        
        Args:
            task_id: 任务ID
            func: 原始函数
            
        Returns:
            Callable: 包装后的函数
        """
        def wrapper(*args, **kwargs):
            with self._lock:
                if task_id in self._tasks:
                    self._tasks[task_id].status = TaskStatus.RUNNING
                    self._tasks[task_id].started_at = datetime.now()
            
            try:
                result = func(*args, **kwargs)
                
                with self._lock:
                    if task_id in self._tasks:
                        self._tasks[task_id].status = TaskStatus.COMPLETED
                        self._tasks[task_id].completed_at = datetime.now()
                        self._tasks[task_id].result = result
                        self._stats['completed_tasks'] += 1
                
                return result
                
            except Exception as e:
                with self._lock:
                    if task_id in self._tasks:
                        self._tasks[task_id].status = TaskStatus.FAILED
                        self._tasks[task_id].completed_at = datetime.now()
                        self._tasks[task_id].error = e
                        self._stats['failed_tasks'] += 1
                
                raise
        
        return wrapper
    
    def get_task_result(self, task_id: str, timeout: Optional[float] = None) -> Any:
        """获取任务结果
        
        Args:
            task_id: 任务ID
            timeout: 超时时间（秒）
            
        Returns:
            Any: 任务结果
        """
        with self._lock:
            if task_id not in self._futures:
                raise ValueError(f"任务不存在: {task_id}")
            
            future = self._futures[task_id]
        
        return future.result(timeout=timeout)
    
    def get_task_info(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[TaskInfo]: 任务信息
        """
        with self._lock:
            return self._tasks.get(task_id)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        with self._lock:
            if task_id not in self._futures:
                return False
            
            future = self._futures[task_id]
            cancelled = future.cancel()
            
            if cancelled and task_id in self._tasks:
                self._tasks[task_id].status = TaskStatus.CANCELLED
                self._tasks[task_id].completed_at = datetime.now()
            
            return cancelled
    
    def get_stats(self) -> Dict[str, Any]:
        """获取线程池统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            # 更新活跃线程数
            if self._executor:
                self._stats['active_threads'] = len(self._executor._threads)
                self._stats['queue_size'] = self._executor._work_queue.qsize()
            
            return self._stats.copy()
    
    def wait_for_completion(self, task_ids: Optional[List[str]] = None, 
                          timeout: Optional[float] = None) -> Dict[str, Any]:
        """等待任务完成
        
        Args:
            task_ids: 要等待的任务ID列表，如果为None则等待所有任务
            timeout: 超时时间（秒）
            
        Returns:
            Dict[str, Any]: 完成的任务结果
        """
        with self._lock:
            if task_ids is None:
                futures_to_wait = list(self._futures.values())
                task_id_map = {future: task_id for task_id, future in self._futures.items()}
            else:
                futures_to_wait = [self._futures[tid] for tid in task_ids if tid in self._futures]
                task_id_map = {self._futures[tid]: tid for tid in task_ids if tid in self._futures}
        
        results = {}
        
        try:
            for future in as_completed(futures_to_wait, timeout=timeout):
                task_id = task_id_map[future]
                try:
                    results[task_id] = future.result()
                except Exception as e:
                    results[task_id] = e
        except TimeoutError:
            self._logger.warning(f"等待任务完成超时: {timeout}秒")
        
        return results


class ProcessPoolManager:
    """进程池管理器
    
    管理进程池的创建、任务分配和资源监控。
    """
    
    def __init__(self, max_workers: Optional[int] = None):
        self.max_workers = max_workers or multiprocessing.cpu_count()
        self._executor: Optional[ProcessPoolExecutor] = None
        self._tasks: Dict[str, TaskInfo] = {}
        self._futures: Dict[str, Future] = {}
        self._lock = threading.RLock()
        self._logger = logging.getLogger(__name__)
        self._task_counter = 0
        
        # 性能统计
        self._stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'active_processes': 0
        }
    
    def start(self) -> None:
        """启动进程池"""
        with self._lock:
            if self._executor is None:
                self._executor = ProcessPoolExecutor(max_workers=self.max_workers)
                self._logger.info(f"进程池已启动，最大工作进程数: {self.max_workers}")
            else:
                self._logger.warning("进程池已经在运行")
    
    def shutdown(self, wait: bool = True) -> None:
        """关闭进程池
        
        Args:
            wait: 是否等待所有任务完成
        """
        with self._lock:
            if self._executor is not None:
                self._executor.shutdown(wait=wait)
                self._executor = None
                self._logger.info("进程池已关闭")
    
    def submit_task(self, func: Callable, *args, 
                   task_name: str = "",
                   **kwargs) -> str:
        """提交任务到进程池
        
        Args:
            func: 要执行的函数（必须是可序列化的）
            *args: 函数参数
            task_name: 任务名称
            **kwargs: 函数关键字参数
            
        Returns:
            str: 任务ID
        """
        if self._executor is None:
            self.start()
        
        with self._lock:
            self._task_counter += 1
            task_id = f"process_task_{self._task_counter}"
            
            task_info = TaskInfo(
                task_id=task_id,
                name=task_name or func.__name__,
                priority=TaskPriority.NORMAL,
                status=TaskStatus.PENDING,
                created_at=datetime.now()
            )
            
            # 提交任务
            future = self._executor.submit(func, *args, **kwargs)
            
            self._tasks[task_id] = task_info
            self._futures[task_id] = future
            self._stats['total_tasks'] += 1
            
            # 添加完成回调
            future.add_done_callback(lambda f: self._task_completed(task_id, f))
            
            self._logger.debug(f"任务已提交到进程池: {task_id} ({task_name})")
            return task_id
    
    def _task_completed(self, task_id: str, future: Future) -> None:
        """任务完成回调
        
        Args:
            task_id: 任务ID
            future: Future对象
        """
        with self._lock:
            if task_id in self._tasks:
                task_info = self._tasks[task_id]
                task_info.completed_at = datetime.now()
                
                if future.cancelled():
                    task_info.status = TaskStatus.CANCELLED
                elif future.exception():
                    task_info.status = TaskStatus.FAILED
                    task_info.error = future.exception()
                    self._stats['failed_tasks'] += 1
                else:
                    task_info.status = TaskStatus.COMPLETED
                    task_info.result = future.result()
                    self._stats['completed_tasks'] += 1
    
    def get_task_result(self, task_id: str, timeout: Optional[float] = None) -> Any:
        """获取任务结果
        
        Args:
            task_id: 任务ID
            timeout: 超时时间（秒）
            
        Returns:
            Any: 任务结果
        """
        with self._lock:
            if task_id not in self._futures:
                raise ValueError(f"任务不存在: {task_id}")
            
            future = self._futures[task_id]
        
        return future.result(timeout=timeout)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取进程池统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            return self._stats.copy()


class AsyncTaskManager:
    """异步任务管理器
    
    管理异步任务的执行和监控。
    """
    
    def __init__(self):
        self._tasks: Dict[str, TaskInfo] = {}
        self._running_tasks: Dict[str, asyncio.Task] = {}
        self._lock = asyncio.Lock()
        self._logger = logging.getLogger(__name__)
        self._task_counter = 0
        
        # 性能统计
        self._stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'running_tasks': 0
        }
    
    async def submit_task(self, coro: Awaitable, 
                         task_name: str = "",
                         priority: TaskPriority = TaskPriority.NORMAL) -> str:
        """提交异步任务
        
        Args:
            coro: 协程对象
            task_name: 任务名称
            priority: 任务优先级
            
        Returns:
            str: 任务ID
        """
        async with self._lock:
            self._task_counter += 1
            task_id = f"async_task_{self._task_counter}"
            
            task_info = TaskInfo(
                task_id=task_id,
                name=task_name or str(coro),
                priority=priority,
                status=TaskStatus.PENDING,
                created_at=datetime.now()
            )
            
            # 包装协程以便跟踪状态
            wrapped_coro = self._wrap_coroutine(task_id, coro)
            
            # 创建任务
            task = asyncio.create_task(wrapped_coro)
            
            self._tasks[task_id] = task_info
            self._running_tasks[task_id] = task
            self._stats['total_tasks'] += 1
            self._stats['running_tasks'] += 1
            
            self._logger.debug(f"异步任务已提交: {task_id} ({task_name})")
            return task_id
    
    async def _wrap_coroutine(self, task_id: str, coro: Awaitable) -> Any:
        """包装协程以便跟踪状态
        
        Args:
            task_id: 任务ID
            coro: 原始协程
            
        Returns:
            Any: 协程结果
        """
        async with self._lock:
            if task_id in self._tasks:
                self._tasks[task_id].status = TaskStatus.RUNNING
                self._tasks[task_id].started_at = datetime.now()
        
        try:
            result = await coro
            
            async with self._lock:
                if task_id in self._tasks:
                    self._tasks[task_id].status = TaskStatus.COMPLETED
                    self._tasks[task_id].completed_at = datetime.now()
                    self._tasks[task_id].result = result
                    self._stats['completed_tasks'] += 1
                    self._stats['running_tasks'] -= 1
                
                # 清理已完成的任务
                if task_id in self._running_tasks:
                    del self._running_tasks[task_id]
            
            return result
            
        except Exception as e:
            async with self._lock:
                if task_id in self._tasks:
                    self._tasks[task_id].status = TaskStatus.FAILED
                    self._tasks[task_id].completed_at = datetime.now()
                    self._tasks[task_id].error = e
                    self._stats['failed_tasks'] += 1
                    self._stats['running_tasks'] -= 1
                
                # 清理失败的任务
                if task_id in self._running_tasks:
                    del self._running_tasks[task_id]
            
            raise
    
    async def get_task_result(self, task_id: str) -> Any:
        """获取任务结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            Any: 任务结果
        """
        async with self._lock:
            if task_id not in self._running_tasks and task_id not in self._tasks:
                raise ValueError(f"任务不存在: {task_id}")
            
            if task_id in self._running_tasks:
                task = self._running_tasks[task_id]
                return await task
            else:
                task_info = self._tasks[task_id]
                if task_info.status == TaskStatus.COMPLETED:
                    return task_info.result
                elif task_info.status == TaskStatus.FAILED:
                    raise task_info.error
                else:
                    raise RuntimeError(f"任务未完成: {task_id}")
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        async with self._lock:
            if task_id not in self._running_tasks:
                return False
            
            task = self._running_tasks[task_id]
            cancelled = task.cancel()
            
            if cancelled and task_id in self._tasks:
                self._tasks[task_id].status = TaskStatus.CANCELLED
                self._tasks[task_id].completed_at = datetime.now()
                self._stats['running_tasks'] -= 1
                del self._running_tasks[task_id]
            
            return cancelled
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取异步任务统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        async with self._lock:
            return self._stats.copy()


class ConcurrencyOptimizer:
    """并发优化器
    
    统一管理线程池、进程池和异步任务的执行。
    """
    
    def __init__(self, max_workers: int = None, thread_workers: int = None, 
                 process_workers: int = None, enable_monitoring: bool = True):
        """初始化并发优化器
        
        Args:
            max_workers: 最大工作线程数（兼容性参数）
            thread_workers: 线程池工作线程数
            process_workers: 进程池工作进程数
            enable_monitoring: 是否启用监控
        """
        # 兼容性处理：如果提供了max_workers，使用它作为默认值
        if max_workers is not None:
            self.max_workers = max_workers
            self.thread_workers = thread_workers or max_workers
            self.process_workers = process_workers or max_workers
        else:
            self.max_workers = thread_workers or min(32, (multiprocessing.cpu_count() or 1) + 4)
            self.thread_workers = thread_workers or min(32, (multiprocessing.cpu_count() or 1) + 4)
            self.process_workers = process_workers or (multiprocessing.cpu_count() or 1)
        
        self.enable_monitoring = enable_monitoring
        
        self.thread_manager = ThreadPoolManager(max_workers=self.thread_workers)
        self.process_manager = ProcessPoolManager(max_workers=self.process_workers)
        self.async_manager = AsyncTaskManager()
        self._logger = logging.getLogger(__name__)
        
        # 系统资源监控
        self._resource_monitor_enabled = enable_monitoring
        self._resource_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0
        }
    
    def start(self) -> None:
        """启动并发优化器"""
        self.thread_manager.start()
        self.process_manager.start()
        self._logger.info("并发优化器已启动")
    
    def shutdown(self, wait: bool = True) -> None:
        """关闭并发优化器
        
        Args:
            wait: 是否等待所有任务完成
        """
        self.thread_manager.shutdown(wait=wait)
        self.process_manager.shutdown(wait=wait)
        self._logger.info("并发优化器已关闭")
    
    def submit_cpu_bound_task(self, func: Callable, *args, 
                             task_name: str = "", **kwargs) -> str:
        """提交CPU密集型任务到进程池
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            task_name: 任务名称
            **kwargs: 函数关键字参数
            
        Returns:
            str: 任务ID
        """
        return self.process_manager.submit_task(func, *args, task_name=task_name, **kwargs)
    
    def submit_io_bound_task(self, func: Callable, *args, 
                            task_name: str = "",
                            priority: TaskPriority = TaskPriority.NORMAL,
                            **kwargs) -> str:
        """提交IO密集型任务到线程池
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            task_name: 任务名称
            priority: 任务优先级
            **kwargs: 函数关键字参数
            
        Returns:
            str: 任务ID
        """
        return self.thread_manager.submit_task(
            func, *args, task_name=task_name, priority=priority, **kwargs
        )
    
    async def submit_async_task(self, coro: Awaitable, 
                               task_name: str = "",
                               priority: TaskPriority = TaskPriority.NORMAL) -> str:
        """提交异步任务
        
        Args:
            coro: 协程对象
            task_name: 任务名称
            priority: 任务优先级
            
        Returns:
            str: 任务ID
        """
        return await self.async_manager.submit_task(coro, task_name=task_name, priority=priority)
    
    def get_optimal_pool_size(self, task_type: str = "io") -> int:
        """获取最优池大小
        
        Args:
            task_type: 任务类型 ('io' 或 'cpu')
            
        Returns:
            int: 最优池大小
        """
        cpu_count = multiprocessing.cpu_count()
        
        if task_type == "cpu":
            # CPU密集型任务：通常等于CPU核心数
            return cpu_count
        else:
            # IO密集型任务：通常是CPU核心数的2-4倍
            return min(32, cpu_count * 2)
    
    def get_system_load(self) -> Dict[str, float]:
        """获取系统负载信息
        
        Returns:
            Dict[str, float]: 系统负载信息
        """
        try:
            return {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_io_percent': psutil.disk_usage('/').percent if hasattr(psutil, 'disk_usage') else 0,
                'load_average': psutil.getloadavg()[0] if hasattr(psutil, 'getloadavg') else 0
            }
        except Exception as e:
            self._logger.error(f"获取系统负载失败: {e}")
            return {}
    
    def should_throttle(self) -> bool:
        """检查是否应该限制任务提交
        
        Returns:
            bool: 是否应该限制
        """
        if not self._resource_monitor_enabled:
            return False
        
        load = self.get_system_load()
        
        # 检查CPU使用率
        if load.get('cpu_percent', 0) > self._resource_thresholds['cpu_percent']:
            return True
        
        # 检查内存使用率
        if load.get('memory_percent', 0) > self._resource_thresholds['memory_percent']:
            return True
        
        return False
    
    def enable_resource_monitoring(self, 
                                 cpu_threshold: float = 80.0,
                                 memory_threshold: float = 85.0) -> None:
        """启用资源监控
        
        Args:
            cpu_threshold: CPU使用率阈值
            memory_threshold: 内存使用率阈值
        """
        self._resource_monitor_enabled = True
        self._resource_thresholds['cpu_percent'] = cpu_threshold
        self._resource_thresholds['memory_percent'] = memory_threshold
        self._logger.info("资源监控已启用")
    
    def disable_resource_monitoring(self) -> None:
        """禁用资源监控"""
        self._resource_monitor_enabled = False
        self._logger.info("资源监控已禁用")
    
    def parallel_execute(self, func, tasks):
        """并行执行任务
        
        Args:
            func: 要执行的函数
            tasks: 任务列表
            
        Returns:
            list: 执行结果列表
        """
        return self.thread_manager.submit_batch(func, tasks)
    
    async def async_execute(self, coroutine_func, *args, **kwargs):
        """异步执行任务
        
        Args:
            coroutine_func: 协程函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            任务执行结果
        """
        coro = coroutine_func(*args, **kwargs)
        task_id = await self.async_manager.submit_task(coro)
        return await self.async_manager.get_task_result(task_id)
    
    def get_resource_usage(self) -> Dict[str, float]:
        """获取当前系统资源使用情况
        
        Returns:
            Dict[str, float]: 资源使用情况
        """
        try:
            import psutil
            return {
                'cpu_percent': psutil.cpu_percent(interval=0.1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent if hasattr(psutil, 'disk_usage') else 0.0
            }
        except ImportError:
            self._logger.warning("psutil未安装，无法获取系统资源信息")
            return {'cpu_percent': 0.0, 'memory_percent': 0.0, 'disk_percent': 0.0}
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息
        
        Returns:
            Dict[str, Any]: 综合统计信息
        """
        return {
            'thread_pool': self.thread_manager.get_stats(),
            'process_pool': self.process_manager.get_stats(),
            'system_load': self.get_system_load(),
            'resource_monitoring_enabled': self._resource_monitor_enabled,
            'resource_thresholds': self._resource_thresholds.copy()
        }