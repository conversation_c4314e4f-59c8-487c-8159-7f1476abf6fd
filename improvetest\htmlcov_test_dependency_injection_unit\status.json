{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "6c3bff4e6c9bfbe93548f25a7e1284ac", "files": {"z_57760688d1f824db___init___py": {"hash": "b77dc86282be17ece529ef6c9253569f", "index": {"url": "z_57760688d1f824db___init___py.html", "file": "core\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_57760688d1f824db_config_manager_py": {"hash": "6f50d3194ecd263c6e5a22e7636a4b54", "index": {"url": "z_57760688d1f824db_config_manager_py.html", "file": "core\\config_manager.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 176, "n_excluded": 15, "n_missing": 176, "n_branches": 48, "n_partial_branches": 0, "n_missing_branches": 48}}}, "z_57760688d1f824db_dependency_injection_py": {"hash": "164dc44c638c544ead0a636610fdbd12", "index": {"url": "z_57760688d1f824db_dependency_injection_py.html", "file": "core\\dependency_injection.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 309, "n_excluded": 4, "n_missing": 229, "n_branches": 94, "n_partial_branches": 2, "n_missing_branches": 92}}}, "z_57760688d1f824db_drl_agent_py": {"hash": "1843daef043a1389c1e4f1466b714a44", "index": {"url": "z_57760688d1f824db_drl_agent_py.html", "file": "core\\drl_agent.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 245, "n_excluded": 0, "n_missing": 245, "n_branches": 36, "n_partial_branches": 0, "n_missing_branches": 36}}}, "z_ddb02fcb4060c71d___init___py": {"hash": "61c207c03cb5dfaa43a0e21814b0daba", "index": {"url": "z_ddb02fcb4060c71d___init___py.html", "file": "finrl_crypto\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 17, "n_excluded": 0, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4ef26b22018bd3be___init___py": {"hash": "********************************", "index": {"url": "z_4ef26b22018bd3be___init___py.html", "file": "finrl_crypto\\agent\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4ef26b22018bd3be_a2c_py": {"hash": "d2ead63926cd5f3b54a8058c35ba9a13", "index": {"url": "z_4ef26b22018bd3be_a2c_py.html", "file": "finrl_crypto\\agent\\a2c.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 193, "n_excluded": 0, "n_missing": 193, "n_branches": 26, "n_partial_branches": 0, "n_missing_branches": 26}}}, "z_4ef26b22018bd3be_base_py": {"hash": "ea26def5fa206dfe9760d2d7faae1ee8", "index": {"url": "z_4ef26b22018bd3be_base_py.html", "file": "finrl_crypto\\agent\\base.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 118, "n_excluded": 65, "n_missing": 118, "n_branches": 26, "n_partial_branches": 0, "n_missing_branches": 26}}}, "z_4ef26b22018bd3be_dqn_py": {"hash": "2993fdb44e437eec604d14777bce4233", "index": {"url": "z_4ef26b22018bd3be_dqn_py.html", "file": "finrl_crypto\\agent\\dqn.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 113, "n_excluded": 0, "n_missing": 113, "n_branches": 14, "n_partial_branches": 0, "n_missing_branches": 14}}}, "z_4ef26b22018bd3be_factory_py": {"hash": "fea5eebe726e1700c3b739647cf2db84", "index": {"url": "z_4ef26b22018bd3be_factory_py.html", "file": "finrl_crypto\\agent\\factory.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 91, "n_branches": 38, "n_partial_branches": 0, "n_missing_branches": 38}}}, "z_4ef26b22018bd3be_ppo_py": {"hash": "********************************", "index": {"url": "z_4ef26b22018bd3be_ppo_py.html", "file": "finrl_crypto\\agent\\ppo.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 229, "n_excluded": 0, "n_missing": 229, "n_branches": 36, "n_partial_branches": 0, "n_missing_branches": 36}}}, "z_4ef26b22018bd3be_sac_py": {"hash": "4a27366ae82cd6d604de7267fba17e4b", "index": {"url": "z_4ef26b22018bd3be_sac_py.html", "file": "finrl_crypto\\agent\\sac.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 200, "n_excluded": 0, "n_missing": 200, "n_branches": 26, "n_partial_branches": 0, "n_missing_branches": 26}}}, "z_58676b528f8eed05___init___py": {"hash": "54cbc92ea0e7cf1c691dae5f8c9ddd40", "index": {"url": "z_58676b528f8eed05___init___py.html", "file": "finrl_crypto\\environment\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_58676b528f8eed05_base_py": {"hash": "0a9969dc79f07e78425925fb8d0aa02c", "index": {"url": "z_58676b528f8eed05_base_py.html", "file": "finrl_crypto\\environment\\base.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 155, "n_excluded": 43, "n_missing": 155, "n_branches": 42, "n_partial_branches": 0, "n_missing_branches": 42}}}, "z_58676b528f8eed05_crypto_trading_py": {"hash": "15be823f094adb083d2befe766b68657", "index": {"url": "z_58676b528f8eed05_crypto_trading_py.html", "file": "finrl_crypto\\environment\\crypto_trading.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 219, "n_excluded": 0, "n_missing": 219, "n_branches": 64, "n_partial_branches": 0, "n_missing_branches": 64}}}, "z_58676b528f8eed05_factory_py": {"hash": "91897eb6af2e9ff46e56adf787d14027", "index": {"url": "z_58676b528f8eed05_factory_py.html", "file": "finrl_crypto\\environment\\factory.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 111, "n_excluded": 0, "n_missing": 111, "n_branches": 48, "n_partial_branches": 0, "n_missing_branches": 48}}}, "z_58676b528f8eed05_portfolio_py": {"hash": "b347020130970eeda365c1c597d3ff36", "index": {"url": "z_58676b528f8eed05_portfolio_py.html", "file": "finrl_crypto\\environment\\portfolio.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 240, "n_excluded": 0, "n_missing": 240, "n_branches": 76, "n_partial_branches": 0, "n_missing_branches": 76}}}, "z_65d867bacf62c25a___init___py": {"hash": "3da0ab662f52c0665c1ad1b41d4a0eab", "index": {"url": "z_65d867bacf62c25a___init___py.html", "file": "finrl_crypto\\training\\__init__.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_65d867bacf62c25a_base_py": {"hash": "675047b62d30c77fb5ba37ec0caf37d1", "index": {"url": "z_65d867bacf62c25a_base_py.html", "file": "finrl_crypto\\training\\base.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 173, "n_excluded": 28, "n_missing": 173, "n_branches": 38, "n_partial_branches": 0, "n_missing_branches": 38}}}, "z_65d867bacf62c25a_factory_py": {"hash": "ef71e68f8316b01a742bb04a682f824d", "index": {"url": "z_65d867bacf62c25a_factory_py.html", "file": "finrl_crypto\\training\\factory.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 98, "n_excluded": 0, "n_missing": 98, "n_branches": 32, "n_partial_branches": 0, "n_missing_branches": 32}}}, "z_65d867bacf62c25a_multi_asset_py": {"hash": "891cd56b5063a57402f455437704cdb9", "index": {"url": "z_65d867bacf62c25a_multi_asset_py.html", "file": "finrl_crypto\\training\\multi_asset.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 252, "n_excluded": 0, "n_missing": 252, "n_branches": 90, "n_partial_branches": 0, "n_missing_branches": 90}}}, "z_65d867bacf62c25a_portfolio_py": {"hash": "2962dddb820ff9f6e27b7ab7f6c7df20", "index": {"url": "z_65d867bacf62c25a_portfolio_py.html", "file": "finrl_crypto\\training\\portfolio.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 294, "n_excluded": 0, "n_missing": 294, "n_branches": 98, "n_partial_branches": 0, "n_missing_branches": 98}}}, "z_65d867bacf62c25a_single_asset_py": {"hash": "bc22e334b93446eeb7a5f63fbbfc250f", "index": {"url": "z_65d867bacf62c25a_single_asset_py.html", "file": "finrl_crypto\\training\\single_asset.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 160, "n_excluded": 0, "n_missing": 160, "n_branches": 48, "n_partial_branches": 0, "n_missing_branches": 48}}}, "z_65d867bacf62c25a_utils_py": {"hash": "56872c0831ec826ccb85908107f34436", "index": {"url": "z_65d867bacf62c25a_utils_py.html", "file": "finrl_crypto\\training\\utils.py", "description": "", "nums": {"precision": 2, "n_files": 1, "n_statements": 395, "n_excluded": 0, "n_missing": 395, "n_branches": 160, "n_partial_branches": 0, "n_missing_branches": 160}}}}}