# FinRL Crypto 文档构建 Dockerfile
# 用于构建和服务项目文档

FROM python:3.9-slim as docs-base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装文档构建工具
RUN pip install --no-cache-dir \
    mkdocs==1.5.3 \
    mkdocs-material==9.4.6 \
    mkdocs-mermaid2-plugin==1.1.1 \
    mkdocs-git-revision-date-localized-plugin==1.2.0 \
    mkdocs-minify-plugin==0.7.1 \
    mkdocs-redirects==1.2.1 \
    mkdocs-awesome-pages-plugin==2.9.2 \
    mkdocs-macros-plugin==1.0.5 \
    mkdocs-include-markdown-plugin==6.0.3 \
    pymdown-extensions==10.3.1 \
    markdown-include==0.8.1 \
    pygments==2.16.1 \
    plantuml-markdown==3.9.2

# 复制文档配置
COPY mkdocs.yml .
COPY docs/ ./docs/
COPY README.md ./docs/index.md

# 构建阶段
FROM docs-base as docs-builder

# 构建文档
RUN mkdocs build

# 生产阶段 - 使用nginx服务静态文件
FROM nginx:alpine as docs-production

# 复制构建的文档
COPY --from=docs-builder /app/site /usr/share/nginx/html

# 复制nginx配置
COPY <<EOF /etc/nginx/conf.d/default.conf
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;
    
    # 启用gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 缓存静态资源
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 处理SPA路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

# 开发阶段 - 使用mkdocs serve
FROM docs-base as docs-development

# 暴露端口
EXPOSE 8000

# 启动开发服务器
CMD ["mkdocs", "serve", "--dev-addr=0.0.0.0:8000"]

# 默认使用开发阶段
FROM docs-development