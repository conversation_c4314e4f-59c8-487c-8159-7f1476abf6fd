# 文档编写指南

## 📋 概述

本指南详细介绍了 FinRL Crypto 项目的文档标准、编写规范和维护流程，确保项目文档的一致性、完整性和可维护性。

## 📚 文档架构

### 文档分类

```
docs/
├── README.md                    # 项目主页
├── api/                         # API 文档
│   ├── endpoints.md            # API 端点文档
│   ├── authentication.md      # 认证文档
│   ├── rate_limiting.md       # 限流文档
│   └── examples.md            # API 使用示例
├── user_guide/                 # 用户指南
│   ├── installation.md        # 安装指南
│   ├── quick_start.md         # 快速开始
│   ├── configuration.md       # 配置指南
│   ├── trading_strategies.md  # 交易策略
│   ├── backtesting.md         # 回测指南
│   └── troubleshooting.md     # 故障排除
├── development/               # 开发文档
│   ├── setup.md              # 开发环境搭建
│   ├── architecture.md       # 架构设计
│   ├── coding_standards.md   # 编码规范
│   ├── testing_guide.md      # 测试指南
│   ├── code_review_guide.md  # 代码审查指南
│   └── documentation_guide.md # 文档指南（本文档）
├── deployment/                # 部署文档
│   ├── docker.md             # Docker 部署
│   ├── kubernetes.md         # Kubernetes 部署
│   ├── ci_cd_guide.md        # CI/CD 指南
│   └── monitoring.md         # 监控配置
├── tutorials/                 # 教程
│   ├── beginner/             # 初学者教程
│   ├── intermediate/         # 中级教程
│   └── advanced/             # 高级教程
└── reference/                # 参考文档
    ├── algorithms.md         # 算法参考
    ├── indicators.md         # 技术指标
    ├── data_sources.md       # 数据源
    └── glossary.md          # 术语表
```

### 文档类型

#### 1. 用户文档
- **目标读者**: 最终用户、交易员、分析师
- **内容重点**: 功能介绍、使用方法、最佳实践
- **语言风格**: 简洁明了、面向任务

#### 2. 开发者文档
- **目标读者**: 开发人员、贡献者
- **内容重点**: 技术实现、架构设计、开发流程
- **语言风格**: 技术准确、详细完整

#### 3. API 文档
- **目标读者**: 集成开发者、第三方用户
- **内容重点**: 接口规范、参数说明、示例代码
- **语言风格**: 规范标准、示例丰富

## ✍️ 编写规范

### Markdown 规范

#### 1. 文件结构
```markdown
# 文档标题

## 📋 概述
简要介绍文档内容和目标读者。

## 🎯 目标
- 目标1
- 目标2
- 目标3

## 📖 内容章节
### 子章节1
内容...

### 子章节2
内容...

## 📚 参考资源
- [链接1](url1)
- [链接2](url2)

---
结尾说明或版权信息
```

#### 2. 标题规范
```markdown
# H1: 文档标题（每个文档只有一个）
## H2: 主要章节
### H3: 子章节
#### H4: 详细说明
##### H5: 特殊情况使用
###### H6: 避免使用
```

#### 3. 格式规范

**强调文本**
```markdown
**粗体**: 重要概念、关键词
*斜体*: 变量名、参数名
`代码`: 代码片段、命令、文件名
```

**列表格式**
```markdown
# 有序列表
1. 第一项
2. 第二项
   - 子项目
   - 子项目

# 无序列表
- 项目1
- 项目2
  - 子项目
  - 子项目

# 任务列表
- [x] 已完成任务
- [ ] 待完成任务
```

**代码块**
```markdown
# 行内代码
使用 `pip install finrl-crypto` 安装包。

# 代码块
```python
def example_function():
    """示例函数"""
    return "Hello, World!"
```

# 带文件名的代码块
```python:finrl_crypto/example.py
def example_function():
    """示例函数"""
    return "Hello, World!"
```
```

**链接和图片**
```markdown
# 内部链接
[安装指南](./installation.md)
[API 文档](../api/endpoints.md)

# 外部链接
[GitHub 仓库](https://github.com/username/finrl-crypto)

# 图片
![架构图](../images/architecture.png)
![流程图](https://example.com/flowchart.png)

# 带标题的图片
![架构图](../images/architecture.png "系统架构图")
```

**表格**
```markdown
| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| symbol | str | 是 | 交易对符号 |
| amount | float | 是 | 交易数量 |
| price | float | 否 | 限价价格 |
```

**引用和注释**
```markdown
> **注意**: 这是一个重要提示。

> **警告**: 这个操作可能有风险。

> **提示**: 这是一个有用的建议。
```

### 代码文档规范

#### 1. Python Docstring
```python
def calculate_rsi(prices: List[float], period: int = 14) -> List[float]:
    """
    计算相对强弱指数 (RSI)。
    
    RSI 是一个动量振荡器，用于衡量价格变化的速度和幅度。
    RSI 值在 0 到 100 之间波动，通常用于识别超买和超卖条件。
    
    Args:
        prices (List[float]): 价格序列，通常是收盘价
        period (int, optional): 计算周期，默认为 14
        
    Returns:
        List[float]: RSI 值序列
        
    Raises:
        ValueError: 当价格序列长度小于周期时
        TypeError: 当输入参数类型不正确时
        
    Example:
        >>> prices = [100, 102, 101, 103, 105, 104, 106]
        >>> rsi_values = calculate_rsi(prices, period=6)
        >>> print(f"RSI: {rsi_values[-1]:.2f}")
        RSI: 66.67
        
    Note:
        - RSI > 70 通常被认为是超买信号
        - RSI < 30 通常被认为是超卖信号
        - 建议结合其他技术指标使用
        
    References:
        - Wilder, J. W. (1978). New Concepts in Technical Trading Systems
        - https://en.wikipedia.org/wiki/Relative_strength_index
    """
    if len(prices) < period:
        raise ValueError(f"价格序列长度 ({len(prices)}) 必须大于等于周期 ({period})")
    
    # 实现代码...
    pass
```

#### 2. 类文档
```python
class TradingStrategy:
    """
    交易策略基类。
    
    这个类定义了所有交易策略的通用接口和基本功能。
    子类需要实现 `generate_signals` 方法来定义具体的交易逻辑。
    
    Attributes:
        name (str): 策略名称
        parameters (Dict[str, Any]): 策略参数
        is_active (bool): 策略是否激活
        
    Example:
        >>> class MyStrategy(TradingStrategy):
        ...     def generate_signals(self, data):
        ...         # 实现交易信号生成逻辑
        ...         pass
        >>> 
        >>> strategy = MyStrategy("我的策略")
        >>> strategy.activate()
        >>> signals = strategy.generate_signals(market_data)
    """
    
    def __init__(self, name: str, parameters: Optional[Dict[str, Any]] = None):
        """
        初始化交易策略。
        
        Args:
            name (str): 策略名称
            parameters (Optional[Dict[str, Any]]): 策略参数字典
        """
        self.name = name
        self.parameters = parameters or {}
        self.is_active = False
```

#### 3. 模块文档
```python
"""
FinRL Crypto 技术指标模块。

这个模块包含了常用的技术分析指标实现，包括趋势指标、动量指标、
波动率指标和成交量指标。所有指标都经过优化，支持向量化计算。

主要功能:
    - 趋势指标: SMA, EMA, MACD, Bollinger Bands
    - 动量指标: RSI, Stochastic, Williams %R
    - 波动率指标: ATR, Volatility
    - 成交量指标: OBV, VWAP

使用示例:
    >>> from finrl_crypto.indicators import calculate_rsi, calculate_sma
    >>> 
    >>> # 计算简单移动平均线
    >>> sma = calculate_sma(prices, period=20)
    >>> 
    >>> # 计算 RSI
    >>> rsi = calculate_rsi(prices, period=14)

注意事项:
    - 所有指标函数都要求输入数据为数值类型
    - 计算结果的长度可能小于输入数据长度
    - 建议在使用前检查数据质量和完整性

Author: FinRL Crypto Team
Version: 1.0.0
License: MIT
"""

import numpy as np
import pandas as pd
from typing import List, Union, Optional

# 模块内容...
```

### API 文档规范

#### 1. OpenAPI/Swagger 规范
```yaml
# api/openapi.yml
openapi: 3.0.3
info:
  title: FinRL Crypto API
  description: |
    FinRL Crypto 交易系统 API
    
    这个 API 提供了完整的加密货币交易功能，包括:
    - 市场数据获取
    - 交易策略管理
    - 订单执行
    - 账户管理
    - 风险控制
    
    ## 认证
    API 使用 JWT Token 进行认证。请在请求头中包含:
    ```
    Authorization: Bearer <your-token>
    ```
    
    ## 限流
    - 公共端点: 100 请求/分钟
    - 私有端点: 1000 请求/分钟
    - 交易端点: 10 请求/秒
    
    ## 错误处理
    API 使用标准 HTTP 状态码，错误响应格式:
    ```json
    {
      "error": {
        "code": "INVALID_PARAMETER",
        "message": "参数 'symbol' 是必需的",
        "details": {
          "field": "symbol",
          "value": null
        }
      }
    }
    ```
  version: 1.0.0
  contact:
    name: FinRL Crypto Team
    email: <EMAIL>
    url: https://github.com/finrl-crypto/finrl-crypto
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.finrl-crypto.com/v1
    description: 生产环境
  - url: https://staging-api.finrl-crypto.com/v1
    description: 测试环境
  - url: http://localhost:8000/v1
    description: 开发环境

paths:
  /market/symbols:
    get:
      summary: 获取交易对列表
      description: |
        获取所有可用的交易对信息，包括基础货币、计价货币、
        最小交易量、价格精度等详细信息。
      tags:
        - Market Data
      parameters:
        - name: base_currency
          in: query
          description: 基础货币过滤
          required: false
          schema:
            type: string
            example: BTC
        - name: quote_currency
          in: query
          description: 计价货币过滤
          required: false
          schema:
            type: string
            example: USDT
        - name: active
          in: query
          description: 是否只返回活跃交易对
          required: false
          schema:
            type: boolean
            default: true
      responses:
        '200':
          description: 成功返回交易对列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Symbol'
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
              example:
                data:
                  - symbol: "BTCUSDT"
                    base_currency: "BTC"
                    quote_currency: "USDT"
                    min_quantity: 0.00001
                    max_quantity: 1000
                    price_precision: 2
                    quantity_precision: 5
                    is_active: true
                meta:
                  total: 150
                  page: 1
                  per_page: 50
        '400':
          $ref: '#/components/responses/BadRequest'
        '429':
          $ref: '#/components/responses/RateLimitExceeded'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  schemas:
    Symbol:
      type: object
      description: 交易对信息
      required:
        - symbol
        - base_currency
        - quote_currency
        - is_active
      properties:
        symbol:
          type: string
          description: 交易对符号
          example: "BTCUSDT"
        base_currency:
          type: string
          description: 基础货币
          example: "BTC"
        quote_currency:
          type: string
          description: 计价货币
          example: "USDT"
        min_quantity:
          type: number
          format: float
          description: 最小交易量
          example: 0.00001
        max_quantity:
          type: number
          format: float
          description: 最大交易量
          example: 1000
        price_precision:
          type: integer
          description: 价格精度（小数位数）
          example: 2
        quantity_precision:
          type: integer
          description: 数量精度（小数位数）
          example: 5
        is_active:
          type: boolean
          description: 是否活跃
          example: true
```

#### 2. 端点文档模板
```markdown
## POST /api/v1/orders

创建新的交易订单。

### 描述

此端点用于创建新的买入或卖出订单。支持市价单、限价单和止损单等多种订单类型。

### 认证

需要有效的 JWT Token。

### 请求参数

#### Headers

| 名称 | 类型 | 必需 | 描述 |
|------|------|------|------|
| Authorization | string | 是 | Bearer Token |
| Content-Type | string | 是 | application/json |

#### Body Parameters

```json
{
  "symbol": "BTCUSDT",
  "side": "buy",
  "type": "limit",
  "quantity": 0.001,
  "price": 50000.00,
  "time_in_force": "GTC"
}
```

| 参数 | 类型 | 必需 | 描述 | 示例 |
|------|------|------|------|------|
| symbol | string | 是 | 交易对符号 | "BTCUSDT" |
| side | string | 是 | 交易方向: "buy" 或 "sell" | "buy" |
| type | string | 是 | 订单类型: "market", "limit", "stop" | "limit" |
| quantity | number | 是 | 交易数量 | 0.001 |
| price | number | 否* | 价格（限价单必需） | 50000.00 |
| stop_price | number | 否* | 止损价格（止损单必需） | 49000.00 |
| time_in_force | string | 否 | 有效期: "GTC", "IOC", "FOK" | "GTC" |

*注：根据订单类型，某些参数可能是必需的。

### 响应

#### 成功响应 (201 Created)

```json
{
  "data": {
    "order_id": "12345678-1234-1234-1234-123456789012",
    "symbol": "BTCUSDT",
    "side": "buy",
    "type": "limit",
    "quantity": 0.001,
    "price": 50000.00,
    "status": "pending",
    "created_at": "2023-12-01T10:30:00Z",
    "updated_at": "2023-12-01T10:30:00Z"
  }
}
```

#### 错误响应

**400 Bad Request**
```json
{
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "价格必须大于 0",
    "details": {
      "field": "price",
      "value": -100
    }
  }
}
```

**401 Unauthorized**
```json
{
  "error": {
    "code": "UNAUTHORIZED",
    "message": "无效的认证令牌"
  }
}
```

**429 Too Many Requests**
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "请求频率超过限制",
    "details": {
      "limit": 10,
      "window": "1 minute",
      "retry_after": 30
    }
  }
}
```

### 示例代码

#### Python
```python
import requests

url = "https://api.finrl-crypto.com/v1/orders"
headers = {
    "Authorization": "Bearer your-jwt-token",
    "Content-Type": "application/json"
}
data = {
    "symbol": "BTCUSDT",
    "side": "buy",
    "type": "limit",
    "quantity": 0.001,
    "price": 50000.00
}

response = requests.post(url, headers=headers, json=data)
if response.status_code == 201:
    order = response.json()["data"]
    print(f"订单创建成功: {order['order_id']}")
else:
    print(f"错误: {response.json()['error']['message']}")
```

#### JavaScript
```javascript
const response = await fetch('https://api.finrl-crypto.com/v1/orders', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    symbol: 'BTCUSDT',
    side: 'buy',
    type: 'limit',
    quantity: 0.001,
    price: 50000.00
  })
});

if (response.ok) {
  const result = await response.json();
  console.log('订单创建成功:', result.data.order_id);
} else {
  const error = await response.json();
  console.error('错误:', error.error.message);
}
```

#### cURL
```bash
curl -X POST "https://api.finrl-crypto.com/v1/orders" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "BTCUSDT",
    "side": "buy",
    "type": "limit",
    "quantity": 0.001,
    "price": 50000.00
  }'
```

### 注意事项

- 订单创建后状态为 "pending"，需要等待系统处理
- 限价单可能不会立即执行，取决于市场条件
- 请确保账户有足够的余额来执行订单
- 建议在生产环境使用前先在测试环境验证
```

## 🎨 视觉规范

### 图表和图像

#### 1. 架构图
```mermaid
graph TD
    A[用户界面] --> B[API 网关]
    B --> C[认证服务]
    B --> D[交易服务]
    B --> E[数据服务]
    D --> F[订单管理]
    D --> G[风险控制]
    E --> H[市场数据]
    E --> I[历史数据]
    F --> J[交易所 API]
    H --> J
```

#### 2. 流程图
```mermaid
flowchart LR
    A[开始] --> B{检查参数}
    B -->|有效| C[创建订单]
    B -->|无效| D[返回错误]
    C --> E{风险检查}
    E -->|通过| F[提交交易所]
    E -->|不通过| G[拒绝订单]
    F --> H[更新状态]
    G --> I[记录日志]
    H --> J[结束]
    I --> J
```

#### 3. 时序图
```mermaid
sequenceDiagram
    participant U as 用户
    participant A as API
    participant D as 数据库
    participant E as 交易所
    
    U->>A: 创建订单请求
    A->>A: 验证参数
    A->>D: 检查账户余额
    D-->>A: 余额信息
    A->>A: 风险检查
    A->>E: 提交订单
    E-->>A: 订单确认
    A->>D: 更新订单状态
    A-->>U: 返回订单信息
```

### 图像规范

#### 文件格式
- **矢量图**: 优先使用 SVG 格式
- **截图**: 使用 PNG 格式
- **照片**: 使用 JPEG 格式

#### 命名规范
```
# 架构图
architecture_overview.svg
architecture_detailed.svg

# 流程图
flow_order_creation.svg
flow_risk_management.svg

# 截图
screenshot_dashboard.png
screenshot_trading_interface.png

# 图标
icon_trading.svg
icon_analytics.svg
```

#### 尺寸规范
- **架构图**: 最大宽度 800px
- **流程图**: 最大宽度 600px
- **截图**: 最大宽度 1200px
- **图标**: 24x24px, 32x32px, 48x48px

## 🔄 文档维护

### 版本控制

#### 文档版本号
```
# 语义化版本控制
MAJOR.MINOR.PATCH

# 示例
1.0.0  # 初始版本
1.1.0  # 新增功能文档
1.1.1  # 修复文档错误
2.0.0  # 重大更新
```

#### 变更记录
```markdown
# 变更日志

## [1.2.0] - 2023-12-01

### 新增
- 添加了 WebSocket API 文档
- 新增风险管理策略说明
- 增加了性能优化指南

### 修改
- 更新了安装指南，支持 Python 3.11
- 改进了 API 认证流程说明
- 优化了代码示例格式

### 修复
- 修正了配置文件示例中的错误
- 更新了过时的链接地址
- 修复了表格格式问题

### 删除
- 移除了已废弃的 API 端点文档
```

### 审查流程

#### 文档审查清单
```markdown
## 文档审查清单

### 内容质量
- [ ] 信息准确性
- [ ] 内容完整性
- [ ] 逻辑清晰性
- [ ] 示例有效性

### 格式规范
- [ ] Markdown 语法正确
- [ ] 标题层级合理
- [ ] 代码块格式正确
- [ ] 链接有效性

### 用户体验
- [ ] 目标读者明确
- [ ] 语言简洁易懂
- [ ] 结构组织合理
- [ ] 导航便利性

### 技术准确性
- [ ] 代码示例可运行
- [ ] API 规范正确
- [ ] 配置参数有效
- [ ] 版本信息最新
```

### 自动化工具

#### 文档生成
```python
# scripts/generate_docs.py
import os
import subprocess
from pathlib import Path

def generate_api_docs():
    """生成 API 文档"""
    subprocess.run([
        "sphinx-apidoc",
        "-o", "docs/api",
        "finrl_crypto/",
        "--force",
        "--separate"
    ])

def generate_openapi_docs():
    """生成 OpenAPI 文档"""
    subprocess.run([
        "redoc-cli",
        "build",
        "api/openapi.yml",
        "--output", "docs/api/index.html"
    ])

def validate_links():
    """验证文档链接"""
    subprocess.run([
        "markdown-link-check",
        "docs/**/*.md"
    ])

if __name__ == "__main__":
    generate_api_docs()
    generate_openapi_docs()
    validate_links()
    print("文档生成完成！")
```

#### 文档检查
```yaml
# .github/workflows/docs.yml
name: Documentation

on:
  push:
    paths:
      - 'docs/**'
      - '*.md'
  pull_request:
    paths:
      - 'docs/**'
      - '*.md'

jobs:
  check-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Check Markdown
        uses: markdownlint/markdownlint-action@v1
        with:
          config: .markdownlint.yml
          
      - name: Check Links
        uses: gaurav-nelson/github-action-markdown-link-check@v1
        with:
          use-quiet-mode: 'yes'
          
      - name: Spell Check
        uses: streetsidesoftware/cspell-action@v2
        with:
          files: 'docs/**/*.md'
          
  build-docs:
    runs-on: ubuntu-latest
    needs: check-docs
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.8'
          
      - name: Install Dependencies
        run: |
          pip install sphinx sphinx-rtd-theme
          pip install -r requirements-docs.txt
          
      - name: Build Documentation
        run: |
          cd docs
          make html
          
      - name: Deploy to GitHub Pages
        if: github.ref == 'refs/heads/main'
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./docs/_build/html
```

## 📊 质量指标

### 文档指标

#### 覆盖率指标
```python
# scripts/doc_coverage.py
import ast
import os
from pathlib import Path

def calculate_docstring_coverage(source_dir: str) -> float:
    """计算文档字符串覆盖率"""
    total_functions = 0
    documented_functions = 0
    
    for py_file in Path(source_dir).rglob("*.py"):
        with open(py_file, 'r', encoding='utf-8') as f:
            try:
                tree = ast.parse(f.read())
                for node in ast.walk(tree):
                    if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                        total_functions += 1
                        if ast.get_docstring(node):
                            documented_functions += 1
            except SyntaxError:
                continue
    
    return (documented_functions / total_functions * 100) if total_functions > 0 else 0

def generate_coverage_report():
    """生成文档覆盖率报告"""
    coverage = calculate_docstring_coverage("finrl_crypto")
    
    report = f"""
    # 文档覆盖率报告
    
    ## 总体覆盖率: {coverage:.1f}%
    
    ### 目标
    - 函数文档覆盖率: >= 90%
    - 类文档覆盖率: >= 95%
    - 模块文档覆盖率: >= 100%
    
    ### 建议
    {'✅ 覆盖率良好' if coverage >= 90 else '❌ 需要改进文档覆盖率'}
    """
    
    with open("docs/reports/coverage.md", "w") as f:
        f.write(report)

if __name__ == "__main__":
    generate_coverage_report()
```

#### 质量检查
```bash
#!/bin/bash
# scripts/check_docs.sh

echo "🔍 检查文档质量..."

# 检查 Markdown 语法
echo "检查 Markdown 语法..."
markdownlint docs/**/*.md

# 检查拼写
echo "检查拼写..."
cspell "docs/**/*.md"

# 检查链接
echo "检查链接有效性..."
markdown-link-check docs/**/*.md

# 检查代码示例
echo "验证代码示例..."
python scripts/validate_code_examples.py

# 生成覆盖率报告
echo "生成文档覆盖率报告..."
python scripts/doc_coverage.py

echo "✅ 文档质量检查完成！"
```

## 🚀 最佳实践

### 1. 用户导向
- 从用户角度组织内容
- 提供清晰的导航路径
- 包含丰富的示例和用例

### 2. 保持更新
- 代码变更时同步更新文档
- 定期审查和更新过时内容
- 建立文档维护责任制

### 3. 协作友好
- 使用版本控制管理文档
- 建立清晰的贡献流程
- 鼓励社区参与文档改进

### 4. 多媒体支持
- 适当使用图表和图像
- 提供视频教程链接
- 支持交互式示例

### 5. 国际化支持
- 考虑多语言文档需求
- 使用清晰简洁的语言
- 避免文化特定的表达

## 📚 工具和资源

### 编写工具
- **编辑器**: VS Code, Typora, Mark Text
- **图表**: Mermaid, Draw.io, Lucidchart
- **截图**: Snagit, LightShot, Greenshot

### 生成工具
- **API 文档**: Swagger/OpenAPI, Redoc
- **代码文档**: Sphinx, MkDocs, GitBook
- **静态站点**: Jekyll, Hugo, VuePress

### 检查工具
- **语法检查**: markdownlint, textlint
- **拼写检查**: cspell, aspell
- **链接检查**: markdown-link-check

### 参考资源
- [Google 技术写作指南](https://developers.google.com/tech-writing)
- [Microsoft 写作风格指南](https://docs.microsoft.com/en-us/style-guide/)
- [GitLab 文档风格指南](https://docs.gitlab.com/ee/development/documentation/styleguide/)

---

通过遵循本指南，我们可以创建高质量、用户友好的文档，提升项目的可用性和开发者体验。 📖✨