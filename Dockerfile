# FinRL Crypto - Multi-stage Docker Build
# 基于Python 3.10的轻量级镜像

# ================================
# Stage 1: Builder
# ================================
FROM python:3.10-slim as builder

# 设置构建参数
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION

# 添加标签
LABEL maintainer="FinRL Team" \
      org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="finrl-crypto" \
      org.label-schema.description="FinRL Crypto Trading Environment" \
      org.label-schema.url="https://github.com/AI4Finance-Foundation/FinRL" \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.vcs-url="https://github.com/AI4Finance-Foundation/FinRL" \
      org.label-schema.vendor="AI4Finance Foundation" \
      org.label-schema.version=$VERSION \
      org.label-schema.schema-version="1.0"

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    g++ \
    gfortran \
    libopenblas-dev \
    liblapack-dev \
    libhdf5-dev \
    pkg-config \
    git \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 创建虚拟环境
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 升级pip和安装构建工具
RUN pip install --upgrade pip setuptools wheel

# 复制requirements文件
COPY requirements.txt /tmp/requirements.txt

# 安装Python依赖
RUN pip install --no-cache-dir -r /tmp/requirements.txt

# 安装额外的生产依赖
RUN pip install --no-cache-dir \
    gunicorn \
    uvicorn[standard] \
    prometheus-client \
    structlog \
    python-json-logger

# ================================
# Stage 2: Runtime
# ================================
FROM python:3.10-slim as runtime

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/opt/venv/bin:$PATH" \
    DEBIAN_FRONTEND=noninteractive \
    APP_HOME=/app \
    APP_USER=finrl \
    APP_GROUP=finrl \
    APP_UID=1000 \
    APP_GID=1000

# 安装运行时依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    libopenblas0 \
    liblapack3 \
    libhdf5-103 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户和组
RUN groupadd -g $APP_GID $APP_GROUP && \
    useradd -u $APP_UID -g $APP_GID -m -s /bin/bash $APP_USER

# 复制虚拟环境
COPY --from=builder /opt/venv /opt/venv

# 创建应用目录
RUN mkdir -p $APP_HOME && \
    chown -R $APP_USER:$APP_GROUP $APP_HOME

# 设置工作目录
WORKDIR $APP_HOME

# 复制应用代码
COPY --chown=$APP_USER:$APP_GROUP . .

# 创建必要的目录
RUN mkdir -p logs data models checkpoints results && \
    chown -R $APP_USER:$APP_GROUP logs data models checkpoints results

# 切换到应用用户
USER $APP_USER

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 暴露端口
EXPOSE 8000

# 设置入口点
COPY --chown=$APP_USER:$APP_GROUP docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]

# 默认命令
CMD ["python", "main.py"]

# ================================
# Stage 3: Development (可选)
# ================================
FROM runtime as development

# 切换回root用户安装开发工具
USER root

# 安装开发依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    vim \
    nano \
    htop \
    git \
    ssh \
    && rm -rf /var/lib/apt/lists/*

# 安装开发Python包
RUN pip install --no-cache-dir \
    pytest \
    pytest-cov \
    pytest-xdist \
    black \
    isort \
    flake8 \
    mypy \
    bandit \
    safety \
    pre-commit \
    jupyter \
    ipython

# 切换回应用用户
USER $APP_USER

# 开发环境默认命令
CMD ["bash"]

# ================================
# Stage 4: Testing (可选)
# ================================
FROM development as testing

# 复制测试文件
COPY --chown=$APP_USER:$APP_GROUP tests/ tests/
COPY --chown=$APP_USER:$APP_GROUP improvetest/ improvetest/
COPY --chown=$APP_USER:$APP_GROUP pytest.ini .
COPY --chown=$APP_USER:$APP_GROUP .coveragerc .

# 运行测试
RUN pytest --cov=. --cov-report=html --cov-report=term

# 测试环境默认命令
CMD ["pytest", "-v"]