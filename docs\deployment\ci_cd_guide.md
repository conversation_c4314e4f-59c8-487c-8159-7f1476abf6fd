# 持续集成/持续部署 (CI/CD) 指南

## 📋 概述

本指南详细介绍了 FinRL Crypto 项目的持续集成和持续部署流程，包括自动化测试、代码质量检查、构建、部署和监控。

## 🏗️ CI/CD 架构

```mermaid
graph TD
    A[开发者推送代码] --> B[GitHub Actions触发]
    B --> C[代码质量检查]
    B --> D[单元测试]
    B --> E[集成测试]
    C --> F[构建应用]
    D --> F
    E --> F
    F --> G[安全扫描]
    G --> H[部署到测试环境]
    H --> I[端到端测试]
    I --> J[部署到生产环境]
    J --> K[监控和告警]
```

## 🔄 工作流程

### 1. 代码提交触发

#### 触发条件
- **Push到主分支**: `main`, `develop`
- **Pull Request**: 针对主分支的PR
- **定时任务**: 每日自动构建
- **手动触发**: 通过GitHub界面手动触发

#### 分支策略
```yaml
# 分支保护规则
main:
  - 需要PR审查
  - 需要CI检查通过
  - 需要管理员批准
  - 禁止直接推送

develop:
  - 需要CI检查通过
  - 允许快进合并
  - 自动部署到测试环境

feature/*:
  - 自动运行CI检查
  - 不自动部署
```

### 2. 代码质量检查阶段

#### Pre-commit 钩子
```bash
# 安装pre-commit
pip install pre-commit
pre-commit install

# 运行所有检查
pre-commit run --all-files
```

#### 自动化检查项目
- **代码格式化**: Black, isort
- **代码质量**: flake8, pylint, mypy
- **安全检查**: bandit, safety
- **文档检查**: pydocstyle
- **复杂度检查**: xenon

### 3. 测试阶段

#### 测试矩阵
```yaml
strategy:
  matrix:
    os: [ubuntu-latest, windows-latest, macos-latest]
    python-version: ['3.8', '3.9', '3.10', '3.11']
    exclude:
      - os: macos-latest
        python-version: '3.8'
      - os: windows-latest
        python-version: '3.8'
```

#### 测试类型
1. **单元测试**
   ```bash
   pytest tests/unit/ \
     --cov=finrl_crypto \
     --cov-report=xml \
     --cov-report=html \
     --junitxml=reports/junit.xml
   ```

2. **集成测试**
   ```bash
   pytest tests/integration/ \
     --cov-append \
     --timeout=300
   ```

3. **端到端测试**
   ```bash
   pytest tests/e2e/ \
     --timeout=600 \
     --maxfail=1
   ```

4. **性能测试**
   ```bash
   pytest tests/ -m performance \
     --benchmark-only \
     --benchmark-json=reports/benchmark.json
   ```

### 4. 构建阶段

#### Docker 构建
```dockerfile
# 多阶段构建
FROM python:3.8-slim as base

# 依赖安装阶段
FROM base as dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 应用构建阶段
FROM dependencies as application
COPY . .
RUN python setup.py build

# 生产镜像
FROM base as production
COPY --from=application /app .
EXPOSE 8000
CMD ["uvicorn", "finrl_crypto.api:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 构建优化
- **多阶段构建**: 减少镜像大小
- **层缓存**: 优化构建速度
- **安全扫描**: Trivy, Snyk
- **镜像签名**: Cosign

### 5. 部署阶段

#### 环境配置
```yaml
# 环境定义
environments:
  development:
    url: https://dev.finrl-crypto.com
    auto_deploy: true
    protection_rules: []
    
  staging:
    url: https://staging.finrl-crypto.com
    auto_deploy: false
    protection_rules:
      - required_reviewers: 1
      
  production:
    url: https://finrl-crypto.com
    auto_deploy: false
    protection_rules:
      - required_reviewers: 2
      - wait_timer: 5
```

#### 部署策略

1. **蓝绿部署**
   ```yaml
   # 蓝绿部署配置
   deployment:
     strategy:
       type: BlueGreen
       blueGreen:
         autoPromotionEnabled: false
         scaleDownDelaySeconds: 30
         prePromotionAnalysis:
           templates:
           - templateName: success-rate
           args:
           - name: service-name
             value: finrl-crypto
   ```

2. **滚动更新**
   ```yaml
   # 滚动更新配置
   deployment:
     strategy:
       type: RollingUpdate
       rollingUpdate:
         maxUnavailable: 25%
         maxSurge: 25%
   ```

3. **金丝雀部署**
   ```yaml
   # 金丝雀部署配置
   deployment:
     strategy:
       type: Canary
       canary:
         steps:
         - setWeight: 10
         - pause: {duration: 1h}
         - setWeight: 50
         - pause: {duration: 30m}
         - setWeight: 100
   ```

## 🔧 配置管理

### 环境变量管理

#### GitHub Secrets
```yaml
# 敏感信息存储
secrets:
  DATABASE_URL: ${{ secrets.DATABASE_URL }}
  REDIS_URL: ${{ secrets.REDIS_URL }}
  API_KEY: ${{ secrets.API_KEY }}
  DOCKER_REGISTRY_TOKEN: ${{ secrets.DOCKER_REGISTRY_TOKEN }}
```

#### 配置文件模板
```yaml
# config/production.yml
database:
  url: ${DATABASE_URL}
  pool_size: 20
  max_overflow: 30

redis:
  url: ${REDIS_URL}
  max_connections: 100

api:
  host: 0.0.0.0
  port: 8000
  workers: 4
  
logging:
  level: INFO
  format: json
```

### 基础设施即代码 (IaC)

#### Terraform 配置
```hcl
# infrastructure/main.tf
resource "aws_ecs_cluster" "finrl_crypto" {
  name = "finrl-crypto-cluster"
  
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

resource "aws_ecs_service" "finrl_crypto_api" {
  name            = "finrl-crypto-api"
  cluster         = aws_ecs_cluster.finrl_crypto.id
  task_definition = aws_ecs_task_definition.finrl_crypto_api.arn
  desired_count   = 3
  
  deployment_configuration {
    maximum_percent         = 200
    minimum_healthy_percent = 100
  }
}
```

#### Kubernetes 配置
```yaml
# k8s/deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: finrl-crypto-api
  labels:
    app: finrl-crypto-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: finrl-crypto-api
  template:
    metadata:
      labels:
        app: finrl-crypto-api
    spec:
      containers:
      - name: api
        image: finrl-crypto:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: finrl-crypto-secrets
              key: database-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 📊 监控和告警

### 应用监控

#### Prometheus 指标
```python
# finrl_crypto/monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge

# 业务指标
trading_operations = Counter(
    'trading_operations_total',
    'Total number of trading operations',
    ['operation_type', 'symbol']
)

model_inference_duration = Histogram(
    'model_inference_duration_seconds',
    'Time spent on model inference',
    ['model_type']
)

active_strategies = Gauge(
    'active_strategies_count',
    'Number of active trading strategies'
)

# 系统指标
api_requests = Counter(
    'api_requests_total',
    'Total number of API requests',
    ['method', 'endpoint', 'status']
)

api_request_duration = Histogram(
    'api_request_duration_seconds',
    'API request duration',
    ['method', 'endpoint']
)
```

#### Grafana 仪表板
```json
{
  "dashboard": {
    "title": "FinRL Crypto Monitoring",
    "panels": [
      {
        "title": "API Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(api_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Trading Operations",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(trading_operations_total)",
            "legendFormat": "Total Operations"
          }
        ]
      },
      {
        "title": "Model Performance",
        "type": "heatmap",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, model_inference_duration_seconds_bucket)",
            "legendFormat": "95th percentile"
          }
        ]
      }
    ]
  }
}
```

### 日志管理

#### 结构化日志
```python
# finrl_crypto/logging/config.py
import structlog
from structlog.stdlib import LoggerFactory

structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()
```

#### ELK Stack 配置
```yaml
# docker-compose.logging.yml
version: '3.8'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      
  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline
    ports:
      - "5044:5044"
    depends_on:
      - elasticsearch
      
  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
```

### 告警配置

#### AlertManager 规则
```yaml
# alerts/rules.yml
groups:
- name: finrl_crypto_alerts
  rules:
  - alert: HighErrorRate
    expr: rate(api_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"
      
  - alert: ModelInferenceLatency
    expr: histogram_quantile(0.95, model_inference_duration_seconds_bucket) > 1.0
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "Model inference latency is high"
      description: "95th percentile latency is {{ $value }} seconds"
      
  - alert: TradingSystemDown
    expr: up{job="finrl-crypto-api"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Trading system is down"
      description: "The trading system has been down for more than 1 minute"
```

## 🔒 安全和合规

### 安全扫描

#### 容器安全
```yaml
# .github/workflows/security.yml
- name: Run Trivy vulnerability scanner
  uses: aquasecurity/trivy-action@master
  with:
    image-ref: 'finrl-crypto:latest'
    format: 'sarif'
    output: 'trivy-results.sarif'
    
- name: Upload Trivy scan results to GitHub Security tab
  uses: github/codeql-action/upload-sarif@v2
  with:
    sarif_file: 'trivy-results.sarif'
```

#### 代码安全
```yaml
- name: Run CodeQL Analysis
  uses: github/codeql-action/analyze@v2
  with:
    languages: python
    queries: security-and-quality
```

### 合规检查

#### SAST (静态应用安全测试)
```bash
# 使用 Bandit 进行安全检查
bandit -r finrl_crypto/ -f json -o reports/bandit-report.json

# 使用 Safety 检查依赖漏洞
safety check --json --output reports/safety-report.json

# 使用 Semgrep 进行代码分析
semgrep --config=auto finrl_crypto/
```

#### DAST (动态应用安全测试)
```yaml
# 使用 OWASP ZAP 进行动态扫描
- name: ZAP Scan
  uses: zaproxy/action-full-scan@v0.4.0
  with:
    target: 'https://staging.finrl-crypto.com'
    rules_file_name: '.zap/rules.tsv'
    cmd_options: '-a'
```

## 📈 性能优化

### 构建优化

#### 并行构建
```yaml
# 使用构建矩阵并行执行
strategy:
  matrix:
    include:
      - component: api
        dockerfile: Dockerfile.api
      - component: worker
        dockerfile: Dockerfile.worker
      - component: scheduler
        dockerfile: Dockerfile.scheduler
```

#### 缓存策略
```yaml
# Docker 层缓存
- name: Set up Docker Buildx
  uses: docker/setup-buildx-action@v2
  
- name: Build and push
  uses: docker/build-push-action@v3
  with:
    context: .
    push: true
    tags: finrl-crypto:latest
    cache-from: type=gha
    cache-to: type=gha,mode=max
```

### 部署优化

#### 健康检查
```python
# finrl_crypto/api/health.py
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from ..database import get_db
from ..services import redis_client

router = APIRouter()

@router.get("/health")
async def health_check():
    """基本健康检查"""
    return {"status": "healthy", "timestamp": datetime.utcnow()}

@router.get("/ready")
async def readiness_check(db: Session = Depends(get_db)):
    """就绪检查 - 检查依赖服务"""
    checks = {
        "database": False,
        "redis": False,
        "model": False
    }
    
    try:
        # 检查数据库连接
        db.execute("SELECT 1")
        checks["database"] = True
    except Exception:
        pass
    
    try:
        # 检查Redis连接
        redis_client.ping()
        checks["redis"] = True
    except Exception:
        pass
    
    try:
        # 检查模型加载状态
        from ..services.model_service import model_service
        checks["model"] = model_service.is_ready()
    except Exception:
        pass
    
    all_ready = all(checks.values())
    status_code = 200 if all_ready else 503
    
    return JSONResponse(
        status_code=status_code,
        content={
            "status": "ready" if all_ready else "not_ready",
            "checks": checks,
            "timestamp": datetime.utcnow().isoformat()
        }
    )
```

## 🚀 最佳实践

### 1. 版本管理
- 使用语义化版本控制 (SemVer)
- 自动生成变更日志
- 标签和发布管理

### 2. 分支策略
- Git Flow 或 GitHub Flow
- 保护主分支
- 自动合并策略

### 3. 测试策略
- 测试金字塔原则
- 并行测试执行
- 测试数据管理

### 4. 部署策略
- 零停机部署
- 回滚机制
- 环境一致性

### 5. 监控策略
- 全链路监控
- 业务指标监控
- 主动告警

## 🔧 故障排除

### 常见问题

#### 1. 构建失败
```bash
# 检查构建日志
gh run view --log

# 本地复现构建
docker build -t finrl-crypto:debug .

# 检查依赖冲突
pip-compile --verbose requirements.in
```

#### 2. 测试失败
```bash
# 运行特定测试
pytest tests/unit/test_specific.py -v

# 调试模式运行
pytest --pdb tests/unit/test_specific.py

# 检查测试覆盖率
pytest --cov=finrl_crypto --cov-report=html
```

#### 3. 部署问题
```bash
# 检查部署状态
kubectl get pods -l app=finrl-crypto-api

# 查看日志
kubectl logs -f deployment/finrl-crypto-api

# 检查配置
kubectl describe configmap finrl-crypto-config
```

### 调试工具
- **GitHub Actions**: 工作流调试
- **Docker**: 容器调试
- **Kubernetes**: 集群调试
- **监控工具**: 性能调试

## 📚 参考资源

### 文档
- [GitHub Actions 文档](https://docs.github.com/en/actions)
- [Docker 最佳实践](https://docs.docker.com/develop/dev-best-practices/)
- [Kubernetes 部署指南](https://kubernetes.io/docs/concepts/workloads/controllers/deployment/)
- [Prometheus 监控指南](https://prometheus.io/docs/guides/)

### 工具
- **CI/CD**: GitHub Actions, GitLab CI, Jenkins
- **容器化**: Docker, Podman
- **编排**: Kubernetes, Docker Swarm
- **监控**: Prometheus, Grafana, ELK Stack
- **安全**: Trivy, Snyk, OWASP ZAP

---

通过遵循本指南，您可以建立一个健壮、安全、高效的 CI/CD 流程，确保 FinRL Crypto 项目的高质量交付和稳定运行。 🚀