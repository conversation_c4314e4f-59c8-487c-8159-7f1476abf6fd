"""具体数据处理器实现模块

包含各种数据源的具体实现。
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Optional, Dict, Any
from binance.client import Client
import yfinance as yf

# 条件导入talib函数
try:
    from talib import RSI, MACD, CCI, DX, ROC, ULTOSC, WILLR, OBV, HT_DCPHASE
    TALIB_AVAILABLE = True
except ImportError:
    # 如果talib不可用，定义占位符函数
    RSI = MACD = CCI = DX = ROC = ULTOSC = WILLR = OBV = HT_DCPHASE = None
    TALIB_AVAILABLE = False
    import logging
    logging.warning("TA-Lib not available. Some technical indicators will use alternative implementations.")

from .base import DataProcessor


class BinanceProcessor(DataProcessor):
    """Binance数据处理器
    
    从Binance交易所获取加密货币数据。
    """
    
    def __init__(self, 
                 start_date: str,
                 end_date: str,
                 time_interval: str = '1d',
                 api_key: Optional[str] = None,
                 api_secret: Optional[str] = None,
                 **kwargs):
        """初始化Binance数据处理器
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            time_interval: 时间间隔
            api_key: Binance API密钥
            api_secret: Binance API密钥
            **kwargs: 其他参数
        """
        # 从kwargs中移除data_source以避免重复参数
        kwargs.pop('data_source', None)
        
        super().__init__(
            data_source='binance',
            start_date=start_date,
            end_date=end_date,
            time_interval=time_interval,
            **kwargs
        )
        
        self.api_key = api_key
        self.api_secret = api_secret
        self.correlation_threshold = kwargs.get('correlation_threshold', 0.9)
        
        # 初始化Binance客户端
        if api_key and api_secret:
            self.client = Client(api_key=api_key, api_secret=api_secret)
        else:
            # 使用公共API（有限制）
            self.client = Client()
            self.logger.warning("未提供API密钥，使用公共API（有速率限制）")
    
    def download_data(self, ticker_list: List[str]) -> pd.DataFrame:
        """从Binance下载数据
        
        Args:
            ticker_list: 交易对列表（如['BTCUSDT', 'ETHUSDT']）
            
        Returns:
            包含下载数据的DataFrame
        """
        self.logger.info(f"开始从Binance下载数据: {ticker_list}")
        
        final_df = pd.DataFrame()
        
        for ticker in ticker_list:
            try:
                self.logger.debug(f"下载{ticker}数据")
                ticker_data = self._get_binance_klines(
                    symbol=ticker,
                    start_date=self.start_date,
                    end_date=self.end_date,
                    interval=self._convert_time_interval()
                )
                
                if not ticker_data.empty:
                    # 移除最后一行（可能不完整）
                    ticker_data = ticker_data.iloc[:-1]
                    ticker_data = ticker_data.dropna()
                    ticker_data['tic'] = ticker
                    
                    final_df = pd.concat([final_df, ticker_data], ignore_index=True)
                    
            except Exception as e:
                self.logger.error(f"下载{ticker}数据失败: {e}")
                continue
        
        if final_df.empty:
            raise ValueError("未能下载任何数据")
        
        self.dataframe = final_df
        self.logger.info(f"数据下载完成，共{len(final_df)}行")
        return final_df
    
    def check_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检查数据质量
        
        Args:
            df: 要检查的DataFrame
            
        Returns:
            包含质量检查结果的字典
        """
        quality_report = {
            'total_rows': len(df),
            'missing_values': df.isnull().sum().to_dict(),
            'infinite_values': np.isinf(df.select_dtypes(include=[np.number])).sum().to_dict(),
            'zero_volume': (df['volume'] == 0).sum() if 'volume' in df.columns else 0,
            'data_types': df.dtypes.to_dict()
        }
        
        return quality_report
    
    def fetch_data(self, ticker_list: List[str] = None) -> pd.DataFrame:
        """获取数据，实现基类的抽象方法
        
        Args:
            ticker_list: 交易对列表，如果为None则使用默认列表
            
        Returns:
            获取的数据DataFrame
        """
        if ticker_list is None:
            ticker_list = ['BTCUSDT']  # 默认使用BTC/USDT
        return self.download_data(ticker_list)
    
    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """预处理数据
        
        Args:
            data: 原始数据DataFrame
            
        Returns:
            预处理后的DataFrame
        """
        processed_data = data.copy()
        
        # 确保时间列存在
        if 'timestamp' in processed_data.columns:
            processed_data['time'] = pd.to_datetime(processed_data['timestamp'])
        elif 'time' not in processed_data.columns:
            processed_data['time'] = pd.to_datetime(processed_data.index)
        
        # 数据类型转换
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in processed_data.columns:
                processed_data[col] = pd.to_numeric(processed_data[col], errors='coerce')
        
        # 排序
        if 'time' in processed_data.columns:
            processed_data = processed_data.sort_values('time')
        
        return processed_data
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证数据有效性
        
        Args:
            data: 要验证的DataFrame
            
        Returns:
            数据是否有效
        """
        required_columns = ['close']
        
        # 检查必要列是否存在
        for col in required_columns:
            if col not in data.columns:
                return False
        
        # 检查数据是否为空
        if data.empty:
            return False
        
        # 检查关键数据是否有效
        if data['close'].isna().all():
            return False
        
        return True
    
    def _convert_time_interval(self) -> str:
        """转换时间间隔格式为Binance API格式"""
        interval_mapping = {
            '1m': '1m', '3m': '3m', '5m': '5m', '15m': '15m', '30m': '30m',
            '1h': '1h', '2h': '2h', '4h': '4h', '6h': '6h', '8h': '8h', '12h': '12h',
            '1d': '1d', '3d': '3d', '1w': '1w', '1M': '1M'
        }
        
        if self.time_interval in interval_mapping:
            return interval_mapping[self.time_interval]
        else:
            self.logger.warning(f"不支持的时间间隔{self.time_interval}，使用默认1d")
            return '1d'
    
    def _get_binance_klines(self, 
                           symbol: str, 
                           start_date: str, 
                           end_date: str, 
                           interval: str) -> pd.DataFrame:
        """获取Binance K线数据"""
        try:
            klines = self.client.get_historical_klines(
                symbol=symbol,
                interval=interval,
                start_str=start_date,
                end_str=end_date
            )
            
            if not klines:
                return pd.DataFrame()
            
            # 创建DataFrame
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # 删除不需要的列
            df = df.drop(columns=[
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # 转换数据类型
            df = df.apply(pd.to_numeric, errors='coerce')
            
            # 转换时间戳并替换timestamp列
            df['time'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df.drop(columns=['timestamp'])  # 删除原始timestamp列避免重复
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取{symbol}的K线数据失败: {e}")
            return pd.DataFrame()
    
    def add_binance_technical_indicators(self, 
                                       tech_indicator_list: List[str]) -> pd.DataFrame:
        """添加Binance特定的技术指标
        
        Args:
            tech_indicator_list: 技术指标列表
            
        Returns:
            添加技术指标后的DataFrame
        """
        if self.dataframe.empty:
            self.logger.error("数据为空，无法计算技术指标")
            return self.dataframe
        
        final_df = pd.DataFrame()
        
        for ticker in self.dataframe.tic.unique():
            ticker_df = self.dataframe[self.dataframe.tic == ticker].copy()
            ticker_df = self._calculate_talib_indicators(ticker_df, tech_indicator_list)
            final_df = pd.concat([final_df, ticker_df], ignore_index=True)
        
        self.dataframe = final_df
        return final_df
    
    def _calculate_talib_indicators(self, 
                                  df: pd.DataFrame, 
                                  tech_indicator_list: List[str]) -> pd.DataFrame:
        """使用TA-Lib计算技术指标"""
        try:
            if 'rsi' in tech_indicator_list:
                df['rsi'] = RSI(df['close'], timeperiod=14)
            
            if 'macd' in tech_indicator_list:
                df['macd'], df['macd_signal'], df['macd_hist'] = MACD(
                    df['close'], fastperiod=12, slowperiod=26, signalperiod=9
                )
            
            if 'cci' in tech_indicator_list:
                df['cci'] = CCI(df['high'], df['low'], df['close'], timeperiod=14)
            
            if 'dx' in tech_indicator_list:
                df['dx'] = DX(df['high'], df['low'], df['close'], timeperiod=14)
            
            if 'roc' in tech_indicator_list:
                df['roc'] = ROC(df['close'], timeperiod=10)
            
            if 'ultosc' in tech_indicator_list:
                df['ultosc'] = ULTOSC(df['high'], df['low'], df['close'])
            
            if 'willr' in tech_indicator_list:
                df['willr'] = WILLR(df['high'], df['low'], df['close'])
            
            if 'obv' in tech_indicator_list:
                df['obv'] = OBV(df['close'], df['volume'])
            
            if 'ht_dcphase' in tech_indicator_list:
                df['ht_dcphase'] = HT_DCPHASE(df['close'])
                
        except Exception as e:
            self.logger.error(f"计算技术指标失败: {e}")
        
        return df
    
    def drop_correlated_features(self, threshold: Optional[float] = None) -> pd.DataFrame:
        """删除高度相关的特征
        
        Args:
            threshold: 相关性阈值
            
        Returns:
            删除相关特征后的DataFrame
        """
        if threshold is None:
            threshold = self.correlation_threshold
        
        if self.dataframe.empty:
            return self.dataframe
        
        # 计算相关性矩阵
        numeric_cols = self.dataframe.select_dtypes(include=[np.number]).columns
        corr_matrix = self.dataframe[numeric_cols].corr().abs()
        
        # 找到高度相关的特征
        upper_tri = corr_matrix.where(
            np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
        )
        
        to_drop = [
            column for column in upper_tri.columns 
            if any(upper_tri[column] > threshold)
        ]
        
        # 保护重要列
        protected_cols = ['close', 'tic', 'time']
        to_drop = [col for col in to_drop if col not in protected_cols]
        
        if to_drop:
            self.logger.info(f"删除高度相关的特征: {to_drop}")
            self.dataframe = self.dataframe.drop(columns=to_drop)
        
        return self.dataframe


class YahooProcessor(DataProcessor):
    """Yahoo Finance数据处理器
    
    从Yahoo Finance获取金融数据。
    """
    
    def __init__(self, 
                 start_date: str,
                 end_date: str,
                 time_interval: str = '1d',
                 **kwargs):
        """初始化Yahoo Finance数据处理器"""
        # 从kwargs中移除data_source以避免重复参数
        kwargs.pop('data_source', None)
        
        super().__init__(
            data_source='yahoofinance',
            start_date=start_date,
            end_date=end_date,
            time_interval=time_interval,
            **kwargs
        )
    
    def download_data(self, ticker_list: List[str]) -> pd.DataFrame:
        """从Yahoo Finance下载数据
        
        Args:
            ticker_list: 股票代码列表
            
        Returns:
            包含下载数据的DataFrame
        """
        self.logger.info(f"开始从Yahoo Finance下载数据: {ticker_list}")
        
        final_df = pd.DataFrame()
        
        for ticker in ticker_list:
            try:
                self.logger.debug(f"下载{ticker}数据")
                
                # 使用yfinance下载数据
                ticker_obj = yf.Ticker(ticker)
                ticker_data = ticker_obj.history(
                    start=self.start_date,
                    end=self.end_date,
                    interval=self._convert_yahoo_interval()
                )
                
                if not ticker_data.empty:
                    # 重置索引并重命名列
                    ticker_data = ticker_data.reset_index()
                    ticker_data.columns = ticker_data.columns.str.lower()
                    
                    # 重命名列以匹配标准格式
                    column_mapping = {
                        'date': 'time',
                        'datetime': 'time',
                        'adj close': 'adjusted_close'
                    }
                    ticker_data.rename(columns=column_mapping, inplace=True)
                    
                    ticker_data['tic'] = ticker
                    final_df = pd.concat([final_df, ticker_data], ignore_index=True)
                    
            except Exception as e:
                self.logger.error(f"下载{ticker}数据失败: {e}")
                continue
        
        if final_df.empty:
            raise ValueError("未能下载任何数据")
        
        self.dataframe = final_df
        self.logger.info(f"数据下载完成，共{len(final_df)}行")
        return final_df
    
    def _convert_yahoo_interval(self) -> str:
        """转换时间间隔格式为Yahoo Finance API格式"""
        interval_mapping = {
            '1m': '1m', '2m': '2m', '5m': '5m', '15m': '15m', '30m': '30m',
            '60m': '60m', '90m': '90m', '1h': '1h',
            '1d': '1d', '5d': '5d', '1w': '1wk', '1M': '1mo', '3M': '3mo'
        }
        
        if self.time_interval in interval_mapping:
            return interval_mapping[self.time_interval]
        else:
            self.logger.warning(f"不支持的时间间隔{self.time_interval}，使用默认1d")
            return '1d'


class DataProcessorFactory:
    """数据处理器工厂类
    
    用于创建不同类型的数据处理器。
    """
    
    _processors = {
        'binance': BinanceProcessor,
        'yahoo': YahooProcessor,
        'yahoofinance': YahooProcessor
    }
    
    @classmethod
    def create_processor(cls, 
                        data_source: str, 
                        start_date: str,
                        end_date: str,
                        time_interval: str = '1d',
                        **kwargs) -> DataProcessor:
        """创建数据处理器
        
        Args:
            data_source: 数据源名称
            start_date: 开始日期
            end_date: 结束日期
            time_interval: 时间间隔
            **kwargs: 其他参数
            
        Returns:
            数据处理器实例
        """
        if data_source not in cls._processors:
            raise ValueError(f"不支持的数据源: {data_source}")
        
        processor_class = cls._processors[data_source]
        return processor_class(
            start_date=start_date,
            end_date=end_date,
            time_interval=time_interval,
            **kwargs
        )
    
    @classmethod
    def get_supported_sources(cls) -> List[str]:
        """获取支持的数据源列表"""
        return list(cls._processors.keys())