"""性能监控模块

监控系统和算法的性能指标。
"""

import logging
import time
import threading
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from datetime import datetime
from contextlib import contextmanager
from functools import wraps


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: datetime
    operation_name: str
    duration_ms: float
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class PerformanceMonitor:
    """性能监控器
    
    监控函数和操作的执行性能。
    """
    
    def __init__(self):
        self._metrics_history: List[PerformanceMetrics] = []
        self._operation_stats: Dict[str, Dict[str, Any]] = {}
        self._callbacks: List[Callable[[PerformanceMetrics], None]] = []
        self._lock = threading.RLock()
        self._logger = logging.getLogger(__name__)
        self._max_history_size = 10000
        
        # 性能阈值
        self._performance_thresholds = {
            'slow_operation_ms': 1000,  # 超过1秒视为慢操作
            'memory_warning_mb': 1000,  # 超过1GB内存使用发出警告
        }
    
    def add_callback(self, callback: Callable[[PerformanceMetrics], None]) -> None:
        """添加性能监控回调函数
        
        Args:
            callback: 回调函数，接收PerformanceMetrics参数
        """
        with self._lock:
            self._callbacks.append(callback)
    
    def remove_callback(self, callback: Callable[[PerformanceMetrics], None]) -> None:
        """移除性能监控回调函数
        
        Args:
            callback: 要移除的回调函数
        """
        with self._lock:
            if callback in self._callbacks:
                self._callbacks.remove(callback)
    
    @contextmanager
    def measure_operation(self, operation_name: str, metadata: Optional[Dict[str, Any]] = None):
        """上下文管理器，用于测量操作性能
        
        Args:
            operation_name: 操作名称
            metadata: 额外的元数据
            
        Yields:
            Dict[str, Any]: 可以在操作中更新的元数据字典
        """
        start_time = time.perf_counter()
        start_memory = self._get_memory_usage()
        operation_metadata = metadata.copy() if metadata else {}
        
        success = True
        error_message = None
        
        try:
            yield operation_metadata
        except Exception as e:
            success = False
            error_message = str(e)
            raise
        finally:
            end_time = time.perf_counter()
            end_memory = self._get_memory_usage()
            
            duration_ms = (end_time - start_time) * 1000
            memory_usage_mb = end_memory - start_memory if end_memory and start_memory else None
            
            metrics = PerformanceMetrics(
                timestamp=datetime.now(),
                operation_name=operation_name,
                duration_ms=duration_ms,
                memory_usage_mb=memory_usage_mb,
                success=success,
                error_message=error_message,
                metadata=operation_metadata
            )
            
            self._record_metrics(metrics)
    
    def measure_function(self, operation_name: Optional[str] = None, include_args: bool = False):
        """装饰器，用于测量函数性能
        
        Args:
            operation_name: 操作名称，如果为None则使用函数名
            include_args: 是否在元数据中包含函数参数
            
        Returns:
            装饰器函数
        """
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                op_name = operation_name or f"{func.__module__}.{func.__name__}"
                metadata = {}
                
                if include_args:
                    metadata['args_count'] = len(args)
                    metadata['kwargs_keys'] = list(kwargs.keys())
                
                with self.measure_operation(op_name, metadata) as op_metadata:
                    result = func(*args, **kwargs)
                    op_metadata['result_type'] = type(result).__name__
                    return result
            
            return wrapper
        return decorator
    
    def record_custom_metrics(self, 
                            operation_name: str,
                            duration_ms: float,
                            success: bool = True,
                            **kwargs) -> None:
        """记录自定义性能指标
        
        Args:
            operation_name: 操作名称
            duration_ms: 持续时间（毫秒）
            success: 是否成功
            **kwargs: 其他指标数据
        """
        metrics = PerformanceMetrics(
            timestamp=datetime.now(),
            operation_name=operation_name,
            duration_ms=duration_ms,
            success=success,
            metadata=kwargs
        )
        
        self._record_metrics(metrics)
    
    def _record_metrics(self, metrics: PerformanceMetrics) -> None:
        """记录性能指标
        
        Args:
            metrics: 性能指标
        """
        with self._lock:
            # 存储指标
            self._metrics_history.append(metrics)
            
            # 限制历史数据大小
            if len(self._metrics_history) > self._max_history_size:
                self._metrics_history = self._metrics_history[-self._max_history_size:]
            
            # 更新操作统计
            self._update_operation_stats(metrics)
            
            # 检查性能警告
            self._check_performance_warnings(metrics)
            
            # 调用回调函数
            for callback in self._callbacks:
                try:
                    callback(metrics)
                except Exception as e:
                    self._logger.error(f"性能监控回调函数执行失败: {e}")
    
    def _update_operation_stats(self, metrics: PerformanceMetrics) -> None:
        """更新操作统计信息
        
        Args:
            metrics: 性能指标
        """
        op_name = metrics.operation_name
        
        if op_name not in self._operation_stats:
            self._operation_stats[op_name] = {
                'total_calls': 0,
                'successful_calls': 0,
                'failed_calls': 0,
                'total_duration_ms': 0.0,
                'min_duration_ms': float('inf'),
                'max_duration_ms': 0.0,
                'avg_duration_ms': 0.0,
                'last_call_time': None
            }
        
        stats = self._operation_stats[op_name]
        stats['total_calls'] += 1
        stats['last_call_time'] = metrics.timestamp
        
        if metrics.success:
            stats['successful_calls'] += 1
        else:
            stats['failed_calls'] += 1
        
        # 更新持续时间统计
        stats['total_duration_ms'] += metrics.duration_ms
        stats['min_duration_ms'] = min(stats['min_duration_ms'], metrics.duration_ms)
        stats['max_duration_ms'] = max(stats['max_duration_ms'], metrics.duration_ms)
        stats['avg_duration_ms'] = stats['total_duration_ms'] / stats['total_calls']
    
    def _check_performance_warnings(self, metrics: PerformanceMetrics) -> None:
        """检查性能警告
        
        Args:
            metrics: 性能指标
        """
        # 检查慢操作
        if metrics.duration_ms > self._performance_thresholds['slow_operation_ms']:
            self._logger.warning(
                f"检测到慢操作: {metrics.operation_name} 耗时 {metrics.duration_ms:.2f}ms"
            )
        
        # 检查内存使用
        if (metrics.memory_usage_mb and 
            metrics.memory_usage_mb > self._performance_thresholds['memory_warning_mb']):
            self._logger.warning(
                f"检测到高内存使用: {metrics.operation_name} 使用 {metrics.memory_usage_mb:.2f}MB"
            )
    
    def get_operation_stats(self, operation_name: Optional[str] = None) -> Dict[str, Any]:
        """获取操作统计信息
        
        Args:
            operation_name: 操作名称，如果为None则返回所有操作的统计
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            if operation_name:
                return self._operation_stats.get(operation_name, {}).copy()
            else:
                return {name: stats.copy() for name, stats in self._operation_stats.items()}
    
    def get_metrics_history(self, 
                          operation_name: Optional[str] = None,
                          limit: Optional[int] = None) -> List[PerformanceMetrics]:
        """获取性能指标历史
        
        Args:
            operation_name: 操作名称，如果为None则返回所有操作的指标
            limit: 返回的最大记录数
            
        Returns:
            List[PerformanceMetrics]: 指标历史
        """
        with self._lock:
            metrics = self._metrics_history
            
            if operation_name:
                metrics = [m for m in metrics if m.operation_name == operation_name]
            
            if limit:
                metrics = metrics[-limit:]
            
            return [m for m in metrics]  # 返回副本
    
    def get_performance_summary(self, time_window_minutes: Optional[int] = None) -> Dict[str, Any]:
        """获取性能摘要
        
        Args:
            time_window_minutes: 时间窗口（分钟），如果为None则分析所有数据
            
        Returns:
            Dict[str, Any]: 性能摘要
        """
        with self._lock:
            metrics = self._metrics_history
            
            # 应用时间窗口过滤
            if time_window_minutes:
                cutoff_time = datetime.now().timestamp() - (time_window_minutes * 60)
                metrics = [
                    m for m in metrics 
                    if m.timestamp.timestamp() > cutoff_time
                ]
            
            if not metrics:
                return {}
            
            # 计算总体统计
            total_operations = len(metrics)
            successful_operations = sum(1 for m in metrics if m.success)
            failed_operations = total_operations - successful_operations
            
            durations = [m.duration_ms for m in metrics]
            avg_duration = sum(durations) / len(durations)
            min_duration = min(durations)
            max_duration = max(durations)
            
            # 按操作分组统计
            operation_counts = {}
            for m in metrics:
                operation_counts[m.operation_name] = operation_counts.get(m.operation_name, 0) + 1
            
            # 找出最慢的操作
            slow_operations = [
                m for m in metrics 
                if m.duration_ms > self._performance_thresholds['slow_operation_ms']
            ]
            
            return {
                'time_window_minutes': time_window_minutes,
                'total_operations': total_operations,
                'successful_operations': successful_operations,
                'failed_operations': failed_operations,
                'success_rate': successful_operations / total_operations if total_operations > 0 else 0,
                'duration_stats': {
                    'avg_ms': avg_duration,
                    'min_ms': min_duration,
                    'max_ms': max_duration
                },
                'operation_counts': operation_counts,
                'slow_operations_count': len(slow_operations),
                'most_frequent_operation': max(operation_counts.items(), key=lambda x: x[1])[0] if operation_counts else None
            }
    
    def get_slowest_operations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最慢的操作
        
        Args:
            limit: 返回的最大记录数
            
        Returns:
            List[Dict[str, Any]]: 最慢操作列表
        """
        with self._lock:
            sorted_metrics = sorted(
                self._metrics_history,
                key=lambda m: m.duration_ms,
                reverse=True
            )
            
            return [
                {
                    'operation_name': m.operation_name,
                    'duration_ms': m.duration_ms,
                    'timestamp': m.timestamp.isoformat(),
                    'success': m.success,
                    'metadata': m.metadata
                }
                for m in sorted_metrics[:limit]
            ]
    
    def clear_history(self) -> None:
        """清空历史数据"""
        with self._lock:
            self._metrics_history.clear()
            self._operation_stats.clear()
            self._logger.info("性能监控历史数据已清空")
    
    def set_performance_threshold(self, threshold_name: str, value: float) -> None:
        """设置性能阈值
        
        Args:
            threshold_name: 阈值名称
            value: 阈值
        """
        if threshold_name in self._performance_thresholds:
            self._performance_thresholds[threshold_name] = value
            self._logger.info(f"性能阈值 {threshold_name} 已设置为 {value}")
        else:
            self._logger.warning(f"未知的性能阈值: {threshold_name}")
    
    def _get_memory_usage(self) -> Optional[float]:
        """获取当前内存使用量（MB）
        
        Returns:
            Optional[float]: 内存使用量，如果获取失败则返回None
        """
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / (1024 * 1024)  # 转换为MB
        except Exception:
            return None
    
    def export_metrics(self, format_type: str = 'json') -> str:
        """导出性能指标
        
        Args:
            format_type: 导出格式 ('json', 'csv')
            
        Returns:
            str: 导出的数据
        """
        with self._lock:
            if format_type == 'json':
                import json
                data = {
                    'metrics': [
                        {
                            'timestamp': m.timestamp.isoformat(),
                            'operation_name': m.operation_name,
                            'duration_ms': m.duration_ms,
                            'memory_usage_mb': m.memory_usage_mb,
                            'success': m.success,
                            'error_message': m.error_message,
                            'metadata': m.metadata
                        }
                        for m in self._metrics_history
                    ],
                    'operation_stats': self._operation_stats
                }
                return json.dumps(data, indent=2)
            
            elif format_type == 'csv':
                import csv
                import io
                
                output = io.StringIO()
                writer = csv.writer(output)
                
                # 写入标题行
                writer.writerow([
                    'timestamp', 'operation_name', 'duration_ms', 'memory_usage_mb',
                    'success', 'error_message', 'metadata'
                ])
                
                # 写入数据行
                for m in self._metrics_history:
                    writer.writerow([
                        m.timestamp.isoformat(),
                        m.operation_name,
                        m.duration_ms,
                        m.memory_usage_mb,
                        m.success,
                        m.error_message,
                        str(m.metadata)
                    ])
                
                return output.getvalue()
            
            else:
                raise ValueError(f"不支持的导出格式: {format_type}")