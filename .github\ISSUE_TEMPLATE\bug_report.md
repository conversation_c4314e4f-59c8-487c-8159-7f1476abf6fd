---
name: Bug Report
about: 报告一个bug来帮助我们改进
title: '[BUG] '
labels: 'bug'
assignees: ''
---

# 🐛 Bug报告

## 📋 Bug描述

简洁清晰地描述这个bug是什么。

## 🔄 重现步骤

重现此行为的步骤：

1. 进入 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

## ✅ 期望行为

简洁清晰地描述你期望发生什么。

## 📸 截图

如果适用，添加截图来帮助解释你的问题。

## 🖥️ 环境信息

**桌面环境（请完成以下信息）：**
- 操作系统: [例如 Windows 11, macOS 13, Ubuntu 22.04]
- Python版本: [例如 3.9.7]
- 项目版本: [例如 1.0.0]
- 浏览器: [例如 Chrome 115, Safari 16]

**移动设备（请完成以下信息）：**
- 设备: [例如 iPhone 14, Samsung Galaxy S23]
- 操作系统: [例如 iOS 16.5, Android 13]
- 浏览器: [例如 Safari, Chrome]
- 版本: [例如 22]

**依赖版本：**
```
请粘贴 pip freeze 或 requirements.txt 的相关部分
```

## 📝 额外上下文

在这里添加关于问题的任何其他上下文。

## 🔍 错误日志

```
请粘贴相关的错误日志或堆栈跟踪
```

## 🧪 测试用例

如果可能，请提供一个最小的测试用例来重现问题：

```python
# 你的测试代码
```

## 📊 影响评估

- [ ] 阻塞性问题（无法继续工作）
- [ ] 高优先级（严重影响功能）
- [ ] 中优先级（部分功能受影响）
- [ ] 低优先级（轻微影响或有变通方法）

## 🔧 可能的解决方案

如果你有任何关于如何修复此问题的想法，请在此描述。

## ✅ 检查清单

- [ ] 我已经搜索了现有的issues，确认这不是重复问题
- [ ] 我已经检查了最新版本，问题仍然存在
- [ ] 我已经提供了足够的信息来重现问题
- [ ] 我已经包含了相关的错误日志
- [ ] 我已经尝试了基本的故障排除步骤

## 🏷️ 相关标签

请为此issue添加适当的标签：

- [ ] frontend
- [ ] backend
- [ ] api
- [ ] database
- [ ] performance
- [ ] security
- [ ] documentation
- [ ] testing
- [ ] deployment
- [ ] configuration