# 快速教程

本教程将引导您在 15 分钟内完成第一个 FinRL Crypto 项目，从数据准备到策略部署。

## 🎯 教程目标

通过本教程，您将学会：
- 配置 FinRL Crypto 环境
- 下载和处理加密货币数据
- 训练强化学习交易模型
- 进行策略回测和性能评估
- 部署实时交易策略

## 📋 前置条件

确保您已经：
- [x] 安装了 FinRL Crypto（参见 [安装指南](installation.md)）
- [x] 准备了 Python 3.8+ 环境
- [x] 有稳定的网络连接

## 🚀 第一步：环境准备

### 1.1 导入必要的库

```python
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# FinRL Crypto 核心模块
from finrl_crypto import FinRLCrypto
from finrl_crypto.config import Config
from finrl_crypto.data import DataProcessor
from finrl_crypto.models import PPOAgent
from finrl_crypto.env import TradingEnv
from finrl_crypto.utils import (
    setup_logger,
    calculate_metrics,
    plot_results
)

# 设置日志
logger = setup_logger('quickstart', level='INFO')
logger.info("开始 FinRL Crypto 快速教程")
```

### 1.2 创建项目目录

```python
# 创建项目目录结构
project_dirs = [
    'data/raw',
    'data/processed', 
    'models/trained',
    'results/backtest',
    'logs',
    'checkpoints'
]

for dir_path in project_dirs:
    os.makedirs(dir_path, exist_ok=True)
    
logger.info("项目目录创建完成")
```

## 📊 第二步：数据准备

### 2.1 配置数据参数

```python
# 数据配置
DATA_CONFIG = {
    'symbols': ['BTC-USD', 'ETH-USD', 'ADA-USD'],  # 交易对
    'start_date': '2022-01-01',  # 开始日期
    'end_date': '2023-12-31',    # 结束日期
    'timeframe': '1h',           # 时间周期
    'source': 'yfinance'         # 数据源
}

# 技术指标配置
INDICATORS_CONFIG = {
    'sma': [5, 10, 20],          # 简单移动平均
    'ema': [12, 26],             # 指数移动平均
    'rsi': [14],                 # 相对强弱指数
    'macd': [(12, 26, 9)],       # MACD
    'bb': [(20, 2)],             # 布林带
    'atr': [14]                  # 平均真实波幅
}

logger.info(f"配置交易对: {DATA_CONFIG['symbols']}")
```

### 2.2 下载和处理数据

```python
# 初始化数据处理器
data_processor = DataProcessor(
    symbols=DATA_CONFIG['symbols'],
    source=DATA_CONFIG['source']
)

# 下载原始数据
logger.info("开始下载数据...")
raw_data = data_processor.download_data(
    start_date=DATA_CONFIG['start_date'],
    end_date=DATA_CONFIG['end_date'],
    timeframe=DATA_CONFIG['timeframe']
)

logger.info(f"下载完成，数据形状: {raw_data.shape}")

# 数据预处理
logger.info("开始数据预处理...")
processed_data = data_processor.preprocess(
    data=raw_data,
    indicators=INDICATORS_CONFIG,
    normalize=True,
    handle_missing='forward_fill'
)

# 保存处理后的数据
processed_data.to_csv('data/processed/crypto_data.csv', index=False)
logger.info("数据预处理完成并保存")

# 数据概览
print("\n=== 数据概览 ===")
print(f"数据期间: {processed_data['date'].min()} 到 {processed_data['date'].max()}")
print(f"总记录数: {len(processed_data):,}")
print(f"特征数量: {processed_data.shape[1]}")
print(f"\n前5行数据:")
print(processed_data.head())
```

### 2.3 数据质量检查

```python
# 检查数据质量
logger.info("进行数据质量检查...")

# 缺失值检查
missing_data = processed_data.isnull().sum()
if missing_data.sum() > 0:
    logger.warning(f"发现缺失值: {missing_data[missing_data > 0]}")
else:
    logger.info("✓ 无缺失值")

# 异常值检查
for symbol in DATA_CONFIG['symbols']:
    price_col = f"{symbol.replace('-', '_')}_close"
    if price_col in processed_data.columns:
        q1 = processed_data[price_col].quantile(0.25)
        q3 = processed_data[price_col].quantile(0.75)
        iqr = q3 - q1
        outliers = processed_data[
            (processed_data[price_col] < q1 - 1.5 * iqr) |
            (processed_data[price_col] > q3 + 1.5 * iqr)
        ]
        logger.info(f"{symbol} 异常值数量: {len(outliers)}")

logger.info("数据质量检查完成")
```

## 🧠 第三步：模型训练

### 3.1 配置训练环境

```python
# 训练配置
TRAINING_CONFIG = {
    'algorithm': 'PPO',           # 算法类型
    'total_timesteps': 50000,     # 训练步数
    'learning_rate': 3e-4,        # 学习率
    'batch_size': 64,             # 批次大小
    'n_epochs': 10,               # 训练轮数
    'gamma': 0.99,                # 折扣因子
    'gae_lambda': 0.95,           # GAE参数
    'clip_range': 0.2,            # PPO裁剪范围
    'ent_coef': 0.01,             # 熵系数
    'vf_coef': 0.5,               # 价值函数系数
    'max_grad_norm': 0.5          # 梯度裁剪
}

# 环境配置
ENV_CONFIG = {
    'initial_amount': 100000,     # 初始资金
    'transaction_cost_pct': 0.001, # 交易费用
    'reward_scaling': 1e-4,       # 奖励缩放
    'lookback_window': 30,        # 回看窗口
    'max_position': 0.95,         # 最大仓位
    'min_position': 0.05          # 最小仓位
}

logger.info("训练配置完成")
```

### 3.2 创建交易环境

```python
# 分割训练和测试数据
train_end_date = '2023-06-30'
train_data = processed_data[
    processed_data['date'] <= train_end_date
].reset_index(drop=True)

test_data = processed_data[
    processed_data['date'] > train_end_date
].reset_index(drop=True)

logger.info(f"训练数据: {len(train_data)} 条")
logger.info(f"测试数据: {len(test_data)} 条")

# 创建训练环境
train_env = TradingEnv(
    data=train_data,
    symbols=DATA_CONFIG['symbols'],
    **ENV_CONFIG
)

logger.info("交易环境创建完成")
print(f"\n=== 环境信息 ===")
print(f"观察空间维度: {train_env.observation_space.shape}")
print(f"动作空间维度: {train_env.action_space.shape}")
print(f"奖励范围: {train_env.reward_range}")
```

### 3.3 训练模型

```python
# 创建PPO智能体
logger.info("开始创建和训练模型...")

agent = PPOAgent(
    env=train_env,
    **TRAINING_CONFIG
)

# 设置回调函数
from finrl_crypto.callbacks import (
    TrainingCallback,
    CheckpointCallback,
    EarlyStoppingCallback
)

callbacks = [
    TrainingCallback(log_interval=1000),
    CheckpointCallback(
        save_path='checkpoints/',
        save_freq=10000
    ),
    EarlyStoppingCallback(
        patience=5,
        min_delta=0.01
    )
]

# 开始训练
start_time = datetime.now()
logger.info(f"开始训练，时间: {start_time}")

agent.learn(
    total_timesteps=TRAINING_CONFIG['total_timesteps'],
    callbacks=callbacks,
    progress_bar=True
)

end_time = datetime.now()
training_duration = end_time - start_time
logger.info(f"训练完成，耗时: {training_duration}")

# 保存模型
model_path = 'models/trained/ppo_crypto_model'
agent.save(model_path)
logger.info(f"模型已保存到: {model_path}")
```

## 📈 第四步：策略回测

### 4.1 加载模型并创建测试环境

```python
# 创建测试环境
test_env = TradingEnv(
    data=test_data,
    symbols=DATA_CONFIG['symbols'],
    **ENV_CONFIG
)

# 加载训练好的模型
from finrl_crypto.models import load_model
trained_agent = load_model(model_path, env=test_env)

logger.info("模型加载完成，开始回测")
```

### 4.2 执行回测

```python
# 回测配置
BACKTEST_CONFIG = {
    'start_date': test_data['date'].min(),
    'end_date': test_data['date'].max(),
    'initial_amount': ENV_CONFIG['initial_amount'],
    'save_trades': True,
    'save_portfolio': True
}

# 执行回测
from finrl_crypto.backtest import Backtester

backtester = Backtester(
    agent=trained_agent,
    env=test_env,
    **BACKTEST_CONFIG
)

logger.info("开始执行回测...")
backtest_results = backtester.run()

# 保存回测结果
backtest_results['trades'].to_csv('results/backtest/trades.csv', index=False)
backtest_results['portfolio'].to_csv('results/backtest/portfolio.csv', index=False)

logger.info("回测完成，结果已保存")
```

### 4.3 性能分析

```python
# 计算性能指标
metrics = calculate_metrics(
    portfolio_value=backtest_results['portfolio']['total_value'],
    benchmark_returns=backtest_results['benchmark_returns'],
    risk_free_rate=0.02  # 2% 无风险利率
)

# 显示结果
print("\n" + "="*50)
print("           回测结果汇总")
print("="*50)
print(f"回测期间: {BACKTEST_CONFIG['start_date']} 到 {BACKTEST_CONFIG['end_date']}")
print(f"初始资金: ${ENV_CONFIG['initial_amount']:,.2f}")
print(f"最终资金: ${backtest_results['portfolio']['total_value'].iloc[-1]:,.2f}")
print(f"\n=== 收益指标 ===")
print(f"总收益率: {metrics['total_return']:.2%}")
print(f"年化收益率: {metrics['annual_return']:.2%}")
print(f"基准收益率: {metrics['benchmark_return']:.2%}")
print(f"超额收益: {metrics['excess_return']:.2%}")
print(f"\n=== 风险指标 ===")
print(f"年化波动率: {metrics['volatility']:.2%}")
print(f"最大回撤: {metrics['max_drawdown']:.2%}")
print(f"夏普比率: {metrics['sharpe_ratio']:.3f}")
print(f"索提诺比率: {metrics['sortino_ratio']:.3f}")
print(f"信息比率: {metrics['information_ratio']:.3f}")
print(f"\n=== 交易指标 ===")
print(f"总交易次数: {len(backtest_results['trades'])}")
print(f"盈利交易: {len(backtest_results['trades'][backtest_results['trades']['pnl'] > 0])}")
print(f"亏损交易: {len(backtest_results['trades'][backtest_results['trades']['pnl'] < 0])}")
print(f"胜率: {metrics['win_rate']:.2%}")
print(f"平均盈利: ${metrics['avg_win']:.2f}")
print(f"平均亏损: ${metrics['avg_loss']:.2f}")
print(f"盈亏比: {metrics['profit_loss_ratio']:.2f}")
print("="*50)
```

### 4.4 可视化结果

```python
# 绘制结果图表
import matplotlib.pyplot as plt
import seaborn as sns

# 设置图表样式
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# 创建图表
fig, axes = plt.subplots(2, 2, figsize=(15, 10))
fig.suptitle('FinRL Crypto 回测结果', fontsize=16, fontweight='bold')

# 1. 资产组合价值变化
axes[0, 0].plot(
    backtest_results['portfolio']['date'],
    backtest_results['portfolio']['total_value'],
    label='策略收益',
    linewidth=2
)
axes[0, 0].plot(
    backtest_results['portfolio']['date'],
    backtest_results['portfolio']['benchmark_value'],
    label='基准收益',
    linewidth=2,
    alpha=0.7
)
axes[0, 0].set_title('资产组合价值变化')
axes[0, 0].set_ylabel('价值 ($)')
axes[0, 0].legend()
axes[0, 0].grid(True, alpha=0.3)

# 2. 回撤分析
drawdown = backtest_results['portfolio']['drawdown']
axes[0, 1].fill_between(
    backtest_results['portfolio']['date'],
    drawdown,
    0,
    alpha=0.3,
    color='red'
)
axes[0, 1].plot(
    backtest_results['portfolio']['date'],
    drawdown,
    color='red',
    linewidth=1
)
axes[0, 1].set_title('回撤分析')
axes[0, 1].set_ylabel('回撤 (%)')
axes[0, 1].grid(True, alpha=0.3)

# 3. 仓位分布
position_data = backtest_results['portfolio'][[
    col for col in backtest_results['portfolio'].columns 
    if 'position' in col and col != 'total_position'
]]
position_data.plot(kind='area', stacked=True, ax=axes[1, 0], alpha=0.7)
axes[1, 0].set_title('仓位分布')
axes[1, 0].set_ylabel('仓位比例')
axes[1, 0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')

# 4. 收益分布
returns = backtest_results['portfolio']['daily_return'].dropna()
axes[1, 1].hist(returns, bins=50, alpha=0.7, density=True)
axes[1, 1].axvline(returns.mean(), color='red', linestyle='--', label=f'均值: {returns.mean():.4f}')
axes[1, 1].set_title('日收益率分布')
axes[1, 1].set_xlabel('日收益率')
axes[1, 1].set_ylabel('密度')
axes[1, 1].legend()
axes[1, 1].grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('results/backtest/performance_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

logger.info("可视化图表已保存")
```

## 🚀 第五步：实时交易部署

### 5.1 配置实时交易

```python
# 实时交易配置
LIVE_TRADING_CONFIG = {
    'mode': 'paper',              # 模拟交易模式
    'exchange': 'binance',        # 交易所
    'api_key': 'your_api_key',    # API密钥
    'api_secret': 'your_secret',  # API密钥
    'update_interval': 300,       # 更新间隔（秒）
    'max_orders': 10,             # 最大订单数
    'risk_management': {
        'max_position_size': 0.1,  # 单个资产最大仓位
        'stop_loss': 0.05,         # 止损比例
        'take_profit': 0.15,       # 止盈比例
        'max_daily_loss': 0.02     # 日最大亏损
    }
}

logger.info("实时交易配置完成")
```

### 5.2 创建交易机器人

```python
from finrl_crypto.live import TradingBot
from finrl_crypto.risk import RiskManager

# 创建风险管理器
risk_manager = RiskManager(
    **LIVE_TRADING_CONFIG['risk_management']
)

# 创建交易机器人
trading_bot = TradingBot(
    agent=trained_agent,
    risk_manager=risk_manager,
    **LIVE_TRADING_CONFIG
)

logger.info("交易机器人创建完成")
```

### 5.3 启动监控（演示模式）

```python
# 注意：这里只是演示代码，实际部署需要更多配置
print("\n" + "="*50)
print("           实时交易部署")
print("="*50)
print("⚠️  这是演示模式，实际部署请参考完整文档")
print("\n配置信息:")
print(f"交易模式: {LIVE_TRADING_CONFIG['mode']}")
print(f"交易所: {LIVE_TRADING_CONFIG['exchange']}")
print(f"更新间隔: {LIVE_TRADING_CONFIG['update_interval']}秒")
print(f"风险管理: 已启用")
print("\n要启动实时交易，请运行:")
print("python -m finrl_crypto.live --config config_live.py")
print("="*50)

logger.info("实时交易部署配置完成")
```

## 📊 第六步：结果总结

```python
# 生成完整报告
from finrl_crypto.reports import generate_report

report_data = {
    'config': {
        'data': DATA_CONFIG,
        'training': TRAINING_CONFIG,
        'env': ENV_CONFIG,
        'backtest': BACKTEST_CONFIG
    },
    'results': {
        'metrics': metrics,
        'trades': backtest_results['trades'],
        'portfolio': backtest_results['portfolio']
    },
    'model_info': {
        'algorithm': TRAINING_CONFIG['algorithm'],
        'training_time': training_duration,
        'model_path': model_path
    }
}

# 生成HTML报告
report_path = 'results/backtest/quickstart_report.html'
generate_report(report_data, output_path=report_path)

print("\n" + "="*60)
print("              🎉 教程完成！")
print("="*60)
print(f"✅ 数据处理: {len(processed_data):,} 条记录")
print(f"✅ 模型训练: {TRAINING_CONFIG['total_timesteps']:,} 步，耗时 {training_duration}")
print(f"✅ 策略回测: {len(backtest_results['trades'])} 笔交易")
print(f"✅ 性能指标: 年化收益 {metrics['annual_return']:.2%}，夏普比率 {metrics['sharpe_ratio']:.2f}")
print(f"✅ 报告生成: {report_path}")
print("\n📁 生成的文件:")
print("   📊 data/processed/crypto_data.csv - 处理后的数据")
print("   🧠 models/trained/ppo_crypto_model - 训练好的模型")
print("   📈 results/backtest/ - 回测结果和图表")
print("   📋 results/backtest/quickstart_report.html - 完整报告")
print("\n🚀 下一步建议:")
print("   1. 调整模型参数优化性能")
print("   2. 尝试不同的强化学习算法")
print("   3. 添加更多技术指标和特征")
print("   4. 实施更复杂的风险管理策略")
print("   5. 部署到实时交易环境")
print("="*60)

logger.info("快速教程全部完成！")
```

## 🔧 故障排除

### 常见问题

1. **数据下载失败**
   ```python
   # 检查网络连接
   import requests
   try:
       response = requests.get('https://finance.yahoo.com', timeout=10)
       print("网络连接正常")
   except:
       print("网络连接异常，请检查网络设置")
   ```

2. **内存不足**
   ```python
   # 减少数据量或批次大小
   TRAINING_CONFIG['batch_size'] = 32  # 减小批次
   DATA_CONFIG['symbols'] = ['BTC-USD']  # 减少交易对
   ```

3. **训练速度慢**
   ```python
   # 检查GPU可用性
   import torch
   print(f"CUDA可用: {torch.cuda.is_available()}")
   if torch.cuda.is_available():
       print(f"GPU设备: {torch.cuda.get_device_name()}")
   ```

## 📚 进一步学习

完成本教程后，建议您：

1. **深入学习**: 阅读 [用户指南](../user-guide/data-processing.md) 了解更多功能
2. **API文档**: 查看 [API参考](../api/core.md) 学习详细接口
3. **高级教程**: 尝试 [高级教程](../tutorials/advanced-tutorial.md) 和 [自定义策略](../tutorials/custom-strategies.md)
4. **社区交流**: 加入 [GitHub讨论区](https://github.com/your-org/FinRL-Crypto/discussions) 与其他用户交流

恭喜您完成了第一个 FinRL Crypto 项目！🎉