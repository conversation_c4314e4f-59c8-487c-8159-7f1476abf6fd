# FinRL Crypto 第一阶段测试配置文件
# 用于配置测试环境、参数和行为

# 测试环境配置
environment:
  # Python路径设置
  python_path: "../"
  
  # 测试数据目录
  test_data_dir: "test_data"
  
  # 临时文件目录
  temp_dir: "temp"
  
  # 日志配置
  logging:
    level: "INFO"
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: "test_logs.log"

# 测试运行配置
test_runner:
  # 默认超时时间（秒）
  default_timeout: 300
  
  # 并行测试数量
  parallel_workers: 1
  
  # 失败时是否立即停止
  fail_fast: false
  
  # 详细输出
  verbose: true
  
  # 重试次数
  retry_count: 0
  
  # 测试覆盖率阈值
  coverage_threshold: 70

# 第一阶段测试模块配置
phase1_modules:
  # 配置管理测试
  config_manager:
    enabled: true
    test_files:
      - "test_config_manager_unit.py"
    mock_config_files:
      - "test_config.yaml"
      - "test_config.json"
    
  # 依赖注入测试
  dependency_injection:
    enabled: true
    test_files:
      - "test_dependency_injection_unit.py"
    mock_services:
      - "TestService"
      - "DatabaseService"
      - "CacheService"
  
  # 数据模块测试
  data_module:
    enabled: true
    test_files:
      - "test_phase1_data_module.py"
    mock_data:
      symbols: ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
      timeframes: ["1h", "4h", "1d"]
      indicators: ["SMA", "EMA", "RSI", "MACD", "BB"]
    api_limits:
      requests_per_minute: 1200
      max_retries: 3
  
  # 环境模块测试
  environment_module:
    enabled: true
    test_files:
      - "test_phase1_environment_module.py"
    trading_params:
      initial_balance: 10000
      transaction_cost: 0.001
      max_position_size: 0.95
      risk_free_rate: 0.02
    
  # 代理模块测试
  agent_module:
    enabled: true
    test_files:
      - "test_phase1_agent_module.py"
    model_params:
      learning_rate: 0.0003
      batch_size: 64
      memory_size: 100000
      update_frequency: 4
    
  # 训练模块测试
  training_module:
    enabled: true
    test_files:
      - "test_phase1_training_module.py"
    training_params:
      max_episodes: 100
      eval_frequency: 10
      save_frequency: 50
      early_stopping_patience: 20
  
  # 集成测试
  integration:
    enabled: true
    test_files:
      - "test_phase1_integration.py"
    scenarios:
      - "basic_pipeline"
      - "multi_asset_trading"
      - "portfolio_management"
      - "risk_management"

# 模拟数据配置
mock_data:
  # 价格数据
  price_data:
    # 数据点数量
    data_points: 1000
    # 价格范围
    price_range: [100, 50000]
    # 波动率
    volatility: 0.02
    # 趋势强度
    trend_strength: 0.1
  
  # 技术指标数据
  indicators:
    sma_periods: [5, 10, 20, 50]
    ema_periods: [12, 26]
    rsi_period: 14
    macd_params: [12, 26, 9]
    bb_params: [20, 2]
  
  # 交易数据
  trading:
    # 订单类型
    order_types: ["market", "limit", "stop"]
    # 交易对
    trading_pairs: ["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT"]
    # 时间框架
    timeframes: ["1m", "5m", "15m", "1h", "4h", "1d"]

# 性能测试配置
performance:
  # 内存使用限制（MB）
  memory_limit: 1024
  
  # CPU使用限制（%）
  cpu_limit: 80
  
  # 执行时间限制（秒）
  execution_time_limit: 600
  
  # 性能基准
  benchmarks:
    data_processing_speed: 1000  # 每秒处理的数据点
    model_inference_time: 0.1    # 模型推理时间（秒）
    memory_efficiency: 0.8       # 内存使用效率

# 报告配置
reporting:
  # 报告格式
  formats: ["markdown", "html", "json"]
  
  # 报告详细程度
  detail_level: "detailed"  # minimal, standard, detailed
  
  # 包含的信息
  include:
    - "test_results"
    - "coverage_report"
    - "performance_metrics"
    - "error_details"
    - "recommendations"
  
  # 输出目录
  output_dir: "reports"
  
  # 历史报告保留天数
  retention_days: 30

# 错误处理配置
error_handling:
  # 是否捕获所有异常
  catch_all_exceptions: true
  
  # 错误日志级别
  error_log_level: "ERROR"
  
  # 是否生成错误堆栈跟踪
  include_traceback: true
  
  # 错误重试策略
  retry_strategy:
    max_retries: 3
    backoff_factor: 2
    retry_on_errors: ["ConnectionError", "TimeoutError"]

# 外部依赖配置
external_dependencies:
  # 数据源API
  data_sources:
    binance:
      enabled: true
      mock_mode: true  # 测试时使用模拟数据
      rate_limit: 1200
    
    yahoo_finance:
      enabled: false
      mock_mode: true
  
  # 机器学习库
  ml_libraries:
    stable_baselines3:
      enabled: true
      version: ">=1.7.0"
    
    torch:
      enabled: true
      version: ">=1.12.0"
    
    tensorflow:
      enabled: false
      version: ">=2.8.0"

# 测试数据生成配置
test_data_generation:
  # 是否自动生成测试数据
  auto_generate: true
  
  # 数据生成种子（确保可重现性）
  random_seed: 42
  
  # 生成的数据集大小
  dataset_sizes:
    small: 100
    medium: 1000
    large: 10000
  
  # 数据质量参数
  data_quality:
    missing_data_ratio: 0.05
    outlier_ratio: 0.02
    noise_level: 0.01

# 持续集成配置
ci_cd:
  # 是否在CI环境中运行
  ci_mode: false
  
  # CI环境检测
  ci_environments: ["GITHUB_ACTIONS", "JENKINS", "TRAVIS_CI"]
  
  # CI特定配置
  ci_config:
    timeout_multiplier: 2
    memory_limit_multiplier: 1.5
    parallel_workers: 2
    
  # 质量门槛
  quality_gates:
    min_test_coverage: 80
    max_test_duration: 1800  # 30分钟
    max_failure_rate: 0.05   # 5%