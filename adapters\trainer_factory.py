#!/usr/bin/env python3
"""
训练器工厂类

这个模块提供了一个工厂类来创建和管理不同的训练器适配器。
通过工厂模式，可以轻松地添加新的RL框架支持，而无需修改现有代码。

作者: FinRL-Crypto 重构项目
日期: 2024
"""

from typing import Dict, Type, List
from interfaces.trainer_interface import TrainerInterface
from adapters.elegantrl_adapter import ElegantRLAdapter


class TrainerFactory:
    """
    训练器工厂类
    
    负责创建和管理不同类型的训练器适配器。
    支持动态注册新的训练器类型。
    """
    
    # 注册的训练器类型
    _trainers: Dict[str, Type[TrainerInterface]] = {
        'elegantrl': ElegantRLAdapter,
    }
    
    @classmethod
    def create_trainer(cls, trainer_type: str) -> TrainerInterface:
        """
        创建指定类型的训练器
        
        Args:
            trainer_type: 训练器类型名称
            
        Returns:
            TrainerInterface: 训练器实例
            
        Raises:
            ValueError: 不支持的训练器类型
        """
        if trainer_type not in cls._trainers:
            raise ValueError(
                f"不支持的训练器类型: {trainer_type}. "
                f"支持的类型: {list(cls._trainers.keys())}"
            )
        
        trainer_class = cls._trainers[trainer_type]
        return trainer_class()
    
    @classmethod
    def register_trainer(cls, trainer_type: str, trainer_class: Type[TrainerInterface]) -> None:
        """
        注册新的训练器类型
        
        Args:
            trainer_type: 训练器类型名称
            trainer_class: 训练器类
            
        Raises:
            TypeError: 训练器类不是TrainerInterface的子类
        """
        if not issubclass(trainer_class, TrainerInterface):
            raise TypeError(f"训练器类必须继承自TrainerInterface")
        
        cls._trainers[trainer_type] = trainer_class
    
    @classmethod
    def get_supported_trainers(cls) -> List[str]:
        """
        获取支持的训练器类型列表
        
        Returns:
            List[str]: 支持的训练器类型名称列表
        """
        return list(cls._trainers.keys())
    
    @classmethod
    def is_trainer_supported(cls, trainer_type: str) -> bool:
        """
        检查是否支持指定的训练器类型
        
        Args:
            trainer_type: 训练器类型名称
            
        Returns:
            bool: 是否支持
        """
        return trainer_type in cls._trainers
    
    @classmethod
    def get_trainer_info(cls, trainer_type: str) -> Dict[str, str]:
        """
        获取训练器信息
        
        Args:
            trainer_type: 训练器类型名称
            
        Returns:
            Dict[str, str]: 训练器信息
            
        Raises:
            ValueError: 不支持的训练器类型
        """
        if trainer_type not in cls._trainers:
            raise ValueError(f"不支持的训练器类型: {trainer_type}")
        
        trainer_class = cls._trainers[trainer_type]
        
        return {
            'type': trainer_type,
            'class_name': trainer_class.__name__,
            'module': trainer_class.__module__,
            'description': trainer_class.__doc__.split('\n')[0] if trainer_class.__doc__ else ''
        }


# 便利函数
def create_trainer(trainer_type: str = 'elegantrl') -> TrainerInterface:
    """
    创建训练器的便利函数
    
    Args:
        trainer_type: 训练器类型，默认为'elegantrl'
        
    Returns:
        TrainerInterface: 训练器实例
    """
    return TrainerFactory.create_trainer(trainer_type)


def get_available_trainers() -> List[str]:
    """
    获取可用训练器列表的便利函数
    
    Returns:
        List[str]: 可用的训练器类型列表
    """
    return TrainerFactory.get_supported_trainers()