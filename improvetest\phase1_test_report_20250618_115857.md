# FinRL Crypto 第一阶段测试报告

## 测试概览

- **测试时间**: 2025-06-18T11:54:46.517615 - 2025-06-18T11:58:57.815836
- **总耗时**: 251.30 秒
- **测试文件数**: 7
- **通过文件数**: 1
- **失败文件数**: 6
- **成功率**: 14.3%

## 测试状态

🔴 存在测试失败

## 详细结果

| 测试文件 | 状态 | 耗时(s) | 通过 | 失败 | 跳过 |
|---------|------|---------|------|------|------|
| test_config_manager_unit.py | ✅ PASSED | 22.09 | 12 | 0 | 0 |
| test_dependency_injection_unit.py | ❌ FAILED | 20.70 | 10 | 3 | 0 |
| test_phase1_data_module.py | ❌ FAILED | 33.76 | 3 | 15 | 0 |
| test_phase1_environment_module.py | ❌ FAILED | 29.27 | 0 | 0 | 0 |
| test_phase1_agent_module.py | ❌ FAILED | 45.05 | 14 | 22 | 0 |
| test_phase1_training_module.py | ❌ FAILED | 43.18 | 0 | 0 | 1 |
| test_phase1_integration.py | ❌ FAILED | 57.18 | 0 | 0 | 1 |

## 失败测试详情

### test_dependency_injection_unit.py

**状态**: FAILED

**错误信息**:
```
C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytest_asyncio\plugin.py:208: PytestDeprecationWarning: The configuration option "asyncio_default_fixture_loop_scope" is unset.
The event loop scope for asynchronous fixtures will default to the fixture caching scope. Future versions of pytest-asyncio will default the loop scope for asynchronous fixtures to function scope. Set the default fixture loop scope explicitly in order to avoid unexpected behavior in the future. Valid fixture loop scopes are: "function", "class", "module", "package", "session"

  warnings.warn(PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET))
C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\coverage\html.py:124: CoverageWarning: No contexts were measured
  self.coverage._warn("No contexts were measured")

```

### test_phase1_data_module.py

**状态**: FAILED

**错误信息**:
```
C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytest_asyncio\plugin.py:208: PytestDeprecationWarning: The configuration option "asyncio_default_fixture_loop_scope" is unset.
The event loop scope for asynchronous fixtures will default to the fixture caching scope. Future versions of pytest-asyncio will default the loop scope for asynchronous fixtures to function scope. Set the default fixture loop scope explicitly in order to avoid unexpected behavior in the future. Valid fixture loop scopes are: "function", "class", "module", "package", "session"

  warnings.warn(PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET))
C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\coverage\html.py:124: CoverageWarning: No contexts were measured
  self.coverage._warn("No contexts were measured")

```

### test_phase1_environment_module.py

**状态**: FAILED

**错误信息**:
```
C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytest_asyncio\plugin.py:208: PytestDeprecationWarning: The configuration option "asyncio_default_fixture_loop_scope" is unset.
The event loop scope for asynchronous fixtures will default to the fixture caching scope. Future versions of pytest-asyncio will default the loop scope for asynchronous fixtures to function scope. Set the default fixture loop scope explicitly in order to avoid unexpected behavior in the future. Valid fixture loop scopes are: "function", "class", "module", "package", "session"

  warnings.warn(PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET))
C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\coverage\control.py:915: CoverageWarning: No data was collected. (no-data-collected)
  self._warn("No data was collected.", slug="no-data-collected")

```

### test_phase1_agent_module.py

**状态**: FAILED

**错误信息**:
```
C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytest_asyncio\plugin.py:208: PytestDeprecationWarning: The configuration option "asyncio_default_fixture_loop_scope" is unset.
The event loop scope for asynchronous fixtures will default to the fixture caching scope. Future versions of pytest-asyncio will default the loop scope for asynchronous fixtures to function scope. Set the default fixture loop scope explicitly in order to avoid unexpected behavior in the future. Valid fixture loop scopes are: "function", "class", "module", "package", "session"

  warnings.warn(PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET))
C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\coverage\html.py:124: CoverageWarning: No contexts were measured
  self.coverage._warn("No contexts were measured")

```

### test_phase1_training_module.py

**状态**: FAILED

**错误信息**:
```
C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytest_asyncio\plugin.py:208: PytestDeprecationWarning: The configuration option "asyncio_default_fixture_loop_scope" is unset.
The event loop scope for asynchronous fixtures will default to the fixture caching scope. Future versions of pytest-asyncio will default the loop scope for asynchronous fixtures to function scope. Set the default fixture loop scope explicitly in order to avoid unexpected behavior in the future. Valid fixture loop scopes are: "function", "class", "module", "package", "session"

  warnings.warn(PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET))
C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\coverage\html.py:124: CoverageWarning: No contexts were measured
  self.coverage._warn("No contexts were measured")

```

### test_phase1_integration.py

**状态**: FAILED

**错误信息**:
```
C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pytest_asyncio\plugin.py:208: PytestDeprecationWarning: The configuration option "asyncio_default_fixture_loop_scope" is unset.
The event loop scope for asynchronous fixtures will default to the fixture caching scope. Future versions of pytest-asyncio will default the loop scope for asynchronous fixtures to function scope. Set the default fixture loop scope explicitly in order to avoid unexpected behavior in the future. Valid fixture loop scopes are: "function", "class", "module", "package", "session"

  warnings.warn(PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET))
C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\coverage\html.py:124: CoverageWarning: No contexts were measured
  self.coverage._warn("No contexts were measured")

```


## 代码覆盖率

详细的代码覆盖率报告请查看生成的HTML报告文件。


## 建议

- 🔧 请修复失败的测试用例
- 📊 检查代码覆盖率，补充缺失的测试
- 🐛 分析失败原因，可能需要修复代码逻辑

## 下一步

1. 修复所有失败的测试
2. 提高代码覆盖率到80%以上
3. 添加更多边界情况测试
4. 进行性能测试和压力测试
