<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="3" skipped="0" tests="13" time="7.131" timestamp="2025-06-19T02:02:50.688474+09:00" hostname="E-5CG22747W5"><testcase classname="improvetest.test_dependency_injection_unit.TestServiceLifetime" name="test_all_lifetimes_exist" time="0.980" /><testcase classname="improvetest.test_dependency_injection_unit.TestServiceLifetime" name="test_scoped_lifetime" time="0.004" /><testcase classname="improvetest.test_dependency_injection_unit.TestServiceLifetime" name="test_singleton_lifetime" time="0.004" /><testcase classname="improvetest.test_dependency_injection_unit.TestServiceLifetime" name="test_transient_lifetime" time="0.006" /><testcase classname="improvetest.test_dependency_injection_unit.TestServiceDescriptor" name="test_service_descriptor_creation" time="0.006"><failure message="dependency_injection.InvalidServiceError: Service &lt;class 'test_dependency_injection_unit.TestServiceDescriptor.test_service_descriptor_creation.&lt;locals&gt;.TestService'&gt; must have either implementation_type or factory">improvetest\test_dependency_injection_unit.py:56: in test_service_descriptor_creation
    descriptor = ServiceDescriptor(
&lt;string&gt;:8: in __init__
    ???
core\dependency_injection.py:63: in __post_init__
    raise InvalidServiceError(
E   dependency_injection.InvalidServiceError: Service &lt;class 'test_dependency_injection_unit.TestServiceDescriptor.test_service_descriptor_creation.&lt;locals&gt;.TestService'&gt; must have either implementation_type or factory</failure></testcase><testcase classname="improvetest.test_dependency_injection_unit.TestServiceDescriptor" name="test_service_descriptor_with_interface" time="0.004"><failure message="dependency_injection.InvalidServiceError: Service &lt;class 'test_dependency_injection_unit.TestServiceDescriptor.test_service_descriptor_with_interface.&lt;locals&gt;.ITestService'&gt; must have either implementation_type or factory">improvetest\test_dependency_injection_unit.py:75: in test_service_descriptor_with_interface
    descriptor = ServiceDescriptor(
&lt;string&gt;:8: in __init__
    ???
core\dependency_injection.py:63: in __post_init__
    raise InvalidServiceError(
E   dependency_injection.InvalidServiceError: Service &lt;class 'test_dependency_injection_unit.TestServiceDescriptor.test_service_descriptor_with_interface.&lt;locals&gt;.ITestService'&gt; must have either implementation_type or factory</failure></testcase><testcase classname="improvetest.test_dependency_injection_unit.TestDependencyInjectionContainer" name="test_circular_dependency_detection" time="0.005"><failure message="TypeError: TestDependencyInjectionContainer.test_circular_dependency_detection.&lt;locals&gt;.ServiceA.__init__() missing 1 required positional argument: 'service_b'">improvetest\test_dependency_injection_unit.py:334: in test_circular_dependency_detection
    container.resolve(ServiceA)
improvetest\test_dependency_injection_unit.py:324: in resolve
    return implementation()
E   TypeError: TestDependencyInjectionContainer.test_circular_dependency_detection.&lt;locals&gt;.ServiceA.__init__() missing 1 required positional argument: 'service_b'</failure></testcase><testcase classname="improvetest.test_dependency_injection_unit.TestDependencyInjectionContainer" name="test_dependency_injection" time="0.006" /><testcase classname="improvetest.test_dependency_injection_unit.TestDependencyInjectionContainer" name="test_service_registration" time="0.003" /><testcase classname="improvetest.test_dependency_injection_unit.TestDependencyInjectionContainer" name="test_singleton_lifetime" time="0.004" /><testcase classname="improvetest.test_dependency_injection_unit.TestDependencyInjectionContainer" name="test_thread_safety" time="0.018" /><testcase classname="improvetest.test_dependency_injection_unit.TestDependencyInjectionContainer" name="test_transient_lifetime" time="0.004" /><testcase classname="improvetest.test_dependency_injection_unit.TestServiceConfiguration" name="test_configuration_based_registration" time="0.008" /></testsuite></testsuites>