#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化工具模块 - 统一的超参数优化相关功能
文件名：optimization_utils.py
目标：消除优化脚本中的代码重复，提供统一的工具函数
"""

import os
import pickle
import joblib
import numpy as np
from datetime import datetime
from distutils.dir_util import copy_tree
from typing import Dict, Tuple, Any, List


class OptimizationUtils:
    """优化相关的工具函数集合"""
    
    @staticmethod
    def sample_hyperparams(trial, config: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        统一的超参数采样逻辑
        
        Args:
            trial: Optuna trial对象
            config: 配置字典，包含采样范围和训练参数
            
        Returns:
            Tuple[Dict, Dict]: (erl_params, env_params)
        """
        no_candles_for_train = config.get('no_candles_for_train', 1000)
        average_episode_step_min = no_candles_for_train + 0.25 * no_candles_for_train
        
        # ElegantRL参数
        sampled_erl_params = {
            "learning_rate": trial.suggest_categorical(
                "learning_rate", 
                config.get('learning_rates', [3e-2, 2.3e-2, 1.5e-2, 7.5e-3, 5e-6])
            ),
            "batch_size": trial.suggest_categorical(
                "batch_size", 
                config.get('batch_sizes', [512, 1280, 2048, 3080])
            ),
            "gamma": trial.suggest_categorical(
                "gamma", 
                config.get('gammas', [0.85, 0.99, 0.999])
            ),
            "net_dimension": trial.suggest_categorical(
                "net_dimension", 
                config.get('net_dimensions', [2 ** 9, 2 ** 10, 2 ** 11, 2 ** 12])
            ),
            "target_step": trial.suggest_categorical(
                "target_step",
                config.get('target_steps', [
                    average_episode_step_min, 
                    round(1.5 * average_episode_step_min),
                    2 * average_episode_step_min
                ])
            ),
            "eval_time_gap": trial.suggest_categorical(
                "eval_time_gap", 
                config.get('eval_time_gaps', [60])
            ),
            "break_step": trial.suggest_categorical(
                "break_step", 
                config.get('break_steps', [3e4, 4.5e4, 6e4])
            )
        }

        # 环境标准化和回望参数
        sampled_env_params = {
            "lookback": trial.suggest_categorical(
                "lookback", 
                config.get('lookbacks', [1])
            ),
            "norm_cash": trial.suggest_categorical(
                "norm_cash", 
                config.get('norm_cash_values', [2 ** -12])
            ),
            "norm_stocks": trial.suggest_categorical(
                "norm_stocks", 
                config.get('norm_stocks_values', [2 ** -8])
            ),
            "norm_tech": trial.suggest_categorical(
                "norm_tech", 
                config.get('norm_tech_values', [2 ** -15])
            ),
            "norm_reward": trial.suggest_categorical(
                "norm_reward", 
                config.get('norm_reward_values', [2 ** -10])
            ),
            "norm_action": trial.suggest_categorical(
                "norm_action", 
                config.get('norm_action_values', [10000])
            )
        }
        
        return sampled_erl_params, sampled_env_params
    
    @staticmethod
    def set_trial_attributes(trial, config: Dict[str, Any], name_folder: str, 
                           name_test: str, study) -> None:
        """
        统一的试验属性设置逻辑
        
        Args:
            trial: Optuna trial对象
            config: 配置字典
            name_folder: 文件夹名称
            name_test: 测试名称
            study: Optuna study对象
        """
        # 设置用户属性，用于后续保存到pickle模型文件
        trial.set_user_attr("model_name", config.get('model_name'))
        trial.set_user_attr("timeframe", config.get('timeframe'))
        trial.set_user_attr("train_start_date", config.get('train_start_date'))
        trial.set_user_attr("train_end_date", config.get('train_end_date'))
        trial.set_user_attr("test_start_date", config.get('val_start_date'))
        trial.set_user_attr("test_end_date", config.get('val_end_date'))
        trial.set_user_attr("ticker_list", config.get('ticker_list'))
        trial.set_user_attr("technical_indicator_list", config.get('technical_indicators'))
        trial.set_user_attr("name_folder", name_folder)
        trial.set_user_attr("name_test", name_test)
        
        # 保存study
        study_path = os.path.join(
            OptimizationUtils._get_train_results_path(), 
            name_folder, 
            'study.pkl'
        )
        os.makedirs(os.path.dirname(study_path), exist_ok=True)
        joblib.dump(study, study_path)
    
    @staticmethod
    def load_saved_data(timeframe: str, no_candles_for_train: int, 
                       no_candles_for_val: int) -> Tuple[Any, Any, Any, Any]:
        """
        统一的数据加载逻辑
        
        Args:
            timeframe: 时间框架
            no_candles_for_train: 训练蜡烛数
            no_candles_for_val: 验证蜡烛数
            
        Returns:
            Tuple: (data_from_processor, price_array, tech_array, time_array)
        """
        data_folder = OptimizationUtils._get_data_folder_path(
            timeframe, no_candles_for_train + no_candles_for_val
        )
        
        print(f'\nLOADING DATA FOLDER: {data_folder}\n')
        
        # 加载各种数据文件
        data_files = {
            'data_from_processor': 'data_from_processor',
            'price_array': 'price_array',
            'tech_array': 'tech_array',
            'time_array': 'time_array'
        }
        
        loaded_data = []
        for key, filename in data_files.items():
            file_path = os.path.join(data_folder, filename)
            with open(file_path, 'rb') as handle:
                data = pickle.load(handle)
                loaded_data.append(data)
        
        return tuple(loaded_data)
    
    @staticmethod
    def write_logs(name_folder: str, config: Dict[str, Any], trial, 
                  additional_info: Dict[str, Any] = None) -> str:
        """
        统一的日志写入逻辑
        
        Args:
            name_folder: 文件夹名称
            config: 配置字典
            trial: Optuna trial对象
            additional_info: 额外信息字典
            
        Returns:
            str: 日志文件路径
        """
        logs_dir = os.path.join(
            OptimizationUtils._get_train_results_path(), 
            name_folder
        )
        os.makedirs(logs_dir, exist_ok=True)
        
        path_logs = os.path.join(logs_dir, 'logs.txt')
        
        with open(path_logs, 'a') as f:
            f.write(f'\nMODEL NAME: {config.get("model_name")}\n')
            f.write(f'TRIAL NUMBER: {trial.number}\n')
            f.write(f'CWD: {config.get("cwd")}\n')
            f.write(f'{config.get("erl_params")}\n')
            f.write(f'{config.get("env_params")}\n')
            f.write(f'\nTIME START OUTER: {datetime.now()}\n')
            
            # 写入额外信息
            if additional_info:
                for key, value in additional_info.items():
                    f.write(f'{key}: {value}\n')
        
        return path_logs
    
    @staticmethod
    def save_best_agent(trial) -> None:
        """
        统一的最佳代理保存逻辑
        
        Args:
            trial: Optuna trial对象
        """
        print('\n' + OptimizationUtils._get_color_text('Found new best agent!', 'green') + '\n')
        
        # 从trial属性获取路径信息
        name_folder = trial.user_attrs['name_folder']
        name_test = trial.user_attrs['name_test']
        
        train_results_path = OptimizationUtils._get_train_results_path()
        from_directory = os.path.join(train_results_path, 'cwd_tests', name_test)
        to_directory = os.path.join(train_results_path, name_folder, 'stored_agent')
        
        # 创建目标目录
        os.makedirs(to_directory, exist_ok=True)
        
        # 复制代理文件
        copy_tree(from_directory, to_directory)
        
        # 保存trial到pickle文件，避免参数未复制的错误
        trial_path = os.path.join(train_results_path, name_folder, 'best_trial')
        with open(trial_path, "wb") as handle:
            pickle.dump(trial, handle, protocol=pickle.HIGHEST_PROTOCOL)
    
    @staticmethod
    def _get_data_folder_path(timeframe: str, total_candles: int) -> str:
        """
        获取数据文件夹路径
        
        Args:
            timeframe: 时间框架
            total_candles: 总蜡烛数
            
        Returns:
            str: 数据文件夹路径
        """
        return os.path.join('./data', f'{timeframe}_{total_candles}')
    
    @staticmethod
    def _get_train_results_path() -> str:
        """
        获取训练结果路径
        
        Returns:
            str: 训练结果路径
        """
        return './train_results'
    
    @staticmethod
    def _get_color_text(text: str, color: str) -> str:
        """
        获取彩色文本（简化版本，避免依赖bcolors类）
        
        Args:
            text: 文本内容
            color: 颜色名称
            
        Returns:
            str: 彩色文本
        """
        colors = {
            'green': '\033[92m',
            'red': '\033[91m',
            'yellow': '\033[93m',
            'blue': '\033[94m',
            'end': '\033[0m'
        }
        
        color_code = colors.get(color.lower(), '')
        end_code = colors.get('end', '')
        
        return f'{color_code}{text}{end_code}'