[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "finrl-crypto"
version = "0.1.0"
description = "FinRL Crypto - 深度强化学习加密货币交易系统"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "FinRL Crypto Team", email = "<EMAIL>"}
]
keywords = ["fintech", "cryptocurrency", "trading", "reinforcement-learning", "deep-learning"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Financial and Insurance Industry",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Office/Business :: Financial :: Investment"
]
dependencies = [
    "numpy>=1.21.0",
    "pandas>=1.3.0",
    "torch>=1.12.0",
    "gymnasium>=0.26.0",
    "stable-baselines3>=1.6.0",
    "ccxt>=2.0.0",
    "yfinance>=0.1.87",
    "ta>=0.10.2",
    "plotly>=5.0.0",
    "dash>=2.0.0",
    "redis>=4.0.0",
    "sqlalchemy>=1.4.0",
    "psycopg2-binary>=2.9.0",
    "pydantic>=1.10.0",
    "fastapi>=0.85.0",
    "uvicorn>=0.18.0",
    "celery>=5.2.0",
    "prometheus-client>=0.14.0",
    "structlog>=22.1.0",
    "click>=8.0.0",
    "python-dotenv>=0.19.0",
    "requests>=2.28.0",
    "websockets>=10.0",
    "aiohttp>=3.8.0",
    "schedule>=1.1.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-xdist>=3.0.0",
    "pytest-timeout>=2.1.0",
    "pytest-rerunfailures>=11.0",
    "pytest-benchmark>=4.0.0",
    "pytest-mock>=3.8.0",
    "pytest-asyncio>=0.21.0",
    "pytest-env>=0.8.0",
    "pytest-sugar>=0.9.0",
    "pytest-html>=3.1.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "flake8>=5.0.0",
    "mypy>=0.991",
    "pylint>=2.15.0",
    "bandit>=1.7.0",
    "safety>=2.3.0",
    "pre-commit>=2.20.0"
]
docs = [
    "mkdocs>=1.4.0",
    "mkdocs-material>=8.5.0",
    "mkdocs-awesome-pages-plugin>=2.8.0",
    "mkdocs-git-revision-date-localized-plugin>=1.1.0",
    "mkdocs-minify-plugin>=0.6.0",
    "mkdocs-redirects>=1.2.0",
    "mkdocs-macros-plugin>=0.7.0",
    "mkdocs-include-markdown-plugin>=4.0.0"
]
gpu = [
    "torch[cuda]>=1.12.0",
    "cupy>=11.0.0"
]
visualization = [
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0",
    "bokeh>=2.4.0",
    "altair>=4.2.0"
]
performance = [
    "numba>=0.56.0",
    "cython>=0.29.0",
    "joblib>=1.1.0"
]

[project.urls]
Homepage = "https://github.com/finrl-crypto/finrl-crypto"
Documentation = "https://finrl-crypto.readthedocs.io"
Repository = "https://github.com/finrl-crypto/finrl-crypto"
"Bug Tracker" = "https://github.com/finrl-crypto/finrl-crypto/issues"

[project.scripts]
finrl-crypto = "finrl_crypto.cli:main"
finrl-test = "scripts.test_runner:main"

# Black 配置
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
(
  /(
      \.eggs
    | \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
    | migrations
    | train_results
    | plots_and_metrics
    | data
    | models
    | checkpoints
    | weights
    | saved_models
    | outputs
    | htmlcov
    | reports
    | logs
    | cache
    | .cache
    | .pytest_cache
    | node_modules
    | .idea
    | .vscode
  )/
)
'''

# isort 配置
[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = [
    "*/migrations/*",
    "*/train_results/*",
    "*/plots_and_metrics/*",
    "*/data/*",
    "*/models/*",
    "*/checkpoints/*",
    "*/weights/*",
    "*/saved_models/*",
    "*/outputs/*",
    "*/htmlcov/*",
    "*/reports/*",
    "*/logs/*",
    "*/cache/*",
    "*/.cache/*",
    "*/.pytest_cache/*",
    "*/node_modules/*",
    "*/.idea/*",
    "*/.vscode/*"
]
known_first_party = ["finrl_crypto"]
known_third_party = [
    "numpy", "pandas", "torch", "gymnasium", "stable_baselines3",
    "ccxt", "yfinance", "ta", "plotly", "dash", "redis", "sqlalchemy",
    "psycopg2", "pydantic", "fastapi", "uvicorn", "celery",
    "prometheus_client", "structlog", "click", "dotenv", "requests",
    "websockets", "aiohttp", "schedule", "pytest"
]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

# MyPy 配置
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
show_column_numbers = true
ignore_missing_imports = true

# 排除目录
exclude = [
    "migrations/",
    "train_results/",
    "plots_and_metrics/",
    "data/",
    "models/",
    "checkpoints/",
    "weights/",
    "saved_models/",
    "outputs/",
    "htmlcov/",
    "reports/",
    "logs/",
    "cache/",
    ".cache/",
    ".pytest_cache/",
    "node_modules/",
    ".idea/",
    ".vscode/",
    "build/",
    "dist/",
    "*.egg-info/"
]

# 每个模块的特定配置
[[tool.mypy.overrides]]
module = "tests.*"
ignore_errors = true

[[tool.mypy.overrides]]
module = "examples.*"
ignore_errors = true

# Pylint 配置
[tool.pylint.main]
jobs = 0
load-plugins = [
    "pylint.extensions.check_elif",
    "pylint.extensions.bad_builtin",
    "pylint.extensions.docparams",
    "pylint.extensions.for_any_all",
    "pylint.extensions.set_membership",
    "pylint.extensions.code_style",
    "pylint.extensions.overlapping_exceptions",
    "pylint.extensions.typing",
    "pylint.extensions.redefined_variable_type",
    "pylint.extensions.comparison_placement"
]

[tool.pylint.messages_control]
disable = [
    "raw-checker-failed",
    "bad-inline-option",
    "locally-disabled",
    "file-ignored",
    "suppressed-message",
    "useless-suppression",
    "deprecated-pragma",
    "use-symbolic-message-instead",
    "missing-module-docstring",
    "missing-class-docstring",
    "missing-function-docstring",
    "too-few-public-methods",
    "too-many-arguments",
    "too-many-locals",
    "too-many-branches",
    "too-many-statements",
    "too-many-instance-attributes",
    "import-error",
    "no-name-in-module"
]

[tool.pylint.reports]
output-format = "text"
reports = "no"
score = "yes"

[tool.pylint.refactoring]
max-nested-blocks = 5
never-returning-functions = ["sys.exit", "argparse.parse_error"]

[tool.pylint.similarities]
ignore-comments = "yes"
ignore-docstrings = "yes"
ignore-imports = "yes"
ignore-signatures = "yes"
min-similarity-lines = 4

[tool.pylint.spelling]
max-spelling-suggestions = 4
spelling-ignore-comment-directives = "fmt: off,fmt: on,noqa:,noqa,nosec,isort:skip,mypy:"

[tool.pylint.string]
check-str-concat-over-line-jumps = "yes"

[tool.pylint.typecheck]
contextmanager-decorators = ["contextlib.contextmanager"]
ignore-none = "yes"
ignore-on-opaque-inference = "yes"
ignored-classes = ["optparse.Values", "thread._local", "_thread._local"]
ignored-modules = ["numpy", "torch", "tensorflow"]
missing-member-hint = "yes"
missing-member-hint-distance = 1
missing-member-max-choices = 1
mixin-class-rgx = ".*[Mm]ixin"

[tool.pylint.variables]
allow-global-unused-variables = "yes"
allowed-redefined-builtins = ["id"]
callbacks = ["cb_", "_cb"]
dummy-variables-rgx = "_+$|(_[a-zA-Z0-9_]*[a-zA-Z0-9]+?$)|dummy|^ignored_|^unused_"
ignored-argument-names = "_.*|^ignored_|^unused_"
init-import = "no"
redefining-builtins-modules = ["six.moves", "past.builtins", "future.builtins", "builtins", "io"]

# Bandit 配置
[tool.bandit]
exclude_dirs = [
    "tests",
    "migrations",
    "train_results",
    "plots_and_metrics",
    "data",
    "models",
    "checkpoints",
    "weights",
    "saved_models",
    "outputs",
    "htmlcov",
    "reports",
    "logs",
    "cache",
    ".cache",
    ".pytest_cache",
    "node_modules",
    ".idea",
    ".vscode",
    "build",
    "dist"
]
skips = ["B101", "B601"]

# Coverage 配置
[tool.coverage.run]
source = ["finrl_crypto"]
branch = true
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
    "*/.venv/*",
    "*/.env/*",
    "*/site-packages/*",
    "*/migrations/*",
    "*/settings/*",
    "*/manage.py",
    "*/setup.py",
    "*/conftest.py",
    "*/.tox/*",
    "*/build/*",
    "*/dist/*",
    "*/.pytest_cache/*",
    "*/htmlcov/*",
    "*/docs/*",
    "*/examples/*",
    "*/scripts/*",
    "*/tools/*",
    "*/deprecated/*",
    "*/legacy/*",
    "*/backup/*",
    "*/temp/*",
    "*/tmp/*",
    "*/.idea/*",
    "*/.vscode/*",
    "*/.git/*",
    "*/node_modules/*",
    "*/static/*",
    "*/media/*",
    "*/logs/*",
    "*/log/*",
    "*/cache/*",
    "*/.cache/*",
    "*/train_results/*",
    "*/plots_and_metrics/*",
    "*/data/*",
    "*/models/*",
    "*/checkpoints/*",
    "*/weights/*",
    "*/saved_models/*",
    "*/outputs/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
    "@(abc\\.)?abstractproperty",
    "@(abc\\.)?abstractclassmethod",
    "@(abc\\.)?abstractstaticmethod"
]
ignore_errors = true
skip_covered = false
skip_empty = true
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"
title = "FinRL Crypto Test Coverage Report"
show_contexts = true

[tool.coverage.xml]
output = "coverage.xml"

[tool.coverage.json]
output = "coverage.json"
show_contexts = true
pretty_print = true

# Pytest 配置
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--verbose",
    "--tb=short",
    "--strict-markers",
    "--strict-config",
    "--disable-warnings",
    "--color=yes",
    "--durations=10",
    "--cov=finrl_crypto",
    "--cov-report=html:htmlcov",
    "--cov-report=term-missing",
    "--cov-report=xml",
    "--cov-fail-under=80",
    "--junitxml=reports/junit.xml",
    "--html=reports/report.html",
    "--self-contained-html"
]
markers = [
    "slow: 标记测试为慢速测试（运行时间 > 5秒）",
    "integration: 集成测试",
    "unit: 单元测试",
    "e2e: 端到端测试",
    "gpu: 需要GPU的测试",
    "network: 需要网络连接的测试",
    "live: 实盘交易测试（谨慎使用）",
    "data: 数据相关测试",
    "model: 模型相关测试",
    "strategy: 策略相关测试",
    "backtest: 回测相关测试",
    "performance: 性能测试",
    "security: 安全测试",
    "api: API测试",
    "database: 数据库测试",
    "redis: Redis缓存测试",
    "docker: Docker相关测试",
    "windows: Windows特定测试",
    "linux: Linux特定测试",
    "macos: macOS特定测试",
    "python38: Python 3.8特定测试",
    "python39: Python 3.9特定测试",
    "python310: Python 3.10特定测试",
    "python311: Python 3.11特定测试",
    "experimental: 实验性功能测试",
    "deprecated: 已弃用功能测试",
    "smoke: 冒烟测试",
    "regression: 回归测试",
    "stress: 压力测试",
    "memory: 内存测试",
    "timeout: 超时测试",
    "parallel: 并行测试",
    "serial: 串行测试",
    "mock: 模拟测试",
    "real: 真实环境测试"
]
filterwarnings = [
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore::FutureWarning",
    "ignore:.*:pytest.PytestUnraisableExceptionWarning",
    "error::RuntimeWarning",
    "error::ResourceWarning"
]
minversion = "6.0"
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)8s] %(name)s: %(message)s"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"
log_file = "tests/logs/pytest.log"
log_file_level = "DEBUG"
log_file_format = "%(asctime)s [%(levelname)8s] %(filename)s:%(lineno)d %(funcName)s(): %(message)s"
log_file_date_format = "%Y-%m-%d %H:%M:%S"
log_auto_indent = true
timeout = 300
timeout_method = "thread"
env = [
    "TESTING = 1",
    "LOG_LEVEL = DEBUG",
    "PYTHONPATH = .",
    "COVERAGE_CORE = sysmon"
]
asyncio_mode = "auto"
cache_dir = ".pytest_cache"
collect_ignore = [
    "setup.py",
    "build",
    "dist",
    ".git",
    ".tox",
    "__pycache__",
    "*.egg-info"
]
doctest_optionflags = "NORMALIZE_WHITESPACE IGNORE_EXCEPTION_DETAIL"
junit_suite_name = "FinRL_Crypto_Tests"
junit_logging = "system-out"
junit_log_passing_tests = false
junit_duration_report = "total"
junit_family = "xunit2"