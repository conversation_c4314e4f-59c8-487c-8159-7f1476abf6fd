{"tests/unit/test_training_module.py::TestTrainingConfig": true, "tests/unit/test_training_module.py::TestEvaluationMetrics": true, "tests/unit/test_training_module.py::TestTrainingModuleIntegration": true, "tests/unit/test_training_module.py::TestTrainingUtilities": true, "chonggoutest/test_backtest_module.py::TestBacktester::test_backtest_execution": true, "chonggoutest/test_backtest_module.py::TestBacktester::test_backtester_creation": true, "chonggoutest/test_backtest_module.py::TestBacktester::test_commission_and_slippage": true, "chonggoutest/test_backtest_module.py::TestBacktester::test_position_sizing": true, "chonggoutest/test_backtest_module.py::TestBacktester::test_risk_management": true, "chonggoutest/test_backtest_module.py::TestBacktestResult::test_backtest_result_creation": true, "chonggoutest/test_backtest_module.py::TestBacktestResult::test_drawdown_analysis": true, "chonggoutest/test_backtest_module.py::TestBacktestResult::test_monthly_returns": true, "chonggoutest/test_backtest_module.py::TestBacktestResult::test_performance_metrics_calculation": true, "chonggoutest/test_backtest_module.py::TestBacktestResult::test_trade_analysis": true, "chonggoutest/test_backtest_module.py::TestBacktestMetrics::test_advanced_metrics_calculation": true, "chonggoutest/test_backtest_module.py::TestBacktestMetrics::test_basic_metrics_calculation": true, "chonggoutest/test_backtest_module.py::TestBacktestMetrics::test_benchmark_comparison_metrics": true, "chonggoutest/test_backtest_module.py::TestBacktestMetrics::test_risk_metrics_calculation": true, "chonggoutest/test_backtest_module.py::TestBacktestVisualization::test_drawdown_plot": true, "chonggoutest/test_backtest_module.py::TestBacktestVisualization::test_portfolio_value_plot": true, "chonggoutest/test_backtest_module.py::TestBacktestVisualization::test_returns_distribution_plot": true, "chonggoutest/test_backtest_module.py::TestBacktestComparison::test_performance_ranking": true, "chonggoutest/test_backtest_module.py::TestBacktestComparison::test_strategy_comparison": true, "chonggoutest/test_backtest_module.py::TestBacktestOptimization::test_monte_carlo_simulation": true, "chonggoutest/test_backtest_module.py::TestBacktestOptimization::test_walk_forward_analysis": true, "chonggoutest/test_risk_module.py::TestRiskManager::test_correlation_check": true, "chonggoutest/test_risk_module.py::TestRiskManager::test_portfolio_risk_check": true, "chonggoutest/test_risk_module.py::TestRiskManager::test_position_risk_check": true, "chonggoutest/test_risk_module.py::TestRiskManager::test_risk_manager_creation": true, "chonggoutest/test_risk_module.py::TestRiskManager::test_stop_loss_take_profit": true, "chonggoutest/test_risk_module.py::TestPositionSizer::test_kelly_criterion": true, "chonggoutest/test_risk_module.py::TestPositionSizer::test_position_size_constraints": true, "chonggoutest/test_risk_module.py::TestPositionSizer::test_position_sizer_creation": true, "chonggoutest/test_risk_module.py::TestPositionSizer::test_risk_parity_sizing": true, "chonggoutest/test_risk_module.py::TestPositionSizer::test_volatility_targeting": true, "chonggoutest/test_risk_module.py::TestRiskMetrics::test_cvar_calculation": true, "chonggoutest/test_risk_module.py::TestRiskMetrics::test_downside_risk_metrics": true, "chonggoutest/test_risk_module.py::TestRiskMetrics::test_maximum_drawdown": true, "chonggoutest/test_risk_module.py::TestRiskMetrics::test_tail_risk_metrics": true, "chonggoutest/test_risk_module.py::TestRiskMetrics::test_var_calculation": true, "chonggoutest/test_risk_module.py::TestPortfolioRisk::test_component_risk_contribution": true, "chonggoutest/test_risk_module.py::TestPortfolioRisk::test_diversification_ratio": true, "chonggoutest/test_risk_module.py::TestPortfolioRisk::test_marginal_risk_contribution": true, "chonggoutest/test_risk_module.py::TestPortfolioRisk::test_portfolio_risk_creation": true, "chonggoutest/test_risk_module.py::TestPortfolioRisk::test_portfolio_var": true, "chonggoutest/test_risk_module.py::TestPortfolioRisk::test_portfolio_volatility": true, "chonggoutest/test_risk_module.py::TestStressTest::test_historical_stress_test": true, "chonggoutest/test_risk_module.py::TestStressTest::test_monte_carlo_stress_test": true, "chonggoutest/test_risk_module.py::TestStressTest::test_scenario_stress_test": true, "chonggoutest/test_risk_module.py::TestStressTest::test_stress_test_creation": true, "chonggoutest/test_risk_module.py::TestStressTest::test_stress_test_reporting": true, "chonggoutest/test_risk_module.py::TestRiskReporting::test_risk_alerts": true, "chonggoutest/test_risk_module.py::TestRiskReporting::test_risk_dashboard": true, "chonggoutest/test_strategy_module.py::TestBaseStrategy::test_base_strategy_interface": true, "chonggoutest/test_strategy_module.py::TestBaseStrategy::test_signal_generation_interface": true, "chonggoutest/test_strategy_module.py::TestBaseStrategy::test_strategy_initialization": true, "chonggoutest/test_strategy_module.py::TestMovingAverageStrategy::test_moving_average_parameter_update": true, "chonggoutest/test_strategy_module.py::TestMovingAverageStrategy::test_moving_average_signal_generation": true, "chonggoutest/test_strategy_module.py::TestMovingAverageStrategy::test_moving_average_strategy_creation": true, "chonggoutest/test_strategy_module.py::TestRSIStrategy::test_rsi_signal_generation": true, "chonggoutest/test_strategy_module.py::TestRSIStrategy::test_rsi_strategy_creation": true, "chonggoutest/test_strategy_module.py::TestRLStrategy::test_rl_model_integration": true, "chonggoutest/test_strategy_module.py::TestRLStrategy::test_rl_strategy_creation": true, "chonggoutest/test_strategy_module.py::TestStrategyComposition::test_composite_signal_generation": true, "chonggoutest/test_strategy_module.py::TestStrategyComposition::test_strategy_composition_creation": true, "chonggoutest/test_strategy_module.py::TestStrategyOptimization::test_parameter_optimization": true, "chonggoutest/test_strategy_module.py::TestStrategyOptimization::test_strategy_optimizer_creation": true, "chonggoutest/test_strategy_module.py::TestStrategyFactory::test_strategy_creation_from_factory": true, "chonggoutest/test_strategy_module.py::TestStrategyFactory::test_strategy_factory_creation": true, "chonggoutest/test_strategy_module.py::TestStrategyPerformanceMetrics::test_benchmark_comparison": true, "chonggoutest/test_strategy_module.py::TestStrategyPerformanceMetrics::test_strategy_performance_calculation": true, "chonggoutest/test_visualization_module.py::TestChartGenerator::test_candlestick_chart": true, "chonggoutest/test_visualization_module.py::TestChartGenerator::test_chart_customization": true, "chonggoutest/test_visualization_module.py::TestChartGenerator::test_chart_generator_creation": true, "chonggoutest/test_visualization_module.py::TestChartGenerator::test_heatmap": true, "chonggoutest/test_visualization_module.py::TestChartGenerator::test_line_chart": true, "chonggoutest/test_visualization_module.py::TestChartGenerator::test_scatter_plot": true, "chonggoutest/test_visualization_module.py::TestPerformanceVisualizer::test_cumulative_returns_plot": true, "chonggoutest/test_visualization_module.py::TestPerformanceVisualizer::test_drawdown_plot": true, "chonggoutest/test_visualization_module.py::TestPerformanceVisualizer::test_monthly_returns_heatmap": true, "chonggoutest/test_visualization_module.py::TestPerformanceVisualizer::test_performance_metrics_table": true, "chonggoutest/test_visualization_module.py::TestPerformanceVisualizer::test_performance_visualizer_creation": true, "chonggoutest/test_visualization_module.py::TestPerformanceVisualizer::test_returns_distribution_plot": true, "chonggoutest/test_visualization_module.py::TestPerformanceVisualizer::test_rolling_metrics_plot": true, "chonggoutest/test_visualization_module.py::TestRiskVisualizer::test_correlation_heatmap": true, "chonggoutest/test_visualization_module.py::TestRiskVisualizer::test_risk_contribution_plot": true, "chonggoutest/test_visualization_module.py::TestRiskVisualizer::test_risk_visualizer_creation": true, "chonggoutest/test_visualization_module.py::TestRiskVisualizer::test_stress_test_visualization": true, "chonggoutest/test_visualization_module.py::TestRiskVisualizer::test_tail_risk_visualization": true, "chonggoutest/test_visualization_module.py::TestRiskVisualizer::test_var_plot": true, "chonggoutest/test_visualization_module.py::TestPortfolioVisualizer::test_asset_allocation_over_time": true, "chonggoutest/test_visualization_module.py::TestPortfolioVisualizer::test_correlation_network_plot": true, "chonggoutest/test_visualization_module.py::TestPortfolioVisualizer::test_efficient_frontier_plot": true, "chonggoutest/test_visualization_module.py::TestPortfolioVisualizer::test_portfolio_composition_plot": true, "chonggoutest/test_visualization_module.py::TestPortfolioVisualizer::test_portfolio_performance_attribution": true, "chonggoutest/test_visualization_module.py::TestPortfolioVisualizer::test_portfolio_visualizer_creation": true, "chonggoutest/test_visualization_module.py::TestInteractiveDashboard::test_dashboard_components": true, "chonggoutest/test_visualization_module.py::TestInteractiveDashboard::test_dashboard_creation": true, "chonggoutest/test_visualization_module.py::TestInteractiveDashboard::test_dashboard_export": true, "chonggoutest/test_visualization_module.py::TestInteractiveDashboard::test_real_time_updates": true, "chonggoutest/test_visualization_module.py::TestVisualizationUtils::test_chart_annotations": true, "chonggoutest/test_visualization_module.py::TestVisualizationUtils::test_chart_styling": true, "chonggoutest/test_visualization_module.py::TestVisualizationUtils::test_color_palette_generation": true, "chonggoutest/test_visualization_module.py::TestVisualizationUtils::test_data_formatting": true, "improvetest/test_dependency_injection_unit.py::TestServiceDescriptor::test_service_descriptor_creation": true, "improvetest/test_dependency_injection_unit.py::TestServiceDescriptor::test_service_descriptor_with_interface": true, "improvetest/test_dependency_injection_unit.py::TestDependencyInjectionContainer::test_circular_dependency_detection": true, "improvetest/test_phase1_environment_module.py::TestBaseEnvironment::test_base_environment_initialization": true, "improvetest/test_phase1_environment_module.py::TestBaseEnvironment::test_base_environment_with_optional_params": true, "improvetest/test_phase1_environment_module.py::TestBaseEnvironment::test_gym_env_inheritance": true, "improvetest/test_phase1_environment_module.py::TestEnvironmentIntegration::test_complete_trading_episode": true, "improvetest/test_phase1_training_module.py::TestSingleAssetTrainer::test_training_with_validation": true, "improvetest/test_phase1_training_module.py::TestPortfolioTrainer::test_portfolio_initialization": true, "improvetest/test_phase1_training_module.py::TestPortfolioTrainer::test_position_management": true, "improvetest/test_phase1_training_module.py::TestPortfolioTrainer::test_risk_management": true, "improvetest/test_phase1_training_module.py::TestTrainerFactory::test_create_multi_asset_trainer": true, "improvetest/test_phase1_training_module.py::TestTrainerFactory::test_create_portfolio_trainer": true}