# FinRL Crypto 第一阶段测试依赖
# 运行测试套件所需的Python包

# 核心测试框架
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-html>=3.1.0
pytest-mock>=3.10.0
pytest-xdist>=3.0.0  # 并行测试支持
pytest-timeout>=2.1.0  # 测试超时控制
pytest-benchmark>=4.0.0  # 性能基准测试

# 数据处理和科学计算
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0
scikit-learn>=1.0.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# 机器学习和强化学习
stable-baselines3>=1.7.0
gym>=0.21.0
torch>=1.12.0
tensorflow>=2.8.0  # 可选，如果使用TensorFlow

# 配置和数据格式
pyyaml>=6.0
jsonschema>=4.0.0
toml>=0.10.0
configparser>=5.0.0

# 网络和API
requests>=2.28.0
requests-mock>=1.9.0
httpx>=0.23.0
aiohttp>=3.8.0

# 数据库和缓存（测试用）
sqlalchemy>=1.4.0
redis>=4.0.0

# 加密货币数据源
ccxt>=2.0.0  # 加密货币交易所API
yfinance>=0.1.87  # Yahoo Finance数据
alpaca-trade-api>=2.0.0  # Alpaca交易API

# 时间和日期处理
python-dateutil>=2.8.0
pytz>=2022.1
arrow>=1.2.0

# 并发和异步
aiofiles>=0.8.0
asyncio-throttle>=1.0.0

# 日志和监控
loguru>=0.6.0
rich>=12.0.0  # 美化终端输出
tqdm>=4.64.0  # 进度条

# 代码质量和格式化
black>=22.0.0
flake8>=5.0.0
isort>=5.10.0
mypy>=0.991
pylint>=2.15.0

# 安全和验证
cryptography>=37.0.0
pyjwt>=2.4.0

# 开发工具
ipython>=8.0.0
jupyter>=1.0.0
notebook>=6.4.0

# 性能分析
memory-profiler>=0.60.0
line-profiler>=4.0.0
py-spy>=0.3.0

# 文档生成
sphinx>=5.0.0
sphinx-rtd-theme>=1.0.0

# 环境管理
python-dotenv>=0.20.0

# 数据验证
pydantic>=1.10.0
marshmallow>=3.17.0

# 文件处理
openpyxl>=3.0.0  # Excel文件
h5py>=3.7.0  # HDF5文件
parquet>=1.3.0  # Parquet文件

# 统计和金融计算
statsmodels>=0.13.0
arch>=5.3.0  # GARCH模型
ta-lib>=0.4.0  # 技术分析库
quantlib>=1.29  # 量化金融库

# 风险管理
riskfolio-lib>=3.0.0
portfolio-optimization>=1.0.0

# 回测框架
backtrader>=1.9.0
zipline>=2.2.0
vectorbt>=0.25.0

# 数据存储
sqlite3  # Python内置
psycopg2-binary>=2.9.0  # PostgreSQL
pymongo>=4.0.0  # MongoDB

# 消息队列
celery>=5.2.0
redis>=4.0.0

# 监控和指标
prometheus-client>=0.14.0
grafana-api>=1.0.0

# 容器化支持
docker>=6.0.0
kubernetes>=24.0.0

# 云服务
boto3>=1.24.0  # AWS
azure-storage-blob>=12.0.0  # Azure
google-cloud-storage>=2.5.0  # GCP

# 实时数据流
kafka-python>=2.0.0
websocket-client>=1.4.0

# 数学和优化
cvxpy>=1.2.0
scipy>=1.9.0
numba>=0.56.0  # JIT编译加速

# 并行计算
joblib>=1.1.0
dask>=2022.8.0
ray>=2.0.0

# 图形和网络分析
networkx>=2.8.0
igraph>=0.10.0

# 自然语言处理（用于新闻分析）
nltk>=3.7.0
spacy>=3.4.0
transformers>=4.21.0

# 图像处理（用于图表分析）
Pillow>=9.2.0
opencv-python>=4.6.0

# 地理数据（用于全球市场分析）
geopandas>=0.11.0
folium>=0.12.0

# 时间序列分析
statsforecast>=1.0.0
sktime>=0.13.0
prophet>=1.1.0

# 特征工程
feature-engine>=1.4.0
tsfresh>=0.19.0

# 模型解释性
shap>=0.41.0
lime>=0.2.0
eli5>=0.13.0

# 超参数优化
optuna>=3.0.0
hyperopt>=0.2.7
scikit-optimize>=0.9.0

# 实验跟踪
mlflow>=1.28.0
wandb>=0.13.0
neptune-client>=0.16.0

# 数据版本控制
dvc>=2.12.0
git-lfs>=1.0.0

# API开发和测试
fastapi>=0.85.0
uvicorn>=0.18.0
starlette>=0.20.0

# 配置管理
hydra-core>=1.2.0
omegaconf>=2.2.0

# 缓存
diskcache>=5.4.0
joblib>=1.1.0

# 序列化
pickle5>=0.0.12
dill>=0.3.5

# 压缩
lz4>=4.0.0
zstandard>=0.18.0

# 系统监控
psutil>=5.9.0
GPUtil>=1.4.0

# 代码覆盖率增强
coverage[toml]>=6.4.0
coveragepy-lcov>=0.1.0

# 测试数据生成
faker>=15.0.0
factory-boy>=3.2.0
hypothesis>=6.54.0

# 性能测试
locust>=2.11.0
pytest-benchmark>=4.0.0

# 内存分析
memory-profiler>=0.60.0
tracemalloc  # Python内置

# 调试工具
pdbpp>=0.10.0
ipdb>=0.13.0

# 类型检查
mypy>=0.991
typeguard>=2.13.0

# 文档测试
doctest  # Python内置
pytest-doctestplus>=0.12.0

# 安全测试
bandit>=1.7.0
safety>=2.1.0

# 代码复杂度分析
radon>=5.1.0
xenon>=0.9.0

# 依赖分析
pipdeptree>=2.3.0
pip-audit>=2.4.0

# 环境隔离
virtualenv>=20.16.0
conda>=4.14.0

# 版本管理
bump2version>=1.0.0
semver>=2.13.0