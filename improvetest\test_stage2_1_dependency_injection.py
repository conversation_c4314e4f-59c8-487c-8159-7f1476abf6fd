#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件：test_stage2_1_dependency_injection.py
目标：测试依赖注入容器的功能
作者：AI Assistant
日期：2024

测试覆盖范围：
1. 依赖注入容器基本功能
2. 服务注册和解析
3. 单例和瞬态生命周期管理
4. 循环依赖检测
5. 配置驱动的依赖管理
6. 异常处理和错误场景
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any, Optional, Type, Protocol
from abc import ABC, abstractmethod
import threading
import time

# 导入待测试的模块（这些模块将在实现阶段创建）
try:
    from core.dependency_injection import (
        DIContainer,
        ServiceLifetime,
        ServiceDescriptor,
        DIError,
        CircularDependencyError,
        ServiceNotFoundError,
        InvalidServiceError
    )
except ImportError:
    # 在实现之前，这些类还不存在，我们先定义占位符
    class DIContainer:
        pass
    
    class ServiceLifetime:
        SINGLETON = "singleton"
        TRANSIENT = "transient"
        SCOPED = "scoped"
    
    class ServiceDescriptor:
        pass
    
    class DIError(Exception):
        pass
    
    class CircularDependencyError(DIError):
        pass
    
    class ServiceNotFoundError(DIError):
        pass
    
    class InvalidServiceError(DIError):
        pass


class TestDIContainer(unittest.TestCase):
    """测试依赖注入容器的核心功能"""
    
    def setUp(self):
        """每个测试前的设置"""
        self.container = DIContainer()
    
    def test_container_initialization(self):
        """测试容器初始化"""
        container = DIContainer()
        self.assertIsNotNone(container)
        # 容器会自动注册自身，所以服务数量为1
        self.assertEqual(len(container.get_registered_services()), 1)
        self.assertTrue(container.is_registered(DIContainer))
    
    def test_register_singleton_service(self):
        """测试注册单例服务"""
        # 定义测试服务
        class TestService:
            def __init__(self):
                self.value = "test"
        
        # 注册服务
        self.container.register_singleton(TestService)
        
        # 验证注册
        self.assertTrue(self.container.is_registered(TestService))
        
        # 获取服务实例
        instance1 = self.container.get_service(TestService)
        instance2 = self.container.get_service(TestService)
        
        # 验证单例行为
        self.assertIsInstance(instance1, TestService)
        self.assertIs(instance1, instance2)  # 同一个实例
        self.assertEqual(instance1.value, "test")
    
    def test_register_transient_service(self):
        """测试注册瞬态服务"""
        class TestService:
            def __init__(self):
                self.id = id(self)
        
        # 注册瞬态服务
        self.container.register_transient(TestService)
        
        # 获取多个实例
        instance1 = self.container.get_service(TestService)
        instance2 = self.container.get_service(TestService)
        
        # 验证瞬态行为
        self.assertIsInstance(instance1, TestService)
        self.assertIsInstance(instance2, TestService)
        self.assertIsNot(instance1, instance2)  # 不同实例
        self.assertNotEqual(instance1.id, instance2.id)
    
    def test_register_with_interface(self):
        """测试接口注册"""
        # 定义接口和实现
        class ITestService(Protocol):
            def get_value(self) -> str:
                ...
        
        class TestServiceImpl:
            def get_value(self) -> str:
                return "implementation"
        
        # 注册接口映射
        self.container.register_singleton(ITestService, TestServiceImpl)
        
        # 通过接口获取服务
        service = self.container.get_service(ITestService)
        self.assertIsInstance(service, TestServiceImpl)
        self.assertEqual(service.get_value(), "implementation")
    
    def test_register_with_factory(self):
        """测试工厂函数注册"""
        class TestService:
            def __init__(self, value: str):
                self.value = value
        
        # 使用工厂函数注册
        factory = lambda: TestService("factory_created")
        self.container.register_factory(TestService, factory, ServiceLifetime.SINGLETON)
        
        # 获取服务
        service = self.container.get_service(TestService)
        self.assertEqual(service.value, "factory_created")
    
    def test_dependency_injection(self):
        """测试依赖注入"""
        # 定义依赖服务
        class DatabaseService:
            def __init__(self):
                self.connected = True
        
        class UserService:
            def __init__(self, db: DatabaseService):
                self.db = db
            
            def get_user(self, user_id: int):
                if self.db.connected:
                    return f"User {user_id}"
                return None
        
        # 注册服务
        self.container.register_singleton(DatabaseService)
        self.container.register_singleton(UserService)
        
        # 获取服务（应该自动注入依赖）
        user_service = self.container.get_service(UserService)
        
        self.assertIsInstance(user_service, UserService)
        self.assertIsInstance(user_service.db, DatabaseService)
        self.assertTrue(user_service.db.connected)
        self.assertEqual(user_service.get_user(1), "User 1")
    
    def test_circular_dependency_detection(self):
        """测试循环依赖检测"""
        class ServiceA:
            def __init__(self, service_b: 'ServiceB'):
                self.service_b = service_b
        
        class ServiceB:
            def __init__(self, service_a: ServiceA):
                self.service_a = service_a
        
        # 注册循环依赖的服务
        self.container.register_singleton(ServiceA)
        self.container.register_singleton(ServiceB)
        
        # 应该抛出循环依赖异常
        with self.assertRaises(CircularDependencyError):
            self.container.get_service(ServiceA)
    
    def test_service_not_found(self):
        """测试服务未找到异常"""
        class UnregisteredService:
            pass
        
        with self.assertRaises(ServiceNotFoundError):
            self.container.get_service(UnregisteredService)
    
    def test_thread_safety(self):
        """测试线程安全性"""
        class TestService:
            def __init__(self):
                self.created_at = time.time()
                time.sleep(0.01)  # 模拟初始化时间
        
        self.container.register_singleton(TestService)
        
        instances = []
        
        def get_service():
            instance = self.container.get_service(TestService)
            instances.append(instance)
        
        # 创建多个线程同时获取服务
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=get_service)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证所有实例都是同一个（单例）
        self.assertEqual(len(instances), 10)
        first_instance = instances[0]
        for instance in instances:
            self.assertIs(instance, first_instance)


class TestServiceDescriptor(unittest.TestCase):
    """测试服务描述符"""
    
    def test_service_descriptor_creation(self):
        """测试服务描述符创建"""
        class TestService:
            pass
        
        descriptor = ServiceDescriptor(
            service_type=TestService,
            implementation_type=TestService,
            lifetime=ServiceLifetime.SINGLETON
        )
        
        self.assertEqual(descriptor.service_type, TestService)
        self.assertEqual(descriptor.implementation_type, TestService)
        self.assertEqual(descriptor.lifetime, ServiceLifetime.SINGLETON)
        self.assertIsNone(descriptor.factory)
    
    def test_service_descriptor_with_factory(self):
        """测试带工厂的服务描述符"""
        class TestService:
            def __init__(self, value: str):
                self.value = value
        
        factory = lambda: TestService("test")
        
        descriptor = ServiceDescriptor(
            service_type=TestService,
            factory=factory,
            lifetime=ServiceLifetime.TRANSIENT
        )
        
        self.assertEqual(descriptor.service_type, TestService)
        self.assertIsNone(descriptor.implementation_type)
        self.assertEqual(descriptor.factory, factory)
        self.assertEqual(descriptor.lifetime, ServiceLifetime.TRANSIENT)


class TestConfigurationDrivenDI(unittest.TestCase):
    """测试配置驱动的依赖注入"""
    
    def setUp(self):
        self.container = DIContainer()
    
    def test_load_from_config(self):
        """测试从配置加载服务"""
        config = {
            "services": [
                {
                    "service_type": "test_module.DatabaseService",
                    "implementation_type": "test_module.SqlDatabaseService",
                    "lifetime": "singleton"
                },
                {
                    "service_type": "test_module.UserService",
                    "implementation_type": "test_module.UserService",
                    "lifetime": "transient"
                }
            ]
        }
        
        # 模拟配置加载
        with patch.object(self.container, 'load_from_config') as mock_load:
            self.container.load_from_config(config)
            mock_load.assert_called_once_with(config)
    
    def test_validate_configuration(self):
        """测试配置验证"""
        # 有效配置
        valid_config = {
            "services": [
                {
                    "service_type": "test.Service",
                    "implementation_type": "test.ServiceImpl",
                    "lifetime": "singleton"
                }
            ]
        }
        
        # 无效配置（缺少必需字段）
        invalid_config = {
            "services": [
                {
                    "service_type": "test.Service"
                    # 缺少 implementation_type 和 lifetime
                }
            ]
        }
        
        # 验证配置
        self.assertTrue(self.container.validate_config(valid_config))
        
        with self.assertRaises(InvalidServiceError):
            self.container.validate_config(invalid_config)


class TestDIContainerIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_complex_dependency_graph(self):
        """测试复杂依赖图"""
        # 定义复杂的服务依赖关系
        class ConfigService:
            def __init__(self):
                self.database_url = "sqlite:///:memory:"
        
        class DatabaseService:
            def __init__(self, config: ConfigService):
                self.config = config
                self.connected = True
        
        class CacheService:
            def __init__(self):
                self.cache = {}
        
        class UserRepository:
            def __init__(self, db: DatabaseService, cache: CacheService):
                self.db = db
                self.cache = cache
        
        class UserService:
            def __init__(self, repo: UserRepository, config: ConfigService):
                self.repo = repo
                self.config = config
        
        # 注册所有服务
        container = DIContainer()
        container.register_singleton(ConfigService)
        container.register_singleton(DatabaseService)
        container.register_singleton(CacheService)
        container.register_singleton(UserRepository)
        container.register_singleton(UserService)
        
        # 获取顶层服务
        user_service = container.get_service(UserService)
        
        # 验证依赖注入正确
        self.assertIsInstance(user_service, UserService)
        self.assertIsInstance(user_service.repo, UserRepository)
        self.assertIsInstance(user_service.repo.db, DatabaseService)
        self.assertIsInstance(user_service.repo.cache, CacheService)
        self.assertIsInstance(user_service.config, ConfigService)
        
        # 验证单例行为
        config1 = user_service.config
        config2 = user_service.repo.db.config
        self.assertIs(config1, config2)
    
    def test_mixed_lifetimes(self):
        """测试混合生命周期"""
        class SingletonService:
            def __init__(self):
                self.id = id(self)
        
        class TransientService:
            def __init__(self, singleton: SingletonService):
                self.singleton = singleton
                self.id = id(self)
        
        container = DIContainer()
        container.register_singleton(SingletonService)
        container.register_transient(TransientService)
        
        # 获取多个瞬态服务实例
        transient1 = container.get_service(TransientService)
        transient2 = container.get_service(TransientService)
        
        # 验证瞬态服务是不同实例
        self.assertIsNot(transient1, transient2)
        self.assertNotEqual(transient1.id, transient2.id)
        
        # 验证单例服务是同一实例
        self.assertIs(transient1.singleton, transient2.singleton)
        self.assertEqual(transient1.singleton.id, transient2.singleton.id)


class TestDIContainerPerformance(unittest.TestCase):
    """性能测试"""
    
    def test_service_resolution_performance(self):
        """测试服务解析性能"""
        class TestService:
            def __init__(self):
                self.value = "test"
        
        container = DIContainer()
        container.register_singleton(TestService)
        
        # 预热
        container.get_service(TestService)
        
        # 性能测试
        start_time = time.time()
        for _ in range(1000):
            service = container.get_service(TestService)
        end_time = time.time()
        
        # 验证性能（1000次解析应该在合理时间内完成）
        elapsed = end_time - start_time
        self.assertLess(elapsed, 1.0)  # 应该在1秒内完成
    
    def test_concurrent_access_performance(self):
        """测试并发访问性能"""
        class TestService:
            def __init__(self):
                self.value = "test"
        
        container = DIContainer()
        container.register_singleton(TestService)
        
        def worker():
            for _ in range(100):
                service = container.get_service(TestService)
        
        # 并发测试
        start_time = time.time()
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=worker)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # 验证并发性能
        self.assertLess(elapsed, 2.0)  # 10个线程各100次调用应该在2秒内完成


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)