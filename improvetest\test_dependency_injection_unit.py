# -*- coding: utf-8 -*-
"""
依赖注入模块单元测试

测试 core/dependency_injection.py 的各项功能
"""

import unittest
import threading
import time
from unittest.mock import Mock, patch, MagicMock
from typing import Protocol, Optional
from abc import ABC, abstractmethod

# 导入被测试的模块
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
from dependency_injection import (
    ServiceLifetime,
    ServiceDescriptor
)


class TestServiceLifetime(unittest.TestCase):
    """测试服务生命周期枚举"""
    
    def test_singleton_lifetime(self):
        """测试单例生命周期"""
        self.assertEqual(ServiceLifetime.SINGLETON.value, "singleton")
    
    def test_transient_lifetime(self):
        """测试瞬态生命周期"""
        self.assertEqual(ServiceLifetime.TRANSIENT.value, "transient")
    
    def test_scoped_lifetime(self):
        """测试作用域生命周期"""
        self.assertEqual(ServiceLifetime.SCOPED.value, "scoped")
    
    def test_all_lifetimes_exist(self):
        """测试所有生命周期类型都存在"""
        lifetimes = [item.value for item in ServiceLifetime]
        expected = ["singleton", "transient", "scoped"]
        self.assertEqual(sorted(lifetimes), sorted(expected))


class TestServiceDescriptor(unittest.TestCase):
    """测试服务描述符"""
    
    def test_service_descriptor_creation(self):
        """测试服务描述符创建"""
        # 创建一个简单的服务类型
        class TestService:
            pass
        
        descriptor = ServiceDescriptor(
            service_type=TestService
        )
        
        self.assertEqual(descriptor.service_type, TestService)
    
    def test_service_descriptor_with_interface(self):
        """测试带接口的服务描述符"""
        # 定义接口
        class ITestService(ABC):
            @abstractmethod
            def do_something(self) -> str:
                pass
        
        # 定义实现
        class TestServiceImpl(ITestService):
            def do_something(self) -> str:
                return "test"
        
        descriptor = ServiceDescriptor(
            service_type=ITestService
        )
        
        self.assertEqual(descriptor.service_type, ITestService)


# 定义测试用的接口和实现
class IRepository(ABC):
    """仓储接口"""
    @abstractmethod
    def get_data(self) -> str:
        pass


class IService(ABC):
    """服务接口"""
    @abstractmethod
    def process(self) -> str:
        pass


class MockRepository(IRepository):
    """模拟仓储实现"""
    def __init__(self, data: str = "mock_data"):
        self.data = data
    
    def get_data(self) -> str:
        return self.data


class MockService(IService):
    """模拟服务实现"""
    def __init__(self, repository: IRepository):
        self.repository = repository
    
    def process(self) -> str:
        data = self.repository.get_data()
        return f"processed_{data}"


class SingletonService:
    """单例服务测试类"""
    _instance_count = 0
    
    def __init__(self):
        SingletonService._instance_count += 1
        self.instance_id = SingletonService._instance_count
    
    @classmethod
    def reset_count(cls):
        cls._instance_count = 0


class TransientService:
    """瞬态服务测试类"""
    _instance_count = 0
    
    def __init__(self):
        TransientService._instance_count += 1
        self.instance_id = TransientService._instance_count
    
    @classmethod
    def reset_count(cls):
        cls._instance_count = 0


class TestDependencyInjectionContainer(unittest.TestCase):
    """依赖注入容器测试"""
    
    def setUp(self):
        """设置测试环境"""
        # 重置实例计数
        SingletonService.reset_count()
        TransientService.reset_count()
    
    def test_service_registration(self):
        """测试服务注册"""
        # 由于我们只看到了ServiceDescriptor的定义，
        # 这里提供一个模拟的容器测试框架
        
        # 模拟容器
        class MockContainer:
            def __init__(self):
                self.services = {}
            
            def register(self, service_type, implementation, lifetime=ServiceLifetime.TRANSIENT):
                self.services[service_type] = {
                    'implementation': implementation,
                    'lifetime': lifetime,
                    'instance': None
                }
            
            def resolve(self, service_type):
                if service_type not in self.services:
                    raise ValueError(f"Service {service_type} not registered")
                
                service_info = self.services[service_type]
                
                if service_info['lifetime'] == ServiceLifetime.SINGLETON:
                    if service_info['instance'] is None:
                        service_info['instance'] = service_info['implementation']()
                    return service_info['instance']
                else:
                    return service_info['implementation']()
        
        container = MockContainer()
        
        # 注册服务
        container.register(IRepository, MockRepository, ServiceLifetime.SINGLETON)
        container.register(TransientService, TransientService, ServiceLifetime.TRANSIENT)
        
        # 验证注册
        self.assertIn(IRepository, container.services)
        self.assertIn(TransientService, container.services)
    
    def test_singleton_lifetime(self):
        """测试单例生命周期"""
        class MockContainer:
            def __init__(self):
                self.services = {}
            
            def register_singleton(self, service_type, implementation):
                self.services[service_type] = {
                    'implementation': implementation,
                    'lifetime': ServiceLifetime.SINGLETON,
                    'instance': None
                }
            
            def resolve(self, service_type):
                service_info = self.services[service_type]
                if service_info['instance'] is None:
                    service_info['instance'] = service_info['implementation']()
                return service_info['instance']
        
        container = MockContainer()
        container.register_singleton(SingletonService, SingletonService)
        
        # 多次解析应该返回同一个实例
        instance1 = container.resolve(SingletonService)
        instance2 = container.resolve(SingletonService)
        
        self.assertIs(instance1, instance2)
        self.assertEqual(instance1.instance_id, instance2.instance_id)
    
    def test_transient_lifetime(self):
        """测试瞬态生命周期"""
        class MockContainer:
            def __init__(self):
                self.services = {}
            
            def register_transient(self, service_type, implementation):
                self.services[service_type] = {
                    'implementation': implementation,
                    'lifetime': ServiceLifetime.TRANSIENT
                }
            
            def resolve(self, service_type):
                service_info = self.services[service_type]
                return service_info['implementation']()
        
        container = MockContainer()
        container.register_transient(TransientService, TransientService)
        
        # 多次解析应该返回不同的实例
        instance1 = container.resolve(TransientService)
        instance2 = container.resolve(TransientService)
        
        self.assertIsNot(instance1, instance2)
        self.assertNotEqual(instance1.instance_id, instance2.instance_id)
    
    def test_dependency_injection(self):
        """测试依赖注入"""
        class MockContainer:
            def __init__(self):
                self.services = {}
            
            def register(self, service_type, implementation, lifetime=ServiceLifetime.TRANSIENT):
                self.services[service_type] = {
                    'implementation': implementation,
                    'lifetime': lifetime,
                    'instance': None
                }
            
            def resolve(self, service_type):
                if service_type not in self.services:
                    raise ValueError(f"Service {service_type} not registered")
                
                service_info = self.services[service_type]
                implementation = service_info['implementation']
                
                # 简单的依赖注入实现
                import inspect
                sig = inspect.signature(implementation.__init__)
                params = []
                
                for param_name, param in sig.parameters.items():
                    if param_name == 'self':
                        continue
                    
                    # 假设参数类型就是要注入的服务类型
                    if param.annotation in self.services:
                        params.append(self.resolve(param.annotation))
                
                return implementation(*params)
        
        container = MockContainer()
        
        # 注册依赖
        container.register(IRepository, MockRepository, ServiceLifetime.SINGLETON)
        container.register(IService, MockService, ServiceLifetime.TRANSIENT)
        
        # 解析服务（应该自动注入依赖）
        service = container.resolve(IService)
        
        self.assertIsInstance(service, MockService)
        self.assertIsInstance(service.repository, MockRepository)
        
        # 测试服务功能
        result = service.process()
        self.assertEqual(result, "processed_mock_data")
    
    def test_circular_dependency_detection(self):
        """测试循环依赖检测"""
        # 定义循环依赖的类
        class ServiceA:
            def __init__(self, service_b):
                self.service_b = service_b
        
        class ServiceB:
            def __init__(self, service_a):
                self.service_a = service_a
        
        class MockContainer:
            def __init__(self):
                self.services = {}
                self.resolving = set()
            
            def register(self, service_type, implementation):
                self.services[service_type] = implementation
            
            def resolve(self, service_type):
                if service_type in self.resolving:
                    raise ValueError(f"Circular dependency detected: {service_type}")
                
                self.resolving.add(service_type)
                try:
                    implementation = self.services[service_type]
                    # 简化的依赖解析
                    return implementation()
                finally:
                    self.resolving.remove(service_type)
        
        container = MockContainer()
        container.register(ServiceA, ServiceA)
        container.register(ServiceB, ServiceB)
        
        # 应该检测到循环依赖
        with self.assertRaises(ValueError) as context:
            container.resolve(ServiceA)
        
        self.assertIn("Circular dependency", str(context.exception))
    
    def test_thread_safety(self):
        """测试线程安全"""
        class ThreadSafeContainer:
            def __init__(self):
                self.services = {}
                self.instances = {}
                self.lock = threading.Lock()
            
            def register_singleton(self, service_type, implementation):
                with self.lock:
                    self.services[service_type] = implementation
            
            def resolve(self, service_type):
                with self.lock:
                    if service_type not in self.instances:
                        self.instances[service_type] = self.services[service_type]()
                    return self.instances[service_type]
        
        container = ThreadSafeContainer()
        container.register_singleton(SingletonService, SingletonService)
        
        instances = []
        
        def resolve_service():
            instance = container.resolve(SingletonService)
            instances.append(instance)
        
        # 创建多个线程同时解析服务
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=resolve_service)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证所有实例都是同一个
        first_instance = instances[0]
        for instance in instances:
            self.assertIs(instance, first_instance)


class TestServiceConfiguration(unittest.TestCase):
    """测试服务配置"""
    
    def test_configuration_based_registration(self):
        """测试基于配置的服务注册"""
        # 模拟配置驱动的服务注册
        config = {
            "services": [
                {
                    "interface": "IRepository",
                    "implementation": "MockRepository",
                    "lifetime": "singleton"
                },
                {
                    "interface": "IService",
                    "implementation": "MockService",
                    "lifetime": "transient"
                }
            ]
        }
        
        class ConfigurableContainer:
            def __init__(self):
                self.services = {}
            
            def configure_from_dict(self, config_dict):
                for service_config in config_dict.get("services", []):
                    interface = service_config["interface"]
                    implementation = service_config["implementation"]
                    lifetime = ServiceLifetime(service_config["lifetime"])
                    
                    self.services[interface] = {
                        "implementation": implementation,
                        "lifetime": lifetime
                    }
        
        container = ConfigurableContainer()
        container.configure_from_dict(config)
        
        # 验证配置加载
        self.assertIn("IRepository", container.services)
        self.assertIn("IService", container.services)
        
        repo_config = container.services["IRepository"]
        self.assertEqual(repo_config["implementation"], "MockRepository")
        self.assertEqual(repo_config["lifetime"], ServiceLifetime.SINGLETON)
        
        service_config = container.services["IService"]
        self.assertEqual(service_config["implementation"], "MockService")
        self.assertEqual(service_config["lifetime"], ServiceLifetime.TRANSIENT)


if __name__ == '__main__':
    unittest.main()