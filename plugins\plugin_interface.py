"""插件接口定义模块

定义插件系统的核心接口和元数据结构。
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional
from enum import Enum


class PluginType(Enum):
    """插件类型枚举"""
    STRATEGY = "strategy"  # 策略插件
    INDICATOR = "indicator"  # 指标插件
    OPTIMIZER = "optimizer"  # 优化器插件
    MONITOR = "monitor"  # 监控插件
    DATA_PROCESSOR = "data_processor"  # 数据处理插件


@dataclass
class PluginMetadata:
    """插件元数据"""
    name: str
    version: str
    description: str
    author: str
    plugin_type: PluginType
    dependencies: List[str] = None
    config_schema: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.config_schema is None:
            self.config_schema = {}


class IPlugin(ABC):
    """插件基础接口
    
    所有插件都必须实现这个接口。
    """
    
    @property
    @abstractmethod
    def name(self) -> str:
        """插件名称
        
        Returns:
            str: 插件名称
        """
        pass
    
    @property
    @abstractmethod
    def version(self) -> str:
        """插件版本
        
        Returns:
            str: 插件版本
        """
        pass
    
    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化插件
        
        Args:
            config: 插件配置
            
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行插件主要功能
        
        Args:
            context: 执行上下文
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """清理插件资源"""
        pass
    
    @property
    @abstractmethod
    def metadata(self) -> PluginMetadata:
        """获取插件元数据
        
        Returns:
            PluginMetadata: 插件元数据
        """
        pass
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置
        
        Args:
            config: 配置字典
            
        Returns:
            bool: 配置是否有效
        """
        # 默认实现，子类可以重写
        return True
    
    @property
    def is_initialized(self) -> bool:
        """插件是否已初始化"""
        return self._is_initialized
    
    @property
    def is_active(self) -> bool:
        """插件是否处于活动状态"""
        return self._is_active
    
    def activate(self) -> bool:
        """激活插件"""
        if not self._is_initialized:
            if not self.initialize():
                return False
            self._is_initialized = True
        
        self._is_active = True
        return True
    
    def deactivate(self) -> bool:
        """停用插件"""
        self._is_active = False
        return True


class IStrategyPlugin(IPlugin):
    """策略插件接口"""
    
    @abstractmethod
    def generate_signals(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成交易信号
        
        Args:
            market_data: 市场数据
            
        Returns:
            Dict[str, Any]: 交易信号
        """
        pass


class IIndicatorPlugin(IPlugin):
    """指标插件接口"""
    
    @abstractmethod
    def calculate(self, data: List[float], **params) -> List[float]:
        """计算指标值
        
        Args:
            data: 输入数据
            **params: 计算参数
            
        Returns:
            List[float]: 指标值
        """
        pass


class IOptimizerPlugin(IPlugin):
    """优化器插件接口"""
    
    @abstractmethod
    def optimize(self, objective_function, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行优化
        
        Args:
            objective_function: 目标函数
            parameters: 优化参数
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        pass


class IMonitorPlugin(IPlugin):
    """监控插件接口"""
    
    @abstractmethod
    def start_monitoring(self) -> bool:
        """开始监控
        
        Returns:
            bool: 是否成功开始监控
        """
        pass
    
    @abstractmethod
    def stop_monitoring(self) -> bool:
        """停止监控
        
        Returns:
            bool: 是否成功停止监控
        """
        pass
    
    @abstractmethod
    def get_metrics(self) -> Dict[str, Any]:
        """获取监控指标
        
        Returns:
            Dict[str, Any]: 监控指标数据
        """
        pass