#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一阶段集成测试
测试已完成和部分完成的模块之间的集成功能
"""

import pytest
import asyncio
import tempfile
import os
import json
import yaml
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 导入被测试的模块
try:
    from core.config_manager import Config<PERSON>anager, ConfigLoader, ConfigLoadError, ConfigValidationError
    from core.dependency_injection import DIContainer, ServiceLifetime, ServiceDescriptor
    from finrl_crypto.data.base import DataProcessor
    from finrl_crypto.data.processors import BinanceProcessor
    from finrl_crypto.data.manager import DataManager
    from finrl_crypto.environment.base import BaseEnvironment
    from finrl_crypto.environment.crypto_trading import TradingEnvironment
    from finrl_crypto.agent.base import BaseAgent
    from finrl_crypto.agent.factory import AgentFactory
except ImportError as e:
    pytest.skip(f"Required modules not available: {e}", allow_module_level=True)


class TestPhase1Integration:
    """第一阶段集成测试类"""
    
    @pytest.fixture
    def temp_config_dir(self):
        """创建临时配置目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def sample_config(self):
        """示例配置数据"""
        return {
            "data": {
                "sources": ["binance", "yahoo"],
                "symbols": ["BTCUSDT", "ETHUSDT"],
                "timeframe": "1h",
                "lookback_days": 30
            },
            "environment": {
                "type": "trading",
                "initial_balance": 10000,
                "transaction_cost": 0.001,
                "max_position_size": 0.1
            },
            "agent": {
                "type": "ppo",
                "learning_rate": 0.0003,
                "batch_size": 64,
                "hidden_size": 256
            },
            "training": {
                "episodes": 1000,
                "max_steps": 1000,
                "save_interval": 100
            }
        }
    
    @pytest.fixture
    def di_container(self):
        """依赖注入容器"""
        container = DIContainer()
        return container
    
    @pytest.fixture
    def config_manager(self, temp_config_dir, sample_config):
        """配置管理器"""
        config_file = temp_config_dir / "config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)
        
        manager = ConfigManager()
        manager.load_config(str(config_file))
        return manager

    def test_config_di_integration(self, di_container, config_manager):
        """测试配置管理和依赖注入的集成"""
        # 注册配置管理器到DI容器
        di_container.register(
            ConfigManager,
            lambda: config_manager,
            ServiceLifetime.SINGLETON
        )
        
        # 从容器获取配置管理器
        retrieved_manager = di_container.resolve(ConfigManager)
        assert retrieved_manager is config_manager
        
        # 测试配置访问
        data_config = retrieved_manager.get_config("data")
        assert data_config["symbols"] == ["BTCUSDT", "ETHUSDT"]
        assert data_config["timeframe"] == "1h"

    def test_data_config_integration(self, config_manager, di_container):
        """测试数据模块与配置管理的集成"""
        # 注册配置管理器
        di_container.register(
            ConfigManager,
            lambda: config_manager,
            ServiceLifetime.SINGLETON
        )
        
        # 模拟数据处理器工厂
        def create_data_processor():
            config = di_container.resolve(ConfigManager)
            data_config = config.get_config("data")
            
            # 根据配置创建数据处理器
            if "binance" in data_config["sources"]:
                processor = Mock(spec=BinanceProcessor)
                processor.symbols = data_config["symbols"]
                processor.timeframe = data_config["timeframe"]
                return processor
            return None
        
        di_container.register(
            "DataProcessor",
            create_data_processor,
            ServiceLifetime.SINGLETON
        )
        
        # 测试数据处理器创建
        processor = di_container.resolve("DataProcessor")
        assert processor is not None
        assert processor.symbols == ["BTCUSDT", "ETHUSDT"]
        assert processor.timeframe == "1h"

    def test_environment_config_integration(self, config_manager, di_container):
        """测试环境模块与配置管理的集成"""
        # 注册配置管理器
        di_container.register(
            ConfigManager,
            lambda: config_manager,
            ServiceLifetime.SINGLETON
        )
        
        # 模拟环境工厂
        def create_environment():
            config = di_container.resolve(ConfigManager)
            env_config = config.get_config("environment")
            
            # 根据配置创建环境
            env = Mock(spec=TradingEnvironment)
            env.initial_balance = env_config["initial_balance"]
            env.transaction_cost = env_config["transaction_cost"]
            env.max_position_size = env_config["max_position_size"]
            return env
        
        di_container.register(
            "TradingEnvironment",
            create_environment,
            ServiceLifetime.TRANSIENT
        )
        
        # 测试环境创建
        env = di_container.resolve("TradingEnvironment")
        assert env.initial_balance == 10000
        assert env.transaction_cost == 0.001
        assert env.max_position_size == 0.1

    def test_agent_config_integration(self, config_manager, di_container):
        """测试代理模块与配置管理的集成"""
        # 注册配置管理器
        di_container.register(
            ConfigManager,
            lambda: config_manager,
            ServiceLifetime.SINGLETON
        )
        
        # 模拟代理工厂
        def create_agent():
            config = di_container.resolve(ConfigManager)
            agent_config = config.get_config("agent")
            
            # 根据配置创建代理
            agent = Mock(spec=BaseAgent)
            agent.agent_type = agent_config["type"]
            agent.learning_rate = agent_config["learning_rate"]
            agent.batch_size = agent_config["batch_size"]
            agent.hidden_size = agent_config["hidden_size"]
            return agent
        
        di_container.register(
            "Agent",
            create_agent,
            ServiceLifetime.SINGLETON
        )
        
        # 测试代理创建
        agent = di_container.resolve("Agent")
        assert agent.agent_type == "ppo"
        assert agent.learning_rate == 0.0003
        assert agent.batch_size == 64
        assert agent.hidden_size == 256

    def test_full_pipeline_integration(self, config_manager, di_container):
        """测试完整管道的集成"""
        # 注册所有服务
        di_container.register(
            ConfigManager,
            lambda: config_manager,
            ServiceLifetime.SINGLETON
        )
        
        # 数据管理器
        def create_data_manager():
            config = di_container.resolve(ConfigManager)
            data_config = config.get_config("data")
            
            manager = Mock(spec=DataManager)
            manager.symbols = data_config["symbols"]
            manager.timeframe = data_config["timeframe"]
            manager.lookback_days = data_config["lookback_days"]
            
            # 模拟数据获取
            def get_data(symbol, start_date, end_date):
                dates = pd.date_range(start_date, end_date, freq='H')
                return pd.DataFrame({
                    'timestamp': dates,
                    'open': np.random.uniform(100, 200, len(dates)),
                    'high': np.random.uniform(200, 300, len(dates)),
                    'low': np.random.uniform(50, 100, len(dates)),
                    'close': np.random.uniform(100, 200, len(dates)),
                    'volume': np.random.uniform(1000, 10000, len(dates))
                })
            
            manager.get_data = get_data
            return manager
        
        di_container.register(
            "DataManager",
            create_data_manager,
            ServiceLifetime.SINGLETON
        )
        
        # 环境
        def create_environment():
            config = di_container.resolve(ConfigManager)
            data_manager = di_container.resolve("DataManager")
            env_config = config.get_config("environment")
            
            env = Mock(spec=TradingEnvironment)
            env.data_manager = data_manager
            env.initial_balance = env_config["initial_balance"]
            env.current_balance = env_config["initial_balance"]
            env.transaction_cost = env_config["transaction_cost"]
            
            # 模拟环境步骤
            def step(action):
                # 简单的奖励计算
                reward = np.random.uniform(-1, 1)
                done = np.random.choice([True, False], p=[0.1, 0.9])
                obs = np.random.uniform(0, 1, 10)
                info = {"balance": env.current_balance}
                return obs, reward, done, info
            
            def reset():
                env.current_balance = env.initial_balance
                return np.random.uniform(0, 1, 10)
            
            env.step = step
            env.reset = reset
            return env
        
        di_container.register(
            "Environment",
            create_environment,
            ServiceLifetime.TRANSIENT
        )
        
        # 代理
        def create_agent():
            config = di_container.resolve(ConfigManager)
            agent_config = config.get_config("agent")
            
            agent = Mock(spec=BaseAgent)
            agent.agent_type = agent_config["type"]
            
            # 模拟预测
            def predict(observation):
                return np.random.choice([0, 1, 2])  # 买入、卖出、持有
            
            def train(experiences):
                return {"loss": np.random.uniform(0, 1)}
            
            agent.predict = predict
            agent.train = train
            return agent
        
        di_container.register(
            "Agent",
            create_agent,
            ServiceLifetime.SINGLETON
        )
        
        # 测试完整管道
        data_manager = di_container.resolve("DataManager")
        environment = di_container.resolve("Environment")
        agent = di_container.resolve("Agent")
        
        # 验证组件创建
        assert data_manager is not None
        assert environment is not None
        assert agent is not None
        
        # 测试数据流
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        data = data_manager.get_data("BTCUSDT", start_date, end_date)
        assert len(data) > 0
        assert "close" in data.columns
        
        # 测试环境交互
        obs = environment.reset()
        assert len(obs) == 10
        
        action = agent.predict(obs)
        assert action in [0, 1, 2]
        
        next_obs, reward, done, info = environment.step(action)
        assert len(next_obs) == 10
        assert isinstance(reward, (int, float))
        assert isinstance(done, bool)
        assert "balance" in info

    def test_config_validation_integration(self, temp_config_dir, di_container):
        """测试配置验证的集成"""
        # 创建无效配置
        invalid_config = {
            "data": {
                "symbols": [],  # 空符号列表
                "timeframe": "invalid"  # 无效时间框架
            },
            "environment": {
                "initial_balance": -1000  # 负余额
            }
        }
        
        config_file = temp_config_dir / "invalid_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(invalid_config, f)
        
        # 测试配置验证失败
        manager = ConfigManager()
        with pytest.raises((ConfigValidationError, ValueError)):
            manager.load_config(str(config_file))

    def test_service_lifecycle_integration(self, config_manager, di_container):
        """测试服务生命周期的集成"""
        # 注册单例服务
        di_container.register(
            "SingletonService",
            lambda: {"id": id(object()), "created_at": datetime.now()},
            ServiceLifetime.SINGLETON
        )
        
        # 注册瞬态服务
        di_container.register(
            "TransientService",
            lambda: {"id": id(object()), "created_at": datetime.now()},
            ServiceLifetime.TRANSIENT
        )
        
        # 测试单例行为
        singleton1 = di_container.resolve("SingletonService")
        singleton2 = di_container.resolve("SingletonService")
        assert singleton1["id"] == singleton2["id"]
        
        # 测试瞬态行为
        transient1 = di_container.resolve("TransientService")
        transient2 = di_container.resolve("TransientService")
        assert transient1["id"] != transient2["id"]

    def test_error_handling_integration(self, config_manager, di_container):
        """测试错误处理的集成"""
        # 注册会失败的服务
        def failing_service():
            raise ValueError("Service creation failed")
        
        di_container.register(
            "FailingService",
            failing_service,
            ServiceLifetime.SINGLETON
        )
        
        # 测试错误传播
        with pytest.raises(ValueError, match="Service creation failed"):
            di_container.resolve("FailingService")
        
        # 测试未注册服务
        with pytest.raises(KeyError):
            di_container.resolve("NonExistentService")

    @pytest.mark.asyncio
    async def test_async_integration(self, config_manager, di_container):
        """测试异步操作的集成"""
        # 注册异步服务
        async def async_data_service():
            await asyncio.sleep(0.1)  # 模拟异步操作
            return {"data": "async_result", "timestamp": datetime.now()}
        
        di_container.register(
            "AsyncDataService",
            async_data_service,
            ServiceLifetime.TRANSIENT
        )
        
        # 测试异步解析
        result = await di_container.resolve("AsyncDataService")
        assert result["data"] == "async_result"
        assert "timestamp" in result

    def test_configuration_override_integration(self, temp_config_dir, di_container):
        """测试配置覆盖的集成"""
        # 基础配置
        base_config = {
            "data": {"symbols": ["BTCUSDT"], "timeframe": "1h"},
            "environment": {"initial_balance": 10000}
        }
        
        # 覆盖配置
        override_config = {
            "data": {"symbols": ["ETHUSDT", "ADAUSDT"]},
            "environment": {"transaction_cost": 0.002}
        }
        
        base_file = temp_config_dir / "base.yaml"
        override_file = temp_config_dir / "override.yaml"
        
        with open(base_file, 'w') as f:
            yaml.dump(base_config, f)
        with open(override_file, 'w') as f:
            yaml.dump(override_config, f)
        
        # 加载并合并配置
        manager = ConfigManager()
        manager.load_config(str(base_file))
        manager.merge_config(str(override_file))
        
        # 验证配置合并
        data_config = manager.get_config("data")
        env_config = manager.get_config("environment")
        
        assert data_config["symbols"] == ["ETHUSDT", "ADAUSDT"]  # 覆盖
        assert data_config["timeframe"] == "1h"  # 保留
        assert env_config["initial_balance"] == 10000  # 保留
        assert env_config["transaction_cost"] == 0.002  # 新增


if __name__ == "__main__":
    pytest.main([__file__, "-v"])