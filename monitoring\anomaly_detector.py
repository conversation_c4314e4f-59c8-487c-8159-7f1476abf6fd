"""异常检测模块

检测系统和训练过程中的异常情况。
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Callable, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from collections import deque
import threading


class AnomalyType(Enum):
    """异常类型枚举"""
    PERFORMANCE_DEGRADATION = "performance_degradation"
    MEMORY_LEAK = "memory_leak"
    TRAINING_STAGNATION = "training_stagnation"
    LOSS_EXPLOSION = "loss_explosion"
    GRADIENT_VANISHING = "gradient_vanishing"
    SYSTEM_OVERLOAD = "system_overload"
    DATA_QUALITY = "data_quality"
    CUSTOM = "custom"


class AnomalySeverity(Enum):
    """异常严重程度枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class AnomalyEvent:
    """异常事件数据类"""
    timestamp: datetime
    anomaly_type: AnomalyType
    severity: AnomalySeverity
    description: str
    metric_name: str
    metric_value: float
    threshold: float
    confidence: float  # 0.0 - 1.0
    metadata: Dict[str, Any] = None
    resolved: bool = False
    resolution_time: Optional[datetime] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class StatisticalDetector:
    """统计异常检测器
    
    使用统计方法检测异常值。
    """
    
    def __init__(self, window_size: int = 100, z_threshold: float = 3.0):
        self.window_size = window_size
        self.z_threshold = z_threshold
        self._data_windows: Dict[str, deque] = {}
    
    def add_data_point(self, metric_name: str, value: float) -> Optional[AnomalyEvent]:
        """添加数据点并检测异常
        
        Args:
            metric_name: 指标名称
            value: 指标值
            
        Returns:
            Optional[AnomalyEvent]: 如果检测到异常则返回异常事件
        """
        if metric_name not in self._data_windows:
            self._data_windows[metric_name] = deque(maxlen=self.window_size)
        
        window = self._data_windows[metric_name]
        window.append(value)
        
        # 需要足够的数据点才能进行检测
        if len(window) < 10:
            return None
        
        # 计算Z分数
        data_array = np.array(window)
        mean = np.mean(data_array[:-1])  # 排除当前值
        std = np.std(data_array[:-1])
        
        if std == 0:
            return None
        
        z_score = abs((value - mean) / std)
        
        if z_score > self.z_threshold:
            confidence = min(z_score / self.z_threshold, 1.0)
            severity = self._determine_severity(z_score)
            
            return AnomalyEvent(
                timestamp=datetime.now(),
                anomaly_type=AnomalyType.CUSTOM,
                severity=severity,
                description=f"统计异常: {metric_name} 值 {value:.4f} 超出正常范围 (Z分数: {z_score:.2f})",
                metric_name=metric_name,
                metric_value=value,
                threshold=mean + self.z_threshold * std,
                confidence=confidence,
                metadata={
                    'z_score': z_score,
                    'mean': mean,
                    'std': std,
                    'window_size': len(window)
                }
            )
        
        return None
    
    def _determine_severity(self, z_score: float) -> AnomalySeverity:
        """根据Z分数确定严重程度
        
        Args:
            z_score: Z分数
            
        Returns:
            AnomalySeverity: 严重程度
        """
        if z_score > 5.0:
            return AnomalySeverity.CRITICAL
        elif z_score > 4.0:
            return AnomalySeverity.HIGH
        elif z_score > 3.5:
            return AnomalySeverity.MEDIUM
        else:
            return AnomalySeverity.LOW


class TrendDetector:
    """趋势异常检测器
    
    检测指标的异常趋势变化。
    """
    
    def __init__(self, window_size: int = 50, trend_threshold: float = 0.1):
        self.window_size = window_size
        self.trend_threshold = trend_threshold
        self._data_windows: Dict[str, deque] = {}
    
    def add_data_point(self, metric_name: str, value: float) -> Optional[AnomalyEvent]:
        """添加数据点并检测趋势异常
        
        Args:
            metric_name: 指标名称
            value: 指标值
            
        Returns:
            Optional[AnomalyEvent]: 如果检测到异常则返回异常事件
        """
        if metric_name not in self._data_windows:
            self._data_windows[metric_name] = deque(maxlen=self.window_size)
        
        window = self._data_windows[metric_name]
        window.append(value)
        
        # 需要足够的数据点才能进行趋势分析
        if len(window) < 20:
            return None
        
        # 计算线性趋势
        data_array = np.array(window)
        x = np.arange(len(data_array))
        
        # 使用最小二乘法拟合直线
        coeffs = np.polyfit(x, data_array, 1)
        slope = coeffs[0]
        
        # 计算相对趋势强度
        mean_value = np.mean(data_array)
        if mean_value != 0:
            relative_slope = abs(slope) / abs(mean_value)
        else:
            relative_slope = abs(slope)
        
        if relative_slope > self.trend_threshold:
            # 确定异常类型
            if slope > 0:
                if 'loss' in metric_name.lower():
                    anomaly_type = AnomalyType.LOSS_EXPLOSION
                elif 'memory' in metric_name.lower():
                    anomaly_type = AnomalyType.MEMORY_LEAK
                else:
                    anomaly_type = AnomalyType.PERFORMANCE_DEGRADATION
                description = f"检测到上升趋势异常: {metric_name}"
            else:
                if 'reward' in metric_name.lower() or 'profit' in metric_name.lower():
                    anomaly_type = AnomalyType.PERFORMANCE_DEGRADATION
                    description = f"检测到下降趋势异常: {metric_name}"
                else:
                    return None  # 下降趋势可能是正常的（如loss下降）
            
            confidence = min(relative_slope / self.trend_threshold, 1.0)
            severity = self._determine_severity(relative_slope)
            
            return AnomalyEvent(
                timestamp=datetime.now(),
                anomaly_type=anomaly_type,
                severity=severity,
                description=description,
                metric_name=metric_name,
                metric_value=value,
                threshold=self.trend_threshold,
                confidence=confidence,
                metadata={
                    'slope': slope,
                    'relative_slope': relative_slope,
                    'window_size': len(window),
                    'trend_direction': 'increasing' if slope > 0 else 'decreasing'
                }
            )
        
        return None
    
    def _determine_severity(self, relative_slope: float) -> AnomalySeverity:
        """根据相对斜率确定严重程度
        
        Args:
            relative_slope: 相对斜率
            
        Returns:
            AnomalySeverity: 严重程度
        """
        if relative_slope > 0.5:
            return AnomalySeverity.CRITICAL
        elif relative_slope > 0.3:
            return AnomalySeverity.HIGH
        elif relative_slope > 0.2:
            return AnomalySeverity.MEDIUM
        else:
            return AnomalySeverity.LOW


class ThresholdDetector:
    """阈值异常检测器
    
    基于预定义阈值检测异常。
    """
    
    def __init__(self):
        self._thresholds: Dict[str, Dict[str, float]] = {}
    
    def set_threshold(self, metric_name: str, min_value: Optional[float] = None, 
                    max_value: Optional[float] = None) -> None:
        """设置指标阈值
        
        Args:
            metric_name: 指标名称
            min_value: 最小值阈值
            max_value: 最大值阈值
        """
        self._thresholds[metric_name] = {}
        if min_value is not None:
            self._thresholds[metric_name]['min'] = min_value
        if max_value is not None:
            self._thresholds[metric_name]['max'] = max_value
    
    def check_threshold(self, metric_name: str, value: float) -> Optional[AnomalyEvent]:
        """检查阈值异常
        
        Args:
            metric_name: 指标名称
            value: 指标值
            
        Returns:
            Optional[AnomalyEvent]: 如果检测到异常则返回异常事件
        """
        if metric_name not in self._thresholds:
            return None
        
        thresholds = self._thresholds[metric_name]
        
        # 检查最小值阈值
        if 'min' in thresholds and value < thresholds['min']:
            severity = self._determine_severity_by_deviation(value, thresholds['min'], 'min')
            return AnomalyEvent(
                timestamp=datetime.now(),
                anomaly_type=self._determine_anomaly_type(metric_name, 'min'),
                severity=severity,
                description=f"指标 {metric_name} 值 {value:.4f} 低于最小阈值 {thresholds['min']:.4f}",
                metric_name=metric_name,
                metric_value=value,
                threshold=thresholds['min'],
                confidence=1.0,
                metadata={'threshold_type': 'min'}
            )
        
        # 检查最大值阈值
        if 'max' in thresholds and value > thresholds['max']:
            severity = self._determine_severity_by_deviation(value, thresholds['max'], 'max')
            return AnomalyEvent(
                timestamp=datetime.now(),
                anomaly_type=self._determine_anomaly_type(metric_name, 'max'),
                severity=severity,
                description=f"指标 {metric_name} 值 {value:.4f} 超过最大阈值 {thresholds['max']:.4f}",
                metric_name=metric_name,
                metric_value=value,
                threshold=thresholds['max'],
                confidence=1.0,
                metadata={'threshold_type': 'max'}
            )
        
        return None
    
    def _determine_anomaly_type(self, metric_name: str, threshold_type: str) -> AnomalyType:
        """根据指标名称和阈值类型确定异常类型
        
        Args:
            metric_name: 指标名称
            threshold_type: 阈值类型 ('min' 或 'max')
            
        Returns:
            AnomalyType: 异常类型
        """
        metric_lower = metric_name.lower()
        
        if 'memory' in metric_lower and threshold_type == 'max':
            return AnomalyType.MEMORY_LEAK
        elif 'cpu' in metric_lower and threshold_type == 'max':
            return AnomalyType.SYSTEM_OVERLOAD
        elif 'loss' in metric_lower and threshold_type == 'max':
            return AnomalyType.LOSS_EXPLOSION
        elif 'gradient' in metric_lower and threshold_type == 'min':
            return AnomalyType.GRADIENT_VANISHING
        else:
            return AnomalyType.CUSTOM
    
    def _determine_severity_by_deviation(self, value: float, threshold: float, 
                                       threshold_type: str) -> AnomalySeverity:
        """根据偏差程度确定严重程度
        
        Args:
            value: 实际值
            threshold: 阈值
            threshold_type: 阈值类型
            
        Returns:
            AnomalySeverity: 严重程度
        """
        if threshold_type == 'max':
            deviation_ratio = (value - threshold) / threshold
        else:  # min
            deviation_ratio = (threshold - value) / threshold
        
        if deviation_ratio > 1.0:  # 超过100%
            return AnomalySeverity.CRITICAL
        elif deviation_ratio > 0.5:  # 超过50%
            return AnomalySeverity.HIGH
        elif deviation_ratio > 0.2:  # 超过20%
            return AnomalySeverity.MEDIUM
        else:
            return AnomalySeverity.LOW


class AnomalyDetector:
    """异常检测器主类
    
    集成多种异常检测方法。
    """
    
    def __init__(self):
        self._statistical_detector = StatisticalDetector()
        self._trend_detector = TrendDetector()
        self._threshold_detector = ThresholdDetector()
        
        self._anomaly_history: List[AnomalyEvent] = []
        self._callbacks: List[Callable[[AnomalyEvent], None]] = []
        self._lock = threading.RLock()
        self._logger = logging.getLogger(__name__)
        
        # 配置
        self._max_history_size = 1000
        self._enabled_detectors = {
            'statistical': True,
            'trend': True,
            'threshold': True
        }
    
    def add_callback(self, callback: Callable[[AnomalyEvent], None]) -> None:
        """添加异常检测回调函数
        
        Args:
            callback: 回调函数，接收AnomalyEvent参数
        """
        with self._lock:
            self._callbacks.append(callback)
    
    def remove_callback(self, callback: Callable[[AnomalyEvent], None]) -> None:
        """移除异常检测回调函数
        
        Args:
            callback: 要移除的回调函数
        """
        with self._lock:
            if callback in self._callbacks:
                self._callbacks.remove(callback)
    
    def set_threshold(self, metric_name: str, min_value: Optional[float] = None,
                    max_value: Optional[float] = None) -> None:
        """设置指标阈值
        
        Args:
            metric_name: 指标名称
            min_value: 最小值阈值
            max_value: 最大值阈值
        """
        self._threshold_detector.set_threshold(metric_name, min_value, max_value)
        self._logger.info(f"为指标 {metric_name} 设置阈值: min={min_value}, max={max_value}")
    
    def check_metric(self, metric_name: str, value: float) -> List[AnomalyEvent]:
        """检查指标异常
        
        Args:
            metric_name: 指标名称
            value: 指标值
            
        Returns:
            List[AnomalyEvent]: 检测到的异常事件列表
        """
        anomalies = []
        
        # 统计异常检测
        if self._enabled_detectors['statistical']:
            anomaly = self._statistical_detector.add_data_point(metric_name, value)
            if anomaly:
                anomalies.append(anomaly)
        
        # 趋势异常检测
        if self._enabled_detectors['trend']:
            anomaly = self._trend_detector.add_data_point(metric_name, value)
            if anomaly:
                anomalies.append(anomaly)
        
        # 阈值异常检测
        if self._enabled_detectors['threshold']:
            anomaly = self._threshold_detector.check_threshold(metric_name, value)
            if anomaly:
                anomalies.append(anomaly)
        
        # 记录和通知异常
        for anomaly in anomalies:
            self._record_anomaly(anomaly)
        
        return anomalies
    
    def _record_anomaly(self, anomaly: AnomalyEvent) -> None:
        """记录异常事件
        
        Args:
            anomaly: 异常事件
        """
        with self._lock:
            # 存储异常
            self._anomaly_history.append(anomaly)
            
            # 限制历史数据大小
            if len(self._anomaly_history) > self._max_history_size:
                self._anomaly_history = self._anomaly_history[-self._max_history_size:]
            
            # 记录日志
            log_level = {
                AnomalySeverity.LOW: logging.INFO,
                AnomalySeverity.MEDIUM: logging.WARNING,
                AnomalySeverity.HIGH: logging.ERROR,
                AnomalySeverity.CRITICAL: logging.CRITICAL
            }.get(anomaly.severity, logging.WARNING)
            
            self._logger.log(log_level, f"检测到异常: {anomaly.description}")
            
            # 调用回调函数
            for callback in self._callbacks:
                try:
                    callback(anomaly)
                except Exception as e:
                    self._logger.error(f"异常检测回调函数执行失败: {e}")
    
    def get_anomaly_history(self, 
                          severity: Optional[AnomalySeverity] = None,
                          anomaly_type: Optional[AnomalyType] = None,
                          time_window_hours: Optional[int] = None) -> List[AnomalyEvent]:
        """获取异常历史
        
        Args:
            severity: 严重程度过滤
            anomaly_type: 异常类型过滤
            time_window_hours: 时间窗口（小时）
            
        Returns:
            List[AnomalyEvent]: 异常历史
        """
        with self._lock:
            anomalies = self._anomaly_history.copy()
            
            # 应用过滤条件
            if severity:
                anomalies = [a for a in anomalies if a.severity == severity]
            
            if anomaly_type:
                anomalies = [a for a in anomalies if a.anomaly_type == anomaly_type]
            
            if time_window_hours:
                cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
                anomalies = [a for a in anomalies if a.timestamp > cutoff_time]
            
            return anomalies
    
    def get_anomaly_summary(self, time_window_hours: Optional[int] = None) -> Dict[str, Any]:
        """获取异常摘要
        
        Args:
            time_window_hours: 时间窗口（小时）
            
        Returns:
            Dict[str, Any]: 异常摘要
        """
        anomalies = self.get_anomaly_history(time_window_hours=time_window_hours)
        
        if not anomalies:
            return {}
        
        # 按严重程度统计
        severity_counts = {}
        for severity in AnomalySeverity:
            severity_counts[severity.value] = sum(
                1 for a in anomalies if a.severity == severity
            )
        
        # 按类型统计
        type_counts = {}
        for anomaly_type in AnomalyType:
            type_counts[anomaly_type.value] = sum(
                1 for a in anomalies if a.anomaly_type == anomaly_type
            )
        
        # 按指标统计
        metric_counts = {}
        for anomaly in anomalies:
            metric_counts[anomaly.metric_name] = metric_counts.get(anomaly.metric_name, 0) + 1
        
        # 未解决的异常
        unresolved_count = sum(1 for a in anomalies if not a.resolved)
        
        return {
            'time_window_hours': time_window_hours,
            'total_anomalies': len(anomalies),
            'unresolved_anomalies': unresolved_count,
            'severity_distribution': severity_counts,
            'type_distribution': type_counts,
            'metric_distribution': metric_counts,
            'most_problematic_metric': max(metric_counts.items(), key=lambda x: x[1])[0] if metric_counts else None
        }
    
    def resolve_anomaly(self, anomaly_index: int) -> bool:
        """标记异常为已解决
        
        Args:
            anomaly_index: 异常在历史列表中的索引
            
        Returns:
            bool: 是否成功标记
        """
        with self._lock:
            if 0 <= anomaly_index < len(self._anomaly_history):
                anomaly = self._anomaly_history[anomaly_index]
                anomaly.resolved = True
                anomaly.resolution_time = datetime.now()
                self._logger.info(f"异常已标记为解决: {anomaly.description}")
                return True
            return False
    
    def enable_detector(self, detector_type: str, enabled: bool = True) -> None:
        """启用或禁用检测器
        
        Args:
            detector_type: 检测器类型 ('statistical', 'trend', 'threshold')
            enabled: 是否启用
        """
        if detector_type in self._enabled_detectors:
            self._enabled_detectors[detector_type] = enabled
            self._logger.info(f"检测器 {detector_type} 已{'启用' if enabled else '禁用'}")
        else:
            self._logger.warning(f"未知的检测器类型: {detector_type}")
    
    def clear_history(self) -> None:
        """清空异常历史"""
        with self._lock:
            self._anomaly_history.clear()
            self._logger.info("异常检测历史数据已清空")