#!/usr/bin/env python3
"""
检查SingleAssetTrainer的属性
"""

import pandas as pd
import numpy as np
from unittest.mock import Mock

# 导入需要测试的类
from finrl_crypto.training.single_asset import SingleAssetTrainer
from finrl_crypto.training.base import TrainingConfig

def check_single_asset_attributes():
    """检查SingleAssetTrainer的属性"""
    # 创建mock对象
    mock_agent = Mock()
    mock_agent.predict.return_value = [0.5, 0.3, 0.2]
    mock_agent.train.return_value = {'loss': 0.1}
    
    mock_env = Mock()
    mock_env.reset.return_value = np.random.random(10)
    mock_env.step.return_value = (np.random.random(10), 1.0, False, {})
    
    # 创建配置
    config = TrainingConfig()
    
    # 创建SingleAssetTrainer
    trainer = SingleAssetTrainer(
        agent=mock_agent,
        env=mock_env,
        config=config
    )
    
    print("检查SingleAssetTrainer的属性:")
    print("="*50)
    
    # 检查基本属性
    attributes_to_check = [
        'agent', 'env', 'config',
        'training_history', 'current_episode',
        'portfolio_values', 'positions', 'trades', 
        'returns', 'sharpe_ratios', 'max_drawdowns'
    ]
    
    for attr in attributes_to_check:
        has_attr = hasattr(trainer, attr)
        print(f"{attr:20}: {'✓' if has_attr else '✗'} {has_attr}")
        if has_attr:
            value = getattr(trainer, attr)
            print(f"{'':20}  类型: {type(value).__name__}")
            if hasattr(value, '__len__') and not isinstance(value, str):
                try:
                    print(f"{'':20}  长度: {len(value)}")
                except:
                    pass
    
    print("\n所有属性检查完成！")
    
    # 检查哪些属性缺失
    missing_attrs = [attr for attr in attributes_to_check if not hasattr(trainer, attr)]
    if missing_attrs:
        print(f"\n缺失的属性: {missing_attrs}")
    else:
        print("\n所有预期属性都存在！")

if __name__ == "__main__":
    check_single_asset_attributes()