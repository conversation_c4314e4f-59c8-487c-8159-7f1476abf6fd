"""环境工厂模块

提供统一的环境创建接口。
"""

import pandas as pd
from typing import Dict, List, Optional, Any, Type
import logging

from .base import BaseEnvironment
from .crypto_trading import CryptoTradingEnvironment
from .portfolio import PortfolioEnvironment


class EnvironmentFactory:
    """环境工厂类
    
    提供统一的接口来创建不同类型的交易环境。
    """
    
    # 注册的环境类型
    _environment_registry: Dict[str, Type[BaseEnvironment]] = {
        'crypto_trading': CryptoTradingEnvironment,
        'portfolio': PortfolioEnvironment,
    }
    
    @classmethod
    def create_environment(cls,
                          env_type: str,
                          data: pd.DataFrame,
                          **kwargs) -> BaseEnvironment:
        """创建环境实例
        
        Args:
            env_type: 环境类型
            data: 交易数据
            **kwargs: 环境参数
            
        Returns:
            环境实例
            
        Raises:
            ValueError: 如果环境类型不支持
        """
        if env_type not in cls._environment_registry:
            available_types = list(cls._environment_registry.keys())
            raise ValueError(
                f"不支持的环境类型: {env_type}. "
                f"支持的类型: {available_types}"
            )
        
        env_class = cls._environment_registry[env_type]
        
        try:
            environment = env_class(data=data, **kwargs)
            logging.info(f"成功创建 {env_type} 环境")
            return environment
        except Exception as e:
            logging.error(f"创建环境失败: {e}")
            raise
    
    @classmethod
    def register_environment(cls,
                           env_type: str,
                           env_class: Type[BaseEnvironment]):
        """注册新的环境类型
        
        Args:
            env_type: 环境类型名称
            env_class: 环境类
        """
        if not issubclass(env_class, BaseEnvironment):
            raise ValueError("环境类必须继承自 BaseEnvironment")
        
        cls._environment_registry[env_type] = env_class
        logging.info(f"注册新环境类型: {env_type}")
    
    @classmethod
    def get_available_environments(cls) -> List[str]:
        """获取可用的环境类型
        
        Returns:
            环境类型列表
        """
        return list(cls._environment_registry.keys())
    
    @classmethod
    def create_crypto_trading_environment(cls,
                                        data: pd.DataFrame,
                                        initial_amount: float = 10000,
                                        transaction_cost_pct: float = 0.001,
                                        **kwargs) -> CryptoTradingEnvironment:
        """创建加密货币交易环境的便捷方法
        
        Args:
            data: 交易数据
            initial_amount: 初始资金
            transaction_cost_pct: 交易成本百分比
            **kwargs: 其他参数
            
        Returns:
            加密货币交易环境实例
        """
        return cls.create_environment(
            env_type='crypto_trading',
            data=data,
            initial_amount=initial_amount,
            transaction_cost_pct=transaction_cost_pct,
            **kwargs
        )
    
    @classmethod
    def create_portfolio_environment(cls,
                                   data: pd.DataFrame,
                                   initial_amount: float = 10000,
                                   rebalance_frequency: int = 1,
                                   **kwargs) -> PortfolioEnvironment:
        """创建投资组合环境的便捷方法
        
        Args:
            data: 交易数据
            initial_amount: 初始资金
            rebalance_frequency: 再平衡频率
            **kwargs: 其他参数
            
        Returns:
            投资组合环境实例
        """
        return cls.create_environment(
            env_type='portfolio',
            data=data,
            initial_amount=initial_amount,
            rebalance_frequency=rebalance_frequency,
            **kwargs
        )
    
    @classmethod
    def get_environment_info(cls, env_type: str) -> Dict[str, Any]:
        """获取环境类型信息
        
        Args:
            env_type: 环境类型
            
        Returns:
            环境信息字典
        """
        if env_type not in cls._environment_registry:
            raise ValueError(f"不支持的环境类型: {env_type}")
        
        env_class = cls._environment_registry[env_type]
        
        return {
            'name': env_type,
            'class': env_class.__name__,
            'module': env_class.__module__,
            'description': env_class.__doc__ or '无描述',
        }
    
    @classmethod
    def validate_environment_config(cls,
                                  env_type: str,
                                  config: Dict[str, Any]) -> bool:
        """验证环境配置
        
        Args:
            env_type: 环境类型
            config: 配置字典
            
        Returns:
            是否有效
        """
        if env_type not in cls._environment_registry:
            return False
        
        # 基本配置检查
        required_keys = ['data']
        for key in required_keys:
            if key not in config:
                logging.error(f"缺少必需的配置项: {key}")
                return False
        
        # 数据验证
        data = config['data']
        if not isinstance(data, pd.DataFrame):
            logging.error("数据必须是 pandas DataFrame")
            return False
        
        if data.empty:
            logging.error("数据不能为空")
            return False
        
        # 环境特定验证
        if env_type == 'crypto_trading':
            return cls._validate_crypto_trading_config(config)
        elif env_type == 'portfolio':
            return cls._validate_portfolio_config(config)
        
        return True
    
    @classmethod
    def _validate_crypto_trading_config(cls, config: Dict[str, Any]) -> bool:
        """验证加密货币交易环境配置"""
        data = config['data']
        
        # 检查必需的列
        required_columns = ['close']
        for col in required_columns:
            if col not in data.columns:
                logging.error(f"数据缺少必需的列: {col}")
                return False
        
        # 检查数值参数
        numeric_params = {
            'initial_amount': (0, float('inf')),
            'transaction_cost_pct': (0, 1),
            'reward_scaling': (0, float('inf')),
            'max_position': (0, float('inf')),
            'min_position': (float('-inf'), float('inf')),
        }
        
        for param, (min_val, max_val) in numeric_params.items():
            if param in config:
                value = config[param]
                if not isinstance(value, (int, float)):
                    logging.error(f"{param} 必须是数值")
                    return False
                if not (min_val <= value <= max_val):
                    logging.error(f"{param} 必须在 [{min_val}, {max_val}] 范围内")
                    return False
        
        return True
    
    @classmethod
    def _validate_portfolio_config(cls, config: Dict[str, Any]) -> bool:
        """验证投资组合环境配置"""
        data = config['data']
        
        # 检查必需的列
        required_columns = ['close']
        for col in required_columns:
            if col not in data.columns:
                logging.error(f"数据缺少必需的列: {col}")
                return False
        
        # 检查数值参数
        numeric_params = {
            'initial_amount': (0, float('inf')),
            'transaction_cost_pct': (0, 1),
            'rebalance_frequency': (1, float('inf')),
            'risk_free_rate': (0, 1),
            'max_weight': (0, 1),
            'min_weight': (0, 1),
        }
        
        for param, (min_val, max_val) in numeric_params.items():
            if param in config:
                value = config[param]
                if not isinstance(value, (int, float)):
                    logging.error(f"{param} 必须是数值")
                    return False
                if not (min_val <= value <= max_val):
                    logging.error(f"{param} 必须在 [{min_val}, {max_val}] 范围内")
                    return False
        
        # 检查权重约束
        if 'max_weight' in config and 'min_weight' in config:
            if config['min_weight'] > config['max_weight']:
                logging.error("min_weight 不能大于 max_weight")
                return False
        
        return True


# 便捷函数
def create_trading_environment(data: pd.DataFrame, **kwargs) -> CryptoTradingEnvironment:
    """创建交易环境的便捷函数
    
    Args:
        data: 交易数据
        **kwargs: 环境参数
        
    Returns:
        交易环境实例
    """
    return EnvironmentFactory.create_crypto_trading_environment(data, **kwargs)


def create_portfolio_environment(data: pd.DataFrame, **kwargs) -> PortfolioEnvironment:
    """创建投资组合环境的便捷函数
    
    Args:
        data: 交易数据
        **kwargs: 环境参数
        
    Returns:
        投资组合环境实例
    """
    return EnvironmentFactory.create_portfolio_environment(data, **kwargs)


def get_supported_environments() -> List[str]:
    """获取支持的环境类型
    
    Returns:
        环境类型列表
    """
    return EnvironmentFactory.get_available_environments()