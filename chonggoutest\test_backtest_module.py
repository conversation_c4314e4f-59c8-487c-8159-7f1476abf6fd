#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回测模块测试 - TDD测试驱动开发

测试finrl_crypto.backtest模块的所有功能：
- Backtester回测引擎
- BacktestResult结果分析
- 性能指标计算
- 风险分析
- 多种回测模式
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Mock外部依赖
sys.modules['gym'] = Mock()
sys.modules['stable_baselines3'] = Mock()
sys.modules['torch'] = Mock()
sys.modules['tensorflow'] = Mock()
sys.modules['matplotlib'] = Mock()
sys.modules['seaborn'] = Mock()
sys.modules['plotly'] = Mock()
sys.modules['ta'] = Mock()
sys.modules['talib'] = Mock()

class TestBacktester(unittest.TestCase):
    """测试Backtester回测引擎"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        np.random.seed(42)
        
        self.test_data = pd.DataFrame({
            'timestamp': dates,
            'open': 100 + np.cumsum(np.random.normal(0, 1, 252)),
            'high': 105 + np.cumsum(np.random.normal(0, 1, 252)),
            'low': 95 + np.cumsum(np.random.normal(0, 1, 252)),
            'close': 100 + np.cumsum(np.random.normal(0, 1, 252)),
            'volume': np.random.uniform(10000, 100000, 252)
        })
        
        # 创建测试策略信号
        self.test_signals = pd.Series(np.random.choice([-1, 0, 1], 252, p=[0.2, 0.6, 0.2]))
        
        self.backtest_config = {
            'initial_capital': 100000,
            'commission': 0.001,
            'slippage': 0.0005,
            'position_sizing': 'fixed_fraction',
            'max_position_size': 0.1,
            'risk_management': {
                'stop_loss': 0.05,
                'take_profit': 0.1,
                'max_drawdown': 0.2
            }
        }
    
    def test_backtester_creation(self):
        """测试回测器创建"""
        try:
            from finrl_crypto.backtest.backtester import Backtester
            
            backtester = Backtester(self.backtest_config)
            
            self.assertEqual(backtester.initial_capital, 100000)
            self.assertEqual(backtester.commission, 0.001)
            self.assertEqual(backtester.slippage, 0.0005)
            
        except ImportError:
            self.fail("Backtester类未实现")
    
    def test_backtest_execution(self):
        """测试回测执行"""
        try:
            from finrl_crypto.backtest.backtester import Backtester
            
            backtester = Backtester(self.backtest_config)
            result = backtester.run(self.test_data, self.test_signals)
            
            # 检查回测结果类型
            self.assertIsNotNone(result)
            self.assertTrue(hasattr(result, 'portfolio_value'))
            self.assertTrue(hasattr(result, 'positions'))
            self.assertTrue(hasattr(result, 'trades'))
            
        except ImportError:
            self.fail("Backtester类未实现")
    
    def test_position_sizing(self):
        """测试仓位管理"""
        try:
            from finrl_crypto.backtest.backtester import Backtester
            
            backtester = Backtester(self.backtest_config)
            
            # 测试固定比例仓位
            position_size = backtester.calculate_position_size(
                signal=1, 
                current_price=100, 
                portfolio_value=100000
            )
            
            expected_size = 100000 * 0.1 / 100  # 10%的资金买入
            self.assertAlmostEqual(position_size, expected_size, places=2)
            
        except ImportError:
            self.fail("Backtester类未实现")
    
    def test_commission_and_slippage(self):
        """测试手续费和滑点计算"""
        try:
            from finrl_crypto.backtest.backtester import Backtester
            
            backtester = Backtester(self.backtest_config)
            
            # 测试手续费计算
            commission = backtester.calculate_commission(trade_value=10000)
            expected_commission = 10000 * 0.001
            self.assertEqual(commission, expected_commission)
            
            # 测试滑点计算
            execution_price = backtester.apply_slippage(price=100, side='buy')
            expected_price = 100 * (1 + 0.0005)  # 买入时价格上涨
            self.assertEqual(execution_price, expected_price)
            
        except ImportError:
            self.fail("Backtester类未实现")
    
    def test_risk_management(self):
        """测试风险管理"""
        try:
            from finrl_crypto.backtest.backtester import Backtester
            
            backtester = Backtester(self.backtest_config)
            
            # 测试止损检查
            should_stop_loss = backtester.check_stop_loss(
                entry_price=100,
                current_price=94,  # 6%的损失
                position_side='long'
            )
            self.assertTrue(should_stop_loss)  # 应该触发5%的止损
            
            # 测试止盈检查
            should_take_profit = backtester.check_take_profit(
                entry_price=100,
                current_price=111,  # 11%的收益
                position_side='long'
            )
            self.assertTrue(should_take_profit)  # 应该触发10%的止盈
            
        except ImportError:
            self.fail("Backtester类未实现")

class TestBacktestResult(unittest.TestCase):
    """测试BacktestResult结果分析"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟回测结果数据
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        np.random.seed(42)
        
        # 模拟组合价值变化
        returns = np.random.normal(0.0008, 0.02, 252)  # 日收益率
        portfolio_values = [100000]
        for ret in returns:
            portfolio_values.append(portfolio_values[-1] * (1 + ret))
        
        self.result_data = {
            'portfolio_value': pd.Series(portfolio_values[1:], index=dates),
            'positions': pd.DataFrame({
                'timestamp': dates,
                'position': np.random.choice([-1, 0, 1], 252),
                'quantity': np.random.uniform(0, 1000, 252),
                'price': 100 + np.cumsum(np.random.normal(0, 1, 252))
            }),
            'trades': pd.DataFrame({
                'timestamp': dates[::10],  # 每10天一笔交易
                'side': np.random.choice(['buy', 'sell'], 26),
                'quantity': np.random.uniform(100, 1000, 26),
                'price': 100 + np.random.normal(0, 5, 26),
                'commission': np.random.uniform(1, 10, 26)
            })
        }
    
    def test_backtest_result_creation(self):
        """测试回测结果创建"""
        try:
            from finrl_crypto.backtest.result import BacktestResult
            
            result = BacktestResult(self.result_data)
            
            self.assertIsNotNone(result.portfolio_value)
            self.assertIsNotNone(result.positions)
            self.assertIsNotNone(result.trades)
            
        except ImportError:
            self.fail("BacktestResult类未实现")
    
    def test_performance_metrics_calculation(self):
        """测试性能指标计算"""
        try:
            from finrl_crypto.backtest.result import BacktestResult
            
            result = BacktestResult(self.result_data)
            metrics = result.calculate_performance_metrics()
            
            # 检查基本性能指标
            required_metrics = [
                'total_return',
                'annualized_return', 
                'volatility',
                'sharpe_ratio',
                'max_drawdown',
                'calmar_ratio',
                'win_rate',
                'profit_factor'
            ]
            
            for metric in required_metrics:
                self.assertIn(metric, metrics)
                self.assertIsInstance(metrics[metric], (int, float))
            
        except ImportError:
            self.fail("BacktestResult类未实现")
    
    def test_drawdown_analysis(self):
        """测试回撤分析"""
        try:
            from finrl_crypto.backtest.result import BacktestResult
            
            result = BacktestResult(self.result_data)
            drawdown_analysis = result.analyze_drawdowns()
            
            self.assertIn('max_drawdown', drawdown_analysis)
            self.assertIn('max_drawdown_duration', drawdown_analysis)
            self.assertIn('drawdown_periods', drawdown_analysis)
            
            # 最大回撤应该是负数或零
            self.assertLessEqual(drawdown_analysis['max_drawdown'], 0)
            
        except ImportError:
            self.fail("BacktestResult类未实现")
    
    def test_trade_analysis(self):
        """测试交易分析"""
        try:
            from finrl_crypto.backtest.result import BacktestResult
            
            result = BacktestResult(self.result_data)
            trade_analysis = result.analyze_trades()
            
            required_trade_metrics = [
                'total_trades',
                'winning_trades',
                'losing_trades',
                'win_rate',
                'average_win',
                'average_loss',
                'profit_factor',
                'largest_win',
                'largest_loss'
            ]
            
            for metric in required_trade_metrics:
                self.assertIn(metric, trade_analysis)
            
        except ImportError:
            self.fail("BacktestResult类未实现")
    
    def test_monthly_returns(self):
        """测试月度收益分析"""
        try:
            from finrl_crypto.backtest.result import BacktestResult
            
            result = BacktestResult(self.result_data)
            monthly_returns = result.get_monthly_returns()
            
            self.assertIsInstance(monthly_returns, pd.Series)
            self.assertTrue(len(monthly_returns) > 0)
            
        except ImportError:
            self.fail("BacktestResult类未实现")

class TestBacktestMetrics(unittest.TestCase):
    """测试回测性能指标"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试收益率数据
        np.random.seed(42)
        self.returns = pd.Series(np.random.normal(0.001, 0.02, 252))  # 日收益率
        self.benchmark_returns = pd.Series(np.random.normal(0.0005, 0.015, 252))
        
        # 创建价格数据
        self.prices = pd.Series([100])
        for ret in self.returns:
            self.prices = pd.concat([self.prices, pd.Series([self.prices.iloc[-1] * (1 + ret)])])
        self.prices = self.prices.iloc[1:]  # 移除初始价格
    
    def test_basic_metrics_calculation(self):
        """测试基本指标计算"""
        try:
            from finrl_crypto.backtest.metrics import BacktestMetrics
            
            metrics = BacktestMetrics()
            
            # 测试总收益率
            total_return = metrics.calculate_total_return(self.returns)
            self.assertIsInstance(total_return, float)
            
            # 测试年化收益率
            annualized_return = metrics.calculate_annualized_return(self.returns)
            self.assertIsInstance(annualized_return, float)
            
            # 测试波动率
            volatility = metrics.calculate_volatility(self.returns)
            self.assertIsInstance(volatility, float)
            self.assertGreater(volatility, 0)
            
        except ImportError:
            self.fail("BacktestMetrics类未实现")
    
    def test_risk_metrics_calculation(self):
        """测试风险指标计算"""
        try:
            from finrl_crypto.backtest.metrics import BacktestMetrics
            
            metrics = BacktestMetrics()
            
            # 测试夏普比率
            sharpe_ratio = metrics.calculate_sharpe_ratio(self.returns)
            self.assertIsInstance(sharpe_ratio, float)
            
            # 测试最大回撤
            max_drawdown = metrics.calculate_max_drawdown(self.prices)
            self.assertIsInstance(max_drawdown, float)
            self.assertLessEqual(max_drawdown, 0)
            
            # 测试VaR
            var_95 = metrics.calculate_var(self.returns, confidence_level=0.95)
            self.assertIsInstance(var_95, float)
            self.assertLess(var_95, 0)  # VaR应该是负数
            
            # 测试CVaR
            cvar_95 = metrics.calculate_cvar(self.returns, confidence_level=0.95)
            self.assertIsInstance(cvar_95, float)
            self.assertLess(cvar_95, var_95)  # CVaR应该比VaR更负
            
        except ImportError:
            self.fail("BacktestMetrics类未实现")
    
    def test_benchmark_comparison_metrics(self):
        """测试基准比较指标"""
        try:
            from finrl_crypto.backtest.metrics import BacktestMetrics
            
            metrics = BacktestMetrics()
            
            # 测试Alpha
            alpha = metrics.calculate_alpha(self.returns, self.benchmark_returns)
            self.assertIsInstance(alpha, float)
            
            # 测试Beta
            beta = metrics.calculate_beta(self.returns, self.benchmark_returns)
            self.assertIsInstance(beta, float)
            
            # 测试信息比率
            information_ratio = metrics.calculate_information_ratio(
                self.returns, self.benchmark_returns
            )
            self.assertIsInstance(information_ratio, float)
            
            # 测试跟踪误差
            tracking_error = metrics.calculate_tracking_error(
                self.returns, self.benchmark_returns
            )
            self.assertIsInstance(tracking_error, float)
            self.assertGreater(tracking_error, 0)
            
        except ImportError:
            self.fail("BacktestMetrics类未实现")
    
    def test_advanced_metrics_calculation(self):
        """测试高级指标计算"""
        try:
            from finrl_crypto.backtest.metrics import BacktestMetrics
            
            metrics = BacktestMetrics()
            
            # 测试Sortino比率
            sortino_ratio = metrics.calculate_sortino_ratio(self.returns)
            self.assertIsInstance(sortino_ratio, float)
            
            # 测试Calmar比率
            calmar_ratio = metrics.calculate_calmar_ratio(self.returns, self.prices)
            self.assertIsInstance(calmar_ratio, float)
            
            # 测试Omega比率
            omega_ratio = metrics.calculate_omega_ratio(self.returns, threshold=0)
            self.assertIsInstance(omega_ratio, float)
            self.assertGreater(omega_ratio, 0)
            
        except ImportError:
            self.fail("BacktestMetrics类未实现")

class TestBacktestVisualization(unittest.TestCase):
    """测试回测可视化"""
    
    def setUp(self):
        """测试前准备"""
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        np.random.seed(42)
        
        self.portfolio_data = pd.DataFrame({
            'timestamp': dates,
            'portfolio_value': 100000 * np.cumprod(1 + np.random.normal(0.0008, 0.02, 252)),
            'benchmark_value': 100000 * np.cumprod(1 + np.random.normal(0.0005, 0.015, 252))
        })
    
    def test_portfolio_value_plot(self):
        """测试组合价值图表"""
        try:
            from finrl_crypto.backtest.visualization import BacktestVisualization
            
            viz = BacktestVisualization()
            
            # 测试组合价值图表生成
            self.assertTrue(hasattr(viz, 'plot_portfolio_value'))
            
            # 测试图表保存功能
            self.assertTrue(hasattr(viz, 'save_plot'))
            
        except ImportError:
            self.fail("BacktestVisualization类未实现")
    
    def test_drawdown_plot(self):
        """测试回撤图表"""
        try:
            from finrl_crypto.backtest.visualization import BacktestVisualization
            
            viz = BacktestVisualization()
            
            # 测试回撤图表生成
            self.assertTrue(hasattr(viz, 'plot_drawdown'))
            
        except ImportError:
            self.fail("BacktestVisualization类未实现")
    
    def test_returns_distribution_plot(self):
        """测试收益分布图表"""
        try:
            from finrl_crypto.backtest.visualization import BacktestVisualization
            
            viz = BacktestVisualization()
            
            # 测试收益分布图表生成
            self.assertTrue(hasattr(viz, 'plot_returns_distribution'))
            
        except ImportError:
            self.fail("BacktestVisualization类未实现")

class TestBacktestComparison(unittest.TestCase):
    """测试回测结果比较"""
    
    def setUp(self):
        """测试前准备"""
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        np.random.seed(42)
        
        # 创建多个策略的回测结果
        self.strategy_results = {
            'strategy_1': {
                'portfolio_value': 100000 * np.cumprod(1 + np.random.normal(0.001, 0.02, 252)),
                'returns': np.random.normal(0.001, 0.02, 252)
            },
            'strategy_2': {
                'portfolio_value': 100000 * np.cumprod(1 + np.random.normal(0.0008, 0.018, 252)),
                'returns': np.random.normal(0.0008, 0.018, 252)
            },
            'benchmark': {
                'portfolio_value': 100000 * np.cumprod(1 + np.random.normal(0.0005, 0.015, 252)),
                'returns': np.random.normal(0.0005, 0.015, 252)
            }
        }
    
    def test_strategy_comparison(self):
        """测试策略比较"""
        try:
            from finrl_crypto.backtest.comparison import BacktestComparison
            
            comparison = BacktestComparison()
            
            # 测试比较方法
            self.assertTrue(hasattr(comparison, 'compare_strategies'))
            self.assertTrue(hasattr(comparison, 'generate_comparison_report'))
            
        except ImportError:
            self.fail("BacktestComparison类未实现")
    
    def test_performance_ranking(self):
        """测试性能排名"""
        try:
            from finrl_crypto.backtest.comparison import BacktestComparison
            
            comparison = BacktestComparison()
            
            # 测试排名功能
            self.assertTrue(hasattr(comparison, 'rank_strategies'))
            
        except ImportError:
            self.fail("BacktestComparison类未实现")

class TestBacktestOptimization(unittest.TestCase):
    """测试回测优化"""
    
    def test_walk_forward_analysis(self):
        """测试滚动窗口分析"""
        try:
            from finrl_crypto.backtest.optimization import WalkForwardAnalysis
            
            wfa = WalkForwardAnalysis({
                'training_period': 252,  # 1年训练期
                'testing_period': 63,    # 3个月测试期
                'step_size': 21          # 每月滚动
            })
            
            self.assertTrue(hasattr(wfa, 'run_analysis'))
            self.assertTrue(hasattr(wfa, 'get_results'))
            
        except ImportError:
            self.fail("WalkForwardAnalysis类未实现")
    
    def test_monte_carlo_simulation(self):
        """测试蒙特卡洛模拟"""
        try:
            from finrl_crypto.backtest.optimization import MonteCarloSimulation
            
            mc_sim = MonteCarloSimulation({
                'num_simulations': 1000,
                'confidence_levels': [0.05, 0.95]
            })
            
            self.assertTrue(hasattr(mc_sim, 'run_simulation'))
            self.assertTrue(hasattr(mc_sim, 'get_confidence_intervals'))
            
        except ImportError:
            self.fail("MonteCarloSimulation类未实现")

if __name__ == '__main__':
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加所有测试类
    test_classes = [
        TestBacktester,
        TestBacktestResult,
        TestBacktestMetrics,
        TestBacktestVisualization,
        TestBacktestComparison,
        TestBacktestOptimization
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果统计
    print(f"\n测试结果统计:")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")