"""投资组合环境模块

实现专门用于投资组合管理的强化学习环境。
"""

try:
    import gymnasium as gym
except ImportError:
    try:
        import gym
    except ImportError:
        # 如果都没有安装，创建一个简单的替代
        class MockSpace:
            def __init__(self, *args, **kwargs):
                pass
                
        class MockSpaces:
            Box = MockSpace
            Discrete = MockSpace
            
        class MockEnv:
            def __init__(self):
                pass
                
        class MockGym:
            Env = MockEnv
            spaces = MockSpaces()
            
        gym = MockGym()
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
import logging

from .base import BaseEnvironment


class PortfolioEnvironment(BaseEnvironment):
    """投资组合管理环境
    
    专门为投资组合管理设计的强化学习环境。
    支持多资产配置、风险管理和动态再平衡。
    """
    
    def __init__(self,
                 data: pd.DataFrame,
                 initial_amount: float = 10000,
                 transaction_cost_pct: float = 0.001,
                 reward_scaling: float = 1e-4,
                 state_space: Optional[int] = None,
                 action_space: Optional[int] = None,
                 tech_indicator_list: Optional[List[str]] = None,
                 turbulence_threshold: Optional[float] = None,
                 risk_indicator_col: str = 'turbulence',
                 make_plots: bool = False,
                 print_verbosity: int = 10,
                 day: int = 0,
                 initial: bool = True,
                 previous_state: Optional[List] = None,
                 model_name: str = '',
                 mode: str = '',
                 iteration: str = '',
                 rebalance_frequency: int = 1,
                 risk_free_rate: float = 0.02,
                 target_volatility: Optional[float] = None,
                 max_weight: float = 1.0,
                 min_weight: float = 0.0,
                 enable_cash: bool = True,
                 **kwargs):
        """初始化投资组合环境
        
        Args:
            data: 交易数据
            initial_amount: 初始资金
            transaction_cost_pct: 交易成本百分比
            reward_scaling: 奖励缩放因子
            state_space: 状态空间维度
            action_space: 动作空间维度
            tech_indicator_list: 技术指标列表
            turbulence_threshold: 波动阈值
            risk_indicator_col: 风险指标列名
            make_plots: 是否生成图表
            print_verbosity: 打印详细程度
            day: 当前交易日
            initial: 是否为初始状态
            previous_state: 前一状态
            model_name: 模型名称
            mode: 模式
            iteration: 迭代次数
            rebalance_frequency: 再平衡频率（天）
            risk_free_rate: 无风险利率
            target_volatility: 目标波动率
            max_weight: 最大权重
            min_weight: 最小权重
            enable_cash: 是否允许持有现金
            **kwargs: 其他参数
        """
        # 投资组合特定参数
        self.rebalance_frequency = rebalance_frequency
        self.risk_free_rate = risk_free_rate
        self.target_volatility = target_volatility
        self.max_weight = max_weight
        self.min_weight = min_weight
        self.enable_cash = enable_cash
        
        # 预处理数据以获取stock_dim
        self.data = data.copy()
        if 'tic' in self.data.columns:
            self.stock_dim = len(self.data.tic.unique())
        else:
            self.stock_dim = 1
        
        # 初始化投资组合权重（在调用父类之前）
        if self.enable_cash:
            # 包含现金的权重（现金 + 各资产）
            self.weights = np.zeros(self.stock_dim + 1)
            self.weights[0] = 1.0  # 初始全部为现金
        else:
            # 不包含现金，平均分配
            self.weights = np.ones(self.stock_dim) / self.stock_dim
        
        # 调用父类初始化
        super().__init__(
            data=data,
            initial_amount=initial_amount,
            transaction_cost_pct=transaction_cost_pct,
            reward_scaling=reward_scaling,
            state_space=state_space,
            action_space=action_space,
            tech_indicator_list=tech_indicator_list,
            turbulence_threshold=turbulence_threshold,
            risk_indicator_col=risk_indicator_col,
            make_plots=make_plots,
            print_verbosity=print_verbosity,
            day=day,
            initial=initial,
            previous_state=previous_state,
            model_name=model_name,
            mode=mode,
            iteration=iteration,
            **kwargs
        )
        
        # 投资组合历史
        self.weight_memory = []
        self.return_memory = []
        self.volatility_memory = []
        self.sharpe_memory = []
        
        # 再平衡计数器
        self.days_since_rebalance = 0
        
        # 收益率计算窗口
        self.return_window = 252  # 一年的交易日
        self.price_memory = []
    
    def _setup_spaces(self, state_space: Optional[int], action_space: Optional[int]):
        """设置动作和观察空间"""
        # 计算状态空间维度
        if state_space is None:
            # 市场特征
            market_features = len(self.tech_indicator_list) * self.stock_dim
            
            # 投资组合特征
            portfolio_features = len(self.weights)  # 当前权重
            portfolio_features += 3  # 投资组合收益率、波动率、夏普比率
            
            # 风险特征
            risk_features = 1  # 波动指标
            
            state_dim = market_features + portfolio_features + risk_features
        else:
            state_dim = state_space
        
        # 设置观察空间
        self.observation_space = gym.spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(state_dim,),
            dtype=np.float32
        )
        
        # 设置动作空间
        if action_space is None:
            if self.enable_cash:
                action_dim = self.stock_dim + 1  # 包含现金
            else:
                action_dim = self.stock_dim
        else:
            action_dim = action_space
        
        # 动作为投资组合权重，范围为[min_weight, max_weight]
        self.action_space = gym.spaces.Box(
            low=self.min_weight,
            high=self.max_weight,
            shape=(action_dim,),
            dtype=np.float32
        )
    
    def _get_state(self) -> np.ndarray:
        """获取当前状态"""
        state_features = []
        
        # 市场特征
        current_data = self._get_current_data()
        for tic in self.tic_list:
            if 'tic' in current_data.columns:
                tic_data = current_data[current_data['tic'] == tic]
            else:
                tic_data = current_data
            
            for indicator in self.tech_indicator_list:
                if not tic_data.empty and indicator in tic_data.columns:
                    value = tic_data[indicator].iloc[0]
                else:
                    value = 0.0
                state_features.append(float(value))
        
        # 投资组合特征
        state_features.extend(self.weights.tolist())
        
        # 投资组合性能指标
        portfolio_return = self._calculate_portfolio_return()
        portfolio_volatility = self._calculate_portfolio_volatility()
        sharpe_ratio = self._calculate_sharpe_ratio(portfolio_return, portfolio_volatility)
        
        state_features.extend([portfolio_return, portfolio_volatility, sharpe_ratio])
        
        # 风险特征
        state_features.append(self.turbulence)
        
        return np.array(state_features, dtype=np.float32)
    
    def _calculate_reward(self, action: np.ndarray) -> float:
        """计算奖励"""
        # 计算投资组合收益率
        portfolio_return = self._calculate_portfolio_return()
        
        # 基础奖励：投资组合收益率
        reward = portfolio_return
        
        # 风险调整奖励
        if self.target_volatility is not None:
            portfolio_volatility = self._calculate_portfolio_volatility()
            if portfolio_volatility > 0:
                # 如果波动率超过目标，降低奖励
                volatility_penalty = max(0, portfolio_volatility - self.target_volatility)
                reward -= volatility_penalty
        
        # 夏普比率奖励
        portfolio_volatility = self._calculate_portfolio_volatility()
        if portfolio_volatility > 0:
            sharpe_ratio = (portfolio_return - self.risk_free_rate / 252) / portfolio_volatility
            reward += sharpe_ratio * 0.1  # 夏普比率权重
        
        # 交易成本惩罚
        if self.days_since_rebalance == 0:  # 只在再平衡时计算交易成本
            transaction_cost = self._calculate_transaction_cost(action)
            reward -= transaction_cost
        
        # 波动惩罚
        if self.turbulence_threshold is not None and self.turbulence > self.turbulence_threshold:
            reward *= 0.5
        
        # 应用奖励缩放
        reward *= self.reward_scaling
        
        return reward
    
    def _execute_action(self, action: np.ndarray) -> Dict[str, Any]:
        """执行动作"""
        # 标准化权重
        action = np.array(action)
        action = np.clip(action, self.min_weight, self.max_weight)
        
        # 确保权重和为1
        if np.sum(action) > 0:
            action = action / np.sum(action)
        else:
            # 如果所有权重都为0，平均分配
            action = np.ones_like(action) / len(action)
        
        # 检查是否需要再平衡
        need_rebalance = (self.days_since_rebalance >= self.rebalance_frequency)
        
        transaction_cost = 0.0
        if need_rebalance:
            # 计算交易成本
            transaction_cost = self._calculate_transaction_cost(action)
            
            # 更新权重
            self.weights = action.copy()
            self.days_since_rebalance = 0
        else:
            # 不再平衡，权重因价格变动而自然变化
            self._update_weights_by_price_change()
            self.days_since_rebalance += 1
        
        # 计算投资组合价值
        portfolio_value = self._calculate_portfolio_value()
        
        # 计算收益率
        portfolio_return = self._calculate_portfolio_return()
        portfolio_volatility = self._calculate_portfolio_volatility()
        sharpe_ratio = self._calculate_sharpe_ratio(portfolio_return, portfolio_volatility)
        
        # 更新记录
        self.asset_memory.append(portfolio_value)
        self.portfolio_return_memory.append(portfolio_return)
        self.date_memory.append(self._get_date())
        
        self.weight_memory.append(self.weights.copy())
        self.return_memory.append(portfolio_return)
        self.volatility_memory.append(portfolio_volatility)
        self.sharpe_memory.append(sharpe_ratio)
        
        # 更新价格记录
        current_prices = self._get_current_prices()
        self.price_memory.append(current_prices)
        
        # 更新波动指标
        self._update_turbulence()
        
        return {
            'portfolio_value': portfolio_value,
            'portfolio_return': portfolio_return,
            'portfolio_volatility': portfolio_volatility,
            'sharpe_ratio': sharpe_ratio,
            'weights': self.weights.copy(),
            'transaction_cost': transaction_cost,
            'rebalanced': need_rebalance,
            'turbulence': self.turbulence
        }
    
    def _calculate_portfolio_return(self) -> float:
        """计算投资组合收益率"""
        if len(self.asset_memory) < 2:
            return 0.0
        
        current_value = self.asset_memory[-1]
        previous_value = self.asset_memory[-2]
        
        return (current_value - previous_value) / previous_value
    
    def _calculate_portfolio_volatility(self) -> float:
        """计算投资组合波动率"""
        if len(self.return_memory) < 2:
            return 0.0
        
        # 使用最近的收益率计算波动率
        window_size = min(30, len(self.return_memory))  # 30天窗口
        recent_returns = self.return_memory[-window_size:]
        
        return np.std(recent_returns) * np.sqrt(252)  # 年化波动率
    
    def _calculate_sharpe_ratio(self, portfolio_return: float, portfolio_volatility: float) -> float:
        """计算夏普比率"""
        if portfolio_volatility == 0:
            return 0.0
        
        # 计算年化收益率
        if len(self.return_memory) > 0:
            window_size = min(252, len(self.return_memory))
            recent_returns = self.return_memory[-window_size:]
            annualized_return = np.mean(recent_returns) * 252
        else:
            annualized_return = portfolio_return * 252
        
        return (annualized_return - self.risk_free_rate) / portfolio_volatility
    
    def _calculate_portfolio_value(self) -> float:
        """计算投资组合价值"""
        if len(self.asset_memory) == 0:
            return self.initial_amount
        
        # 获取当前价格
        current_prices = self._get_current_prices()
        
        if self.enable_cash:
            # 现金部分
            cash_value = self.weights[0] * self.asset_memory[-1]
            
            # 资产部分
            asset_weights = self.weights[1:]
            if len(self.price_memory) > 0:
                previous_prices = self.price_memory[-1]
                price_changes = current_prices / previous_prices
                asset_values = asset_weights * self.asset_memory[-1] * price_changes
            else:
                asset_values = asset_weights * self.asset_memory[-1]
            
            return cash_value + np.sum(asset_values)
        else:
            # 只有资产，没有现金
            if len(self.price_memory) > 0:
                previous_prices = self.price_memory[-1]
                price_changes = current_prices / previous_prices
                asset_values = self.weights * self.asset_memory[-1] * price_changes
            else:
                asset_values = self.weights * self.asset_memory[-1]
            
            return np.sum(asset_values)
    
    def _get_current_prices(self) -> np.ndarray:
        """获取当前价格"""
        current_data = self._get_current_data()
        prices = np.zeros(self.stock_dim)
        
        for i, tic in enumerate(self.tic_list):
            if 'tic' in current_data.columns:
                tic_data = current_data[current_data['tic'] == tic]
            else:
                tic_data = current_data
            
            if not tic_data.empty and 'close' in tic_data.columns:
                prices[i] = tic_data['close'].iloc[0]
            else:
                prices[i] = 1.0  # 默认价格
        
        return prices
    
    def _update_weights_by_price_change(self):
        """根据价格变动更新权重"""
        if len(self.price_memory) == 0:
            return
        
        current_prices = self._get_current_prices()
        previous_prices = self.price_memory[-1]
        
        if self.enable_cash:
            # 现金权重不变
            cash_weight = self.weights[0]
            
            # 资产权重根据价格变动调整
            asset_weights = self.weights[1:]
            price_changes = current_prices / previous_prices
            new_asset_weights = asset_weights * price_changes
            
            # 重新标准化
            total_weight = cash_weight + np.sum(new_asset_weights)
            if total_weight > 0:
                self.weights[0] = cash_weight / total_weight
                self.weights[1:] = new_asset_weights / total_weight
        else:
            # 所有权重根据价格变动调整
            price_changes = current_prices / previous_prices
            new_weights = self.weights * price_changes
            
            # 重新标准化
            if np.sum(new_weights) > 0:
                self.weights = new_weights / np.sum(new_weights)
    
    def _calculate_transaction_cost(self, new_weights: np.ndarray) -> float:
        """计算交易成本"""
        # 计算权重变化
        weight_changes = np.abs(new_weights - self.weights)
        
        # 假设交易成本与权重变化成正比
        total_change = np.sum(weight_changes)
        
        # 当前投资组合价值
        current_value = self.asset_memory[-1] if self.asset_memory else self.initial_amount
        
        return total_change * current_value * self.transaction_cost_pct
    
    def _reset_specific_state(self):
        """重置特定于子类的状态"""
        if self.enable_cash:
            self.weights = np.zeros(self.stock_dim + 1)
            self.weights[0] = 1.0  # 初始全部为现金
        else:
            self.weights = np.ones(self.stock_dim) / self.stock_dim
        
        self.weight_memory = []
        self.return_memory = []
        self.volatility_memory = []
        self.sharpe_memory = []
        self.days_since_rebalance = 0
        self.price_memory = []
        self.turbulence = 0
    
    def get_portfolio_weights(self) -> Dict[str, float]:
        """获取当前投资组合权重
        
        Returns:
            投资组合权重字典
        """
        weights_dict = {}
        
        if self.enable_cash:
            weights_dict['cash'] = self.weights[0]
            for i, tic in enumerate(self.tic_list):
                weights_dict[tic] = self.weights[i + 1]
        else:
            for i, tic in enumerate(self.tic_list):
                weights_dict[tic] = self.weights[i]
        
        return weights_dict
    
    def get_portfolio_performance(self) -> Dict[str, float]:
        """获取投资组合性能指标
        
        Returns:
            性能指标字典
        """
        if len(self.return_memory) == 0:
            return {
                'total_return': 0.0,
                'annualized_return': 0.0,
                'volatility': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0
            }
        
        # 总收益率
        if self.asset_memory:
            total_return = (self.asset_memory[-1] - self.initial_amount) / self.initial_amount
        else:
            total_return = 0.0
        
        # 年化收益率
        annualized_return = np.mean(self.return_memory) * 252
        
        # 波动率
        volatility = np.std(self.return_memory) * np.sqrt(252)
        
        # 夏普比率
        if volatility > 0:
            sharpe_ratio = (annualized_return - self.risk_free_rate) / volatility
        else:
            sharpe_ratio = 0.0
        
        # 最大回撤
        max_drawdown = self.get_max_drawdown()
        
        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown
        }
    
    def get_detailed_info(self) -> Dict[str, Any]:
        """获取详细信息
        
        Returns:
            详细信息字典
        """
        return {
            'day': self.day,
            'date': self._get_date(),
            'portfolio_value': self._calculate_portfolio_value(),
            'weights': self.get_portfolio_weights(),
            'performance': self.get_portfolio_performance(),
            'days_since_rebalance': self.days_since_rebalance,
            'turbulence': self.turbulence,
            'rebalance_frequency': self.rebalance_frequency
        }