"""端到端完整工作流测试

测试从数据获取到策略部署的完整工作流程。
"""

import pytest
import pandas as pd
import numpy as np
import tempfile
import shutil
import json
import time
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime, timedelta

# 注意：这里假设项目模块的结构，实际实现时需要根据项目结构调整
# from finrl_crypto.data import DataSource, DataProcessor
# from finrl_crypto.env import TradingEnvironment
# from finrl_crypto.agent import RLAgent
# from finrl_crypto.strategy import RLStrategy
# from finrl_crypto.backtest import Backtester
# from finrl_crypto.live import LiveTrader
# from finrl_crypto.utils import Config, Logger
# from finrl_crypto.monitoring import PerformanceMonitor

# 临时模拟类用于端到端测试
class MockDataSource:
    """模拟数据源"""
    
    def __init__(self, source_type: str = 'binance'):
        self.source_type = source_type
        self.connection_status = True
        self.rate_limit_remaining = 1000
    
    def connect(self) -> bool:
        """连接数据源"""
        # 模拟连接延迟
        time.sleep(0.1)
        return self.connection_status
    
    def fetch_historical_data(self, symbol: str, start_date: str, end_date: str, 
                            timeframe: str = '1h') -> pd.DataFrame:
        """获取历史数据"""
        if not self.connection_status:
            raise ConnectionError("Data source not connected")
        
        # 生成模拟历史数据
        start = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)
        dates = pd.date_range(start, end, freq=timeframe)
        
        # 基于符号生成不同的价格模式
        np.random.seed(hash(symbol) % 2**32)
        
        if 'BTC' in symbol:
            base_price = 50000
            volatility = 0.02
        elif 'ETH' in symbol:
            base_price = 3000
            volatility = 0.025
        else:
            base_price = 100
            volatility = 0.03
        
        # 生成价格序列
        returns = np.random.normal(0, volatility, len(dates))
        prices = base_price * np.exp(np.cumsum(returns))
        
        # 生成OHLCV数据
        data = pd.DataFrame({
            'timestamp': dates,
            'symbol': symbol,
            'open': prices,
            'high': prices * (1 + np.random.uniform(0, 0.01, len(dates))),
            'low': prices * (1 - np.random.uniform(0, 0.01, len(dates))),
            'close': prices,
            'volume': np.random.randint(1000, 100000, len(dates))
        })
        
        # 模拟API限制
        self.rate_limit_remaining -= 1
        
        return data
    
    def fetch_real_time_data(self, symbol: str) -> Dict[str, Any]:
        """获取实时数据"""
        if not self.connection_status:
            raise ConnectionError("Data source not connected")
        
        # 生成模拟实时数据
        base_price = 50000 if 'BTC' in symbol else 3000
        current_price = base_price * (1 + np.random.normal(0, 0.001))
        
        return {
            'symbol': symbol,
            'price': current_price,
            'timestamp': datetime.now(),
            'volume': np.random.randint(100, 1000),
            'bid': current_price * 0.999,
            'ask': current_price * 1.001
        }
    
    def get_rate_limit_status(self) -> Dict[str, int]:
        """获取API限制状态"""
        return {
            'remaining': self.rate_limit_remaining,
            'reset_time': int(time.time()) + 3600
        }

class MockExchange:
    """模拟交易所"""
    
    def __init__(self, exchange_name: str = 'binance'):
        self.exchange_name = exchange_name
        self.connected = False
        self.balance = {'USD': 100000, 'BTC': 0, 'ETH': 0}
        self.orders = []
        self.order_id_counter = 1
        self.trading_fees = 0.001
    
    def connect(self, api_key: str, api_secret: str) -> bool:
        """连接交易所"""
        # 模拟认证过程
        if api_key and api_secret:
            self.connected = True
            return True
        return False
    
    def get_balance(self) -> Dict[str, float]:
        """获取账户余额"""
        if not self.connected:
            raise ConnectionError("Exchange not connected")
        return self.balance.copy()
    
    def place_order(self, symbol: str, side: str, amount: float, 
                   order_type: str = 'market', price: Optional[float] = None) -> Dict[str, Any]:
        """下单"""
        if not self.connected:
            raise ConnectionError("Exchange not connected")
        
        order_id = f"order_{self.order_id_counter}"
        self.order_id_counter += 1
        
        # 模拟订单执行
        if order_type == 'market':
            # 市价单立即执行
            execution_price = self._get_current_price(symbol)
            status = 'filled'
        else:
            # 限价单待执行
            execution_price = price
            status = 'open'
        
        order = {
            'id': order_id,
            'symbol': symbol,
            'side': side,
            'amount': amount,
            'type': order_type,
            'price': execution_price,
            'status': status,
            'timestamp': datetime.now(),
            'fee': amount * execution_price * self.trading_fees if status == 'filled' else 0
        }
        
        self.orders.append(order)
        
        # 更新余额（如果订单已执行）
        if status == 'filled':
            self._update_balance_after_trade(order)
        
        return order
    
    def get_order_status(self, order_id: str) -> Dict[str, Any]:
        """获取订单状态"""
        for order in self.orders:
            if order['id'] == order_id:
                return order
        raise ValueError(f"Order {order_id} not found")
    
    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        for order in self.orders:
            if order['id'] == order_id and order['status'] == 'open':
                order['status'] = 'cancelled'
                return True
        return False
    
    def _get_current_price(self, symbol: str) -> float:
        """获取当前价格"""
        if 'BTC' in symbol:
            return 50000 * (1 + np.random.normal(0, 0.001))
        elif 'ETH' in symbol:
            return 3000 * (1 + np.random.normal(0, 0.001))
        return 100
    
    def _update_balance_after_trade(self, order: Dict[str, Any]):
        """交易后更新余额"""
        symbol = order['symbol']
        side = order['side']
        amount = order['amount']
        price = order['price']
        fee = order['fee']
        
        base_asset = symbol.split('-')[0]  # 例如 BTC-USD -> BTC
        quote_asset = symbol.split('-')[1]  # 例如 BTC-USD -> USD
        
        if side == 'buy':
            # 买入：减少报价资产，增加基础资产
            self.balance[quote_asset] -= amount * price + fee
            self.balance[base_asset] = self.balance.get(base_asset, 0) + amount
        else:
            # 卖出：减少基础资产，增加报价资产
            self.balance[base_asset] -= amount
            self.balance[quote_asset] = self.balance.get(quote_asset, 0) + amount * price - fee

class MockLiveTrader:
    """模拟实盘交易器"""
    
    def __init__(self, exchange: MockExchange, strategy, risk_manager=None):
        self.exchange = exchange
        self.strategy = strategy
        self.risk_manager = risk_manager
        self.is_running = False
        self.positions = {}
        self.trade_history = []
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0
        }
    
    def start_trading(self, symbols: List[str]):
        """开始交易"""
        if not self.exchange.connected:
            raise ConnectionError("Exchange not connected")
        
        self.is_running = True
        self.symbols = symbols
        
        # 模拟交易循环（简化版）
        for _ in range(10):  # 模拟10个交易周期
            if not self.is_running:
                break
            
            self._trading_cycle()
            time.sleep(0.1)  # 模拟时间间隔
    
    def stop_trading(self):
        """停止交易"""
        self.is_running = False
    
    def _trading_cycle(self):
        """单个交易周期"""
        for symbol in self.symbols:
            try:
                # 获取市场数据
                market_data = self._get_market_data(symbol)
                
                # 策略决策
                signal = self.strategy.generate_signal(market_data)
                
                # 风险管理
                if self.risk_manager:
                    signal = self.risk_manager.filter_signal(signal, self.positions)
                
                # 执行交易
                if signal and signal['action'] != 'hold':
                    self._execute_trade(symbol, signal)
                    
            except Exception as e:
                print(f"Error in trading cycle for {symbol}: {e}")
    
    def _get_market_data(self, symbol: str) -> Dict[str, Any]:
        """获取市场数据"""
        # 这里应该从数据源获取实时数据
        return {
            'symbol': symbol,
            'price': self.exchange._get_current_price(symbol),
            'timestamp': datetime.now()
        }
    
    def _execute_trade(self, symbol: str, signal: Dict[str, Any]):
        """执行交易"""
        try:
            order = self.exchange.place_order(
                symbol=symbol,
                side=signal['action'],
                amount=signal['amount'],
                order_type='market'
            )
            
            if order['status'] == 'filled':
                self.trade_history.append(order)
                self.performance_metrics['total_trades'] += 1
                
                # 更新持仓
                self._update_positions(symbol, order)
                
        except Exception as e:
            print(f"Failed to execute trade: {e}")
    
    def _update_positions(self, symbol: str, order: Dict[str, Any]):
        """更新持仓"""
        if symbol not in self.positions:
            self.positions[symbol] = {'amount': 0, 'avg_price': 0}
        
        current_pos = self.positions[symbol]
        
        if order['side'] == 'buy':
            # 计算新的平均价格
            total_amount = current_pos['amount'] + order['amount']
            if total_amount > 0:
                avg_price = ((current_pos['amount'] * current_pos['avg_price']) + 
                           (order['amount'] * order['price'])) / total_amount
                self.positions[symbol] = {'amount': total_amount, 'avg_price': avg_price}
        else:
            # 卖出
            self.positions[symbol]['amount'] -= order['amount']
            if self.positions[symbol]['amount'] <= 0:
                del self.positions[symbol]
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        return {
            'total_trades': len(self.trade_history),
            'current_positions': len(self.positions),
            'balance': self.exchange.get_balance(),
            'trade_history': self.trade_history[-10:],  # 最近10笔交易
            'performance_metrics': self.performance_metrics
        }

class MockStrategy:
    """模拟策略"""
    
    def __init__(self, strategy_type: str = 'rl'):
        self.strategy_type = strategy_type
        self.model = None
        self.parameters = {}
    
    def load_model(self, model_path: str):
        """加载模型"""
        # 模拟模型加载
        self.model = {'path': model_path, 'loaded': True}
    
    def generate_signal(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成交易信号"""
        if not self.model:
            # 随机策略
            action = np.random.choice(['buy', 'sell', 'hold'], p=[0.3, 0.3, 0.4])
        else:
            # 基于模型的策略
            action = np.random.choice(['buy', 'sell', 'hold'], p=[0.4, 0.4, 0.2])
        
        if action == 'hold':
            return {'action': 'hold'}
        
        # 计算交易数量（简化）
        amount = np.random.uniform(0.01, 0.1)  # 随机数量
        
        return {
            'action': action,
            'amount': amount,
            'confidence': np.random.uniform(0.5, 1.0),
            'timestamp': datetime.now()
        }

class MockRiskManager:
    """模拟风险管理器"""
    
    def __init__(self, max_position_size: float = 0.1, max_daily_loss: float = 0.05):
        self.max_position_size = max_position_size
        self.max_daily_loss = max_daily_loss
        self.daily_pnl = 0.0
    
    def filter_signal(self, signal: Dict[str, Any], positions: Dict[str, Any]) -> Dict[str, Any]:
        """过滤交易信号"""
        if signal['action'] == 'hold':
            return signal
        
        # 检查持仓限制
        if signal['amount'] > self.max_position_size:
            signal['amount'] = self.max_position_size
        
        # 检查日损失限制
        if self.daily_pnl < -self.max_daily_loss:
            return {'action': 'hold'}
        
        return signal

class MockPerformanceMonitor:
    """模拟性能监控器"""
    
    def __init__(self):
        self.metrics_history = []
        self.alerts = []
    
    def update_metrics(self, portfolio_value: float, positions: Dict, trades: List):
        """更新指标"""
        metrics = {
            'timestamp': datetime.now(),
            'portfolio_value': portfolio_value,
            'num_positions': len(positions),
            'num_trades': len(trades),
            'daily_return': np.random.normal(0, 0.01)  # 模拟日收益
        }
        
        self.metrics_history.append(metrics)
        
        # 检查警报条件
        if abs(metrics['daily_return']) > 0.05:
            self.alerts.append({
                'type': 'high_volatility',
                'message': f"High daily return: {metrics['daily_return']:.2%}",
                'timestamp': datetime.now()
            })
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        if not self.metrics_history:
            return {'error': 'No metrics available'}
        
        recent_metrics = self.metrics_history[-30:]  # 最近30个数据点
        
        return {
            'total_return': np.random.uniform(-0.1, 0.2),
            'sharpe_ratio': np.random.uniform(0.5, 2.0),
            'max_drawdown': np.random.uniform(0.05, 0.15),
            'win_rate': np.random.uniform(0.4, 0.7),
            'avg_trade_return': np.random.uniform(-0.01, 0.02),
            'volatility': np.random.uniform(0.1, 0.3),
            'num_trades': len(self.metrics_history),
            'alerts': self.alerts[-5:]  # 最近5个警报
        }

class TestCompleteWorkflow:
    """完整工作流端到端测试类"""
    
    @pytest.fixture
    def temp_workspace(self):
        """临时工作空间"""
        workspace = tempfile.mkdtemp(prefix="e2e_test_")
        yield Path(workspace)
        shutil.rmtree(workspace, ignore_errors=True)
    
    @pytest.fixture
    def mock_config(self, temp_workspace):
        """模拟配置"""
        config = {
            'data': {
                'source': 'binance',
                'symbols': ['BTC-USD', 'ETH-USD'],
                'timeframe': '1h',
                'lookback_days': 30
            },
            'training': {
                'algorithm': 'PPO',
                'total_timesteps': 5000,
                'save_path': str(temp_workspace / 'models')
            },
            'backtest': {
                'initial_capital': 100000,
                'start_date': '2023-01-01',
                'end_date': '2023-03-31'
            },
            'live_trading': {
                'exchange': 'binance',
                'api_key': 'test_key',
                'api_secret': 'test_secret',
                'max_position_size': 0.1
            },
            'risk_management': {
                'max_daily_loss': 0.05,
                'stop_loss': 0.02,
                'take_profit': 0.05
            },
            'monitoring': {
                'update_frequency': 60,
                'alert_thresholds': {
                    'daily_loss': 0.03,
                    'drawdown': 0.1
                }
            }
        }
        
        # 保存配置文件
        config_path = temp_workspace / 'config.json'
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        return config
    
    def test_full_pipeline_data_to_deployment(self, mock_config, temp_workspace):
        """测试从数据获取到部署的完整流程"""
        # 1. 数据获取阶段
        print("\n=== 数据获取阶段 ===")
        data_source = MockDataSource('binance')
        assert data_source.connect()
        
        # 获取历史数据
        all_data = []
        for symbol in mock_config['data']['symbols']:
            data = data_source.fetch_historical_data(
                symbol=symbol,
                start_date='2023-01-01',
                end_date='2023-06-30',
                timeframe=mock_config['data']['timeframe']
            )
            all_data.append(data)
        
        combined_data = pd.concat(all_data, ignore_index=True)
        assert not combined_data.empty
        assert len(combined_data) > 1000  # 足够的数据点
        
        # 保存原始数据
        data_path = temp_workspace / 'raw_data.csv'
        combined_data.to_csv(data_path, index=False)
        assert data_path.exists()
        
        # 2. 数据预处理阶段
        print("\n=== 数据预处理阶段 ===")
        # 添加技术指标（简化版）
        processed_data = combined_data.copy()
        for symbol in mock_config['data']['symbols']:
            symbol_data = processed_data[processed_data['symbol'] == symbol]
            # 添加简单移动平均
            processed_data.loc[processed_data['symbol'] == symbol, 'sma_20'] = \
                symbol_data['close'].rolling(20).mean()
        
        # 分割训练和测试数据
        train_end_date = '2023-04-30'
        train_data = processed_data[processed_data['timestamp'] <= train_end_date]
        test_data = processed_data[processed_data['timestamp'] > train_end_date]
        
        assert len(train_data) > 0
        assert len(test_data) > 0
        
        # 3. 模型训练阶段
        print("\n=== 模型训练阶段 ===")
        # 创建训练环境（模拟）
        from tests.integration.test_training_pipeline import make_env, create_agent
        
        train_env = make_env('multi_asset', data=train_data)
        agent = create_agent(
            mock_config['training']['algorithm'],
            train_env
        )
        
        # 训练模型
        agent.train(total_timesteps=mock_config['training']['total_timesteps'])
        assert agent.is_trained
        
        # 保存训练好的模型
        model_dir = Path(mock_config['training']['save_path'])
        model_dir.mkdir(parents=True, exist_ok=True)
        model_path = model_dir / 'trained_model.json'
        agent.save(str(model_path))
        assert model_path.exists()
        
        # 4. 回测阶段
        print("\n=== 回测阶段 ===")
        test_env = make_env('multi_asset', data=test_data, 
                          initial_amount=mock_config['backtest']['initial_capital'])
        
        # 加载模型进行回测
        backtest_agent = create_agent(mock_config['training']['algorithm'], test_env)
        backtest_agent.load(str(model_path))
        
        # 运行回测
        obs = test_env.reset()
        backtest_results = {
            'trades': [],
            'portfolio_values': [test_env.portfolio_value],
            'actions': []
        }
        
        done = False
        step_count = 0
        while not done and step_count < 100:
            action, _ = backtest_agent.predict(obs, deterministic=True)
            backtest_results['actions'].append(action.tolist())
            
            obs, reward, done, info = test_env.step(action)
            backtest_results['portfolio_values'].append(info['portfolio_value'])
            step_count += 1
        
        # 计算回测指标
        initial_value = backtest_results['portfolio_values'][0]
        final_value = backtest_results['portfolio_values'][-1]
        total_return = (final_value - initial_value) / initial_value
        
        backtest_summary = {
            'total_return': total_return,
            'final_portfolio_value': final_value,
            'num_steps': step_count,
            'num_actions': len(backtest_results['actions'])
        }
        
        # 保存回测结果
        backtest_path = temp_workspace / 'backtest_results.json'
        with open(backtest_path, 'w') as f:
            json.dump(backtest_summary, f, indent=2)
        
        assert backtest_path.exists()
        assert isinstance(total_return, (int, float))
        
        # 5. 策略部署准备阶段
        print("\n=== 策略部署准备阶段 ===")
        # 创建策略对象
        strategy = MockStrategy('rl')
        strategy.load_model(str(model_path))
        
        # 创建风险管理器
        risk_manager = MockRiskManager(
            max_position_size=mock_config['live_trading']['max_position_size'],
            max_daily_loss=mock_config['risk_management']['max_daily_loss']
        )
        
        # 创建性能监控器
        performance_monitor = MockPerformanceMonitor()
        
        # 6. 模拟实盘交易阶段
        print("\n=== 模拟实盘交易阶段 ===")
        # 连接交易所
        exchange = MockExchange(mock_config['live_trading']['exchange'])
        connected = exchange.connect(
            mock_config['live_trading']['api_key'],
            mock_config['live_trading']['api_secret']
        )
        assert connected
        
        # 创建实盘交易器
        live_trader = MockLiveTrader(exchange, strategy, risk_manager)
        
        # 开始交易（短时间模拟）
        live_trader.start_trading(mock_config['data']['symbols'])
        
        # 获取交易结果
        performance_summary = live_trader.get_performance_summary()
        
        assert 'total_trades' in performance_summary
        assert 'balance' in performance_summary
        
        # 7. 监控和报告阶段
        print("\n=== 监控和报告阶段 ===")
        # 更新性能指标
        performance_monitor.update_metrics(
            portfolio_value=sum(performance_summary['balance'].values()),
            positions=live_trader.positions,
            trades=live_trader.trade_history
        )
        
        # 生成性能报告
        performance_report = performance_monitor.get_performance_report()
        
        assert 'total_return' in performance_report
        assert 'sharpe_ratio' in performance_report
        
        # 保存最终报告
        final_report = {
            'workflow_completed': True,
            'data_points_processed': len(combined_data),
            'training_steps': mock_config['training']['total_timesteps'],
            'backtest_return': total_return,
            'live_trading_summary': performance_summary,
            'performance_report': performance_report,
            'completion_time': datetime.now().isoformat()
        }
        
        report_path = temp_workspace / 'final_report.json'
        with open(report_path, 'w') as f:
            json.dump(final_report, f, indent=2, default=str)
        
        assert report_path.exists()
        
        # 验证整个流程的完整性
        assert final_report['workflow_completed']
        assert final_report['data_points_processed'] > 0
        assert final_report['training_steps'] > 0
        
        print(f"\n=== 工作流程完成 ===")
        print(f"处理数据点: {final_report['data_points_processed']}")
        print(f"训练步数: {final_report['training_steps']}")
        print(f"回测收益: {final_report['backtest_return']:.2%}")
        print(f"实盘交易次数: {final_report['live_trading_summary']['total_trades']}")
    
    def test_workflow_error_handling(self, mock_config, temp_workspace):
        """测试工作流程错误处理"""
        # 测试数据源连接失败
        data_source = MockDataSource()
        data_source.connection_status = False
        
        with pytest.raises(ConnectionError):
            data_source.fetch_historical_data('BTC-USD', '2023-01-01', '2023-01-31')
        
        # 测试交易所连接失败
        exchange = MockExchange()
        assert not exchange.connect('', '')  # 空凭据
        
        with pytest.raises(ConnectionError):
            exchange.get_balance()
        
        # 测试无效模型路径
        strategy = MockStrategy()
        invalid_path = temp_workspace / 'nonexistent_model.json'
        
        # 这里应该有适当的错误处理
        try:
            strategy.load_model(str(invalid_path))
        except FileNotFoundError:
            pass  # 预期的错误
    
    def test_workflow_performance_benchmarks(self, mock_config, temp_workspace):
        """测试工作流程性能基准"""
        start_time = time.time()
        
        # 简化的性能测试流程
        data_source = MockDataSource()
        data_source.connect()
        
        # 数据获取性能
        data_start = time.time()
        data = data_source.fetch_historical_data('BTC-USD', '2023-01-01', '2023-03-31')
        data_time = time.time() - data_start
        
        assert data_time < 5.0  # 数据获取应在5秒内完成
        assert len(data) > 0
        
        # 训练性能
        from tests.integration.test_training_pipeline import make_env, create_agent
        
        train_start = time.time()
        env = make_env('single_asset', data=data)
        agent = create_agent('PPO', env)
        agent.train(total_timesteps=1000)  # 较少的步数用于性能测试
        train_time = time.time() - train_start
        
        assert train_time < 10.0  # 训练应在10秒内完成
        assert agent.is_trained
        
        # 回测性能
        backtest_start = time.time()
        obs = env.reset()
        for _ in range(50):  # 50步回测
            action, _ = agent.predict(obs)
            obs, _, done, _ = env.step(action)
            if done:
                break
        backtest_time = time.time() - backtest_start
        
        assert backtest_time < 2.0  # 回测应在2秒内完成
        
        total_time = time.time() - start_time
        assert total_time < 20.0  # 整个流程应在20秒内完成
        
        # 记录性能指标
        performance_metrics = {
            'data_fetch_time': data_time,
            'training_time': train_time,
            'backtest_time': backtest_time,
            'total_time': total_time
        }
        
        metrics_path = temp_workspace / 'performance_metrics.json'
        with open(metrics_path, 'w') as f:
            json.dump(performance_metrics, f, indent=2)
        
        assert metrics_path.exists()
    
    def test_workflow_data_integrity(self, mock_config, temp_workspace):
        """测试工作流程数据完整性"""
        # 数据获取
        data_source = MockDataSource()
        data_source.connect()
        
        original_data = data_source.fetch_historical_data('BTC-USD', '2023-01-01', '2023-02-28')
        
        # 保存和加载数据
        data_path = temp_workspace / 'test_data.csv'
        original_data.to_csv(data_path, index=False)
        loaded_data = pd.read_csv(data_path)
        
        # 验证数据完整性
        assert len(original_data) == len(loaded_data)
        assert list(original_data.columns) == list(loaded_data.columns)
        
        # 验证数据类型
        assert loaded_data['close'].dtype in [np.float64, np.float32]
        assert loaded_data['volume'].dtype in [np.int64, np.int32]
        
        # 验证数据范围
        assert loaded_data['close'].min() > 0
        assert loaded_data['volume'].min() >= 0
        assert loaded_data['high'].min() >= loaded_data['low'].min()
        
        # 验证时间序列连续性
        timestamps = pd.to_datetime(loaded_data['timestamp'])
        time_diffs = timestamps.diff().dropna()
        
        # 大部分时间差应该是一致的（1小时）
        expected_diff = pd.Timedelta(hours=1)
        consistent_diffs = (time_diffs == expected_diff).sum()
        assert consistent_diffs / len(time_diffs) > 0.8  # 80%的时间差应该一致
    
    @pytest.mark.slow
    def test_extended_workflow_simulation(self, mock_config, temp_workspace):
        """测试扩展工作流程模拟"""
        # 模拟更长时间的工作流程
        extended_config = mock_config.copy()
        extended_config['data']['symbols'] = ['BTC-USD', 'ETH-USD', 'ADA-USD']
        extended_config['training']['total_timesteps'] = 10000
        
        # 数据获取阶段
        data_source = MockDataSource()
        data_source.connect()
        
        all_data = []
        for symbol in extended_config['data']['symbols']:
            data = data_source.fetch_historical_data(
                symbol=symbol,
                start_date='2023-01-01',
                end_date='2023-12-31',  # 全年数据
                timeframe='1h'
            )
            all_data.append(data)
        
        combined_data = pd.concat(all_data, ignore_index=True)
        assert len(combined_data) > 5000  # 大量数据点
        
        # 训练阶段
        from tests.integration.test_training_pipeline import make_env, create_agent
        
        train_data = combined_data.iloc[:int(len(combined_data) * 0.8)]
        env = make_env('multi_asset', data=train_data)
        agent = create_agent('PPO', env)
        
        # 分阶段训练
        training_stages = [2000, 4000, 6000, 8000, 10000]
        training_history = []
        
        for stage in training_stages:
            prev_stage = training_history[-1]['total_steps'] if training_history else 0
            steps_to_train = stage - prev_stage
            
            agent.train(total_timesteps=steps_to_train)
            
            # 记录训练进度
            stage_metrics = {
                'stage': len(training_history) + 1,
                'total_steps': stage,
                'is_trained': agent.is_trained,
                'history_length': len(agent.training_history)
            }
            training_history.append(stage_metrics)
        
        # 验证训练进度
        assert len(training_history) == len(training_stages)
        assert all(stage['is_trained'] for stage in training_history)
        
        # 多轮回测
        test_data = combined_data.iloc[int(len(combined_data) * 0.8):]
        test_periods = [
            test_data.iloc[:len(test_data)//3],
            test_data.iloc[len(test_data)//3:2*len(test_data)//3],
            test_data.iloc[2*len(test_data)//3:]
        ]
        
        backtest_results = []
        for i, period_data in enumerate(test_periods):
            test_env = make_env('multi_asset', data=period_data)
            
            obs = test_env.reset()
            period_results = {'period': i+1, 'steps': 0, 'final_value': 0}
            
            done = False
            while not done and period_results['steps'] < 100:
                action, _ = agent.predict(obs, deterministic=True)
                obs, _, done, info = test_env.step(action)
                period_results['steps'] += 1
                period_results['final_value'] = info['portfolio_value']
            
            backtest_results.append(period_results)
        
        # 验证回测结果
        assert len(backtest_results) == 3
        assert all(result['steps'] > 0 for result in backtest_results)
        
        # 保存扩展测试结果
        extended_results = {
            'data_points': len(combined_data),
            'symbols_tested': len(extended_config['data']['symbols']),
            'training_stages': training_history,
            'backtest_periods': backtest_results,
            'completion_timestamp': datetime.now().isoformat()
        }
        
        results_path = temp_workspace / 'extended_results.json'
        with open(results_path, 'w') as f:
            json.dump(extended_results, f, indent=2, default=str)
        
        assert results_path.exists()

class TestWorkflowIntegration:
    """工作流程集成测试类"""
    
    def test_component_integration(self):
        """测试组件集成"""
        # 测试数据源和交易所集成
        data_source = MockDataSource()
        exchange = MockExchange()
        
        # 数据源连接
        assert data_source.connect()
        
        # 交易所连接
        assert exchange.connect('test_key', 'test_secret')
        
        # 获取实时数据
        real_time_data = data_source.fetch_real_time_data('BTC-USD')
        assert 'price' in real_time_data
        
        # 基于实时数据下单
        order = exchange.place_order(
            symbol='BTC-USD',
            side='buy',
            amount=0.01,
            order_type='market'
        )
        
        assert order['status'] == 'filled'
        assert order['symbol'] == 'BTC-USD'
    
    def test_strategy_risk_integration(self):
        """测试策略和风险管理集成"""
        strategy = MockStrategy()
        risk_manager = MockRiskManager(max_position_size=0.05)
        
        # 生成信号
        market_data = {'symbol': 'BTC-USD', 'price': 50000}
        signal = strategy.generate_signal(market_data)
        
        if signal['action'] != 'hold':
            # 风险过滤
            filtered_signal = risk_manager.filter_signal(signal, {})
            
            # 验证风险限制
            if filtered_signal['action'] != 'hold':
                assert filtered_signal['amount'] <= risk_manager.max_position_size
    
    def test_monitoring_integration(self):
        """测试监控集成"""
        monitor = MockPerformanceMonitor()
        exchange = MockExchange()
        exchange.connect('test_key', 'test_secret')
        
        # 模拟交易活动
        trades = []
        for i in range(5):
            order = exchange.place_order(
                symbol='BTC-USD',
                side='buy' if i % 2 == 0 else 'sell',
                amount=0.01,
                order_type='market'
            )
            trades.append(order)
        
        # 更新监控指标
        balance = exchange.get_balance()
        portfolio_value = sum(balance.values())
        
        monitor.update_metrics(
            portfolio_value=portfolio_value,
            positions={},
            trades=trades
        )
        
        # 获取报告
        report = monitor.get_performance_report()
        assert 'total_return' in report
        assert 'num_trades' in report
    
    def test_end_to_end_data_flow(self):
        """测试端到端数据流"""
        # 创建所有组件
        data_source = MockDataSource()
        exchange = MockExchange()
        strategy = MockStrategy()
        risk_manager = MockRiskManager()
        monitor = MockPerformanceMonitor()
        
        # 连接
        data_source.connect()
        exchange.connect('test_key', 'test_secret')
        
        # 数据流测试
        for _ in range(3):
            # 1. 获取市场数据
            market_data = data_source.fetch_real_time_data('BTC-USD')
            
            # 2. 策略决策
            signal = strategy.generate_signal(market_data)
            
            # 3. 风险过滤
            filtered_signal = risk_manager.filter_signal(signal, {})
            
            # 4. 执行交易（如果需要）
            if filtered_signal['action'] != 'hold':
                order = exchange.place_order(
                    symbol='BTC-USD',
                    side=filtered_signal['action'],
                    amount=filtered_signal['amount'],
                    order_type='market'
                )
                
                # 5. 更新监控
                balance = exchange.get_balance()
                monitor.update_metrics(
                    portfolio_value=sum(balance.values()),
                    positions={},
                    trades=[order]
                )
        
        # 验证数据流完整性
        final_report = monitor.get_performance_report()
        assert isinstance(final_report, dict)
        
        final_balance = exchange.get_balance()
        assert isinstance(final_balance, dict)
        assert 'USD' in final_balance

class TestWorkflowScalability:
    """工作流程可扩展性测试类"""
    
    @pytest.mark.parametrize("num_symbols", [1, 3, 5])
    def test_multi_symbol_scalability(self, num_symbols):
        """测试多符号可扩展性"""
        symbols = [f"SYMBOL{i}-USD" for i in range(num_symbols)]
        
        data_source = MockDataSource()
        data_source.connect()
        
        # 获取多符号数据
        all_data = []
        for symbol in symbols:
            data = data_source.fetch_historical_data(
                symbol=symbol,
                start_date='2023-01-01',
                end_date='2023-01-31'
            )
            all_data.append(data)
        
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # 验证数据规模
        expected_symbols = set(symbols)
        actual_symbols = set(combined_data['symbol'].unique())
        assert expected_symbols == actual_symbols
        
        # 验证数据量随符号数量线性增长
        symbol_counts = combined_data['symbol'].value_counts()
        assert len(symbol_counts) == num_symbols
        
        # 每个符号的数据量应该相似
        count_std = symbol_counts.std()
        count_mean = symbol_counts.mean()
        
        # 检查是否有有效的统计值
        if count_mean > 0 and not pd.isna(count_std) and not pd.isna(count_mean):
            coefficient_of_variation = count_std / count_mean
            assert coefficient_of_variation < 0.1  # 变异系数小于10%
        else:
            # 如果统计值无效，至少确保每个符号都有数据
            assert all(count > 0 for count in symbol_counts)
    
    def test_high_frequency_data_handling(self):
        """测试高频数据处理"""
        data_source = MockDataSource()
        data_source.connect()
        
        # 获取高频数据（1分钟）
        high_freq_data = data_source.fetch_historical_data(
            symbol='BTC-USD',
            start_date='2023-01-01',
            end_date='2023-01-02',  # 1天的分钟数据
            timeframe='1min'
        )
        
        # 验证数据量
        expected_points = 24 * 60  # 1天 * 24小时 * 60分钟
        assert len(high_freq_data) >= expected_points * 0.9  # 允许10%的误差
        
        # 验证时间间隔
        timestamps = pd.to_datetime(high_freq_data['timestamp'])
        time_diffs = timestamps.diff().dropna()
        
        # 大部分时间差应该是1分钟
        expected_diff = pd.Timedelta(minutes=1)
        consistent_diffs = (time_diffs == expected_diff).sum()
        assert consistent_diffs / len(time_diffs) > 0.8
    
    def test_memory_efficiency_large_datasets(self, memory_monitor):
        """测试大数据集的内存效率"""
        initial_memory = memory_monitor
        
        data_source = MockDataSource()
        data_source.connect()
        
        # 获取大量数据
        large_datasets = []
        for i in range(10):  # 10个数据集
            data = data_source.fetch_historical_data(
                symbol=f'SYMBOL{i}-USD',
                start_date='2023-01-01',
                end_date='2023-06-30',  # 6个月数据
                timeframe='1h'
            )
            large_datasets.append(data)
        
        # 合并数据
        combined_large_data = pd.concat(large_datasets, ignore_index=True)
        
        # 验证数据规模
        assert len(combined_large_data) > 10000  # 大量数据点
        
        # 内存使用应该在合理范围内
        # memory_monitor fixture 会在测试结束时检查
        
        # 清理数据以释放内存
        del large_datasets
        del combined_large_data
    
    def test_concurrent_operations(self):
        """测试并发操作"""
        import threading
        import queue
        
        data_source = MockDataSource()
        data_source.connect()
        
        results_queue = queue.Queue()
        
        def fetch_data_worker(symbol, result_queue):
            """数据获取工作线程"""
            try:
                data = data_source.fetch_historical_data(
                    symbol=symbol,
                    start_date='2023-01-01',
                    end_date='2023-01-31'
                )
                result_queue.put(('success', symbol, len(data)))
            except Exception as e:
                result_queue.put(('error', symbol, str(e)))
        
        # 启动多个并发线程
        symbols = ['BTC-USD', 'ETH-USD', 'ADA-USD', 'DOT-USD']
        threads = []
        
        for symbol in symbols:
            thread = threading.Thread(
                target=fetch_data_worker,
                args=(symbol, results_queue)
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=10)  # 10秒超时
        
        # 收集结果
        results = []
        while not results_queue.empty():
            results.append(results_queue.get())
        
        # 验证结果
        assert len(results) == len(symbols)
        
        successful_results = [r for r in results if r[0] == 'success']
        assert len(successful_results) == len(symbols)  # 所有请求都应该成功
        
        # 验证每个符号都有数据
        fetched_symbols = {r[1] for r in successful_results}
        assert fetched_symbols == set(symbols)