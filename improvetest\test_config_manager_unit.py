# -*- coding: utf-8 -*-
"""
配置管理模块单元测试

测试 core/config_manager.py 的各项功能
"""

import os
import json
import yaml
import tempfile
import pytest
import unittest
from unittest.mock import patch, mock_open, MagicMock
from pathlib import Path

# 导入被测试的模块
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
from config_manager import (
    ConfigLoadError,
    ConfigValidationError,
    ConfigLoader
)


class TestConfigLoadError(unittest.TestCase):
    """测试配置加载错误异常"""
    
    def test_config_load_error_creation(self):
        """测试配置加载错误的创建"""
        error_msg = "配置文件不存在"
        error = ConfigLoadError(error_msg)
        self.assertEqual(str(error), error_msg)
        self.assertIsInstance(error, Exception)


class TestConfigValidationError(unittest.TestCase):
    """测试配置验证错误异常"""
    
    def test_config_validation_error_creation(self):
        """测试配置验证错误的创建"""
        error_msg = "配置格式不正确"
        error = ConfigValidationError(error_msg)
        self.assertEqual(str(error), error_msg)
        self.assertIsInstance(error, Exception)


class MockConfigLoader(ConfigLoader):
    """模拟配置加载器实现"""
    
    def load(self, source: str, **kwargs):
        """模拟加载配置"""
        if source == "valid_config":
            return {"key": "value", "number": 42}
        elif source == "invalid_config":
            raise ConfigLoadError("无法加载配置")
        else:
            return {}


class TestConfigLoader(unittest.TestCase):
    """测试配置加载器抽象基类"""
    
    def setUp(self):
        """设置测试环境"""
        self.loader = MockConfigLoader()
    
    def test_abstract_base_class(self):
        """测试抽象基类不能直接实例化"""
        with self.assertRaises(TypeError):
            ConfigLoader()
    
    def test_load_valid_config(self):
        """测试加载有效配置"""
        result = self.loader.load("valid_config")
        expected = {"key": "value", "number": 42}
        self.assertEqual(result, expected)
    
    def test_load_invalid_config(self):
        """测试加载无效配置"""
        with self.assertRaises(ConfigLoadError):
            self.loader.load("invalid_config")
    
    def test_load_empty_config(self):
        """测试加载空配置"""
        result = self.loader.load("empty_config")
        self.assertEqual(result, {})


class TestConfigManagerIntegration(unittest.TestCase):
    """配置管理器集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.yaml")
        
        # 创建测试配置文件
        test_config = {
            "database": {
                "host": "localhost",
                "port": 5432,
                "name": "test_db"
            },
            "api": {
                "timeout": 30,
                "retries": 3
            },
            "features": {
                "enable_cache": True,
                "debug_mode": False
            }
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(test_config, f, default_flow_style=False)
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_yaml_config_loading(self):
        """测试YAML配置文件加载"""
        # 这里需要实际的ConfigManager实现来测试
        # 由于只看到了抽象基类，这里提供测试框架
        self.assertTrue(os.path.exists(self.config_file))
        
        with open(self.config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        self.assertIn("database", config)
        self.assertEqual(config["database"]["host"], "localhost")
        self.assertEqual(config["database"]["port"], 5432)
    
    def test_json_config_loading(self):
        """测试JSON配置文件加载"""
        json_config_file = os.path.join(self.temp_dir, "test_config.json")
        test_config = {
            "app_name": "FinRL_Crypto",
            "version": "1.0.0",
            "settings": {
                "log_level": "INFO",
                "max_workers": 4
            }
        }
        
        with open(json_config_file, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, indent=2)
        
        with open(json_config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        self.assertEqual(config["app_name"], "FinRL_Crypto")
        self.assertEqual(config["settings"]["log_level"], "INFO")
    
    def test_environment_variable_override(self):
        """测试环境变量覆盖配置"""
        # 设置环境变量
        os.environ["TEST_DB_HOST"] = "production.db.com"
        os.environ["TEST_API_TIMEOUT"] = "60"
        
        try:
            # 验证环境变量设置
            self.assertEqual(os.environ.get("TEST_DB_HOST"), "production.db.com")
            self.assertEqual(os.environ.get("TEST_API_TIMEOUT"), "60")
        finally:
            # 清理环境变量
            os.environ.pop("TEST_DB_HOST", None)
            os.environ.pop("TEST_API_TIMEOUT", None)


class TestConfigValidation(unittest.TestCase):
    """配置验证测试"""
    
    def test_schema_validation_success(self):
        """测试配置模式验证成功"""
        schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "number", "minimum": 0}
            },
            "required": ["name"]
        }
        
        valid_config = {"name": "test", "age": 25}
        
        try:
            from jsonschema import validate
            validate(valid_config, schema)
        except Exception as e:
            self.fail(f"配置验证失败: {e}")
    
    def test_schema_validation_failure(self):
        """测试配置模式验证失败"""
        schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "number", "minimum": 0}
            },
            "required": ["name"]
        }
        
        invalid_config = {"age": -5}  # 缺少必需的name字段，age为负数
        
        from jsonschema import validate, ValidationError
        with self.assertRaises(ValidationError):
            validate(invalid_config, schema)


class TestConfigMerging(unittest.TestCase):
    """配置合并测试"""
    
    def test_deep_merge_configs(self):
        """测试深度合并配置"""
        base_config = {
            "database": {
                "host": "localhost",
                "port": 5432,
                "options": {
                    "timeout": 30,
                    "pool_size": 10
                }
            },
            "logging": {
                "level": "INFO"
            }
        }
        
        override_config = {
            "database": {
                "host": "production.db.com",
                "options": {
                    "timeout": 60
                }
            },
            "cache": {
                "enabled": True
            }
        }
        
        # 简单的深度合并实现
        def deep_merge(base, override):
            result = base.copy()
            for key, value in override.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value
            return result
        
        merged = deep_merge(base_config, override_config)
        
        # 验证合并结果
        self.assertEqual(merged["database"]["host"], "production.db.com")
        self.assertEqual(merged["database"]["port"], 5432)  # 保持原值
        self.assertEqual(merged["database"]["options"]["timeout"], 60)  # 覆盖值
        self.assertEqual(merged["database"]["options"]["pool_size"], 10)  # 保持原值
        self.assertEqual(merged["logging"]["level"], "INFO")  # 保持原值
        self.assertTrue(merged["cache"]["enabled"])  # 新增值


if __name__ == '__main__':
    unittest.main()