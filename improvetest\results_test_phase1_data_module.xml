<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="8" skipped="0" tests="18" time="40.580" timestamp="2025-06-19T02:03:12.936262+09:00" hostname="E-5CG22747W5"><testcase classname="improvetest.test_phase1_data_module.TestDataProcessor" name="test_data_processor_abstract_methods" time="1.304" /><testcase classname="improvetest.test_phase1_data_module.TestDataProcessor" name="test_data_processor_initialization" time="0.030" /><testcase classname="improvetest.test_phase1_data_module.TestDataProcessor" name="test_data_processor_with_optional_params" time="0.005" /><testcase classname="improvetest.test_phase1_data_module.TestBinanceProcessor" name="test_binance_processor_initialization" time="0.051" /><testcase classname="improvetest.test_phase1_data_module.TestBinanceProcessor" name="test_data_preprocessing" time="0.297"><failure message="AttributeError: 'BinanceProcessor' object has no attribute 'preprocess_data'">improvetest\test_phase1_data_module.py:175: in test_data_preprocessing
    processed_data = processor.preprocess_data(self.mock_data)
E   AttributeError: 'BinanceProcessor' object has no attribute 'preprocess_data'</failure></testcase><testcase classname="improvetest.test_phase1_data_module.TestBinanceProcessor" name="test_data_validation" time="0.110"><failure message="AttributeError: 'BinanceProcessor' object has no attribute 'validate_data'">improvetest\test_phase1_data_module.py:160: in test_data_validation
    is_valid = processor.validate_data(self.mock_data)
E   AttributeError: 'BinanceProcessor' object has no attribute 'validate_data'</failure></testcase><testcase classname="improvetest.test_phase1_data_module.TestBinanceProcessor" name="test_fetch_data" time="0.064"><failure message="ValueError: 未能下载任何数据">improvetest\test_phase1_data_module.py:149: in test_fetch_data
    data = processor.download_data(['BTCUSDT'])
finrl_crypto\data\processors.py:106: in download_data
    raise ValueError("未能下载任何数据")
E   ValueError: 未能下载任何数据</failure></testcase><testcase classname="improvetest.test_phase1_data_module.TestTechnicalIndicators" name="test_bollinger_bands_calculation" time="0.018" /><testcase classname="improvetest.test_phase1_data_module.TestTechnicalIndicators" name="test_ema_calculation" time="0.006" /><testcase classname="improvetest.test_phase1_data_module.TestTechnicalIndicators" name="test_macd_calculation" time="0.011" /><testcase classname="improvetest.test_phase1_data_module.TestTechnicalIndicators" name="test_rsi_calculation" time="0.023" /><testcase classname="improvetest.test_phase1_data_module.TestTechnicalIndicators" name="test_sma_calculation" time="0.007" /><testcase classname="improvetest.test_phase1_data_module.TestDataManager" name="test_add_data_source" time="0.208"><failure message="AttributeError: 'DataManager' object has no attribute 'add_data_source'. Did you mean: 'data_source'?">improvetest\test_phase1_data_module.py:281: in test_add_data_source
    self.data_manager.add_data_source('test_source', processor)
E   AttributeError: 'DataManager' object has no attribute 'add_data_source'. Did you mean: 'data_source'?</failure></testcase><testcase classname="improvetest.test_phase1_data_module.TestDataManager" name="test_data_manager_initialization" time="0.062" /><testcase classname="improvetest.test_phase1_data_module.TestDataManager" name="test_get_data" time="0.064"><failure message="AttributeError: Mock object has no attribute 'fetch_data'">improvetest\test_phase1_data_module.py:289: in test_get_data
    mock_processor.fetch_data.return_value = self.mock_data
C:\Python313\Lib\unittest\mock.py:690: in __getattr__
    raise AttributeError("Mock object has no attribute %r" % name)
E   AttributeError: Mock object has no attribute 'fetch_data'</failure></testcase><testcase classname="improvetest.test_phase1_data_module.TestDataManager" name="test_merge_data_sources" time="0.092"><failure message="AttributeError: Mock object has no attribute 'fetch_data'">improvetest\test_phase1_data_module.py:309: in test_merge_data_sources
    mock_processor1.fetch_data.return_value = data1
C:\Python313\Lib\unittest\mock.py:690: in __getattr__
    raise AttributeError("Mock object has no attribute %r" % name)
E   AttributeError: Mock object has no attribute 'fetch_data'</failure></testcase><testcase classname="improvetest.test_phase1_data_module.TestDataModuleIntegration" name="test_data_quality_checks" time="0.008"><failure message="TypeError: finrl_crypto.data.base.DataProcessor.__init__() got multiple values for keyword argument 'data_source'">improvetest\test_phase1_data_module.py:399: in test_data_quality_checks
    processor = BinanceProcessor(
finrl_crypto\data\processors.py:50: in __init__
    super().__init__(
E   TypeError: finrl_crypto.data.base.DataProcessor.__init__() got multiple values for keyword argument 'data_source'</failure></testcase><testcase classname="improvetest.test_phase1_data_module.TestDataModuleIntegration" name="test_end_to_end_data_pipeline" time="0.030"><failure message="ValueError: 未能下载任何数据">improvetest\test_phase1_data_module.py:350: in test_end_to_end_data_pipeline
    raw_data = processor.download_data(self.symbols)
finrl_crypto\data\processors.py:106: in download_data
    raise ValueError("未能下载任何数据")
E   ValueError: 未能下载任何数据</failure></testcase></testsuite></testsuites>