[run]
# 覆盖率运行配置
source = .
branch = True
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */env/*
    */.venv/*
    */.env/*
    */site-packages/*
    */migrations/*
    */settings/*
    */manage.py
    */setup.py
    */conftest.py
    */.tox/*
    */build/*
    */dist/*
    */.pytest_cache/*
    */htmlcov/*
    */docs/*
    */examples/*
    */scripts/*
    */tools/*
    */deprecated/*
    */legacy/*
    */backup/*
    */temp/*
    */tmp/*
    */.idea/*
    */.vscode/*
    */.git/*
    */node_modules/*
    */static/*
    */media/*
    */logs/*
    */log/*
    */cache/*
    */.cache/*
    */train_results/*
    */plots_and_metrics/*
    */data/*
    */models/*
    */checkpoints/*
    */weights/*
    */saved_models/*
    */outputs/*
    */results/*
    */reports/*
    */artifacts/*
    */downloads/*
    */uploads/*
    */backups/*
    */archives/*
    */dumps/*
    */exports/*
    */imports/*
    */snapshots/*
    */screenshots/*
    */recordings/*
    */captures/*
    */traces/*
    */profiles/*
    */benchmarks/*
    */performance/*
    */monitoring/*
    */metrics/*
    */analytics/*
    */statistics/*
    */visualizations/*
    */charts/*
    */graphs/*
    */plots/*
    */images/*
    */videos/*
    */audio/*
    */documents/*
    */pdfs/*
    */spreadsheets/*
    */presentations/*
    */slides/*
    */notebooks/*
    */jupyter/*
    */ipynb_checkpoints/*
    */research/*
    */experiments/*
    */prototypes/*
    */demos/*
    */samples/*
    */fixtures/*
    */mocks/*
    */stubs/*
    */fakes/*
    */dummies/*
    */placeholders/*
    */templates/*
    */boilerplate/*
    */scaffolding/*
    */generators/*
    */builders/*
    */compilers/*
    */transpilers/*
    */bundlers/*
    */packagers/*
    */installers/*
    */deployers/*
    */publishers/*
    */distributors/*
    */vendors/*
    */third_party/*
    */external/*
    */libs/*
    */libraries/*
    */dependencies/*
    */packages/*
    */modules/*
    */plugins/*
    */extensions/*
    */addons/*
    */widgets/*
    */components/*
    */controls/*
    */elements/*
    */fragments/*
    */partials/*
    */includes/*
    */snippets/*
    */blocks/*
    */sections/*
    */pages/*
    */views/*
    */screens/*
    */forms/*
    */dialogs/*
    */modals/*
    */popups/*
    */tooltips/*
    */notifications/*
    */alerts/*
    */messages/*
    */banners/*
    */headers/*
    */footers/*
    */sidebars/*
    */menus/*
    */navigation/*
    */breadcrumbs/*
    */pagination/*
    */tabs/*
    */accordions/*
    */carousels/*
    */sliders/*
    */galleries/*
    */grids/*
    */tables/*
    */lists/*
    */cards/*
    */tiles/*
    */panels/*
    */containers/*
    */wrappers/*
    */layouts/*
    */themes/*
    */styles/*
    */css/*
    */scss/*
    */sass/*
    */less/*
    */stylus/*
    */js/*
    */javascript/*
    */typescript/*
    */jsx/*
    */tsx/*
    */vue/*
    */react/*
    */angular/*
    */svelte/*
    */ember/*
    */backbone/*
    */jquery/*
    */bootstrap/*
    */foundation/*
    */materialize/*
    */bulma/*
    */tailwind/*
    */semantic/*
    */ant/*
    */element/*
    */vuetify/*
    */quasar/*
    */chakra/*
    */mantine/*
    */nextui/*
    */primereact/*
    */primevue/*
    */primefaces/*
    */primeblocks/*
    */primeflex/*
    */primeicons/*
    */primeng/*
    */sakai/*
    */apollo/*
    */diamond/*
    */avalon/*
    */babylon/*
    */freya/*
    */ultima/*
    */serenity/*
    */poseidon/*
    */roma/*
    */manhattan/*
    */harmony/*
    */prestige/*
    */mirage/*
    */california/*
    */ecuador/*
    */ecuador-ng/*
    */ecuador-react/*
    */ecuador-vue/*
    */ecuador-angular/*
    */ecuador-svelte/*
    */ecuador-ember/*
    */ecuador-backbone/*
    */ecuador-jquery/*
    */ecuador-bootstrap/*
    */ecuador-foundation/*
    */ecuador-materialize/*
    */ecuador-bulma/*
    */ecuador-tailwind/*
    */ecuador-semantic/*
    */ecuador-ant/*
    */ecuador-element/*
    */ecuador-vuetify/*
    */ecuador-quasar/*
    */ecuador-chakra/*
    */ecuador-mantine/*
    */ecuador-nextui/*
    */ecuador-primereact/*
    */ecuador-primevue/*
    */ecuador-primefaces/*
    */ecuador-primeblocks/*
    */ecuador-primeflex/*
    */ecuador-primeicons/*
    */ecuador-primeng/*
    */ecuador-sakai/*
    */ecuador-apollo/*
    */ecuador-diamond/*
    */ecuador-avalon/*
    */ecuador-babylon/*
    */ecuador-freya/*
    */ecuador-ultima/*
    */ecuador-serenity/*
    */ecuador-poseidon/*
    */ecuador-roma/*
    */ecuador-manhattan/*
    */ecuador-harmony/*
    */ecuador-prestige/*
    */ecuador-mirage/*
    */ecuador-california/*

[report]
# 报告配置
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
    @(abc\.)?abstractproperty
    @(abc\.)?abstractclassmethod
    @(abc\.)?abstractstaticmethod
    def __str__
    def __unicode__
    def __bytes__
    def __format__
    def __sizeof__
    def __hash__
    def __bool__
    def __nonzero__
    def __len__
    def __iter__
    def __next__
    def __reversed__
    def __contains__
    def __getitem__
    def __setitem__
    def __delitem__
    def __missing__
    def __enter__
    def __exit__
    def __call__
    def __getattr__
    def __setattr__
    def __delattr__
    def __getattribute__
    def __dir__
    def __get__
    def __set__
    def __delete__
    def __set_name__
    def __init_subclass__
    def __class_getitem__
    def __getnewargs__
    def __getnewargs_ex__
    def __getstate__
    def __setstate__
    def __reduce__
    def __reduce_ex__
    def __copy__
    def __deepcopy__
    def __getinitargs__
    def __pos__
    def __neg__
    def __abs__
    def __invert__
    def __round__
    def __trunc__
    def __floor__
    def __ceil__
    def __add__
    def __sub__
    def __mul__
    def __matmul__
    def __truediv__
    def __floordiv__
    def __mod__
    def __divmod__
    def __pow__
    def __lshift__
    def __rshift__
    def __and__
    def __xor__
    def __or__
    def __radd__
    def __rsub__
    def __rmul__
    def __rmatmul__
    def __rtruediv__
    def __rfloordiv__
    def __rmod__
    def __rdivmod__
    def __rpow__
    def __rlshift__
    def __rrshift__
    def __rand__
    def __rxor__
    def __ror__
    def __iadd__
    def __isub__
    def __imul__
    def __imatmul__
    def __itruediv__
    def __ifloordiv__
    def __imod__
    def __ipow__
    def __ilshift__
    def __irshift__
    def __iand__
    def __ixor__
    def __ior__
    def __lt__
    def __le__
    def __eq__
    def __ne__
    def __gt__
    def __ge__
    def __complex__
    def __int__
    def __float__
    def __index__
    def __round__
    def __trunc__
    def __floor__
    def __ceil__

ignore_errors = True
skip_covered = False
skip_empty = True
show_missing = True
precision = 2

[html]
# HTML报告配置
directory = htmlcov
title = FinRL Crypto Test Coverage Report
show_contexts = True

[xml]
# XML报告配置
output = coverage.xml

[json]
# JSON报告配置
output = coverage.json
show_contexts = True
pretty_print = True