#!/usr/bin/env python3
"""
训练器接口定义

这个模块定义了训练器的抽象接口，用于解耦具体的强化学习框架（如ElegantRL）。
通过这个接口，可以轻松地切换不同的训练框架或添加新的训练实现。

作者: FinRL-Crypto 重构项目
日期: 2024
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Tuple, List
import numpy as np


class TrainerInterface(ABC):
    """
    训练器抽象接口
    
    定义了所有训练器必须实现的方法，包括训练、评估、预测等核心功能。
    这个接口将具体的强化学习框架（如ElegantRL）与业务逻辑解耦。
    """
    
    @abstractmethod
    def train(self, agent_config: Dict[str, Any], env_config: Dict[str, Any], 
              training_config: Dict[str, Any]) -> str:
        """
        训练强化学习代理
        
        Args:
            agent_config: 代理配置参数
            env_config: 环境配置参数
            training_config: 训练配置参数
            
        Returns:
            str: 训练结果保存路径
            
        Raises:
            TrainingError: 训练过程中发生错误
        """
        pass
    
    @abstractmethod
    def evaluate(self, model_path: str, env_config: Dict[str, Any], 
                 evaluation_config: Dict[str, Any]) -> Dict[str, float]:
        """
        评估训练好的模型
        
        Args:
            model_path: 模型文件路径
            env_config: 环境配置参数
            evaluation_config: 评估配置参数
            
        Returns:
            Dict[str, float]: 评估指标字典
            
        Raises:
            EvaluationError: 评估过程中发生错误
        """
        pass
    
    @abstractmethod
    def predict(self, model_path: str, env_config: Dict[str, Any], 
                prediction_config: Dict[str, Any]) -> List[float]:
        """
        使用训练好的模型进行预测
        
        Args:
            model_path: 模型文件路径
            env_config: 环境配置参数
            prediction_config: 预测配置参数
            
        Returns:
            List[float]: 预测结果（如总资产变化）
            
        Raises:
            PredictionError: 预测过程中发生错误
        """
        pass
    
    @abstractmethod
    def get_supported_models(self) -> List[str]:
        """
        获取支持的模型列表
        
        Returns:
            List[str]: 支持的模型名称列表
        """
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        验证配置参数的有效性
        
        Args:
            config: 配置参数字典
            
        Returns:
            bool: 配置是否有效
        """
        pass
    
    @abstractmethod
    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """
        获取模型信息
        
        Args:
            model_name: 模型名称
            
        Returns:
            Dict[str, Any]: 模型信息字典
            
        Raises:
            ModelNotFoundError: 模型不存在
        """
        pass


class ModelInterface(ABC):
    """
    模型抽象接口
    
    定义了模型对象必须实现的方法。
    """
    
    @abstractmethod
    def save(self, path: str) -> None:
        """
        保存模型到指定路径
        
        Args:
            path: 保存路径
        """
        pass
    
    @abstractmethod
    def load(self, path: str) -> None:
        """
        从指定路径加载模型
        
        Args:
            path: 模型文件路径
        """
        pass
    
    @abstractmethod
    def predict(self, state: np.ndarray) -> np.ndarray:
        """
        根据状态预测动作
        
        Args:
            state: 环境状态
            
        Returns:
            np.ndarray: 预测的动作
        """
        pass


class TrainingError(Exception):
    """训练过程中的错误"""
    pass


class EvaluationError(Exception):
    """评估过程中的错误"""
    pass


class PredictionError(Exception):
    """预测过程中的错误"""
    pass


class ModelNotFoundError(Exception):
    """模型不存在错误"""
    pass


class ConfigurationError(Exception):
    """配置错误"""
    pass