version: '3.8'

# FinRL Crypto Docker Compose配置
# 用于本地开发和生产部署

services:
  # 主应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
      args:
        BUILD_DATE: ${BUILD_DATE:-$(date -u +'%Y-%m-%dT%H:%M:%SZ')}
        VCS_REF: ${VCS_REF:-$(git rev-parse --short HEAD)}
        VERSION: ${VERSION:-latest}
    image: finrl/crypto:${VERSION:-latest}
    container_name: finrl-crypto-app
    restart: unless-stopped
    ports:
      - "${APP_PORT:-8000}:8000"
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=${DEBUG:-false}
      - WORKERS=${WORKERS:-2}
      - HOST=0.0.0.0
      - PORT=8000
      - DATABASE_URL=${DATABASE_URL:-sqlite:///data/finrl.db}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - PYTHONPATH=/app
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - ./checkpoints:/app/checkpoints
      - ./results:/app/results
      - ./config:/app/config
    depends_on:
      - redis
      - postgres
    networks:
      - finrl-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # 后台工作进程
  worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    image: finrl/crypto:${VERSION:-latest}
    container_name: finrl-crypto-worker
    restart: unless-stopped
    command: ["worker"]
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DATABASE_URL=${DATABASE_URL:-sqlite:///data/finrl.db}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - PYTHONPATH=/app
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - ./checkpoints:/app/checkpoints
      - ./results:/app/results
    depends_on:
      - redis
      - postgres
    networks:
      - finrl-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # 任务调度器
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    image: finrl/crypto:${VERSION:-latest}
    container_name: finrl-crypto-scheduler
    restart: unless-stopped
    command: ["scheduler"]
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DATABASE_URL=${DATABASE_URL:-sqlite:///data/finrl.db}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - PYTHONPATH=/app
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
      - postgres
    networks:
      - finrl-network

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: finrl-crypto-postgres
    restart: unless-stopped
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-finrl}
      - POSTGRES_USER=${POSTGRES_USER:-finrl}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-finrl123}
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - finrl-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-finrl} -d ${POSTGRES_DB:-finrl}"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: finrl-crypto-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    networks:
      - finrl-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: finrl-crypto-nginx
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./static:/var/www/static:ro
    depends_on:
      - app
    networks:
      - finrl-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: finrl-crypto-prometheus
    restart: unless-stopped
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - finrl-network

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: finrl-crypto-grafana
    restart: unless-stopped
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    depends_on:
      - prometheus
    networks:
      - finrl-network

  # Jupyter Notebook (开发环境)
  jupyter:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    image: finrl/crypto:dev
    container_name: finrl-crypto-jupyter
    restart: unless-stopped
    ports:
      - "${JUPYTER_PORT:-8888}:8888"
    command: ["jupyter"]
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - JUPYTER_ENABLE_LAB=yes
    volumes:
      - .:/app
      - jupyter_data:/home/<USER>/.jupyter
    networks:
      - finrl-network
    profiles:
      - dev

# 网络配置
networks:
  finrl-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  jupyter_data:
    driver: local